# Metadata
/.buildpath
/.cache
/.metadata
/.project
/.settings
atlassian*
/nbproject
/sitemap
/.idea
/.gitattributes

# Magento specific configuration
/app/.htaccess
/app/bootstrap.php
/app/autoload.php
/app/functions.php
/app/etc/env.php
/app/etc/di.xml
/app/etc/vendor_path.php
/app/etc/NonComposerComponentRegistration.php

# Files re-generated through composer
/CHANGELOG.md
/CONTRIBUTING.md
/COPYING.txt
/ISSUE_TEMPLATE.md
/LICENSE_AFL.txt
/LICENSE.txt
/SECURITY.md
/*.sample
/vendor
/bin
/dev
/lib
/pub
/setup
/var
/generated
/node_modules
/.editorconfig
/.htaccess
/.php_cs.dist
/.user.ini
/phpserver

# Excludes (or a better alternative might be to symlink this to shared folders
!/pub/media/catalog
!/pub/media/downloadable
!/var/composer_home

# Excludes Tailwind builded files
/app/design/frontend/Udigital/Topswtw/web/css/styles.css
/app/design/frontend/Udigital/Topswtw/web/tailwind/tailwind-output.css

*.sql
*.sample
media.tar.gz

/.php-cs-fixer.cache
