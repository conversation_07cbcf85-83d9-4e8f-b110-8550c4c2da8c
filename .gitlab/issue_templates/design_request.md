[//]: <> (This ticket template can be used to specify the build for a front-end page)
[//]: <> (A page usually contains functionality. When this is the case, each feature should be specified with)
[//]: <> (a list of acceptance criteria. Note: when a feature is to big it should be separated to a new ticket)

### General information:
[//]: <> (Link to the figma file)
- Figma link: 

[//]: <> (Any other usefull information regarding the page)


##### Feature/block on the homepage 1
[//]: <> (Short descripton of the feature/block on the page)
- [ ] Acceptance criteria 1
- [ ] Acceptance criteria 2

##### Feature/block on the homepage 1
[//]: <> (Short descripton of the feature/block on the page)
- [ ] Acceptance criteria 1
- [ ] Acceptance criteria 2

### Screenshots
[//]: <> (screenshot of the to be developed item)
