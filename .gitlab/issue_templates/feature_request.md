[//]: <> (This is a base template to create new feature requests for your) 
[//]: <> (project/module. Ensure that you write down the "what" and "why" of)
[//]: <> (the user story and not the "how". The "how" will be defined by the)
[//]: <> (scrum team when this story is picked up.)

Als [rol] wil ik [functionaliteit], zodat [reden].

[//]: <> (Additional information to make the story more clear for the team.)

## Acceptance Criteria

- [ ] Requirement 1
- [ ] Requirement 2

## Testing Requirements
[//]: <> (Here you can add the testing requirements to test this story.)
[//]: <> (This needs to be filled by the developer who picked up the story.)

Steps to test
- Step 1
- Step 2
- Step 3

Expected results:
- Expectation 1
- Expectation 2

## Tech Note
[//]: <> (If needed you can add additional tech notes.)

## Release Notes
[//]: <> (When a story is released and requires actions after or during the release,)
[//]: <> (make sure you add that here. This ensures that anyone can release this and)
[//]: <> (won't forget to do additional steps after the release.)


