[//]: <> (This is a base template to create a bug report within the)
[//]: <> (project/module. Ensure you write down the steps to reproduce the)
[//]: <> (issue and also what you expect that would happen and what actually)
[//]: <> (happens.)

## Steps to Reproduce
[//]: <> (Write down the steps to reproduce the issue. Make sure you write)
[//]: <> (down all the steps and make them as clear as possible.)

1. First step
1. Second step
1. ...

## Expected Results
[//]: <> (Write down the expected results when all the steps to reproduce are done.)

1. Result 1
1. Result 2
1. Result 3

## Actual Results
[//]: <> (Write down the actual results after all the steps to reproduce are done.)

1. Result 1
1. Result 2
1. Result 3

## Tech Note
[//]: <> (If needed you can add additional tech notes.)

## Release Notes
[//]: <> (When a story is released and requires actions after or during the release,)
[//]: <> (make sure you add that here. This ensures that anyone can release this and)
[//]: <> (won't forget to do additional steps after the release.)
