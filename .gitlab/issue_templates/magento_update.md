### Installeren Magento update samen met de Upgrade patch helper

*Zie: [Patchhelper](https://github.com/AmpersandHQ/ampersand-magento2-upgrade-patch-helper)*

#### Maak een backup van je vendor map op later te diffen

* [ ]  `composer install`
* [ ]  `mv vendor vendor_orig`

#### Start update
* [ ]  `composer install`
* [ ]  `composer require magento/product-community-edition 2.x.x --no-update`

#### Fix fouten
Het kan nu zijn dat je dependency fouten krijgt omdat je magento geupdate hebt. Fix deze totdat `composer install` succesvol is verlopen. Notes:

- Heeft de module support voor de nieuwe versie? Kijk naar een alternatief. Bespreek met de klant
- Is de module nog wel nodig?
- Kunnen we eerst even zonder? 
- Kom in contact met de bouwer om te vragen of er een nieuwe versie aan komt
- Is het kritisch en gaat de klant akkoord, fix het dan zelf voor deze module

#### Maak de patch
* [ ] `diff -ur vendor_orig/ vendor/ > vendor.patch`

#### Sanity testen van lokale omgeving
* [ ] Homepage wordt weergegeven
* [ ] Er kan gezocht worden
* [ ] Category overzicht wordt weergegeven / filtering werkt
* [ ] Werkt product detail
* [ ] Kan er een product besteld worden

##### Creeer output adv patch

- git clone https://github.com/AmpersandHQ/ampersand-magento2-upgrade-patch-helper
- cd ampersand-magento2-upgrade-patch-helper
- composer install
- php bin/patch-helper.php analyse /path/to/magento2/

###### Plaats output hier:
```

```

* [ ]  Check en fix/update alle files.

---

# Magento is geupdate!

We willen ook direct extensies meenemen. Ga niet alle extensies bij langs, maar even de mission critical extensies als, payment, shipping, feed generators etc..

#### Extensies 
* [ ] Controleren of alle geïnstalleerde plugins een ge-update versie hebben voor de nieuwste release
```

### Lijst met extentsie en versies ###

```
* [ ] Installeren nieuwe versie van extensies waar nodig
* [ ] Vervangen in overleg indien er geen update meer beschikbaar komt (ivm geen onderhoud derde partij)

---

#### Architectuur 
*(Kopje verwijderen indien niet nodig)*
* [ ]  PHP versie update
* [ ]  Elastic search

---

#### Review en Testen
* [ ]  Review omgeving in de lucht brengen met de update
* [ ]  Uitvoeren van de test ticket ()
