[//]: <> (You can use this ticket as a base template if you have a meeting) 
[//]: <> (When having a meeting it helps to have a clear goal and talkingpoints)
[//]: <> (Remove any headers that are not required for the meeting for a clear overview)


#### Check-in
[//]: <> (Quick checkin before the meeting starts) 
[//]: <> (Check if there are any points that need to be added to the agenda)
[//]: <> (Communicate the timebox for the meeting) 
 
- [ ] Check "last agenda points"
- [ ] Is the current agenda up-to-date
- [ ] Timebox for this meeting: x uur

#### Last agenda points
[//]: <> (Talk through the points carried over from a previous meeting) 
[//]: <> (Pro-tip: Connect tickets to the agenda points!) 
- [ ] 

#### Agenda points
[//]: <> (Agenda points that need to be discussed) 
- [ ] 

[//]: <> (Actions that need to be taken per agenda point) 
Per agenda point:
- Present point
- Ask if there are any questions
- React
- Record decision in the ticket (add a comment per agenda point)

#### Checkout
- [ ] Record any agenda points that need to be revisited
- [ ] Create any tickets that may be needed for follow up (check comments!)
- [ ] Close meeting
