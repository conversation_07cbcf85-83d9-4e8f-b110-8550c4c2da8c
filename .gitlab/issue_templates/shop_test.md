
### Voor de developer
* [ ]  Z<PERSON> de betaling’s methode op test, zodat de klant een order kan plaatsen met alle betaling’s methoden
* [ ]  Speciale coupon code toevoegen voor het testen 10% korting

### Review omgeving
- Review omgeving locatie: {URL_REVIEW_OMGEVING}
---

### Beveiliging’s login
De review omgeving is beschermt met een inlog (htaccess).

Voer de onderstaande username en wachtwoord in om de test omgeving te bekijken:
- Username: elgentos
- Password: elgentos
---

### Login magento admin / beheer omgeving
- Admin url: {URL_REVIEW_OMGEVING}/beheer
- Admin gebruiker: {REVIEW_ADMIN_USER}
- Admin wachtwoord: {REVIEW_ADMIN_PASSWORD}
---

### Wat genegeerd mag worden:
- SSL waarschuwingen.
- De producten die recent zijn toegevoegd. Er wordt niet altijd gebruik gemaakt van een meest recente database kopie bij het klaarzetten van de review omgeving.
- De product afbeeldingen missen. Omdat het vaak om grote hoeveelheden afbeeldingen gaat migreren we deze niet mee.
Daarom kan het voorkomen dat *product afbeeldingen* niet altijd zichtbaar zijn.
- De snelheid van de website. De review draait op een vele male lichtere server omgeving dan de productie website. Hierdoor is de review omgeving trager dan de productie omgeving.
---

### De testlijst
**Algemeen**
Wordt de website in het algemeen nog correct weergegeven?
* [ ]  Wordt de header correct weergegeven?
* [ ]  Wordt de footer nog correct weergegeven
* [ ]  Worden informatie pagina's nog correct weergegeven
* [ ]  Werken alle links in de footer nog?

**Zoeken / Category pagina**
* [ ]  Kan er gezocht worden naar producten in de zoekbalk?
* [ ]  Worden de zelfde zoek resultaten weergegeven op productie als op de review met de zelfde zoekterm?
* [ ]  Worden er zoeksuggesties weergegeven bij het invoeren van een gedeelte van de zoekterm?
* [ ]  Kan er gefilterd worden op attributen van een product?
* [ ]  Werkt de sortering naar behoren?
* [ ]  Worden alle selectie verfijning opties weergegeven die verwacht worden?
* [ ]  Worden categorie teksten / afbeeldingen correct weergegeven

**Product detail pagina(controleer 10 artikelen in verschillende categorieën)**
* [ ]  Worden teksten correct weergegeven?
* [ ]  Worden afbeeldingen correct weergegeven?
* [ ]  In dien er reviews zijn: staan deze erbij?
* [ ]  Klopt de prijs en BTW?
* [ ]  Worden eventuele sets / crossells correct weergegeven?

**Winkelwagen**
* [ ]  Is de weergave van de winkelwagen correct?
* [ ]  Kunnen artikelen toegevoegd worden aan het mandje?
* [ ]  Werkt het toevoegen van een coupon code? (indien dit kan)
* [ ]  Worden prijzen correct weergegeven? (in geval van meerdere landen, klopt de btw dan ook)

**Checkout**
* [ ]  Kan er een bestelling geplaatst worden met ```iDeal``` (hier is test voor nodig)
* [ ]  Kan er een bestelling geplaatst worden met ```Creditcard``` (hier is test voor nodig)
* [ ]  Kan er een bestelling geplaatst worden met ```Bank transfer```
* [ ]  Komt er een bevestiging's email binnen
* [ ]  Als de betaling binnen is wordt er dan ook een Factuur verstuurd?
* [ ]  Worden er shipping updates verstuurd?
* [ ]  Kun je een bestelling plaatsen als je ingelogd bent met verschillende betaling's methoden

**Mijn account**
* [ ]  Kan er een account aangemaakt worden
* [ ]  Ziet het hoofdmenu van de mijn account pagina er netjes uit?
* [ ]  Ziet het kopje ```Recente orders``` er correct uit op de mijn account pagina? (deze is te zien nadat er een order is geplaatst als je ingelogd bent)
* [ ]  Ziet en werkt de pagina ```Mijn account``` nog naar behoren?
* [ ]  Ziet en werkt de pagina ```Downloads``` nog naar behoren?
* [ ]  Ziet en werkt de pagina ```Mijn verlanglijst``` nog naar behoren?
* [ ]  Ziet en werkt de pagina ```Adresboek``` nog naar behoren? (kan er een adres aangemaakt worden / verwijderd worden)
* [ ]  Ziet en werkt de pagina ```Account gegevens``` nog naar behoren? (kan een wachtwoord bv aangepast worden)

---

### Klant specifiek
* [ ]  ..

---

