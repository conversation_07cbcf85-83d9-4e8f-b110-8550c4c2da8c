## Algemeen
- Tijd beschikbaar deze sprint: xx uur
- Tijd gewerkt deze maand: xx uur
- Datum/tijd sprintplanning:

## Sprint voorbereiding
- [ ] Is de volgende milestone aangemaakt?
- [ ] Vul de beschikbare geplande tijd in voor de volgende sprint.

### Wat gaan we oppakken

#### Bestaande tickets
Het kan zijn dat er werk overblijft uit de vorige sprint of werk wat eerder ingeschat is komt nu aan bod: (per ticket)
- [ ] Checken of de uren inschatting nog steeds klopt.
- [ ] Indien er eerder aan de ticket gewerkt is, Check of het geen meta ticket is geworden en re-scope de uren. In geval metaticket deze uitsplitsen.

#### Nieuwe tickets
Zijn er nieuwe tickets ingeschoten door de klant? (check per ticket)
- [ ] Voldoet deze aan het correcte ticket formaat? (incident / nieuwe feature)
- [ ] Is de inhoud duidelijk? zo niet stel de vragen in de ticket. <PERSON><PERSON><PERSON> graag in "reply on comment"

#### Webshop optimaliseren
Indien er niet voldoende werk is aangedragen door de klant voor de aankomende sprint dan kan er uitgeweken worden naar Webshop optimalisatie lijst zie: ```https://gitlab.elgentos.nl/elgentos/internals/-/issues/153``` om de sprint aan te vullen.

<sub>Tickets waar eerder aangewerkt is hebben voorrang</sub>

## Grooming en inschatten
- [ ] Zijn er vragen? Neem contact op met de klant met het verzoek op antwoord van de vragen.
- [ ] Is de ticket voorzien van een label Short-term/Mid-term/Long-term
- [ ] Inschatten alle tickets met label Short-term

## Sprint call
- [ ] Vaststellen met de klant welke tickets opgepakt kunnen worden. Afhankelijk van ingeplande tijd in de sprint.
