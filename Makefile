BASEURL = https://tops.magento2.localhost/

B2_BUCKET = b2://elgentos-tops/

PHP_COMMAND = php -d 'memory_limit=-1'
COMPOSER = composer2

DB_HOST = db8
DB_USER = $(USER)
DB_PASSWORD =
DB_NAME = $(shell [ ! -e app/etc/env.php ] && echo $(USER)_tops_m2 || (grep dbname app/etc/env.php | cut -d"'" -f4))
DB_FILE = development.sql

ADMIN_USER = $(USER)
ADMIN_PASSWORD = Test1234!
ADMIN_EMAIL = $(USER)@elgentos.nl
ADMIN_FIRST = De
ADMIN_LAST = Veloper

MAGENTO_INSTALL_ADDITIONAL =

STATIC_ADMINHTML_LANGUAGES = en_US nl_NL
STATIC_FRONTEND_LANGUAGES = nl_NL
STATIC_FRONTEND_ADDITIONAL = -t Udigital/Topswtw -s compact

SEARCH_ENGINE = elasticsuite
ELASTIC_HOST = elasticsearch7
ELASTIC_PORT = 9200
ELASTIC_INDEX = ap2

IDE = phpstorm

all: install

-include Makefile.includes/Makefile.*

.PHONY: clean
clean:
	rm -rf var/cache var/page_cache generated pub/static vendor app/design/frontend/Udigital/Topswtw/web/tailwind/node_modules/
	mkdir pub/static generated

.PHONY: clean-node
clean-node: 

.PHONY: install
install: vendor/autoload.php db app/etc/env.php gene-key-index setup setup-configvalues dev-mode dev-user urls elasticsuite media hyva reindex gitignore phpstorm-configuration

.PHONY: init
init: clean vendor/autoload.php generated/code/ upgrade

.PHONY: dev
dev: dev-mode dev-user upgrade

.PHONY: dev-mode
dev-mode:
	$(PHP_COMMAND) bin/magento dep:mode:set developer

.PHONY: setup
setup: upgrade
.PHONY: upgrade
upgrade:
	$(PHP_COMMAND) bin/magento set:up

.PHONY: prod
prod: clean vendor-no-dev prod-mode generated/code/ pub/static/adminhtml/ pub/static/frontend/
	$(COMPOSER) dump -oa --no-plugins

.PHONY: prod-mode
prod-mode:
	$(PHP_COMMAND) bin/magento dep:mode:set production --skip-compilation

.PHONY: urls
urls:
	$(PHP_COMMAND) bin/magento config:set -le 'web/url/use_store' 1
	$(PHP_COMMAND) bin/magento config:set -le 'web/seo/use_rewrites' 1
	$(PHP_COMMAND) bin/magento config:set -le 'web/secure/use_in_frontend' 1
	$(PHP_COMMAND) bin/magento config:set -le 'web/unsecure/base_url' "$(BASEURL)"
	$(PHP_COMMAND) bin/magento config:set -le 'web/unsecure/base_link_url' "$(BASEURL)"
	$(PHP_COMMAND) bin/magento config:set -le 'web/secure/base_url' "$(BASEURL)"
	$(PHP_COMMAND) bin/magento config:set -le 'web/secure/base_link_url' "$(BASEURL)"
	echo 'delete from core_config_data where path like "%/%base\_%url" and value like "http%://%";' | mysql $(DB_NAME)
	echo 'delete from core_config_data where path like "%/%cookie\_%domain";' | mysql $(DB_NAME)

.PHONY: dev-user
dev-user:
	magerun2 db:add-default-authorization-entries
	magerun2 admin:user:delete -n "$(ADMIN_USER)" || exit 0
	magerun2 admin:user:create --admin-user="$(ADMIN_USER)" --admin-password="$(ADMIN_PASSWORD)" --admin-email="$(ADMIN_EMAIL)" --admin-firstname="$(ADMIN_FIRST)" --admin-lastname="$(ADMIN_LAST)"

.PHONY: reindex
reindex:
	$(PHP_COMMAND) bin/magento index:reindex

.PHONY: gitignore
gitignore:
	git checkout .gitignore

.PHONY: cc
cc:
	$(PHP_COMMAND) bin/magento ca:cl

.PHONY: media
media: media.tar.gz
	@if [ -f media.tar.gz ]; then \
		echo "Existing media.tar.gz file found."; \
		read -p "Do you want to download a fresh copy? (y/N): " answer; \
		if [ "$$answer" = "y" ] || [ "$$answer" = "Y" ]; then \
			rm -f media.tar.gz; \
			b2 file download ${B2_BUCKET}media.tar.gz .; \
		fi; \
	fi
	tar xzvf media.tar.gz -C pub/

.PHONY: db
db:
	@if [ -f $(DB_FILE) ]; then \
		echo "Existing $(DB_FILE) file found."; \
		read -p "Do you want to download a fresh copy? (y/N): " answer; \
		if [ "$$answer" = "y" ] || [ "$$answer" = "Y" ]; then \
			rm -f $(DB_FILE); \
			b2 file download ${B2_BUCKET}$(DB_FILE) .; \
		fi; \
	else \
		b2 file download ${B2_BUCKET}$(DB_FILE) .; \
	fi
	$(MAKE) db-drop db-create
	cat $(DB_FILE) | mysql $(DB_NAME)

.PHONY: db-drop
db-drop:
	mysql -e"drop database if exists $(DB_NAME)"

.PHONY: db-create
db-create:
	mysql -e"create database if not exists $(DB_NAME)"

.PHONY: update-db
update-db: remove-db db vendor/autoload.php gene-key-index setup setup-configvalues dev-user urls dev-mode reindex

.PHONY: remove-db
remove-db:
	rm -f $(DB_FILE)

.PHONY: update
update: clean
	$(COMPOSER) update --prefer-dist -o $(COMPOSER_ADDITIONAL_PARAMS)

.PHONY: vendor-no-dev
vendor-no-dev:
	$(COMPOSER) install --prefer-dist -o --no-dev $(COMPOSER_ADDITIONAL_PARAMS)

vendor/autoload.php:
	$(COMPOSER) install --prefer-dist -o $(COMPOSER_ADDITIONAL_PARAMS)

app/etc/env.php:
	@echo 'Install Magento'
	$(PHP_COMMAND) bin/magento set:install --backend-frontname=admin \
	    --db-host="$(DB_HOST)" --db-user=$(USER) --db-password="$(DB_PASSWORD)" --db-name="$(DB_NAME)" \
		--search-engine="$(SEARCH_ENGINE)" \
		--elasticsearch-host="$(ELASTIC_HOST)" \
		--elasticsearch-port="$(ELASTIC_PORT)" \
		--elasticsearch-index-prefix=$(ELASTIC_INDEX) \
		$(MAGENTO_INSTALL_ADDITIONAL)

.PHONY: elastic
elastic:
	$(PHP_COMMAND) bin/magento config:set -le 'catalog/search/engine' "$(SEARCH_ENGINE)"
	$(PHP_COMMAND) bin/magento config:set -le 'catalog/search/elasticsearch7_server_hostname' "$(ELASTIC_HOST)"
	$(PHP_COMMAND) bin/magento config:set -le 'catalog/search/elasticsearch7_server_port' "$(ELASTIC_PORT)"
	$(PHP_COMMAND) bin/magento config:set -le 'catalog/search/elasticsearch7_index_prefix' "$(ELASTIC_INDEX)"

generated/code/:
	$(PHP_COMMAND) bin/magento set:di:com

pub/static/adminhtml/:
	$(PHP_COMMAND) bin/magento set:sta:dep -fa adminhtml $(STATIC_ADMINHTML_LANGUAGES) -s compact

pub/static/frontend/:
	$(PHP_COMMAND) bin/magento set:sta:dep -fa frontend $(STATIC_FRONTEND_LANGUAGES) $(STATIC_FRONTEND_ADDITIONAL)

%.sql:
	b2 file download ${B2_BUCKET}$(@F) .

media.tar.gz:
	b2 file download ${B2_BUCKET}media.tar.gz .

.PHONY: ide
ide:
	exec $(IDE) . 2>&1 >/dev/null &

.PHONY: hyva
hyva: hyva-install hyva-build

.PHONY: hyva-install
hyva-install: app/design/frontend/Udigital/Topswtw/web/tailwind/node_modules/

app/design/frontend/Udigital/Topswtw/web/tailwind/node_modules/:
	npm --prefix app/design/frontend/Udigital/Topswtw/web/tailwind install

.PHONY: hyva-build
hyva-build:
	npm --prefix app/design/frontend/Udigital/Topswtw/web/tailwind run build

.PHONY: gene-key-index
gene-key-index:
	echo 'update core_config_data set value = "0" where path = "gene/encryption_key_manager/invalidated_key_index" AND scope = "default"' | mysql $(DB_NAME)
