.PHONY: phpstorm-configuration
phpstorm-configuration: phpstorm-database-configuration phpstorm-php-configuration phpstorm-urn-generation

.PHONY: phpstorm-urn-generation
phpstorm-urn-generation:
	@if [ ! -f .idea/misc.xml ]; then \
		bin/magento dev:urn-catalog:generate .idea/misc.xml; \
	fi

.PHONY: phpstorm-database-configuration
phpstorm-database-configuration:
	@if [ ! -f .idea/dataSources.xml ]; then \
		echo "Creating PhpStorm database configuration..."; \
		mkdir -p .idea; \
		echo '<?xml version="1.0" encoding="UTF-8"?>' > .idea/dataSources.xml; \
		echo '<project version="4">' >> .idea/dataSources.xml; \
		echo '  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">' >> .idea/dataSources.xml; \
		echo '    <data-source source="LOCAL" name="${DB_NAME}@localhost" uuid="a2428579-3407-4dd3-b828-b12a705edba2">' >> .idea/dataSources.xml; \
		echo '      <driver-ref>mysql.8</driver-ref>' >> .idea/dataSources.xml; \
		echo '      <synchronize>true</synchronize>' >> .idea/dataSources.xml; \
		echo '      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>' >> .idea/dataSources.xml; \
		echo '      <jdbc-url>***************************/${DB_NAME}</jdbc-url>' >> .idea/dataSources.xml; \
		echo '      <working-dir>$$ProjectFileDir$$</working-dir>' >> .idea/dataSources.xml; \
		echo '    </data-source>' >> .idea/dataSources.xml; \
		echo '  </component>' >> .idea/dataSources.xml; \
		echo '</project>' >> .idea/dataSources.xml; \
		echo "Configuration file created at .idea/dataSources.xml"; \
	else \
		echo ".idea/dataSources.xml already exists, skipping creation."; \
	fi

.PHONY: phpstorm-php-configuration
phpstorm-php-config:
	@if [ ! -f .idea/php.xml ]; then \
		echo "Creating PhpStorm PHP configuration..."; \
		mkdir -p .idea; \
		echo '<?xml version="1.0" encoding="UTF-8"?>' > .idea/php.xml; \
		echo '<project version="4">' >> .idea/php.xml; \
		echo '  <component name="MessDetector">' >> .idea/php.xml; \
		echo '    <phpmd_settings>' >> .idea/php.xml; \
		echo '      <MessDetectorConfiguration tool_path="$$PROJECT_DIR$$/vendor/bin/phpmd" />' >> .idea/php.xml; \
		echo '    </phpmd_settings>' >> .idea/php.xml; \
		echo '  </component>' >> .idea/php.xml; \
		echo '  <component name="MessDetectorOptionsConfiguration">' >> .idea/php.xml; \
		echo '    <option name="customRulesets">' >> .idea/php.xml; \
		echo '      <list>' >> .idea/php.xml; \
		echo '        <RulesetDescriptor>' >> .idea/php.xml; \
		echo '          <option name="name" value="Youwe PHPMD rules" />' >> .idea/php.xml; \
		echo '          <option name="path" value="$$PROJECT_DIR$$/vendor/youwe/coding-standard-magento2/src/YouweMagento2/phpmd.xml" />' >> .idea/php.xml; \
		echo '        </RulesetDescriptor>' >> .idea/php.xml; \
		echo '      </list>' >> .idea/php.xml; \
		echo '    </option>' >> .idea/php.xml; \
		echo '    <option name="transferred" value="true" />' >> .idea/php.xml; \
		echo '  </component>' >> .idea/php.xml; \
		echo '  <component name="PHPCSFixerOptionsConfiguration">' >> .idea/php.xml; \
		echo '    <option name="codingStandard" value="Custom" />' >> .idea/php.xml; \
		echo '    <option name="rulesetPath" value="$$PROJECT_DIR$$/.php-cs-fixer.dist.php" />' >> .idea/php.xml; \
		echo '    <option name="transferred" value="true" />' >> .idea/php.xml; \
		echo '  </component>' >> .idea/php.xml; \
		echo '  <component name="PHPCodeSnifferOptionsConfiguration">' >> .idea/php.xml; \
		echo '    <option name="codingStandard" value="Custom" />' >> .idea/php.xml; \
		echo '    <option name="customRuleset" value="$$PROJECT_DIR$$/vendor/youwe/coding-standard-magento2/src/YouweMagento2/ruleset.xml" />' >> .idea/php.xml; \
		echo '    <option name="highlightLevel" value="WARNING" />' >> .idea/php.xml; \
		echo '    <option name="installedPaths" value="$$PROJECT_DIR$$/vendor/youwe/coding-standard-magento2" />' >> .idea/php.xml; \
		echo '    <option name="showSniffs" value="true" />' >> .idea/php.xml; \
		echo '    <option name="transferred" value="true" />' >> .idea/php.xml; \
		echo '  </component>' >> .idea/php.xml; \
		echo '  <component name="PhpCSFixer">' >> .idea/php.xml; \
		echo '    <phpcsfixer_settings>' >> .idea/php.xml; \
		echo '      <PhpCSFixerConfiguration standards="PSR1;PSR2;Symfony;DoctrineAnnotation;PHP70Migration;PHP71Migration" tool_path="$$PROJECT_DIR$$/vendor/bin/php-cs-fixer" />' >> .idea/php.xml; \
		echo '    </phpcsfixer_settings>' >> .idea/php.xml; \
		echo '  </component>' >> .idea/php.xml; \
		echo '  <component name="PhpCodeSniffer">' >> .idea/php.xml; \
		echo '    <phpcs_settings>' >> .idea/php.xml; \
		echo '      <PhpCSConfiguration beautifier_path="$$PROJECT_DIR$$/vendor/bin/phpcbf" tool_path="$$PROJECT_DIR$$/vendor/bin/phpcs" />' >> .idea/php.xml; \
		echo '    </phpcs_settings>' >> .idea/php.xml; \
		echo '  </component>' >> .idea/php.xml; \
		echo '  <component name="PhpProjectSharedConfiguration" php_language_level="8.1" />' >> .idea/php.xml; \
		echo '  <component name="PhpStan">' >> .idea/php.xml; \
		echo '    <PhpStan_settings>' >> .idea/php.xml; \
		echo '      <PhpStanConfiguration tool_path="$$PROJECT_DIR$$/vendor/bin/phpstan" />' >> .idea/php.xml; \
		echo '    </PhpStan_settings>' >> .idea/php.xml; \
		echo '  </component>' >> .idea/php.xml; \
		echo '  <component name="PhpStanOptionsConfiguration">' >> .idea/php.xml; \
		echo '    <option name="config" value="$$PROJECT_DIR$$/vendor/bitexpert/phpstan-magento/phpstan.neon" />' >> .idea/php.xml; \
		echo '    <option name="transferred" value="true" />' >> .idea/php.xml; \
		echo '  </component>' >> .idea/php.xml; \
		echo '</project>' >> .idea/php.xml; \
		echo "Configuration file created at .idea/php.xml"; \
	else \
		echo ".idea/php.xml already exists, skipping creation."; \
	fi
