# Tops - Magento 2

## Prequisities
- PHP 8.3
- MySQL 8
- Node 18
- NPM 9
- Composer 2

## Installation
clone the Magento 2 repository into your workspace.

1. Trigger the command `make` in the the magento 2 folder.

The generated JSON from the manager is linked to your Magento 2 project.

## Local url
Your project runs on the url `https://tops.magento2.localhost`

## Magento Config

DPD Parcel
- Make sure you save the dpdparcel password in de admin for a working checkout and cart. Credentials are found in 1password. 
```
php bin/magento config:set dpdshipping/account_settings/username "*"
php bin/magento config:set dpdshipping/account_settings/password "*"
php bin/magento config:set dpdshipping/account_settings/depot "*"
```

## B2 Bucket
Database backups and media is found in the backblaze bucket `b2://elgentos-tops/`

## Hypernode
The live url is `topswtwfilters.hypernode.io`

## Locales
- NL
- DE
- EN

## Makefile
We ship with a `Makefile` to make your life easier.

### `make install` or `make`
Install your local enviroment.
- composer install
- create a database `${USER}_tops_m2`
- bin/magento setup:install
- put in dev mode
- setup urls to local urls

### `make clean`
Remove vendor, generated and pub/static

### `make init`
Cleanup, composer install and di:compile

### `make dev`
Put in dev mode and create a dev user

### `make reindex`
Reindex all indexers

### `make cc`
Clear caches

### `make setup`
Run `bin/magento setup:upgrade`

### `make db`
Download development.sql and reset database.
- `make development.sql`
- `make stripped.sql`
- `make anon.sql`

### `make media`
Download and extract media in pub/media
- `make media.tar.gz`

### `make hyva-install`
Install npm packages for Hyva

### `make hyva-build`
Build styling for Hyva theme
