<?php

namespace Udigital\CategoryAttribute\Helper;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\Image\AdapterFactory;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Catalog\Helper\ImageFactory;
use Psr\Log\LoggerInterface;
use Magento\Framework\View\Asset\Repository as AssetRepository;

class Image extends AbstractHelper
{
    /** @var Filesystem */
    protected $filesystem;

    /** @var DirectoryList */
    protected $directoryList;

    /** @var AdapterFactory */
    protected $adapterFactory;

    /** @var File */
    protected $ioFile;

    /** @var StoreManagerInterface */
    protected $storeManager;

    /** @var ImageFactory */
    protected $imageFactory;

    /** @var AssetRepository */
    protected $assetRepos;

    /** @var LoggerInterface */
    protected $logger;

    /** @var int */
    protected $incResize = 1;

    /** @var int */
    protected $limitResize = 10;

    /**
     * Construct
     *
     * @param Filesystem $filesystem
     * @param DirectoryList $directoryList
     * @param AdapterFactory $adapterFactory
     * @param File $ioFile
     * @param StoreManagerInterface $storeManager
     * @param ImageFactory $imageFactory
     * @param AssetRepository $assetRepos
     * @param LoggerInterface $logger
     */
    public function __construct(
        Filesystem $filesystem,
        DirectoryList $directoryList,
        AdapterFactory $adapterFactory,
        File $ioFile,
        StoreManagerInterface $storeManager,
        ImageFactory $imageFactory,
        AssetRepository $assetRepos,
        LoggerInterface $logger
    ) {
        $this->filesystem = $filesystem;
        $this->directoryList = $directoryList;
        $this->adapterFactory = $adapterFactory;
        $this->ioFile = $ioFile;
        $this->storeManager = $storeManager;
        $this->imageFactory = $imageFactory;
        $this->assetRepos = $assetRepos;
        $this->logger = $logger;
    }

    /**
     * Create From Logger
     *
     * @return void
     */
    public function createFromLogger()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . "/var/log/category_attribute_image.log");
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        return $logger;
    }

    /**
     * Resize
     *
     * @param string $imagePath
     * @param int|null $width
     * @param int|null $height
     * @param string $type
     */
    public function resize($imagePath, $width = null, $height = null, $type = "category")
    {
        $imagePath = str_replace('media/', '', $imagePath);
        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
        $absolutePath = $mediaDirectory->getAbsolutePath($imagePath);
        $fileInfo = $this->ioFile->getPathInfo($imagePath);
        $resizeDirectory = 'resized/' . $width . 'x' . $height;
        $resizedImagePath = $resizeDirectory . '/' . $fileInfo['basename'];
        $resizedPath = $mediaDirectory->getAbsolutePath($resizedImagePath);

        if ($this->ioFile->fileExists($resizedPath)) {
            return $this->getMediaUrl($resizedImagePath);
        }

        try {
            if (!$this->ioFile->fileExists($absolutePath)) {
                if ($type == "product" && $this->incResize <= $this->limitResize) {
                    $expPathImg = explode(".", $imagePath);
                    $lastFormat = end($expPathImg);
                    $pathImgRce = str_replace(
                        ".$lastFormat",
                        "_". $this->incResize .".$lastFormat",
                        $imagePath
                    );
                    $this->incResize++;
                    return $this->resize($pathImgRce, $width, $height, $type);
                }
                $imagePlaceholder = $this->imageFactory->create()->getPlaceholder(
                    'small_image'
                );
                return $this->assetRepos->getUrl($imagePlaceholder);
            }

            $imageResize = $this->adapterFactory->create();
            $imageResize->open($absolutePath);
            $imageResize->constrainOnly(true);
            $imageResize->keepTransparency(true);
            $imageResize->keepFrame(false);
            $imageResize->keepAspectRatio(true);

            if ($width && $height) {
                $imageResize->resize($width, $height);
            } elseif ($width) {
                $imageResize->resize($width);
            }


            $imageResize->save($resizedPath);
        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage());
            $imagePlaceholder = $this->imageFactory->create()->getPlaceholder(
                'small_image'
            );
            return $this->assetRepos->getUrl($imagePlaceholder);
        }

        return $this->getMediaUrl($resizedImagePath);
    }

    /**
     * Resize Image Theme
     *
     * @param string $imagePath
     * @param int|null $width
     * @param int|null $height
     */
    public function resizeImageTheme($imagePath, $width = 100, $height = 100)
    {
        // Full path to the image in the theme's web directory
        $pathTheme = '/app/design/frontend/Udigital/Topswtw/web/';
        $themeImagePath = $this->directoryList->getRoot()
            . $pathTheme
            . $imagePath;

        try {
            $fileInfo = $this->ioFile->getPathInfo($imagePath);

            $resizeDirectory = 'resized/' . $width . 'x' . $height;
            $resizedImagePath = $resizeDirectory . '/' . $fileInfo['basename'];

            // Destination path in the pub/media directory
            $mediaPath = $this->directoryList->getPath(DirectoryList::MEDIA)
                . "/$resizedImagePath";

            // Create media folder if it doesn't exist
            $fileInfoResize = $this->ioFile->getPathInfo($mediaPath);
            $this->ioFile->checkAndCreateFolder($fileInfoResize['dirname']);

            if ($this->ioFile->fileExists($mediaPath)) {
                // If the resized image already exists, return its URL
                return $this->getMediaUrl($resizedImagePath);
            }

            // Resize image using Magento's image adapter
            $imageResize = $this->adapterFactory->create();
            $imageResize->open($themeImagePath);
            $imageResize->constrainOnly(true);
            $imageResize->keepAspectRatio(true);
            $imageResize->resize($width, $height);

            $imageResize->save($mediaPath);
        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage());
            $imagePlaceholder = $this->imageFactory->create()->getPlaceholder(
                'small_image'
            );
            return $this->assetRepos->getUrl($imagePlaceholder);
        }

        return $this->getMediaUrl(
            'resized/' . $width . 'x' . $height . '/' .  $fileInfo['basename']
        );
    }

    /**
     * Get Media Url
     *
     * @param string $path
     * @return string
     */
    public function getMediaUrl($path)
    {
        return $this->storeManager->getStore()->getBaseUrl(
            \Magento\Framework\UrlInterface::URL_TYPE_MEDIA
        ) . $path;
    }
}
