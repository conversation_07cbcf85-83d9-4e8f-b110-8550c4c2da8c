<?php


namespace Udigital\CustomerAttribute\Helper;

use Magento\Customer\Model\Session;
use Magento\Directory\Model\Country;
use Magento\Store\Model\StoreManager;
use Magento\Framework\Session\SessionManagerInterface;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * @var Session
     */
    protected $_customerSession;

     /**
      * @var \Magento\Customer\Model\AddressFactory
      */
    protected $shippingAddress;

     /**
      * @var \Magento\Customer\Api\CustomerRepositoryInterface
      */
    protected $customerRepository;

    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    protected $addressRepository;

    /**
     * @var \Magento\Directory\Model\CountryFactory
     */
    protected $countryFactory;

    /**
     * @var \Magento\Framework\Locale\Resolver
     */
    protected $resolver;

    /**
     * Construct
     *
     * @param Session $customerSession
     * @param \Magento\Customer\Model\AddressFactory $shippingAddress
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Customer\Api\AddressRepositoryInterface $addressRepository
     * @param \Magento\Directory\Model\CountryFactory $countryFactory
     * @param \Magento\Framework\Locale\Resolver $resolver
     */
    public function __construct(
        Session $customerSession,
        \Magento\Customer\Model\AddressFactory $shippingAddress,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Customer\Api\AddressRepositoryInterface $addressRepository,
        \Magento\Directory\Model\CountryFactory $countryFactory,
        \Magento\Framework\Locale\Resolver $resolver
    ) {
        $this->_customerSession = $customerSession;
        $this->shippingAddress =  $shippingAddress;
        $this->customerRepository = $customerRepository;
        $this->addressRepository = $addressRepository;
        $this->countryFactory = $countryFactory;
        $this->resolver = $resolver;
    }

    /**
     * Explode String and Set data to Object
     *
     * @param  object $destination
     * @param  object $source
     * @return void
     */
    public function getcountry()
    {
        $haystack  = $this->resolver->getLocale();
        $lang = strstr($haystack, '_', true);
        return $lang;
    }

    /**
     * Get Info User
     *
     * @param string $data
     * @return array
     */
    public function getInfoUser($data)
    {

        if ($this->_customerSession->isLoggedIn()) {
           
            // get name customer
            $nameCustomer = $this->_customerSession->getCustomer();

            // get company
            $shippingAddressId = $data;
            $shippingAddress =  $this->shippingAddress->create()->load($shippingAddressId);

            // get telepon and vat and address
            
            $customerId = $this->_customerSession->getCustomerId();
            $customer =  $this->customerRepository->getById($customerId);
            $defaultBillingAddressId = $customer->getDefaultBilling();
            if ($defaultBillingAddressId) {
                $address = $this->addressRepository->getById($defaultBillingAddressId);
            }

            // get country

            if (!empty($address)) {
            
                $country = $this->countryFactory->create()->loadByCode($address->getCountryId());
                $countryName = $country->getName();

                return [
                    'name' => $address->getFirstName()." ".$address->getLastName(),
                    'company' => $shippingAddress->getCompany(),
                    'telepon' => $address->getTelephone(),
                    'vat' => $customer->getTaxvat(),
                    'street' => implode("\n", $address->getStreet()),
                    'city_and_postcode' => $address->getCity()." ".$address->getPostCode(),
                    'country' => $countryName
                ];

            } else {
                return [
                    'name' => '',
                    'company' => '',
                    'telepon' => '',
                    'vat' => '',
                    'street' => '',
                    'city_and_postcode' => '',
                    'country' => ''
                ];
            }

        }
    }
}
