<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Api\Data;

interface ProductFilterRelationInterface
{
    public const ENTITY_ID = 'entity_id';
    public const PRODUCT_ID = 'product_id';
    public const CATEGORY_ID = 'category_id';
    public const STORE_ID = 'store_id';
    public const ATTRIBUTE_CODE = 'attribute_code';
    public const ATTRIBUTE_VALUE = 'attribute_value';
    public const URL_KEY = 'url_key';
    public const IS_SELECTED = 'is_selected';
    public const WEBSITE_RECOMMENDS = 'website_recommends';
    public const CREATED_AT = 'created_at';
    public const UPDATED_AT = 'updated_at';

    /**
     * Get entity ID
     *
     * @return int|null
     */
    public function getEntityId(): ?int;

    /**
     * Set entity ID
     *
     * @param int $entityId
     * @return $this
     */
    public function setEntityId(int $entityId): ProductFilterRelationInterface;

    /**
     * Get product ID
     *
     * @return int
     */
    public function getProductId(): int;

    /**
     * Set product ID
     *
     * @param int $productId
     * @return $this
     */
    public function setProductId(int $productId): ProductFilterRelationInterface;

    /**
     * Get category ID
     *
     * @return int
     */
    public function getCategoryId(): int;

    /**
     * Set category ID
     *
     * @param int $categoryId
     * @return $this
     */
    public function setCategoryId(int $categoryId): ProductFilterRelationInterface;

    /**
     * Get store ID
     *
     * @return int
     */
    public function getStoreId(): int;

    /**
     * Set store ID
     *
     * @param int $storeId
     * @return $this
     */
    public function setStoreId(int $storeId): ProductFilterRelationInterface;

    /**
     * Get attribute code
     *
     * @return string
     */
    public function getAttributeCode(): string;

    /**
     * Set attribute code
     *
     * @param string $attributeCode
     * @return $this
     */
    public function setAttributeCode(string $attributeCode): ProductFilterRelationInterface;

    /**
     * Get attribute value
     *
     * @return string|null
     */
    public function getAttributeValue(): ?string;

    /**
     * Set attribute value
     *
     * @param string|null $attributeValue
     * @return $this
     */
    public function setAttributeValue(?string $attributeValue): ProductFilterRelationInterface;

    /**
     * Get URL key
     *
     * @return string
     */
    public function getUrlKey(): string;

    /**
     * Set URL key
     *
     * @param string $urlKey
     * @return $this
     */
    public function setUrlKey(string $urlKey): ProductFilterRelationInterface;

    /**
     * Get is selected
     *
     * @return bool
     */
    public function getIsSelected(): bool;

    /**
     * Set is selected
     *
     * @param bool $isSelected
     * @return $this
     */
    public function setIsSelected(bool $isSelected): ProductFilterRelationInterface;

    /**
     * Get website recommends
     *
     * @return bool
     */
    public function getWebsiteRecommends(): bool;

    /**
     * Set website recommends
     *
     * @param bool $websiteRecommends
     * @return $this
     */
    public function setWebsiteRecommends(bool $websiteRecommends): ProductFilterRelationInterface;

    /**
     * Get created at
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string;

    /**
     * Set created at
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): ProductFilterRelationInterface;

    /**
     * Get updated at
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string;

    /**
     * Set updated at
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): ProductFilterRelationInterface;
}
