<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Api;

use Udigital\DynamicProduct\Api\Data\ProductFilterRelationInterface;

interface ProductFilterRelationRepositoryInterface
{
    /**
     * Save product filter relation
     *
     * @param ProductFilterRelationInterface $productFilterRelation
     * @return ProductFilterRelationInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(ProductFilterRelationInterface $productFilterRelation): ProductFilterRelationInterface;

    /**
     * Get product filter relation by ID
     *
     * @param int $entityId
     * @return ProductFilterRelationInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getById(int $entityId): ProductFilterRelationInterface;

    /**
     * Delete product filter relation
     *
     * @param ProductFilterRelationInterface $productFilterRelation
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(ProductFilterRelationInterface $productFilterRelation): bool;

    /**
     * Delete product filter relation by ID
     *
     * @param int $entityId
     * @return bool
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById(int $entityId): bool;

    /**
     * Get unique filter values for current product
     *
     * @param int $productId
     * @param int $categoryId
     * @param int $storeId
     * @param string $attributeCode
     * @param array $dependsOnAttributes
     * @param array $showAttributes
     * @return array
     */
    public function getUniqueValuesForCurrentProduct(
        int $productId,
        int $categoryId,
        int $storeId,
        string $attributeCode,
        array $dependsOnAttributes = [],
        array $showAttributes = []
    ): array;

    /**
     * Delete all relations for a product
     *
     * @param int $productId
     * @return int
     */
    public function deleteByProductId(int $productId): int;

    /**
     * Delete all relations for a category
     *
     * @param int $categoryId
     * @return int
     */
    public function deleteByCategoryId(int $categoryId): int;

    /**
     * Delete all relations for a store
     *
     * @param int $storeId
     * @return int
     */
    public function deleteByStoreId(int $storeId): int;

    /**
     * Bulk insert filter relations
     *
     * @param array $data
     * @return int
     */
    public function bulkInsert(array $data): int;

    /**
     * Clear all filter relations
     *
     * @return int
     */
    public function clearAll(): int;
}
