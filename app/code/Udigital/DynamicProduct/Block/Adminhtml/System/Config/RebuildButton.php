<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Block\Adminhtml\System\Config;

use Magento\Config\Block\System\Config\Form\Field;
use Magento\Framework\Data\Form\Element\AbstractElement;

class RebuildButton extends Field
{
    /**
     * @var string
     */
    protected $_template = 'Udigital_DynamicProduct::system/config/rebuild_button.phtml';

    /**
     * Remove scope label
     *
     * @param AbstractElement $element
     * @return string
     */
    public function render(AbstractElement $element): string
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }

    /**
     * Return element html
     *
     * @param AbstractElement $element
     * @return string
     */
    protected function _getElementHtml(AbstractElement $element): string
    {
        return $this->_toHtml();
    }

    /**
     * Return ajax url for rebuild button
     *
     * @return string
     */
    public function getAjaxUrl(): string
    {
        return $this->getUrl('udigital_dynamicproduct/system_config/rebuild');
    }

    /**
     * Generate rebuild button html
     *
     * @return string
     */
    public function getButtonHtml(): string
    {
        $button = $this->getLayout()->createBlock(
            \Magento\Backend\Block\Widget\Button::class
        )->setData([
            'id' => 'rebuild_filter_relations',
            'label' => __('Rebuild Filter Relations'),
            'onclick' => 'javascript:rebuildFilterRelations(); return false;'
        ]);

        return $button->toHtml();
    }
}
