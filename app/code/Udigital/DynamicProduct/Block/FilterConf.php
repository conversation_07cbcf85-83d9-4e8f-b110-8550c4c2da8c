<?php

namespace Udigital\DynamicProduct\Block;

use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Model\CategoryRepository;
use Magento\Catalog\Model\ProductRepository;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template\Context;

class FilterConf extends \Magento\Framework\View\Element\Template
{
    protected ?Registry $_coreRegistry;
    protected CollectionFactory $productCollectionFactory;
    protected CategoryRepository $categoryRepository;
    protected ProductRepository $productRepository;
    protected StoreManagerInterface $storeManager;
    protected array $excludedCategoryIds = [2057, 2324, 3851, 3852];

    public function __construct(
        Context $context,
        CollectionFactory $productCollectionFactory,
        CategoryRepository $categoryRepository,
        ProductRepository $productRepository,
        StoreManagerInterface $storeManager,
        Registry $registry,
        array $data = []
    ) {
        $this->productCollectionFactory = $productCollectionFactory;
        $this->categoryRepository = $categoryRepository;
        $this->productRepository = $productRepository;
        $this->storeManager = $storeManager;
        $this->_coreRegistry = $registry;
        parent::__construct($context, $data);
    }

    /**
     * Get Unique Values For Current Product
     *
     * @param string $attributeCode
     * @param array $dependsOnAttribute
     * @param array $showAttribute
     *
     * @return array|string
     */
    public function getUniqueValuesForCurrentProduct(
        $attributeCode,
        $dependsOnAttribute = [],
        $showAttribute = []
    ): array|string {
        try {
            $productId              = $this->_coreRegistry->registry('product')->getId();

            // Load the current product
            $product                = $this->productRepository->getById($productId);

            // Get current store and website ID
            $currentStore           = $this->storeManager->getStore();
            $currentStoreId         = $currentStore->getId();
            $currentRootCategoryId  = $currentStore->getRootCategoryId();

            // Get the category IDs associated with the product
            $categoryIds            = $product->getCategoryIds();

            // Get the current product's 'attributeCode' value
            $currentAttrValue = $product->getData($attributeCode);

            if (!empty($categoryIds)) {
                // Loop through categories and find one that belongs to the current website
                $categoryIdForCurrentStore = null;
                foreach ($categoryIds as $categoryId) {
                    if (in_array($categoryId, $this->excludedCategoryIds)) {
                        continue;
                    }

                    $category           = $this->categoryRepository->get($categoryId);
                    $categoryPathIds    = $category->getPathIds();  // Get stores associated with the category

                    // Check if category is available for the current store
                    if (in_array($currentRootCategoryId, $categoryPathIds)) {
                        $categoryIdForCurrentStore = $categoryId;
                        break;
                    }
                }

                // If no category found for current website, return an empty result
                if (!$categoryIdForCurrentStore) {
                    return [];
                }

                // Load the selected category
                $category = $this->categoryRepository->get($categoryIdForCurrentStore);
                $array_merge = [];
                if (count($showAttribute) > 0) {
                    $array_merge = array_merge($showAttribute, [$attributeCode]);
                }
                // Get product collection for the category
                $productCollection = $this->productCollectionFactory->create()
                    ->setStoreId($currentStoreId) // Set the store ID for the collection
                    ->addAttributeToSelect($array_merge)
                    ->addCategoryFilter($category)
                    ->addAttributeToFilter($attributeCode, ['notnull' => true]); // Exclude current product

                if (count($dependsOnAttribute) > 0) {
                    foreach ($dependsOnAttribute as $dependsAttributeCode) {
                        $depAttrValue = $product->getData($dependsAttributeCode);
                        $productCollection->addAttributeToFilter($dependsAttributeCode, ['eq' => $depAttrValue]);
                    }
                }
                $productCollection->setOrder($attributeCode, 'ASC');

                // Array to store product IDs and attributeCode values
                $result = [];

                // Loop through collection to get product ID and attributeCode values
                foreach ($productCollection as $productItem) {
                    $selected = $productItem->getProductUrl() == $product->getProductUrl() ? 1 : 0;
                    $result[] = [
                        'name' => $productItem->getData($attributeCode),
                        'url_key' => $productItem->getProductUrl(),
                        'selected' => $selected
                    ];
                    if (count($showAttribute) > 0) {
                        $lastIndex = count($result) - 1;
                        foreach ($showAttribute as $showAttributeCode) {
                            $tempVal = $productItem->getData($showAttributeCode);
                            $result[$lastIndex][$showAttributeCode] = $tempVal;
                        }
                    }
                }

                // If no other distinct values are found, use the current product's value
                if (empty($result) && $currentAttrValue) {
                    $result[] = [
                        'name' => $currentAttrValue,
                        'url_key' => $product->getProductUrl(),
                        'selected' => 1
                    ];
                }

                $merkFilters = array_unique(array_map('strtolower', array_column($result, 'name')));
                $uniqueResult = [];

                $attribute = $product->getResource()->getAttribute($attributeCode);
                if ($attribute) {
                    $uniqueResult['title'] = $attribute->getStoreLabel($currentStoreId);
                }

                $selected_show = 0;
                foreach ($result as $item) {
                    $selected_show = $item['selected'];
                    $id = strtolower($item['name']);
                    if (in_array($id, $merkFilters)) {
                        $compared = strtolower((string) $product->getData($attributeCode)) == $id;

                        if ($compared && $selected_show == 0) {
                            continue;
                        } elseif ($compared && $selected_show == 1) {
                            $uniqueResult['content'][] = $item;
                            $merkFilters = array_diff($merkFilters, [$id]);
                        } else {
                            $uniqueResult['content'][] = $item;
                            $merkFilters = array_diff($merkFilters, [$id]);
                        }
                    }
                }

                // sort attribute name alphabetically, however 'Tops filters' always be the first
                if (isset($uniqueResult['content']) && is_array($uniqueResult['content'])) {
                    usort($uniqueResult['content'], function ($a, $b) {
                        if ($a['name'] === "Tops filters") {
                            return -1;
                        }
                        if ($b['name'] === "Tops filters") {
                            return 1;
                        }
                        return strcmp($a['name'], $b['name']);
                    });
                }
                return $uniqueResult;
            } else {
                return [];
            }
        } catch (\Exception $e) {
            // Handle exceptions
            return $e->getMessage();
        }
    }
}
