<?php

namespace Udigital\DynamicProduct\Block;

use Magento\Catalog\Model\CategoryRepository;
use Magento\Catalog\Model\ProductRepository;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template\Context;
use Udigital\DynamicProduct\Api\ProductFilterRelationRepositoryInterface;

class FilterConf extends \Magento\Framework\View\Element\Template
{
    protected ?Registry $_coreRegistry;
    protected CategoryRepository $categoryRepository;
    protected ProductRepository $productRepository;
    protected StoreManagerInterface $storeManager;
    protected ProductFilterRelationRepositoryInterface $filterRelationRepository;
    protected array $excludedCategoryIds = [2057, 2324, 3851, 3852];

    public function __construct(
        Context $context,
        CategoryRepository $categoryRepository,
        ProductRepository $productRepository,
        StoreManagerInterface $storeManager,
        ProductFilterRelationRepositoryInterface $filterRelationRepository,
        Registry $registry,
        array $data = []
    ) {
        $this->categoryRepository = $categoryRepository;
        $this->productRepository = $productRepository;
        $this->storeManager = $storeManager;
        $this->filterRelationRepository = $filterRelationRepository;
        $this->_coreRegistry = $registry;
        parent::__construct($context, $data);
    }

    /**
     * Get Unique Values For Current Product
     *
     * @param string $attributeCode
     * @param array $dependsOnAttribute
     * @param array $showAttribute
     *
     * @return array|string
     */
    public function getUniqueValuesForCurrentProduct(
        $attributeCode,
        $dependsOnAttribute = [],
        $showAttribute = []
    ): array|string {
        try {
            $productId = $this->_coreRegistry->registry('product')->getId();

            // Load the current product
            $product = $this->productRepository->getById($productId);

            // Get current store and website ID
            $currentStore = $this->storeManager->getStore();
            $currentStoreId = (int)$currentStore->getId();
            $currentRootCategoryId = (int)$currentStore->getRootCategoryId();

            // Get the category IDs associated with the product
            $categoryIds = $product->getCategoryIds();

            if (!empty($categoryIds)) {
                // Loop through categories and find one that belongs to the current website
                $categoryIdForCurrentStore = null;
                foreach ($categoryIds as $categoryId) {
                    if (in_array($categoryId, $this->excludedCategoryIds)) {
                        continue;
                    }

                    $category = $this->categoryRepository->get($categoryId);
                    $categoryPathIds = $category->getPathIds();

                    // Check if category is available for the current store
                    if (in_array($currentRootCategoryId, $categoryPathIds)) {
                        $categoryIdForCurrentStore = (int)$categoryId;
                        break;
                    }
                }

                // If no category found for current website, return an empty result
                if (!$categoryIdForCurrentStore) {
                    return [];
                }

                // Use the pre-calculated filter relations from the database
                $result = $this->filterRelationRepository->getUniqueValuesForCurrentProduct(
                    (int)$productId,
                    $categoryIdForCurrentStore,
                    $currentStoreId,
                    $attributeCode,
                    $dependsOnAttribute,
                    $showAttribute
                );

                // If no results found, return empty array
                if (empty($result)) {
                    return [];
                }

                // Prepare the final result structure
                $uniqueResult = [];

                // Get attribute label
                $attribute = $product->getResource()->getAttribute($attributeCode);
                if ($attribute) {
                    $uniqueResult['title'] = $attribute->getStoreLabel($currentStoreId);
                }

                // Mark the current product as selected and process results
                $currentAttrValue = $product->getData($attributeCode);
                foreach ($result as &$item) {
                    // Mark as selected if this is the current product's value
                    if (strtolower((string)$currentAttrValue) === strtolower($item['name'])) {
                        $item['selected'] = true;
                    }
                }

                $uniqueResult['content'] = $result;

                // Sort attribute name alphabetically, however 'Tops filters' always be the first
                if (isset($uniqueResult['content']) && is_array($uniqueResult['content'])) {
                    usort($uniqueResult['content'], function ($a, $b) {
                        if ($a['name'] === "Tops filters") {
                            return -1;
                        }
                        if ($b['name'] === "Tops filters") {
                            return 1;
                        }
                        return strcmp($a['name'], $b['name']);
                    });
                }

                return $uniqueResult;
            } else {
                return [];
            }
        } catch (\Exception $e) {
            // Handle exceptions
            return $e->getMessage();
        }
    }
}
