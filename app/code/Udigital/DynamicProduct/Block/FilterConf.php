<?php

namespace Udigital\DynamicProduct\Block;

use Magento\Catalog\Model\CategoryRepository;
use Magento\Catalog\Model\ProductRepository;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template\Context;
use Udigital\DynamicProduct\Api\ProductFilterRelationRepositoryInterface;

class FilterConf extends \Magento\Framework\View\Element\Template
{
    protected ?Registry $_coreRegistry;
    protected CategoryRepository $categoryRepository;
    protected ProductRepository $productRepository;
    protected StoreManagerInterface $storeManager;
    protected ProductFilterRelationRepositoryInterface $filterRelationRepository;
    protected array $excludedCategoryIds = [2057, 2324, 3851, 3852];

    public function __construct(
        Context $context,
        CategoryRepository $categoryRepository,
        ProductRepository $productRepository,
        StoreManagerInterface $storeManager,
        ProductFilterRelationRepositoryInterface $filterRelationRepository,
        Registry $registry,
        array $data = []
    ) {
        $this->categoryRepository = $categoryRepository;
        $this->productRepository = $productRepository;
        $this->storeManager = $storeManager;
        $this->filterRelationRepository = $filterRelationRepository;
        $this->_coreRegistry = $registry;
        parent::__construct($context, $data);
    }

    /**
     * Get Unique Values For Current Product
     * Now uses pre-calculated data with minimal logic
     *
     * @param string $attributeCode
     * @param array $dependsOnAttribute
     * @param array $showAttribute
     *
     * @return array|string
     */
    public function getUniqueValuesForCurrentProduct(
        $attributeCode,
        $dependsOnAttribute = [],
        $showAttribute = []
    ): array|string {
        try {
            $product = $this->_coreRegistry->registry('product');
            if (!$product) {
                return [];
            }

            $productId = (int)$product->getId();
            $currentStoreId = (int)$this->storeManager->getStore()->getId();

            // Get the first valid category for this product (pre-calculated data should exist for all valid categories)
            $categoryIds = $product->getCategoryIds();
            $validCategoryId = null;

            foreach ($categoryIds as $categoryId) {
                if (!in_array($categoryId, $this->excludedCategoryIds)) {
                    $validCategoryId = (int)$categoryId;
                    break;
                }
            }

            if (!$validCategoryId) {
                return [];
            }

            // Get pre-calculated filter data
            $result = $this->filterRelationRepository->getUniqueValuesForCurrentProduct(
                $productId,
                $validCategoryId,
                $currentStoreId,
                $attributeCode,
                $dependsOnAttribute,
                $showAttribute
            );

            if (empty($result)) {
                return [];
            }

            // Prepare the final result structure
            $uniqueResult = [];

            // Get attribute label
            $attribute = $product->getResource()->getAttribute($attributeCode);
            if ($attribute) {
                $uniqueResult['title'] = $attribute->getStoreLabel($currentStoreId);
            }

            // Mark the current product as selected
            $currentAttrValue = $product->getData($attributeCode);
            foreach ($result as &$item) {
                if (strtolower((string)$currentAttrValue) === strtolower($item['name'])) {
                    $item['selected'] = true;
                }
            }

            $uniqueResult['content'] = $result;

            // Sort results (Tops filters first, then alphabetically)
            if (isset($uniqueResult['content']) && is_array($uniqueResult['content'])) {
                usort($uniqueResult['content'], function ($a, $b) {
                    if ($a['name'] === "Tops filters") {
                        return -1;
                    }
                    if ($b['name'] === "Tops filters") {
                        return 1;
                    }
                    return strcmp($a['name'], $b['name']);
                });
            }

            return $uniqueResult;

        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }
}
