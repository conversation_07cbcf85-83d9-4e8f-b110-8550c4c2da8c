<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;
use Udigital\DynamicProduct\Cron\RebuildProductFilterRelations;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\State;

class RebuildFilterRelations extends Command
{
    private const STORE_ID_OPTION = 'store-id';
    private const CLEAR_ONLY_OPTION = 'clear-only';

    /**
     * @var RebuildProductFilterRelations
     */
    private RebuildProductFilterRelations $rebuildCron;

    /**
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;

    /**
     * @var State
     */
    private State $appState;

    /**
     * @param RebuildProductFilterRelations $rebuildCron
     * @param StoreManagerInterface $storeManager
     * @param State $appState
     * @param string|null $name
     */
    public function __construct(
        RebuildProductFilterRelations $rebuildCron,
        StoreManagerInterface $storeManager,
        State $appState,
        string $name = null
    ) {
        $this->rebuildCron = $rebuildCron;
        $this->storeManager = $storeManager;
        $this->appState = $appState;
        parent::__construct($name);
    }

    /**
     * Configure command
     *
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('udigital:product-filter:rebuild')
            ->setDescription('Rebuild product filter relations')
            ->addOption(
                self::STORE_ID_OPTION,
                's',
                InputOption::VALUE_OPTIONAL,
                'Store ID to rebuild (if not specified, all stores will be processed)'
            )
            ->addOption(
                self::CLEAR_ONLY_OPTION,
                'c',
                InputOption::VALUE_NONE,
                'Only clear existing relations without rebuilding'
            );
    }

    /**
     * Execute command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Starting product filter relations rebuild...</info>');

        try {
            // Set area code to avoid "Area code is not set" error
            try {
                $this->appState->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                // Area code already set, continue
            }
            $storeId = $input->getOption(self::STORE_ID_OPTION);
            $clearOnly = $input->getOption(self::CLEAR_ONLY_OPTION);

            if ($clearOnly) {
                $output->writeln('<comment>Clearing existing filter relations...</comment>');
                $this->rebuildCron->clearExistingRelations();
                $output->writeln('<info>Filter relations cleared successfully!</info>');
                return Command::SUCCESS;
            }

            if ($storeId !== null) {
                $storeId = (int)$storeId;
                $store = $this->storeManager->getStore($storeId);
                $output->writeln("<comment>Processing store: {$store->getName()} (ID: {$storeId})</comment>");
                
                $this->rebuildCron->processStore($storeId);
                
                $output->writeln("<info>Filter relations rebuilt successfully for store {$storeId}!</info>");
            } else {
                $output->writeln('<comment>Processing all stores...</comment>');
                
                $stores = $this->storeManager->getStores();
                $progressBar = new ProgressBar($output, count($stores));
                $progressBar->start();

                foreach ($stores as $store) {
                    $progressBar->setMessage("Processing store: {$store->getName()}");
                    $this->rebuildCron->processStore((int)$store->getId());
                    $progressBar->advance();
                }

                $progressBar->finish();
                $output->writeln('');
                $output->writeln('<info>Filter relations rebuilt successfully for all stores!</info>');
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $output->writeln("<error>Error: {$e->getMessage()}</error>");
            return Command::FAILURE;
        }
    }
}
