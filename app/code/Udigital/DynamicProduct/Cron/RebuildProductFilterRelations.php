<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Cron;

use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Magento\Catalog\Model\CategoryRepository;
use Magento\Catalog\Model\ProductRepository;
use Magento\Store\Model\StoreManagerInterface;
use Udigital\DynamicProduct\Api\ProductFilterRelationRepositoryInterface;
use Psr\Log\LoggerInterface;

class RebuildProductFilterRelations
{
    /**
     * Filter attributes to process
     */
    private const FILTER_ATTRIBUTES = [
        'merk_filter',
        'filter_klasse',
        'bypass',
        'specifiek_type'
    ];

    /**
     * Excluded category IDs
     */
    private const EXCLUDED_CATEGORY_IDS = [2057, 2324, 3851, 3852];

    /**
     * @var ProductCollectionFactory
     */
    private ProductCollectionFactory $productCollectionFactory;

    /**
     * @var CategoryRepository
     */
    private CategoryRepository $categoryRepository;

    /**
     * @var ProductRepository
     */
    private ProductRepository $productRepository;

    /**
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;

    /**
     * @var ProductFilterRelationRepositoryInterface
     */
    private ProductFilterRelationRepositoryInterface $filterRelationRepository;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @param ProductCollectionFactory $productCollectionFactory
     * @param CategoryRepository $categoryRepository
     * @param ProductRepository $productRepository
     * @param StoreManagerInterface $storeManager
     * @param ProductFilterRelationRepositoryInterface $filterRelationRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        ProductCollectionFactory $productCollectionFactory,
        CategoryRepository $categoryRepository,
        ProductRepository $productRepository,
        StoreManagerInterface $storeManager,
        ProductFilterRelationRepositoryInterface $filterRelationRepository,
        LoggerInterface $logger
    ) {
        $this->productCollectionFactory = $productCollectionFactory;
        $this->categoryRepository = $categoryRepository;
        $this->productRepository = $productRepository;
        $this->storeManager = $storeManager;
        $this->filterRelationRepository = $filterRelationRepository;
        $this->logger = $logger;
    }

    /**
     * Execute cron job
     *
     * @return void
     */
    public function execute(): void
    {
        $this->logger->info('Starting product filter relations rebuild');

        try {
            // Clear existing relations
            $this->clearExistingRelations();

            // Process each store
            foreach ($this->storeManager->getStores() as $store) {
                $this->processStore((int)$store->getId());
            }

            $this->logger->info('Product filter relations rebuild completed successfully');
        } catch (\Exception $e) {
            $this->logger->error('Error rebuilding product filter relations: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Clear existing relations
     *
     * @return void
     */
    public function clearExistingRelations(): void
    {
        $this->filterRelationRepository->clearAll();
        $this->logger->info('Cleared existing filter relations');
    }

    /**
     * Process products for a specific store
     *
     * @param int $storeId
     * @return void
     */
    public function processStore(int $storeId): void
    {
        $this->logger->info("Processing store ID: {$storeId}");

        $store = $this->storeManager->getStore($storeId);
        $rootCategoryId = (int)$store->getRootCategoryId();

        // Get all products for this store
        $productCollection = $this->productCollectionFactory->create()
            ->setStoreId($storeId)
            ->addAttributeToSelect(array_merge(self::FILTER_ATTRIBUTES, ['website_recommends']))
            ->addAttributeToFilter('status', \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED);

        $batchData = [];
        $batchSize = 1000;
        $processedCount = 0;

        foreach ($productCollection as $product) {
            try {
                $categoryIds = $product->getCategoryIds();

                foreach ($categoryIds as $categoryId) {
                    if (in_array($categoryId, self::EXCLUDED_CATEGORY_IDS)) {
                        continue;
                    }

                    $category = $this->categoryRepository->get($categoryId);
                    $categoryPathIds = $category->getPathIds();

                    // Check if category belongs to current store
                    if (!in_array($rootCategoryId, $categoryPathIds)) {
                        continue;
                    }

                    // Process each filter attribute
                    foreach (self::FILTER_ATTRIBUTES as $attributeCode) {
                        $this->processProductAttribute(
                            $product,
                            (int)$categoryId,
                            $storeId,
                            $attributeCode,
                            $batchData
                        );
                    }
                }

                $processedCount++;

                // Insert batch when it reaches the batch size
                if (count($batchData) >= $batchSize) {
                    $this->filterRelationRepository->bulkInsert($batchData);
                    $batchData = [];
                    $this->logger->info("Processed {$processedCount} products for store {$storeId}");
                }
            } catch (\Exception $e) {
                $this->logger->error("Error processing product {$product->getId()}: " . $e->getMessage());
                continue;
            }
        }

        // Insert remaining batch data
        if (!empty($batchData)) {
            $this->filterRelationRepository->bulkInsert($batchData);
        }

        $this->logger->info("Completed processing {$processedCount} products for store {$storeId}");
    }

    /**
     * Process a specific attribute for a product
     *
     * @param \Magento\Catalog\Model\Product $product
     * @param int $categoryId
     * @param int $storeId
     * @param string $attributeCode
     * @param array &$batchData
     * @return void
     */
    private function processProductAttribute(
        \Magento\Catalog\Model\Product $product,
        int $categoryId,
        int $storeId,
        string $attributeCode,
        array &$batchData
    ): void {
        $attributeValue = $product->getData($attributeCode);

        if ($attributeValue === null || $attributeValue === '') {
            return;
        }

        // Handle boolean attributes like bypass
        if ($attributeCode === 'bypass') {
            $attributeValue = (bool)$attributeValue;
        }

        $websiteRecommends = (bool)$product->getData('website_recommends');

        $batchData[] = [
            'product_id' => (int)$product->getId(),
            'category_id' => $categoryId,
            'store_id' => $storeId,
            'attribute_code' => $attributeCode,
            'attribute_value' => (string)$attributeValue,
            'url_key' => $product->getProductUrl(),
            'is_selected' => false, // Will be determined dynamically in the frontend
            'website_recommends' => $websiteRecommends,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }
}
