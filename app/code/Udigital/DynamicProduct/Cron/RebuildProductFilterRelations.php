<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Cron;

use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory as CategoryCollectionFactory;
use Magento\Catalog\Model\CategoryRepository;
use Magento\Catalog\Model\ProductRepository;
use Magento\Store\Model\StoreManagerInterface;
use Udigital\DynamicProduct\Api\ProductFilterRelationRepositoryInterface;
use Psr\Log\LoggerInterface;

class RebuildProductFilterRelations
{
    /**
     * Filter attributes configuration with their dependencies
     */
    private const FILTER_ATTRIBUTES_CONFIG = [
        'merk_filter' => [
            'depends_on' => [],
            'show_attributes' => ['website_recommends']
        ],
        'filter_klasse' => [
            'depends_on' => ['merk_filter'],
            'show_attributes' => ['website_recommends']
        ],
        'bypass' => [
            'depends_on' => ['merk_filter', 'filter_klasse'],
            'show_attributes' => []
        ],
        'specifiek_type' => [
            'depends_on' => ['merk_filter', 'filter_klasse'],
            'show_attributes' => []
        ]
    ];

    /**
     * Excluded category IDs
     */
    private const EXCLUDED_CATEGORY_IDS = [2057, 2324, 3851, 3852];

    /**
     * @var ProductCollectionFactory
     */
    private ProductCollectionFactory $productCollectionFactory;

    /**
     * @var CategoryCollectionFactory
     */
    private CategoryCollectionFactory $categoryCollectionFactory;

    /**
     * @var CategoryRepository
     */
    private CategoryRepository $categoryRepository;

    /**
     * @var ProductRepository
     */
    private ProductRepository $productRepository;

    /**
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;

    /**
     * @var ProductFilterRelationRepositoryInterface
     */
    private ProductFilterRelationRepositoryInterface $filterRelationRepository;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @param ProductCollectionFactory $productCollectionFactory
     * @param CategoryCollectionFactory $categoryCollectionFactory
     * @param CategoryRepository $categoryRepository
     * @param ProductRepository $productRepository
     * @param StoreManagerInterface $storeManager
     * @param ProductFilterRelationRepositoryInterface $filterRelationRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        ProductCollectionFactory $productCollectionFactory,
        CategoryCollectionFactory $categoryCollectionFactory,
        CategoryRepository $categoryRepository,
        ProductRepository $productRepository,
        StoreManagerInterface $storeManager,
        ProductFilterRelationRepositoryInterface $filterRelationRepository,
        LoggerInterface $logger
    ) {
        $this->productCollectionFactory = $productCollectionFactory;
        $this->categoryCollectionFactory = $categoryCollectionFactory;
        $this->categoryRepository = $categoryRepository;
        $this->productRepository = $productRepository;
        $this->storeManager = $storeManager;
        $this->filterRelationRepository = $filterRelationRepository;
        $this->logger = $logger;
    }

    /**
     * Execute cron job
     *
     * @return void
     */
    public function execute(): void
    {
        $this->logger->info('Starting product filter relations rebuild');

        try {
            // Clear existing relations
            $this->clearExistingRelations();

            // Process each store
            foreach ($this->storeManager->getStores() as $store) {
                $this->processStore((int)$store->getId());
            }

            $this->logger->info('Product filter relations rebuild completed successfully');
        } catch (\Exception $e) {
            $this->logger->error('Error rebuilding product filter relations: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Clear existing relations
     *
     * @return void
     */
    public function clearExistingRelations(): void
    {
        $this->filterRelationRepository->clearAll();
        $this->logger->info('Cleared existing filter relations');
    }

    /**
     * Process products for a specific store
     *
     * @param int $storeId
     * @return void
     */
    public function processStore(int $storeId): void
    {
        $this->logger->info("Processing store ID: {$storeId}");

        $store = $this->storeManager->getStore($storeId);
        $rootCategoryId = (int)$store->getRootCategoryId();

        // Process each filter attribute configuration
        foreach (self::FILTER_ATTRIBUTES_CONFIG as $attributeCode => $config) {
            $this->processFilterAttribute($storeId, $rootCategoryId, $attributeCode, $config);
        }

        $this->logger->info("Completed processing store {$storeId}");
    }

    /**
     * Process a specific filter attribute for all categories in a store
     *
     * @param int $storeId
     * @param int $rootCategoryId
     * @param string $attributeCode
     * @param array $config
     * @return void
     */
    private function processFilterAttribute(int $storeId, int $rootCategoryId, string $attributeCode, array $config): void
    {
        $this->logger->info("Processing attribute '{$attributeCode}' for store {$storeId}");

        // Get all categories for this store
        $categoryCollection = $this->categoryCollectionFactory->create()
            ->addAttributeToSelect('*')
            ->addPathFilter(sprintf('%d/.*', $rootCategoryId))
            ->addIsActiveFilter();

        foreach ($categoryCollection as $category) {
            if (in_array($category->getId(), self::EXCLUDED_CATEGORY_IDS)) {
                continue;
            }

            $this->processCategoryFilterAttribute((int)$category->getId(), $storeId, $attributeCode, $config);
        }
    }

    /**
     * Process filter attribute for a specific category
     *
     * @param int $categoryId
     * @param int $storeId
     * @param string $attributeCode
     * @param array $config
     * @return void
     */
    private function processCategoryFilterAttribute(int $categoryId, int $storeId, string $attributeCode, array $config): void
    {
        $dependsOn = $config['depends_on'] ?? [];
        $showAttributes = $config['show_attributes'] ?? [];

        // Create filter key
        $filterKey = $this->createFilterKey($attributeCode, $dependsOn);

        // Get all products in this category with the required attributes
        $allAttributes = array_merge([$attributeCode], $dependsOn, $showAttributes);

        // Load the category object
        $category = $this->categoryRepository->get($categoryId);

        $productCollection = $this->productCollectionFactory->create()
            ->setStoreId($storeId)
            ->addAttributeToSelect($allAttributes)
            ->addCategoryFilter($category)
            ->addAttributeToFilter('status', \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED);

        // Group products by dependency values if there are dependencies
        if (!empty($dependsOn)) {
            $this->processProductsWithDependencies($productCollection, $categoryId, $storeId, $attributeCode, $dependsOn, $showAttributes, $filterKey);
        } else {
            $this->processProductsWithoutDependencies($productCollection, $categoryId, $storeId, $attributeCode, $showAttributes, $filterKey);
        }
    }

    /**
     * Process products with dependencies - create separate filter sets for each dependency combination
     *
     * @param \Magento\Catalog\Model\ResourceModel\Product\Collection $productCollection
     * @param int $categoryId
     * @param int $storeId
     * @param string $attributeCode
     * @param array $dependsOn
     * @param array $showAttributes
     * @param string $filterKey
     * @return void
     */
    private function processProductsWithDependencies(
        \Magento\Catalog\Model\ResourceModel\Product\Collection $productCollection,
        int $categoryId,
        int $storeId,
        string $attributeCode,
        array $dependsOn,
        array $showAttributes,
        string $filterKey
    ): void {
        // Group products by their dependency values
        $dependencyGroups = [];

        foreach ($productCollection as $product) {
            $dependencyKey = [];
            foreach ($dependsOn as $depAttribute) {
                $dependencyKey[] = $product->getData($depAttribute);
            }
            $dependencyKeyString = implode('|', $dependencyKey);

            if (!isset($dependencyGroups[$dependencyKeyString])) {
                $dependencyGroups[$dependencyKeyString] = [];
            }
            $dependencyGroups[$dependencyKeyString][] = $product;
        }

        // Process each dependency group separately
        foreach ($dependencyGroups as $dependencyKeyString => $products) {
            $this->createFilterRelationsForProducts($products, $categoryId, $storeId, $attributeCode, $showAttributes, $filterKey);
        }
    }

    /**
     * Process products without dependencies
     *
     * @param \Magento\Catalog\Model\ResourceModel\Product\Collection $productCollection
     * @param int $categoryId
     * @param int $storeId
     * @param string $attributeCode
     * @param array $showAttributes
     * @param string $filterKey
     * @return void
     */
    private function processProductsWithoutDependencies(
        \Magento\Catalog\Model\ResourceModel\Product\Collection $productCollection,
        int $categoryId,
        int $storeId,
        string $attributeCode,
        array $showAttributes,
        string $filterKey
    ): void {
        $products = $productCollection->getItems();
        $this->createFilterRelationsForProducts($products, $categoryId, $storeId, $attributeCode, $showAttributes, $filterKey);
    }

    /**
     * Create filter relations for a group of products
     *
     * @param array $products
     * @param int $categoryId
     * @param int $storeId
     * @param string $attributeCode
     * @param array $showAttributes
     * @param string $filterKey
     * @return void
     */
    private function createFilterRelationsForProducts(
        array $products,
        int $categoryId,
        int $storeId,
        string $attributeCode,
        array $showAttributes,
        string $filterKey
    ): void {
        $batchData = [];
        $uniqueValues = [];

        foreach ($products as $product) {
            $attributeValue = $product->getData($attributeCode);

            if ($attributeValue === null || $attributeValue === '') {
                continue;
            }

            // Handle boolean attributes like bypass
            if ($attributeCode === 'bypass') {
                $attributeValue = (bool)$attributeValue ? 'With Bypass' : 'Without Bypass';
            }

            $valueKey = strtolower((string)$attributeValue);

            // Only add unique values per filter group
            if (!isset($uniqueValues[$valueKey])) {
                $uniqueValues[$valueKey] = true;

                $websiteRecommends = false;
                if (in_array('website_recommends', $showAttributes)) {
                    $websiteRecommends = (bool)$product->getData('website_recommends');
                }

                $batchData[] = [
                    'product_id' => (int)$product->getId(),
                    'category_id' => $categoryId,
                    'store_id' => $storeId,
                    'attribute_code' => $attributeCode,
                    'attribute_value' => (string)$attributeValue,
                    'filter_key' => $filterKey,
                    'url_key' => $product->getProductUrl(),
                    'is_selected' => false,
                    'website_recommends' => $websiteRecommends,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
            }
        }

        if (!empty($batchData)) {
            $this->filterRelationRepository->bulkInsert($batchData);
        }
    }

    /**
     * Create filter key from attribute code and dependencies
     *
     * @param string $attributeCode
     * @param array $dependsOn
     * @return string
     */
    private function createFilterKey(string $attributeCode, array $dependsOn = []): string
    {
        $keyParts = [$attributeCode];
        if (!empty($dependsOn)) {
            sort($dependsOn);
            $keyParts = array_merge($keyParts, $dependsOn);
        }
        return implode('|', $keyParts);
    }
}
