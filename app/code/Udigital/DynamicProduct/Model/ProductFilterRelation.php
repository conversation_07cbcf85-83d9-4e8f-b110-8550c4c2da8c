<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Model;

use Magento\Framework\Model\AbstractModel;
use Udigital\DynamicProduct\Api\Data\ProductFilterRelationInterface;

class ProductFilterRelation extends AbstractModel implements ProductFilterRelationInterface
{
    /**
     * Cache tag
     */
    public const CACHE_TAG = 'udigital_product_filter_relation';

    /**
     * @var string
     */
    protected $_cacheTag = 'udigital_product_filter_relation';

    /**
     * @var string
     */
    protected $_eventPrefix = 'udigital_product_filter_relation';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(\Udigital\DynamicProduct\Model\ResourceModel\ProductFilterRelation::class);
    }

    /**
     * Get entity ID
     *
     * @return int|null
     */
    public function getEntityId(): ?int
    {
        return $this->getData(self::ENTITY_ID) ? (int)$this->getData(self::ENTITY_ID) : null;
    }

    /**
     * Set entity ID
     *
     * @param mixed $entityId
     * @return $this
     */
    public function setEntityId($entityId)
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * Get product ID
     *
     * @return int
     */
    public function getProductId(): int
    {
        return (int)$this->getData(self::PRODUCT_ID);
    }

    /**
     * Set product ID
     *
     * @param int $productId
     * @return $this
     */
    public function setProductId(int $productId): ProductFilterRelationInterface
    {
        return $this->setData(self::PRODUCT_ID, $productId);
    }

    /**
     * Get category ID
     *
     * @return int
     */
    public function getCategoryId(): int
    {
        return (int)$this->getData(self::CATEGORY_ID);
    }

    /**
     * Set category ID
     *
     * @param int $categoryId
     * @return $this
     */
    public function setCategoryId(int $categoryId): ProductFilterRelationInterface
    {
        return $this->setData(self::CATEGORY_ID, $categoryId);
    }

    /**
     * Get store ID
     *
     * @return int
     */
    public function getStoreId(): int
    {
        return (int)$this->getData(self::STORE_ID);
    }

    /**
     * Set store ID
     *
     * @param int $storeId
     * @return $this
     */
    public function setStoreId(int $storeId): ProductFilterRelationInterface
    {
        return $this->setData(self::STORE_ID, $storeId);
    }

    /**
     * Get attribute code
     *
     * @return string
     */
    public function getAttributeCode(): string
    {
        return (string)$this->getData(self::ATTRIBUTE_CODE);
    }

    /**
     * Set attribute code
     *
     * @param string $attributeCode
     * @return $this
     */
    public function setAttributeCode(string $attributeCode): ProductFilterRelationInterface
    {
        return $this->setData(self::ATTRIBUTE_CODE, $attributeCode);
    }

    /**
     * Get attribute value
     *
     * @return string|null
     */
    public function getAttributeValue(): ?string
    {
        return $this->getData(self::ATTRIBUTE_VALUE);
    }

    /**
     * Set attribute value
     *
     * @param string|null $attributeValue
     * @return $this
     */
    public function setAttributeValue(?string $attributeValue): ProductFilterRelationInterface
    {
        return $this->setData(self::ATTRIBUTE_VALUE, $attributeValue);
    }

    /**
     * Get filter key
     *
     * @return string
     */
    public function getFilterKey(): string
    {
        return (string)$this->getData(self::FILTER_KEY);
    }

    /**
     * Set filter key
     *
     * @param string $filterKey
     * @return $this
     */
    public function setFilterKey(string $filterKey): ProductFilterRelationInterface
    {
        return $this->setData(self::FILTER_KEY, $filterKey);
    }

    /**
     * Get URL key
     *
     * @return string
     */
    public function getUrlKey(): string
    {
        return (string)$this->getData(self::URL_KEY);
    }

    /**
     * Set URL key
     *
     * @param string $urlKey
     * @return $this
     */
    public function setUrlKey(string $urlKey): ProductFilterRelationInterface
    {
        return $this->setData(self::URL_KEY, $urlKey);
    }

    /**
     * Get is selected
     *
     * @return bool
     */
    public function getIsSelected(): bool
    {
        return (bool)$this->getData(self::IS_SELECTED);
    }

    /**
     * Set is selected
     *
     * @param bool $isSelected
     * @return $this
     */
    public function setIsSelected(bool $isSelected): ProductFilterRelationInterface
    {
        return $this->setData(self::IS_SELECTED, $isSelected);
    }

    /**
     * Get website recommends
     *
     * @return bool
     */
    public function getWebsiteRecommends(): bool
    {
        return (bool)$this->getData(self::WEBSITE_RECOMMENDS);
    }

    /**
     * Set website recommends
     *
     * @param bool $websiteRecommends
     * @return $this
     */
    public function setWebsiteRecommends(bool $websiteRecommends): ProductFilterRelationInterface
    {
        return $this->setData(self::WEBSITE_RECOMMENDS, $websiteRecommends);
    }

    /**
     * Get created at
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set created at
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): ProductFilterRelationInterface
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * Get updated at
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * Set updated at
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): ProductFilterRelationInterface
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }
}
