<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Model;

use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Udigital\DynamicProduct\Api\Data\ProductFilterRelationInterface;
use Udigital\DynamicProduct\Api\Data\ProductFilterRelationInterfaceFactory;
use Udigital\DynamicProduct\Api\ProductFilterRelationRepositoryInterface;
use Udigital\DynamicProduct\Model\ResourceModel\ProductFilterRelation as ProductFilterRelationResource;
use Udigital\DynamicProduct\Model\ResourceModel\ProductFilterRelation\CollectionFactory;

class ProductFilterRelationRepository implements ProductFilterRelationRepositoryInterface
{
    /**
     * @var ProductFilterRelationResource
     */
    private ProductFilterRelationResource $resource;

    /**
     * @var ProductFilterRelationInterfaceFactory
     */
    private ProductFilterRelationInterfaceFactory $productFilterRelationFactory;

    /**
     * @var CollectionFactory
     */
    private CollectionFactory $collectionFactory;

    /**
     * @param ProductFilterRelationResource $resource
     * @param ProductFilterRelationInterfaceFactory $productFilterRelationFactory
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        ProductFilterRelationResource $resource,
        ProductFilterRelationInterfaceFactory $productFilterRelationFactory,
        CollectionFactory $collectionFactory
    ) {
        $this->resource = $resource;
        $this->productFilterRelationFactory = $productFilterRelationFactory;
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * Save product filter relation
     *
     * @param ProductFilterRelationInterface $productFilterRelation
     * @return ProductFilterRelationInterface
     * @throws CouldNotSaveException
     */
    public function save(ProductFilterRelationInterface $productFilterRelation): ProductFilterRelationInterface
    {
        try {
            $this->resource->save($productFilterRelation);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__($exception->getMessage()));
        }
        return $productFilterRelation;
    }

    /**
     * Get product filter relation by ID
     *
     * @param int $entityId
     * @return ProductFilterRelationInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $entityId): ProductFilterRelationInterface
    {
        $productFilterRelation = $this->productFilterRelationFactory->create();
        $this->resource->load($productFilterRelation, $entityId);
        if (!$productFilterRelation->getEntityId()) {
            throw new NoSuchEntityException(__('Product Filter Relation with id "%1" does not exist.', $entityId));
        }
        return $productFilterRelation;
    }

    /**
     * Delete product filter relation
     *
     * @param ProductFilterRelationInterface $productFilterRelation
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(ProductFilterRelationInterface $productFilterRelation): bool
    {
        try {
            $this->resource->delete($productFilterRelation);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__($exception->getMessage()));
        }
        return true;
    }

    /**
     * Delete product filter relation by ID
     *
     * @param int $entityId
     * @return bool
     * @throws NoSuchEntityException
     * @throws CouldNotDeleteException
     */
    public function deleteById(int $entityId): bool
    {
        return $this->delete($this->getById($entityId));
    }

    /**
     * Get unique filter values for current product
     *
     * @param int $productId
     * @param int $categoryId
     * @param int $storeId
     * @param string $attributeCode
     * @param array $dependsOnAttributes
     * @param array $showAttributes
     * @return array
     */
    public function getUniqueValuesForCurrentProduct(
        int $productId,
        int $categoryId,
        int $storeId,
        string $attributeCode,
        array $dependsOnAttributes = [],
        array $showAttributes = []
    ): array {
        $collection = $this->collectionFactory->create();
        $collection->addCategoryIdFilter($categoryId)
            ->addStoreIdFilter($storeId)
            ->addAttributeCodeFilter($attributeCode);

        // Apply dependent filters
        if (!empty($dependsOnAttributes)) {
            foreach ($dependsOnAttributes as $dependentAttribute) {
                // Get the current product's value for this dependent attribute
                $dependentCollection = $this->collectionFactory->create();
                $dependentCollection->addProductIdFilter($productId)
                    ->addCategoryIdFilter($categoryId)
                    ->addStoreIdFilter($storeId)
                    ->addAttributeCodeFilter($dependentAttribute);

                $dependentValue = $dependentCollection->getFirstItem()->getAttributeValue();
                if ($dependentValue) {
                    // Filter products that have this dependent value
                    $productIdsWithDependentValue = $this->collectionFactory->create();
                    $productIdsWithDependentValue->addCategoryIdFilter($categoryId)
                        ->addStoreIdFilter($storeId)
                        ->addAttributeCodeFilter($dependentAttribute)
                        ->addAttributeValueFilter($dependentValue);

                    $productIds = $productIdsWithDependentValue->getColumnValues('product_id');
                    if (!empty($productIds)) {
                        $collection->addFieldToFilter('product_id', ['in' => $productIds]);
                    }
                }
            }
        }

        $items = $collection->getItems();
        $result = [];
        $uniqueValues = [];

        foreach ($items as $item) {
            $value = $item->getAttributeValue();
            $key = strtolower($value);

            if (!isset($uniqueValues[$key])) {
                $uniqueValues[$key] = true;
                $result[] = [
                    'name' => $value,
                    'url_key' => $item->getUrlKey(),
                    'selected' => $item->getProductId() == $productId,
                    'website_recommends' => $item->getWebsiteRecommends()
                ];
            }
        }

        return $result;
    }

    /**
     * Delete all relations for a product
     *
     * @param int $productId
     * @return int
     */
    public function deleteByProductId(int $productId): int
    {
        return $this->resource->deleteByProductId($productId);
    }

    /**
     * Delete all relations for a category
     *
     * @param int $categoryId
     * @return int
     */
    public function deleteByCategoryId(int $categoryId): int
    {
        return $this->resource->deleteByCategoryId($categoryId);
    }

    /**
     * Delete all relations for a store
     *
     * @param int $storeId
     * @return int
     */
    public function deleteByStoreId(int $storeId): int
    {
        return $this->resource->deleteByStoreId($storeId);
    }

    /**
     * Bulk insert filter relations
     *
     * @param array $data
     * @return int
     */
    public function bulkInsert(array $data): int
    {
        return $this->resource->bulkInsert($data);
    }

    /**
     * Clear all filter relations
     *
     * @return int
     */
    public function clearAll(): int
    {
        return $this->resource->clearAll();
    }
}
