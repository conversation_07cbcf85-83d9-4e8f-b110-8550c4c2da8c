<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;

class ProductFilterRelation extends AbstractDb
{
    /**
     * Table name
     */
    public const TABLE_NAME = 'udigital_product_filter_relation';

    /**
     * Primary key field name
     */
    public const ID_FIELD_NAME = 'entity_id';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(self::TABLE_NAME, self::ID_FIELD_NAME);
    }

    /**
     * Delete all relations for a specific product
     *
     * @param int $productId
     * @return int
     */
    public function deleteByProductId(int $productId): int
    {
        $connection = $this->getConnection();
        return $connection->delete(
            $this->getMainTable(),
            ['product_id = ?' => $productId]
        );
    }

    /**
     * Delete all relations for a specific category
     *
     * @param int $categoryId
     * @return int
     */
    public function deleteByCategoryId(int $categoryId): int
    {
        $connection = $this->getConnection();
        return $connection->delete(
            $this->getMainTable(),
            ['category_id = ?' => $categoryId]
        );
    }

    /**
     * Delete all relations for a specific store
     *
     * @param int $storeId
     * @return int
     */
    public function deleteByStoreId(int $storeId): int
    {
        $connection = $this->getConnection();
        return $connection->delete(
            $this->getMainTable(),
            ['store_id = ?' => $storeId]
        );
    }

    /**
     * Get filter relations for a specific product, category, store and attribute
     *
     * @param int $productId
     * @param int $categoryId
     * @param int $storeId
     * @param string $attributeCode
     * @return array
     */
    public function getFilterRelations(int $productId, int $categoryId, int $storeId, string $attributeCode): array
    {
        $connection = $this->getConnection();
        $select = $connection->select()
            ->from($this->getMainTable())
            ->where('product_id = ?', $productId)
            ->where('category_id = ?', $categoryId)
            ->where('store_id = ?', $storeId)
            ->where('attribute_code = ?', $attributeCode);

        return $connection->fetchAll($select);
    }

    /**
     * Get unique filter values for a category, store and attribute
     *
     * @param int $categoryId
     * @param int $storeId
     * @param string $attributeCode
     * @param array $dependsOnFilters
     * @return array
     */
    public function getUniqueFilterValues(int $categoryId, int $storeId, string $attributeCode, array $dependsOnFilters = []): array
    {
        $connection = $this->getConnection();
        $select = $connection->select()
            ->from($this->getMainTable())
            ->where('category_id = ?', $categoryId)
            ->where('store_id = ?', $storeId)
            ->where('attribute_code = ?', $attributeCode);

        // Apply dependent filters
        if (!empty($dependsOnFilters)) {
            foreach ($dependsOnFilters as $dependentAttribute => $dependentValue) {
                $subSelect = $connection->select()
                    ->from($this->getMainTable(), 'product_id')
                    ->where('category_id = ?', $categoryId)
                    ->where('store_id = ?', $storeId)
                    ->where('attribute_code = ?', $dependentAttribute)
                    ->where('attribute_value = ?', $dependentValue);

                $select->where('product_id IN (?)', $subSelect);
            }
        }

        return $connection->fetchAll($select);
    }

    /**
     * Bulk insert filter relations
     *
     * @param array $data
     * @return int
     */
    public function bulkInsert(array $data): int
    {
        if (empty($data)) {
            return 0;
        }

        $connection = $this->getConnection();
        return $connection->insertMultiple($this->getMainTable(), $data);
    }

    /**
     * Clear all filter relations
     *
     * @return int
     */
    public function clearAll(): int
    {
        $connection = $this->getConnection();
        return $connection->delete($this->getMainTable());
    }
}
