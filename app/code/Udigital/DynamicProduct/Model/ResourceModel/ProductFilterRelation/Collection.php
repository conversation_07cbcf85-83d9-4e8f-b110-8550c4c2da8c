<?php

declare(strict_types=1);

namespace Udigital\DynamicProduct\Model\ResourceModel\ProductFilterRelation;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Udigital\DynamicProduct\Model\ProductFilterRelation;
use Udigital\DynamicProduct\Model\ResourceModel\ProductFilterRelation as ProductFilterRelationResource;

class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'entity_id';

    /**
     * Initialize collection
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(ProductFilterRelation::class, ProductFilterRelationResource::class);
    }

    /**
     * Filter by product ID
     *
     * @param int $productId
     * @return $this
     */
    public function addProductIdFilter(int $productId): self
    {
        $this->addFieldToFilter('product_id', $productId);
        return $this;
    }

    /**
     * Filter by category ID
     *
     * @param int $categoryId
     * @return $this
     */
    public function addCategoryIdFilter(int $categoryId): self
    {
        $this->addFieldToFilter('category_id', $categoryId);
        return $this;
    }

    /**
     * Filter by store ID
     *
     * @param int $storeId
     * @return $this
     */
    public function addStoreIdFilter(int $storeId): self
    {
        $this->addFieldToFilter('store_id', $storeId);
        return $this;
    }

    /**
     * Filter by attribute code
     *
     * @param string $attributeCode
     * @return $this
     */
    public function addAttributeCodeFilter(string $attributeCode): self
    {
        $this->addFieldToFilter('attribute_code', $attributeCode);
        return $this;
    }

    /**
     * Filter by attribute value
     *
     * @param string $attributeValue
     * @return $this
     */
    public function addAttributeValueFilter(string $attributeValue): self
    {
        $this->addFieldToFilter('attribute_value', $attributeValue);
        return $this;
    }

    /**
     * Filter by selected status
     *
     * @param bool $isSelected
     * @return $this
     */
    public function addSelectedFilter(bool $isSelected = true): self
    {
        $this->addFieldToFilter('is_selected', $isSelected);
        return $this;
    }

    /**
     * Filter by website recommends
     *
     * @param bool $websiteRecommends
     * @return $this
     */
    public function addWebsiteRecommendsFilter(bool $websiteRecommends = true): self
    {
        $this->addFieldToFilter('website_recommends', $websiteRecommends);
        return $this;
    }
}
