<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="udigital" translate="label" sortOrder="10">
            <label>U-Digital</label>
        </tab>
        <section id="udigital_dynamic_product" translate="label" sortOrder="130" showInDefault="1" showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>Dynamic Product Filters</label>
            <tab>udigital</tab>
            <resource>Udigital_DynamicProduct::config</resource>
            <group id="filter_relations" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Filter Relations Configuration</label>
                <field id="cron_enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable Cron Job</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable automatic rebuilding of product filter relations via cron job</comment>
                </field>
                <field id="cron_schedule" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Cron Schedule</label>
                    <comment>Cron expression for when to rebuild filter relations (default: 0 2 * * * - daily at 2 AM)</comment>
                    <depends>
                        <field id="cron_enabled">1</field>
                    </depends>
                </field>
                <field id="batch_size" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Batch Size</label>
                    <comment>Number of products to process in each batch (default: 1000)</comment>
                    <validate>validate-digits validate-greater-than-zero</validate>
                </field>
                <field id="rebuild_button" translate="label" type="button" sortOrder="40" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Manual Rebuild</label>
                    <frontend_model>Udigital\DynamicProduct\Block\Adminhtml\System\Config\RebuildButton</frontend_model>
                    <comment>Click to manually rebuild all product filter relations</comment>
                </field>
                <field id="last_rebuild" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Last Rebuild</label>
                    <frontend_model>Udigital\DynamicProduct\Block\Adminhtml\System\Config\LastRebuild</frontend_model>
                    <comment>Shows when the filter relations were last rebuilt</comment>
                </field>
            </group>
            <group id="performance" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Performance Settings</label>
                <field id="cache_enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Filter Cache</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable caching of filter results for better performance</comment>
                </field>
                <field id="cache_lifetime" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Cache Lifetime (seconds)</label>
                    <comment>How long to cache filter results (default: 3600 seconds = 1 hour)</comment>
                    <validate>validate-digits validate-greater-than-zero</validate>
                    <depends>
                        <field id="cache_enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
