<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="udigital_product_filter_relation" resource="default" engine="innodb" comment="Product Filter Relations">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" identity="true" comment="Entity ID"/>
        <column xsi:type="int" name="product_id" unsigned="true" nullable="false" comment="Product ID"/>
        <column xsi:type="int" name="category_id" unsigned="true" nullable="false" comment="Category ID"/>
        <column xsi:type="int" name="store_id" unsigned="true" nullable="false" comment="Store ID"/>
        <column xsi:type="varchar" name="attribute_code" nullable="false" length="255" comment="Attribute Code"/>
        <column xsi:type="text" name="attribute_value" nullable="true" comment="Attribute Value"/>
        <column xsi:type="text" name="url_key" nullable="false" comment="Product URL"/>
        <column xsi:type="boolean" name="is_selected" nullable="false" default="false" comment="Is Selected"/>
        <column xsi:type="boolean" name="website_recommends" nullable="false" default="false" comment="Website Recommends"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP" comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP" comment="Updated At"/>
        
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        
        <constraint xsi:type="foreign" referenceId="UDIGITAL_PRODUCT_FILTER_RELATION_PRODUCT_ID_CATALOG_PRODUCT_ENTITY_ENTITY_ID" 
                    table="udigital_product_filter_relation" column="product_id" 
                    referenceTable="catalog_product_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
        
        <constraint xsi:type="foreign" referenceId="UDIGITAL_PRODUCT_FILTER_RELATION_CATEGORY_ID_CATALOG_CATEGORY_ENTITY_ENTITY_ID" 
                    table="udigital_product_filter_relation" column="category_id" 
                    referenceTable="catalog_category_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
        
        <constraint xsi:type="foreign" referenceId="UDIGITAL_PRODUCT_FILTER_RELATION_STORE_ID_STORE_STORE_ID" 
                    table="udigital_product_filter_relation" column="store_id" 
                    referenceTable="store" referenceColumn="store_id" onDelete="CASCADE"/>
        
        <index referenceId="UDIGITAL_PRODUCT_FILTER_RELATION_PRODUCT_ID_CATEGORY_ID_STORE_ID_ATTRIBUTE_CODE" indexType="btree">
            <column name="product_id"/>
            <column name="category_id"/>
            <column name="store_id"/>
            <column name="attribute_code"/>
        </index>
        
        <index referenceId="UDIGITAL_PRODUCT_FILTER_RELATION_CATEGORY_ID_STORE_ID_ATTRIBUTE_CODE" indexType="btree">
            <column name="category_id"/>
            <column name="store_id"/>
            <column name="attribute_code"/>
        </index>
    </table>
</schema>
