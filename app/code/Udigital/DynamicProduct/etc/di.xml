<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- Repository Interface Binding -->
    <preference for="Udigital\DynamicProduct\Api\ProductFilterRelationRepositoryInterface"
                type="Udigital\DynamicProduct\Model\ProductFilterRelationRepository"/>

    <preference for="Udigital\DynamicProduct\Api\Data\ProductFilterRelationInterface"
                type="Udigital\DynamicProduct\Model\ProductFilterRelation"/>



    <!-- Console Commands -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="udigital_rebuild_filter_relations" xsi:type="object">Udigital\DynamicProduct\Console\Command\RebuildFilterRelations</item>
            </argument>
        </arguments>
    </type>


</config>
