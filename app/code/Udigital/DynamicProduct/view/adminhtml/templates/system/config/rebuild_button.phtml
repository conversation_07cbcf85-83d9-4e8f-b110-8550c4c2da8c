<?php
/**
 * @var \Udigital\DynamicProduct\Block\Adminhtml\System\Config\RebuildButton $block
 */
?>
<script type="text/javascript">
    require([
        'jquery',
        'Magento_Ui/js/modal/alert'
    ], function ($, alert) {
        window.rebuildFilterRelations = function() {
            $('#rebuild_filter_relations').prop('disabled', true).text('<?= $block->escapeJs(__('Rebuilding...')) ?>');
            
            $.ajax({
                url: '<?= $block->escapeJs($block->getAjaxUrl()) ?>',
                type: 'POST',
                dataType: 'json',
                showLoader: true,
                success: function(response) {
                    $('#rebuild_filter_relations').prop('disabled', false).text('<?= $block->escapeJs(__('Rebuild Filter Relations')) ?>');
                    
                    if (response.success) {
                        alert({
                            title: '<?= $block->escapeJs(__('Success')) ?>',
                            content: response.message || '<?= $block->escapeJs(__('Filter relations rebuilt successfully!')) ?>'
                        });
                    } else {
                        alert({
                            title: '<?= $block->escapeJs(__('Error')) ?>',
                            content: response.message || '<?= $block->escapeJs(__('An error occurred while rebuilding filter relations.')) ?>'
                        });
                    }
                },
                error: function() {
                    $('#rebuild_filter_relations').prop('disabled', false).text('<?= $block->escapeJs(__('Rebuild Filter Relations')) ?>');
                    alert({
                        title: '<?= $block->escapeJs(__('Error')) ?>',
                        content: '<?= $block->escapeJs(__('An error occurred while rebuilding filter relations.')) ?>'
                    });
                }
            });
        };
    });
</script>

<?= $block->getButtonHtml() ?>
