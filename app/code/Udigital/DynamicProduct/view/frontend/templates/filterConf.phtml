<?php

/**
 * Copyright Elgentos. All rights reserved.
 * https://elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\Modal;
use Magento\Cms\Block\Block;
use Udigital\DynamicProduct\Block\FilterConf;
use Magento\Framework\Escaper;
use Udigital\DynamicProduct\ViewModel\ProductSorterViewModel;

/**
 * @var ViewModelRegistry $viewModels
 * @var HeroiconsOutline  $heroicons
 * @var Modal             $modalViewModel
 * @var FilterConf        $block
 * @var Escaper           $escaper
 */

$heroicons = $viewModels->require(HeroiconsOutline::class);
$productSorter = $viewModels->require(ProductSorterViewModel::class);
$modalViewModel = $viewModels->require(Modal::class);
?>
<div class="relative mb-1 flex flex-col" x-data="Object.assign({}, hyva.modal())">

    <!-- merk_filter -->
    <?php $attributeFilters = $block->getUniqueValuesForCurrentProduct('merk_filter', [], ['website_recommends']); ?>
    <?php if (!empty($attributeFilters) && is_array($attributeFilters)): ?>
        <div class="items-start py-2 w-full border-gray-300 merk-filter-container">
            <div class="flex mb-2">
            <span class="font-bold text-lg max-lg:text-base">
                <?= $escaper->escapeHtml($attributeFilters['title']); ?>
            </span>
            </div>
            <div class="flex flex-wrap gap-3">
                <?php foreach ($productSorter->sortBrands($attributeFilters['content']) as $attr): ?>
                    <a href="<?= $escaper->escapeUrl($attr['url_key']) ?>"
                       class="py-1 px-3 min-w-28 max-md:min-w-20 max-md:text-sm text-base relative text-center
                    border border-container-darker shadow-sm cursor-pointer select-none
                    bg-container-lighter rounded-lg <?= $attr['selected'] ? 'ring' : ''; ?>">
                        <?= $escaper->escapeHtml($attr['name']) ?>
                        <?php if (isset($attr['website_recommends']) && (bool)$attr['website_recommends']) { ?>
                            <span class="invisible min-w-full text-center rounded px-1.25 absolute z-10 top-full
                                left-0 text-sm flex items-center justify-center text-blue-jeans m-0 tooltiptext mt-3">
                                    <div class="flex flex-nowrap place-content-center rounded-md px-1 py-1
                                    tooltiptext-div bg-blue-prussian">
                                        <?= $escaper->escapeHtml(__('tops')) ?>
                                        <span class="text-white pl-1.25 max-screen-256.25:text-xs">
                                            <?= $escaper->escapeHtml(__('keuze')) ?>
                                        </span>
                                        <button type="button" class="text-xs text-blue-picton ml-2" >
                                                <?= $heroicons->informationCircleHtml('w-4 h-4') ?>
                                        </button>
                                    </div>
                                </span>
                        <?php    }
                        ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
    <!-- filter_klasse -->
    <?php
    $attributeFilters =
        $block->getUniqueValuesForCurrentProduct('filter_klasse', ['merk_filter'], ['website_recommends']);
    ?>
    <?php if (!empty($attributeFilters) && is_array($attributeFilters)): ?>
        <?php if (isset($attributeFilters['content']) && count($attributeFilters['content']) > 0): ?>
            <div class="items-start py-2 w-full border-gray-300 merk-filter-container">
                <div class="flex mb-2">
            <span class="font-bold text-lg max-lg:text-base">
                <?= $escaper->escapeHtml($attributeFilters['title']); ?>
            </span>
                    <div class="make-modal">
                        <?php
                        $contentModal = '';
                        $classModal   = 'justify-center';
                        $classWidth = 'w-6/12';
                        $classMargin = 'mx-';
                        $dialogClass  = 'modal-view-product';
                        $classModal   = 'justify-center';
                        $classWidth = 'w-10/12';
                        $classMargin = 'mx-filterklasse';
                        $contentModal = $block->getLayout()
                            ->createBlock(Block::class)
                            ->setBlockId('overlay-filterklasse-uitleg')
                            ->toHtml();
                        $modal = $modalViewModel->createModal()->withContent('
                            <div id="the-label' . $classMargin . '" class="relative w-full h-full
                            md:h-auto text-blue-prussian text-justify">
                                <!-- Modal header -->
                                <div class="flex items-start justify-end pb-4 lg:pl-24">
                                    <button class="close-button ml-6 lg:ml-44 -mt-4" @click="hide">
                                        <span aria-hidden="true" class="s-close-button text-2xl !text-blue-picton
                                        font-bold text-blue-picton">&times;</span><span class="sr-only">Close</span>
                                    </button>
                                </div>
                                <!-- Modal body -->
                                <div class="body-content-modal pr-6 py-4 body-content-scroll content-modal-attr">
                                    ' . $contentModal . '
                                </div>
                            </div>' . '<script>

                            (() => {

                                function myEventOverlayAdviesCallback() {
                                    const svg_arrow = \'<svg xmlns="http://www.w3.org/2000/svg" \
                                    class="h-3 w-3" \
                                    fill="none" \
                                    viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">\
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />\
                                    </svg>\';
                                    const arrow_down = document.getElementsByClassName("arrow-down");
                                    for (let i = 0; i < arrow_down.length; i++) {
                                        arrow_down[i].innerHTML = svg_arrow;
                                    }
                                }
                                window.addEventListener(\'DOMContentLoaded\', myEventOverlayAdviesCallback);

                                })();
                            </script>')
                            ->withContainerClasses(
                                'fixed',
                                'flex',
                                $classModal,
                                'items-center',
                                $classWidth,
                                "!mx-auto ".$classMargin
                            )
                            ->withAriaLabelledby('the-label')
                            ->addDialogClass($dialogClass); ?>
                        <button type="button" class="text-xs text-blue-picton ml-2"
                                @click="<?= $escaper->escapeHtmlAttr($modal->getShowJs()) ?>">
                            <?= $heroicons->informationCircleHtml('w-4 h-4') ?>
                        </button>
                        <?= ($modal); // @codingStandardsIgnoreLine?>
                    </div>
                </div>
                <div class="flex flex-wrap gap-3">
                    <?php foreach ($productSorter->sortByClasses($attributeFilters['content']) as $attr): ?>
                        <a href="<?= $escaper->escapeUrl($attr['url_key']) ?>"
                           class="py-1 px-2 min-w-28 max-md:min-w-20 max-md:text-sm text-base relative text-center
                    border border-container-darker shadow-sm cursor-pointer select-none bg-container-lighter
                    rounded-lg <?= $attr['selected'] ? 'ring' : ''; ?> tooltip">
                            <?= $escaper->escapeHtml($attr['name']) ?>
                            <?php if (isset($attr['website_recommends']) && (bool)$attr['website_recommends']) { ?>
                                <span class="invisible min-w-full text-center rounded px-1.25 absolute z-10
                                top-full left-0 text-sm flex items-center justify-center text-blue-jeans
                                m-0 tooltiptext mt-3">
                                    <div class="flex flex-nowrap place-content-center rounded-md px-1 py-1
                                    tooltiptext-div bg-blue-prussian">
                                        <?= $escaper->escapeHtml(__('tops')) ?>
                                        <span class="text-white pl-1.25 max-screen-256.25:text-xs">
                                            <?= $escaper->escapeHtml(__('keuze')) ?>
                                        </span>
                                        <button type="button" class="text-xs text-blue-picton ml-2" >
                                            <?= $heroicons->informationCircleHtml('w-4 h-4') ?>
                                        </button>
                                    </div>
                                </span>
                            <?php    }
                            ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif ?>
    <?php endif ?>
    <!-- bypass -->
    <?php $attributeFilters = $block->getUniqueValuesForCurrentProduct('bypass', ['merk_filter', 'filter_klasse']); ?>
    <?php if (!empty($attributeFilters)): ?>
        <?php if (isset($attributeFilters['content']) && count($attributeFilters['content']) > 1): ?>
            <div class="items-start py-2 w-full border-gray-300 merk-filter-container">
                <div class="flex mb-2">
            <span class="font-bold text-lg max-lg:text-base">
                <?= $escaper->escapeHtml($attributeFilters['title']); ?>
            </span>
                    <div class="make-modal">
                        <?php
                        $contentModal = '';
                        $classModal   = 'justify-center';
                        $classWidth = 'w-6/12';
                        $classMargin = 'mx-';
                        $dialogClass  = 'modal-view-product-bypass';
                        $classModal   = 'justify-center';
                        $classWidth = 'w-10/12';
                        $classMargin = 'mx-bypass';
                        $contentModal = $block->getLayout()
                            ->createBlock(Block::class)
                            ->setBlockId('overlay-bypass')
                            ->toHtml();
                        $modal = $modalViewModel->createModal()->withContent('
                                <div id="the-label' . $classMargin . '" class="relative w-full h-full
                                md:h-auto text-blue-prussian text-justify">
                                    <!-- Modal header -->
                                    <div class="flex items-start justify-end pb-4 lg:pl-24">
                                        <button class="close-button ml-6 lg:ml-44 -mt-4" @click="hide">
                                            <span aria-hidden="true" class="s-close-button text-2xl !text-blue-picton
                                            font-bold text-blue-picton">&times;</span><span class="sr-only">Close</span>
                                        </button>
                                    </div>
                                    <!-- Modal body -->
                                    <div class="body-content-modal pr-6 md:py-4 body-content-scroll content-modal-attr">
                                        ' . $contentModal . '
                                    </div>
                                </div>' . '<script>
                                (() => {
                                    function myEventOverlayAdviesCallback() {
                                        const svg_arrow = \'<svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3"\
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">\
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />\
                                        </svg>\';
                                        const arrow_down = document.getElementsByClassName("arrow-down");
                                        for (let i = 0; i < arrow_down.length; i++) {
                                            arrow_down[i].innerHTML = svg_arrow;
                                        }
                                    }
                                    window.addEventListener(\'DOMContentLoaded\', myEventOverlayAdviesCallback);
                                    })();
                                </script>')
                            ->withContainerClasses('fixed', 'flex', $classModal, 'items-center', $classWidth, $classMargin)
                            ->withAriaLabelledby('the-label')
                            ->addDialogClass($dialogClass); ?>
                        <button type="button" class="text-xs text-blue-picton ml-2"
                                @click="<?= $escaper->escapeHtmlAttr($modal->getShowJs()) ?>">
                            <?= $heroicons->informationCircleHtml('w-4 h-4') ?>
                        </button>
                        <?= ($modal); // @codingStandardsIgnoreLine ?>
                    </div>
                </div>
                <div class="flex flex-wrap gap-3">
                    <?php foreach ($attributeFilters['content'] as $attr): ?>
                        <a href="<?= $escaper->escapeUrl($attr['url_key']) ?>"
                           class="py-1 px-3 min-w-28 max-md:min-w-20 max-md:text-sm text-base relative text-center border
                    border-container-darker shadow-sm cursor-pointer select-none bg-container-lighter
                    rounded-lg <?= $attr['selected'] ? 'ring' : ''; ?>">
                            <?= $escaper->escapeHtml($attr['name'] ? __("With Bypass") : __("Without Bypass")) ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <!-- specifiek_type -->
    <?php
    $attributeFilters =
        $block->getUniqueValuesForCurrentProduct('specifiek_type', ['merk_filter', 'filter_klasse']);
    ?>
    <?php if (!empty($attributeFilters)): ?>
        <?php  if (isset($attributeFilters['content'])) {
            if (count($attributeFilters['content']) > 1): ?>
                <div class="items-start py-2 w-full border-gray-300 merk-filter-container">
                    <div class="flex mb-2">
            <span class="font-bold text-lg max-lg:text-base">
                <?= $escaper->escapeHtml($attributeFilters['title']); ?>
            </span>
                    </div>
                    <div class="flex flex-wrap gap-3">
                        <?php foreach ($productSorter->sortByNumberAndName($attributeFilters['content']) as $attr): ?>
                            <a href="<?= $escaper->escapeUrl($attr['url_key']) ?>"
                               class="py-1 px-3 min-w-28 max-md:min-w-20 max-md:text-sm text-base relative
                    text-center border border-container-darker shadow-sm cursor-pointer select-none
                    bg-container-lighter rounded-lg
                    <?= $escaper->escapeHtml($attr['selected'] ? 'ring' : ''); ?>">
                                <?= $escaper->escapeHtml($attr['name']) ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif;
        }; ?>
    <?php endif; ?>
</div>
