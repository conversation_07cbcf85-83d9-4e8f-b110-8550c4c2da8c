<?php
/**
 * This file is part of the Magento 2 Shipping module of DPD Nederland B.V.
 *
 * Copyright (C) 2019  DPD Nederland B.V.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

use Magento\Framework\Escaper;

/** @var Escaper $escaper */
$parcelshop = $this->getParcelshop();
$quote = $this->getQuote();
?>
<div class="content">
    <table style="min-width: 380px" cellpadding="3" cellspacing="3">
        <tbody>
            <tr>
                <td style="padding: 3px; padding-right: 15px;" rowspan="2">
                    <img style="width: 80px; height: 80px;" src="<?= $escaper->escapeUrl($this->getViewFileUrl('DpdConnect_Shipping::images/dpd_parcelshop_logo.png')) ?>" alt="DPD Parcelshop logo"/>
                </td>
                <td style="padding: 3px; padding-top: 6px; width: 100%;" colspan="3">
                    <strong><?= $escaper->escapeHtml($quote->getData('dpd_parcelshop_name')) ?></strong><br />
                    <?= sprintf('%s %s', $escaper->escapeHtml($quote->getData('dpd_parcelshop_street')), $escaper->escapeHtml($quote->getData('dpd_parcelshop_house_number'))) ?><br />
                    <?= sprintf('%s %s', $escaper->escapeHtml($quote->getData('dpd_parcelshop_zip_code')), $escaper->escapeHtml($quote->getData('dpd_parcelshop_city'))) ?>
                </td>
            </tr>
            <tr>
                <td style="padding: 3px; padding-top: 6px; width: 100%;" colspan="3">
                    <strong><?= /* @noEscape */ __('Opening hours') ?></strong>
                </td>
            </tr>
            <?= /* @noEscape */ __($this->getOpeningHoursHtml()) ?>
            <tr>
                <td style="padding: 3px; border: none;"></td>
                <td style="padding: 3px; width: 100%;" colspan="3">
                    <a href="#" class="dpd_connect_change_parcelshop underline pt-4"><?= $escaper->escapeHtml(__('Click here to change your ParcelShop')) ?></a>
                    <input type="hidden" class="DPD-confirmed" value="1"/>
                    <input type="hidden" class="parcelshopId" value="<?= $escaper->escapeHtmlAttr($quote->getData('dpd_parcelshop_id')) ?>" />
                </td>
            </tr>
        </tbody>
    </table>
</div>
