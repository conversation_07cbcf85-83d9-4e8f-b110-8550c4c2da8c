<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Breadcrumbs;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var Breadcrumbs $Breadcrumbs */
$breadcrumbs = $viewModels->require(Breadcrumbs::class);

$navigator = $breadcrumbs->getNavigator();
$currentStep = $navigator->getActiveStep();
$checkout = $navigator->getActiveCheckout();
$settingShowWaypoints = $breadcrumbs->getSystemConfig()->showBreadcrumbsWaypoints();
$settingShowBreadcrumbsCart = $breadcrumbs->getSystemConfig()->showBreadcrumbsCart();
$stepCount = $checkout->countSteps();

$lineHtml = '<span class="progress-bar-line hidden lg:block absolute -translate-y-1/2 -z-10 w-full h-[2px] text-blue-prussian-darker"></span>';
?>

<style>
    .progress-bar-line {
        background-image: linear-gradient(
            to right,
            var(--line-before-start, var(--line-before, currentcolor)),
            var(--line-before-end, var(--line-before, currentcolor)) 50%,
            var(--line-after-start, var(--line-after, currentcolor)) 50%,
            var(--line-after-end, var(--line-after, currentcolor))
        );
    }
</style>

<ul class="progress-bar flex"`>
    <?php if ($settingShowBreadcrumbsCart): ?>
        <?php $isComplete = $currentStep->getPosition() > 0; ?>
        <?php $isPrevious = $currentStep->getPosition() === 1 ?>
        <li class="relative lg:flex-1 lg:flex lg:flex-col lg:items-center [--line-before:#0000]
            <?= $isPrevious ? '' : 'hidden' ?>
            <?= ($isComplete) ? '[--line-after:theme(colors.green-islamic.DEFAULT)]' : '' ?>
        ">
            <a
                href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart', ['_secure' => true])) ?>"
                class="group inline-flex flex-col gap-2 items-center outline-offset-2"
            >
                <span class="flex items-center gap-2 text-blue-prussian-darker">
                    <?php if ($isPrevious): ?>
                        <?= /* @noEscape */ $heroiconsSolid->chevronLeftHtml(
                            'group-hover:-translate-x-0.5 transition rtl:rotate-180 lg:hidden',
                            16,
                            16,
                            ['aria-hidden' => 'true']
                        ); ?>
                    <?php endif ?>
                    <?= $escaper->escapeHtml(__('Cart')) ?>
                </span>
                <?= /* @noEscape */ $lineHtml; ?>
                <div class="hidden lg:flex items-center justify-center w-5 h-5 rounded-full bg-blue-prussian-darker text-blue-prussian-darker">
                    <?= $heroiconsSolid->checkHtml('', 14, 14, ['aria-hidden' => 'true']) ?>
                </div>
            </a>
        </li>
    <?php endif ?>
    <?php foreach ($checkout->getAvailableSteps() as $step): ?>
        <?php $isCurrent = ($step->getPosition() === $currentStep->getPosition()) ?>
        <?php $isComplete = ($step->getPosition() < $currentStep->getPosition()); ?>
        <?php $isPrevious = ($step->getPosition() === ($currentStep->getPosition() - 1)) ?>

        <li class="relative lg:flex-1 lg:flex lg:flex-col items-center first:[--line-before:#0000] last:[--line-after:#0000]
            <?= ($isPrevious) ? '' : 'hidden' ?>
            <?= ($isCurrent) ? '[--line-before:theme(colors.green-islamic.DEFAULT)] [--line-after:theme(colors.green-islamic.darker)]' : '' ?>
            <?= ($isComplete) ? '[--line-before:theme(colors.green-islamic.DEFAULT)] [--line-after:theme(colors.green-islamic.DEFAULT)]' : '' ?>
        ">
            <?php if ($checkout->isStepBackwards($step, $currentStep) || $checkout->isComparison($step, $currentStep)): ?>
                <button
                    type="button"
                    class="group inline-flex flex-col gap-2 items-center outline-offset-2 aria-[current=step]:font-bold"
                    <?php if ($isCurrent): ?>
                        aria-current="step"
                    <?php endif; ?>
                    data-route="<?= $escaper->escapeHtmlAttr($step->getRoute()) ?>"
                    x-on:click="navigateToStep"
                >
                    <div class="flex items-center gap-2 <?= ($isComplete || $isCurrent) ? 'text-green-islamic-darker' : 'text-blue-prussian-darker' ?>">
                        <?php if ($isPrevious): ?>
                            <?= $heroiconsSolid->chevronLeftHtml(
                                'group-hover:-translate-x-0.5 transition rtl:rotate-180 lg:hidden',
                                16,
                                16,
                                ['aria-hidden' => 'true']
                            ); ?>
                        <?php endif; ?>
                        <?= $escaper->escapeHtml(__($step->getLabel())) ?>
                    </div>
                    <div class="hidden lg:flex items-center justify-center w-5 h-5" aria-hidden="true">
                        <?= /* @noEscape */ $lineHtml; ?>
                        <?php if ($isCurrent): ?>
                            <div class="w-4 h-4 rounded-full bg-green-islamic-darker"></div>
                        <?php elseif ($isComplete): ?>
                            <div class="flex items-center justify-center w-4 h-4 rounded-full bg-green-islamic text-green-islamic">
                                <?= $heroiconsSolid->checkHtml('', 14, 14, ['aria-hidden' => 'true']) ?>
                            </div>
                        <?php else: ?>
                            <div class="border-2 border-blue-prussian-darker w-4 h-4 rounded-full bg-blue-prussian-darker"></div>
                        <?php endif; ?>
                    </div>
                </button>
            <?php else: ?>
                <div class="flex flex-col items-center gap-2">
                    <div class="flex items-center gap-2 text-blue-prussian-darker">
                        <?= $escaper->escapeHtml(__($step->getLabel())) ?>
                    </div>
                    <div class="hidden lg:flex items-center justify-center w-5 h-5" aria-hidden="true">
                        <?= /* @noEscape */ $lineHtml; ?>
                        <div class="border-2 border-blue-prussian-darker w-4 h-4 rounded-full bg-blue-prussian-darker"></div>
                    </div>
                </div>
            <?php endif ?>
        </li>
    <?php endforeach ?>
</ul>
