<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Checkout\Magewire\Checkout\CouponCode as Magewire;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Magewire $magewire */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$classCouponAvailability = $magewire->couponCode ? 'available' : 'not-available';
$saveAction = $magewire->couponCode ? 'revokeCouponCode' : 'applyCouponCode';
?>

<div
    x-data="{
        show: false,
        couponCode: $wire.entangle('couponCode'),
        couponHits: $wire.entangle('couponHits')
    }"
>
    <button
        class="section-title w-full border-b border-slate-300 hover:border-blue-picton pb-2 mb-6 text-left flex items-center transition-colors"
        type="button"
        @click.prevent="show = !show"
    >
        <h2 class="text-slate-800 text-xl font-medium block grow">
            <?= $escaper->escapeHtml(__('Apply Discount Code')) ?>
        </h2>
        <span class="transition-all"
              :class="show ? 'rotate-180': ''">
            <?= $heroicons->chevronDownHtml('text-slate-600', 16, 16); ?>
        </span>
    </button>
    <div
        id="checkout-coupon-code-details"
        class="coupon-code <?= $escaper->escapeHtmlAttr($classCouponAvailability) ?> flex flex-col gap-2 mt-4 xl:flex-row"
        aria-label="<?= $escaper->escapeHtml(__('Apply Discount Code')) ?>"
        x-cloak
        x-show="show"
    >
        <label class="flex-1 xl:focus-within:relative">
            <span class="sr-only"><?= $escaper->escapeHtml(__('Discount code')) ?></span>
            <input
                type="text"
                class="form-input w-full"
                placeholder="<?= $escaper->escapeHtmlAttr(__('Enter discount code')) ?>"
                wire:model.defer="couponCode"
                wire:loading.attr="disabled"
                wire:keydown.enter="<?= /* @noEscape */ $saveAction ?>"
                x-bind:class="{ 'has-coupon': couponCode, 'invalid': couponHits !== 0 }"
                x-bind:disabled="couponCode"
            >
        </label>
        <button
            type="button"
            class="btn btn-primary bg-blue-picton justify-center"
            x-bind:class="{ 'has-coupon': couponCode, 'invalid': couponHits !== 0 }"
            wire:click="<?= /* @noEscape */ $saveAction ?>"
            wire:loading.attr="disabled"
        >
            <span wire:loading.block class="hidden">
                <?= $escaper->escapeHtml(__('Processing ...')) ?>
            </span>
            <?php if ($magewire->couponCode): ?>
                <span aria-label="<?= $escaper->escapeHtml(__('Cancel Coupon')) ?>" wire:loading.remove>
                    <?= $escaper->escapeHtml(__('Cancel')) ?>
                </span>
            <?php else: ?>
                <span aria-label="<?= $escaper->escapeHtml(__('Apply Coupon')) ?>" wire:loading.remove>
                    <?= $escaper->escapeHtml(__('Apply')) ?>
                </span>
            <?php endif ?>
        </button>
    </div>
</div>
