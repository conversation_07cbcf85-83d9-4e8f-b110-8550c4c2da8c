<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Checkout\Model\MethodMetaDataInterface;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Checkout\ViewModel\Checkout\Payment\MethodList as ViewModel;
use Hyva\Checkout\Magewire\Checkout\Payment\MethodList as Magewire;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Quote\Api\Data\PaymentMethodInterface;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var ViewModel $viewModel */
/** @var PaymentMethodInterface $method */
/** @var Magewire $magewire */
/** @var Escaper $escaper */
/** @var MethodMetaDataInterface $methodMetaData */

$viewModel = $viewModels->require(ViewModel::class);
$methods = $viewModel->getList();
?>
<div id="payment-methods">
    <?php if ($methods): ?>
        <script>
            window.addEventListener('checkout:step:loaded', () => {
                if('<?= $escaper->escapeJs($magewire->method) ?>' && document.getElementById('payment-method-list')) {
                    window.dispatchEvent(new CustomEvent('checkout:payment:method-activate', { detail: { method: '<?= $escaper->escapeJs($magewire->method) ?>'} }))
                }
            }, { once: true })
        </script>

        <div id="payment-method-list" class="space-y-2">
            <?php foreach ($methods as $method): ?>
                <?php $methodCodeAttr = $escaper->escapeHtmlAttr($method->getCode()) ?>
                <?php $methodMetaData = $viewModel->getMethodMetaData($block, $method) ?>

                <div
                    id="payment-method-option-<?= /* @noEscape */ $methodCodeAttr ?>"
                    class="<?= $magewire->method === $method->getCode() ? 'active' : 'inactive' ?>"
                    wire:key="<?= /* @noEscape */ $methodCodeAttr ?>"
                >
                    <label class="flex gap-x-2.5 mb-0 px-2 cursor-pointer">
                        <div class="flex items-center">
                            <input
                                type="radio"
                                class="form-radio"
                                id="payment-method-<?= /* @noEscape */ $methodCodeAttr ?>"
                                name="payment-method-option"
                                value="<?= /* @noEscape */ $methodCodeAttr ?>"
                                wire:model="method"
                            />
                        </div>

                        <span class="flex justify-between items-center space-x-4">
                            <?php if ($methodMetaData->canRenderIcon() && $iconHtml = $methodMetaData->renderIcon()): ?>
                                <div>
                                    <?= /* @noEscape */ $iconHtml; ?>
                                </div>
                            <?php endif ?>
                            <span class="text-gray-700 font-medium flex flex-col break-words hyphens-auto leading-relaxed">
                                <?= $escaper->escapeHtml($method->getTitle()) ?>

                                <?php if ($methodMetaData->hasSubTitle()): ?>
                                    <span class="text-gray-700 font-medium break-words hyphens-auto">
                                        <?= $escaper->escapeHtml($methodMetaData->getSubTitle()) ?>
                                    </span>
                                <?php endif ?>
                            </span>
                        </span>
                    </label>
                    <?php if ($viewModel->canShowMethod($block, $method, $magewire->method)): ?>
                        <?php $html = $viewModel->getMethodBlock($block, $method)->toHtml() ?>

                        <?php if (! empty($html)): ?>
                            <div id="<?= 'payment-method-view-' . /* @noEscape */ $methodCodeAttr ?>" class="p-4 pt-0">
                                <?= /* @noEscape */ $html ?>
                            </div>
                        <?php endif ?>
                    <?php endif ?>
                </div>
            <?php endforeach ?>
        </div>
    <?php else: ?>
        <div class="message warning">
            <?= $escaper->escapeHtml(__('No Payment method available.')) ?>
        </div>
    <?php endif ?>
</div>
