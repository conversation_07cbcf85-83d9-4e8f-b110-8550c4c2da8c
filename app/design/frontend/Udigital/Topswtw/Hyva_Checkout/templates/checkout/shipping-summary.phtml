<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Checkout\ViewModel\Checkout\ShippingSummary;
use Hyva\Checkout\ViewModel\Checkout\AddressRenderer;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var ShippingSummary $viewModel */

$viewModel = $viewModels->require(ShippingSummary::class);
$addressRenderer = $viewModels->require(AddressRenderer::class);

$address = $viewModel->getShippingAddress();
$carrier = $viewModel->getShippingCarrier();
?>
<div>
    <?php if ($address->getFirstname() || $address->getLastname()): ?>
        <div class="pb-4">
            <div class="section-title mb-4">
                <h2 class="text-slate-800 font-bold">
                    <?= $escaper->escapeHtml((__('Ship To'))) ?>
                </h2>
            </div>

            <address class="not-italic text-slate-800 text-sm">
                <?= /* @noEscape */ $addressRenderer->renderCustomerAddress($address) ?>
            </address>
        </div>
    <?php else: ?>
        <p>
            <?= $escaper->escapeHtml(__('No shipping address set.')) ?>
        </p>
    <?php endif ?>

    <?php if ($carrier): ?>
    <div>
        <div class="section-title mb-4">
            <h2 class="text-slate-800 font-bold">
                <?= $escaper->escapeHtml((__('Shipping Method'))) ?>
            </h2>
        </div>

        <p class="break-all text-sm text-slate-800">
            <?= $escaper->escapeHtml($carrier) ?>
        </p>
    </div>
    <?php else: ?>
        <p>
            <?= $escaper->escapeHtml(__('No shipping method selected.')) ?>
        </p>
    <?php endif ?>
</div>
