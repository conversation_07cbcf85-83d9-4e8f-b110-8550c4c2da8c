<?php

/**
 * Copyright Elgentos. All rights reserved.
 * https://elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\Escaper;
use Tops\Checkout\Block\Usp;

/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Usp $block */

$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
$website_id = $block->getWebsiteId();
$text_free_shipping = ($website_id == 4 ) ? "Free shipping from €100" : "Free shipping from €50";

?>
<!-- USP Block -->
<div class="w-full mt-4 mt-5">
    <div class="card w-full border-b pt-3.75 pb-3.75 pl-5 border-container last:border-0 last:mb-0 total-reviews bg-blue-mist rounded-xl max-w-[26rem]">
        <div class="text-base grid md:grid-cols-[70%_25%] grid-cols-[65%_30%] gap-px place-content-between max-[375px]:grid-cols-[70%_35%]">
            <ol class="ol-stars-text">
                <li class="pl-1 flex items-start place-items-center my-1 text-blue-prussian">
                    <span class="mt-1"><?= /* @noEscape */ $heroiconsSolid->checkHtml("w-4 h-4 text-[#07C605]", 12, 12); ?></span>
                    <span class="ml-1 max-screen-110:text-[12px]">
                            <?= $escaper->escapeHtml(__("Choose your own discount")) ?>
                        </span>
                </li>
                <li class="pl-1 flex items-start place-items-center my-1 text-blue-prussian">
                    <span class="mt-1"><?= /* @noEscape */ $heroiconsSolid->checkHtml("w-4 h-4 text-[#07C605]", 12, 12); ?></span>
                    <span class="ml-1 max-screen-110:text-[12px]">
                            <?= $escaper->escapeHtml(__($text_free_shipping)) ?>
                        </span>
                </li>
                <li class="pl-1 flex items-start place-items-center my-1 text-blue-prussian">
                    <span class="mt-1"><?= /* @noEscape */ $heroiconsSolid->checkHtml("w-4 h-4 text-[#07C605]", 12, 12); ?></span>
                    <span class="ml-1 max-screen-110:text-[12px]">
                            <?= $escaper->escapeHtml(__("Free returns")) ?>
                        </span>
                </li>
            </ol>
            <div class="relative">
                <img
                    class="-mb-[15px] absolute bottom-0 max-w-[95px]"
                    src="<?= $block->getViewFileUrl('images/sven_home/afbeelding.png'); ?>"
                    alt="sven_home_nl_n-mobile" />
            </div>
        </div>
    </div>
</div>
