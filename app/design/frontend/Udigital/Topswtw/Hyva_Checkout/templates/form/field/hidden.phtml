<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Form\EntityField\AbstractEntityField;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */

/** @var AbstractEntityField $element */
$element = $block->getData('element');

// Enforce the input type attribute
$element->setAttribute('type', 'hidden');
?>
<input
    <?php if ($element->hasAttributes()): ?>
        <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
    <?php endif ?>
>
