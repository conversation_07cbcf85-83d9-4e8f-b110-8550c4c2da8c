<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Form\EntityField\AbstractEntityField;
use Hyva\Checkout\ViewModel\Form\Field as FormFieldViewModel;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Template $block */

/** @var FormFieldViewModel $formFieldViewModel */
$formFieldViewModel = $viewModels->require(FormFieldViewModel::class);

/** @var AbstractEntityField $element */
$element = $block->getData('element');
?>
<?php if ($element->hasTooltip() && $formFieldViewModel->designConfig()->showTooltipAsHintText()): ?>
    <p class="form-hint-text mt-2 text-sm text-slate-500 w-full">
        <?= $escaper->escapeHtml(__($element->getTooltip())) ?>
    </p>
<?php endif ?>
