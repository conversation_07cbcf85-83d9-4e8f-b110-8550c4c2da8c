<?php
/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Form\EntityField\AbstractEntityField;
use Hyva\Checkout\Model\Form\EntityFormElement\Renderer\AbstractRenderer;
use Hyva\Checkout\ViewModel\Form\Field as FormFieldViewModel;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var FormFieldViewModel $formFieldViewModel */
$formFieldViewModel = $viewModels->require(FormFieldViewModel::class);

/** @var AbstractEntityField $element */
$element = $block->getData('element');

// Set input type based on the visibility toggle.
$element->setAttribute('x-bind:type', 'showAsText ? \'text\' : \'password\'');

// Enforce the input type attribute
$element->setAttribute('type', 'password');

/** @var AbstractRenderer $renderer */
$renderer = $element->getRenderer();

// Additional variables
$cssClassesFieldWrapperInner = $element->renderClass(['field'], 'field-wrapper-inner');
$inputClasses = ['form-input w-full grow'];

if ($formFieldViewModel->designConfig()->showPasswordVisibilityToggle()) {
    $inputClasses[] = '!rounded-r-none';
}

?>
<div x-data="{ showAsText: false }"
     class="<?= $escaper->escapeHtmlAttr($cssClassesFieldWrapperInner) ?>"
>
    <?= /* @noEscape */ $renderer->renderLabel($element) ?>
    <?= /* @noEscape */ $renderer->renderBefore($element) ?>

    <?php if ($element->hasRelatives()): ?>
    <div class="space-y-2">
        <?php endif ?>

        <div class="group input-group flex gap-4">
            <?php if ($element->hasIcon()): ?>
                <div class="form-input-addon inline-flex gap-2 items-center">
                    <?= /* @noEscape */ $renderer->renderIcon($element) ?>
                </div>
            <?php endif ?>

            <input
                class="<?= $escaper->escapeHtmlAttr($element->renderClass($inputClasses)) ?>"
                <?php if ($element->hasAttributes()): ?>
                    <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
                <?php endif ?>
            >

            <?php if ($formFieldViewModel->designConfig()->showPasswordVisibilityToggle()): ?>
                <div class="form-input-addon inline-flex gap-2 items-center">
                    <button
                        type="button"
                        class="shrink-0"
                        x-on:click="showAsText = !showAsText"
                        x-bind:aria-pressed="showAsText"
                        x-bind:aria-label="showAsText
                            ? '<?= $escaper->escapeJs(__('Hide Password')) ?>'
                            : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                    >
                        <?= $heroiconsSolid->eyeHtml('', 20, 20, [
                            'aria-hidden' => 'true',
                            'x-show' => '!showAsText'
                        ]) ?>

                        <template x-if="showAsText">
                            <?= /* @noEscape */ $heroiconsSolid->eyeOffHtml('', 20, 20, ['aria-hidden' => 'true']) ?>
                        </template>
                    </button>
                </div>
            <?php endif ?>

            <?php if (!empty($renderer->renderTooltip($element))): ?>
                <div class="form-input-addon inline-flex gap-2 items-center">
                    <?= /* @noEscape */ $renderer->renderTooltip($element) ?>
                </div>
            <?php endif ?>
        </div>
        <?= /* @noEscape */ $renderer->renderHintText($element) ?>
        <?= /* @noEscape */ $renderer->renderComment($element) ?>

        <?php if ($element->hasRelatives()): ?>
            <?php foreach ($element->getRelatives() as $relative): ?>
                <?php if ($relative->isVisible()): ?>
                    <?= /* @noEscape */ $relative->render() ?>
                <?php endif ?>
            <?php endforeach ?>
        <?php endif ?>

        <?php if ($element->hasRelatives()): ?>
    </div>
<?php endif ?>

    <?= /* @noEscape */ $renderer->renderAfter($element) ?>
</div>
