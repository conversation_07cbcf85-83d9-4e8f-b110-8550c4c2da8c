<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Form\EntityField\AbstractEntityField;
use Hyva\Checkout\ViewModel\Form\Field\Street as StreetFormFieldViewModel;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var AbstractEntityField $element */
$element = $block->getData('element');

/** @var AbstractRenderer $renderer */
$renderer = $element->getRenderer();

/** @var StreetFormFieldViewModel $streetFormFieldViewModel */
$streetFormFieldViewModel = $viewModels->require(StreetFormFieldViewModel::class);

// Additional variables
$hasRelatives = $element->hasRelatives();
$cssClassIsOneRow = $streetFormFieldViewModel->showAsOneColumnRow() ? 'grow grow-1' : '';
$cssClassesFieldWrapperInner = $element->renderClass(['field field-reserved', $cssClassIsOneRow], 'field-wrapper-inner');
$cssClassesRelatives = '';

if ($streetFormFieldViewModel->showAsTwoColumnGrid()) {
    $cssClassesRelatives .= 'street-two-column-grid grid grid-cols-1 gap-y-2 gap-x-4 sm:grid-cols-2';
    $cssClassesRelatives .= ' sm:[&_.label-street.sr-only]:not-sr-only sm:[&_.label-street.sr-only]:invisible sm:[&_.label-street.sr-only]:mb-2';
} elseif ($streetFormFieldViewModel->showAsOneColumnRow()) {
    $cssClassesRelatives .= 'street-one-column-row flex flex-col gap-y-2 gap-x-4 md:flex-row';
    $cssClassesRelatives .= ' md:[&_label.sr-only]:not-sr-only md:[&_label.sr-only]:invisible md:[&_label.sr-only]:mb-2';
}
$id = uniqid();
?>
<?php if ($hasRelatives): ?>
<div class="<?= $escaper->escapeHtmlAttr($cssClassesRelatives) ?>">
    <?php endif ?>
    <div class="<?= $escaper->escapeHtmlAttr($cssClassesFieldWrapperInner) ?> !mb-0" id="street-<?= $escaper->escapeHtmlAttr($id)?>">
        <?= /* @noEscape */ $element->getRenderer()->renderLabel($element) ?>
        <?= /* @noEscape */ $element->getRenderer()->renderBefore($element) ?>

        <div class="group input-group flex gap-4">
            <?php if ($element->hasIcon()): ?>
                <div class="form-input-addon inline-flex gap-2 items-center">
                    <?= /* @noEscape */ $renderer->renderIcon($element) ?>
                </div>
            <?php endif ?>

            <input
                @change="onChange"
                data-validation-container="#street-<?= $escaper->escapeHtmlAttr($id)?>"
                class="<?= $escaper->escapeHtmlAttr($element->renderClass(['form-input w-full grow'])) ?>"
                <?php if ($element->hasAttributes()): ?>
                    <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
                <?php endif ?>
            >

            <?php if (!empty($renderer->renderTooltip($element))): ?>
                <div class="form-input-addon inline-flex gap-2 items-center">
                    <?= /* @noEscape */ $renderer->renderTooltip($element) ?>
                </div>
            <?php endif ?>
        </div>

        <?= /* @noEscape */ $renderer->renderHintText($element) ?>
        <?= /* @noEscape */ $renderer->renderComment($element) ?>
    </div>

    <?php foreach ($element->getRelatives() as $relative): ?>
        <?php if ($relative->isVisible()): ?>
            <?= /* @noEscape */ $relative->render() ?>
        <?php endif ?>
    <?php endforeach ?>

    <?= /* @noEscape */ $renderer->renderAfter($element) ?>

    <?php if ($hasRelatives): ?>
</div>
<?php endif ?>
