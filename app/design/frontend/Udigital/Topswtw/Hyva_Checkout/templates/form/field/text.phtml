<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Form\EntityField\AbstractEntityField;
use Hyva\Checkout\Model\Form\EntityFormElement\Renderer\AbstractRenderer;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Checkout\ViewModel\Form\Field as FormFieldViewModel;

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Template $block */

/** @var AbstractEntityField $element */
$element = $block->getData('element');

/** @var AbstractRenderer $renderer */
$renderer = $element->getRenderer();

/** @var FormFieldViewModel $formFieldViewModel */
$formFieldViewModel = $viewModels->require(FormFieldViewModel::class);

// Additional variables
$cssClassesFieldWrapperInner = $element->renderClass(['field'], 'field-wrapper-inner')

/** @Tailwind md:w-1/4 md:w-2/4 md:w-3/4 md:w-4/4 mb-2 */
?>
<div class="<?= $escaper->escapeHtmlAttr($cssClassesFieldWrapperInner) ?>">
    <?= /* @noEscape */ $renderer->renderLabel($element) ?>
    <?= /* @noEscape */ $renderer->renderBefore($element) ?>

    <?php if ($element->hasRelatives()): ?>
        <div class="space-y-2">
    <?php endif ?>

    <div class="group input-group flex gap-4 shadow-none">
        <?php if ($element->hasIcon()): ?>
            <div class="form-input-addon inline-flex gap-2 items-center">
                <?= /* @noEscape */ $renderer->renderIcon($element) ?>
            </div>
        <?php endif ?>

        <input
            @change="onChange"
            <?php if ($element->getName() === 'postcode'): ?>data-validate='{"postcode-validator": {}}'<?php endif; ?>
            <?php if ($element->getName() === 'telephone'): ?>data-validate='{"telephone-validator": {}}'<?php endif; ?>
            class="<?= $escaper->escapeHtmlAttr($element->renderClass(['form-input w-full grow shadow-none'])) ?>"
            <?php if ($element->hasAttributes()): ?>
                <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
            <?php endif ?>
        >

        <?php if (!empty($renderer->renderTooltip($element)) && !$formFieldViewModel->designConfig()->showTooltipAsHintText()): ?>
            <div class="form-input-addon inline-flex gap-2 items-center">
                <?= /* @noEscape */ $renderer->renderTooltip($element) ?>
            </div>
        <?php endif ?>
    </div>
    <?= /* @noEscape */ $renderer->renderHintText($element) ?>
    <?= /* @noEscape */ $renderer->renderComment($element) ?>

    <?php if ($element->hasRelatives()): ?>
        <?php foreach ($element->getRelatives() as $relative): ?>
            <?php if ($relative->isVisible()): ?>
                <?= /* @noEscape */ $relative->render() ?>
            <?php endif ?>
        <?php endforeach ?>
    <?php endif ?>

    <?php if ($element->hasRelatives()): ?>
        </div>
    <?php endif ?>

    <?= /* @noEscape */ $renderer->renderAfter($element) ?>
</div>
