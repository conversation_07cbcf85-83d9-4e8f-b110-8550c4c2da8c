<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 */

declare(strict_types=1);

use Magento\Framework\View\Element\Template;
use Tops\Checkout\Block\Usp;

/** @var Template $block */
?>

<div class="grid grid-cols-1 gap-4 lg:gap-6 items-start lg:grid-cols-[minmax(0px,1fr)_26rem]">
    <div class="area-main space-y-4 lg:space-y-6">
        <?= /* @noEscape */ $block->getChildHtml('column.main') ?>
    </div>

    <div class="area-right flex flex-col space-y-0 lg:space-y-0 lg:sticky lg:top-6">

        <!-- Right Column / Checkout Summary -->
        <div class="w-full mb-5 checkout-summary">
            <?= /* @noEscape */ $block->getChildHtml('column.right') ?>

            <!-- Payment Methods Block -->
            <?= $this->getLayout()
                ->createBlock(Template::class)
                ->setTemplate('Hyva_Checkout::checkout/payment-methods.phtml')
                ->toHtml(); ?>
        </div>

        <!-- USP Block -->
        <?= $this->getLayout()
            ->createBlock(Usp::class)
            ->setTemplate('Hyva_Checkout::checkout/usp.phtml')
            ->toHtml(); ?>
    </div>
</div>
