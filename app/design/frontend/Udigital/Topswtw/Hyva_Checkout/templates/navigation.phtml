<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */

$navHtmlLeft = $block->getChildHtml('hyva.checkout.navigation.left');
$navHtmlRight = $block->getChildHtml('hyva.checkout.navigation.right');
?>

<nav
    class="checkout-nav !mt-0 lg:!mt-4"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Checkout Main')) ?>"
    x-data="Object.assign(initCheckoutNavigation(), initNavigation())"
    x-init="initialize()"
>
    <?php if (! empty($navHtmlLeft)): ?>
        <div class="checkout-nav-secondary">
            <?= /* @noEscape */ $navHtmlLeft ?>
        </div>
    <?php endif ?>

    <div class="checkout-nav-main">
        <?= /* @noEscape */ $navHtmlRight ?>
    </div>
</nav>
