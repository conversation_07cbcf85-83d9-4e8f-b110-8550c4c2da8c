<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Navigation\Checkout;
use Hyva\Checkout\ViewModel\Navigation;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Checkout $checkout */

/** @var Navigation $navigation */
$navigation = $viewModels->require(Navigation::class);
$heroicons  = $viewModels->require(HeroiconsOutline::class);

$navigator = $navigation->getNavigator();
$checkout  = $navigator->getActiveCheckout();

if (!$checkout->hasSteps() || !$checkout->isMultiStepper()) {
    return;
}
?>

<?php $previous = $checkout->getStepBefore($navigator->getActiveStep()) ?>
<?php $next = $checkout->getStepAfter($navigator->getActiveStep()) ?>

<?php if ($navigation->getSystemConfig()->showNavigationBackButton()): ?>
    <?php if ($previous): ?>
        <button
            type="button"
            rel="prev"
            class="group text-gray-spanish-darker hover: !ml-0 justify-center relative hidden lg:block"
            x-spread="buttonPrevious('<?= $escaper->escapeJs($previous->getRoute()) ?>')"
            x-bind="buttonPrevious('<?= $escaper->escapeJs($previous->getRoute()) ?>')"
        >
            <span class="group-disabled:invisible flex gap-1 items-center">
                <?= /* @noEscape */ $heroicons->chevronLeftHtml('w-3 h-3'); ?>
                <?= $escaper->escapeHtml(__('Back to %1', __($previous->getLabel('previous step')))) ?>
            </span>

            <span class="hidden group-disabled:flex absolute inset-0 justify-center items-center p-2">
                <?= /* @noEscape */ $block->getChildHtml('loader') ?>
            </span>
        </button>
    <?php elseif ($navigation->getSystemConfig()->showNavigationBackToCartButton()): ?>
        <a
            href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart')) ?>"
            class="btn btn-outlined-alt-green-islamic"
        ><?= $escaper->escapeHtml(__('Back to Cart')) ?></a>
    <?php endif ?>
<?php endif ?>
