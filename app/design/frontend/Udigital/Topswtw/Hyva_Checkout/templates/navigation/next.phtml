<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Navigation\Checkout;
use Hyva\Checkout\ViewModel\Navigation;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Checkout $checkout */

/** @var Navigation $navigation */
$navigation = $viewModels->require(Navigation::class);

$navigator = $navigation->getNavigator();
$checkout  = $navigator->getActiveCheckout();

if (!$checkout->hasSteps() || !$checkout->isMultiStepper()) {
    return;
}

$next = $checkout->getStepAfter($navigator->getActiveStep());

if (! $next) {
    return;
}
?>
<div
    x-data="{...initCheckoutNavigation(), ...initNavigation()}">
    <button
        type="button"
        rel="next"
        class="group btn btn-green-islamic justify-center py-2.5 w-full"
        x-spread="buttonNext('<?= $escaper->escapeJs($next->getRoute()) ?>')"
        x-bind="buttonNext('<?= $escaper->escapeJs($next->getRoute()) ?>')"
    >
        <span class="group-disabled:invisible">
            <?= $escaper->escapeHtml(__('Proceed to %1', __($next->getLabel('next step')))) ?>
        </span>

        <span class="hidden group-disabled:flex absolute inset-0 justify-center items-center p-2">
            <?= /* @noEscape */ $block->getChildHtml('loader') ?>
        </span>
    </button>
</div>
