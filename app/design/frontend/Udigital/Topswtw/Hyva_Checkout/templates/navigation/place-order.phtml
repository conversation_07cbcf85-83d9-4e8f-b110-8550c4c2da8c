<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Navigation;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Navigation $viewModel */

$viewModel = $viewModels->require(Navigation::class);
$navigator = $viewModel->getNavigator();
$checkout  = $navigator->getActiveCheckout();
$next      = $checkout->getStepAfter($navigator->getActiveStep());

if ($next != null) {
    return;
}
?>
<div
    x-data="{...initCheckoutNavigation(), ...initNavigation()}"
    x-init="initialize()"
    >
    <button type="button"
            class="btn btn-green-islamic justify-center py-2.5 w-full"
            x-spread="buttonPlaceOrder()"
            x-bind="buttonPlaceOrder()"
    >
        <?= $escaper->escapeHtml(__('Place Order')) ?>
    </button>
</div>
