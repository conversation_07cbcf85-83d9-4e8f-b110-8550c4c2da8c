<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\Escaper;

/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsSolid $heroiconsSolid */
/** @var Escaper $escaper */

$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
?>

        <div class="title-countainer hidden md:block">
            <a href="javascript:void(0)" class="back-page-cat text-blue-picton flex flex-col
                items-center md:flex-row flex-wrap py-4 top-cart max-md:!pt-0 max-md:pb-0
                max-md:text-transparent" onclick="history.back()">
                <?=
                $heroiconsSolid->renderHtml(
                    'chevron-left',
                    '-ml[11px] mr-2 fill-current text-blue-picton max-md:hidden',
                    18,
                    18,
                    ['aria-hidden' => 'true']
                );
                ?>
                <p class="max-md:text-transparent"><?= $escaper->escapeHtml(__("Back")) ?></p>
            </a>
        </div>
