<?php

/**
 * Copyright Elgentos. All rights reserved.
 * https://elgentos.nl/
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Interactivated\Customerreview\Block\Customerreview;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */

/** @var HeroiconsSolid $heroiconsSolid */

/** @var ViewModelRegistry $viewModels */

$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
// @codingStandardsIgnoreFile

/** @var Customerreview $block */
if ($block->getCorrectData()) :
    $rating_percentage = $block->getRatingPercentage();
    $maxrating = $block->getMaxrating();
    $url = $block->getMicrodataUrl();
    $rating = $block->getRating();
    $reviews = $block->getReviews();
    $display = 'display:none;';
    if ($block->getShowRating()) {
        $display = 'display:block;';
    }
?>

    <div class="kiyoh-shop-snippets hidden screen-94:block">
        <div class="rating-box">
            <div class="rating" style="width:<?php echo $rating_percentage; ?>%"></div>
        </div>
        <div class="kiyoh-schema" >
            <meta />
            <a href="<?= $escaper->escapeUrl($block->getBaseUrl()) ?>" aria-label="kiyoh reviews topswtw" style="display: none">
                <?= $escaper->escapeUrl($block->getBaseUrl()) ?>
            </a>
            <div class="flex flex-col">
                <a href="<?= $escaper->escapeUrl($url); ?>" target="_blank" class="kiyoh-link" aria-label="kiyoh reviews topswtw">
                    <?php
                    $ratingSteps = 5;
                    $starsFilled = is_numeric(floatval($rating)) ? floor($rating / 10 * $ratingSteps) : 0;
                    $starsEmpty = floor($ratingSteps - $starsFilled);
                    ?>
                    <div class="flex flex-row items-center justify-end">
                        <?php $i = 0; ?>
                        <?php while ($i < $starsFilled) : ?>
                            <?= $heroiconsSolid->starHtml('text-saffron h-4 w-4 md:h-5 md:w-5'); ?>
                            <?php $i++; ?>
                        <?php endwhile; ?>
                        <?php $i = 0; ?>
                        <?php while ($i < $starsEmpty) : ?>
                            <?= $heroiconsSolid->starHtml('text-gray-200 h-4 w-4 md:h-5 md:w-5'); ?>
                            <?php $i++; ?>
                        <?php endwhile; ?>
                    </div>
                    <div class="flex flex-grow justify-between">
                        <p class="text-xs-lh-none md:text-3.25 text-white whitespace-nowrap"><?= ' &nbsp; ' . (!empty($rating) ? $rating : 0) ?>
                            <?= $escaper->escapeHtml(' &nbsp;(' . (!empty($reviews) ? number_format((float)$reviews ,0 ,null, ".") : 0) . __(' reviews)')); ?>
                        </p>
                    </div>
                </a>
            </div>
        </div>
    </div>
<?php endif; ?>
