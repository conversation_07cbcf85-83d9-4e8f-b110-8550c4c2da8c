<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Checkout\ViewModel\Checkout\ShippingSummary;
use Hyva\Checkout\ViewModel\Checkout\AddressRenderer;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var ShippingSummary $viewModel */

$viewModel = $viewModels->require(ShippingSummary::class);
$addressRenderer = $viewModels->require(AddressRenderer::class);

$address = $viewModel->getShippingAddress();
?>
<div class="mb-6">
    <small><?= $escaper->escapeHtml(__('Your address')); ?>: <?= /* @noEscape */ $addressRenderer->renderCustomerAddress($address, 'text') ?></small>
</div>
