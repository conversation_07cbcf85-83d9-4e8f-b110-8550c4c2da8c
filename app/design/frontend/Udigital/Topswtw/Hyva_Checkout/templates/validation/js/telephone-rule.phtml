<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HyvaCsp;

/** @var Escaper $escaper  */
/** @var ViewModelRegistry $viewModels  */
/** @var HyvaCsp $hyvaCsp */

$minimumLength = 6;
$maximumLength = 18;
?>

<script>
hyva?.formValidation.addRule('telephone-validator', async (value, options, field, context) => {
    const min = <?= $escaper->escapeJs($minimumLength)?>;
    const max = <?= $escaper->escapeJs($maximumLength)?>;
    const formattedValue = value.replaceAll(/\s/g, '');
    const regex = /[0-9+()]+/g

    if (formattedValue.length < min) {
        return '<?= $escaper->escapeHtml(__('Your telephone number must be at least %1 characters long', $minimumLength))?>';
    }

    if (formattedValue.length > max) {
        return '<?= $escaper->escapeHtml(__('Your telephone number must be no more than %1 characters long', $maximumLength))?>';
    }

    return regex.test(formattedValue)
        ? true
        : '<?= $escaper->escapeHtml(__('Your telephone number is invalid'))?>';
});
</script>
<?php $hyvaCsp->registerInlineScript(); ?>
