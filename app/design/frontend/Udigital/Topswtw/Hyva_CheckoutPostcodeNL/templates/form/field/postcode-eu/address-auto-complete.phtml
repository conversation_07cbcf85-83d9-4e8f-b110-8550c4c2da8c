<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2023-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Form\EntityFieldInterface;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Checkout\ViewModel\Checkout\AddressRenderer;

/** @var Template $block */
/** @var EntityFieldInterface $postcodeElement */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var HeroiconsOutline $iconsViewModel */
/** @var AddressRenderer $addressRenderer */
/** @var HeroiconsOutline $iconsViewModel */

$element = $block->getData('element');
$form = $element->getForm();

$addressRenderer = $viewModels->require(AddressRenderer::class);
$iconsViewModel = $viewModels->require(HeroiconsOutline::class);

$element->setAttribute('x-ref', 'postcode-eu-autocomplete');
$element->removeAttributesStartingWith('wire');
?>

<div x-data="postcodeEUAddressAutoComplete($wire, { form: { namespace: '<?= $escaper->escapeJs($form->getNamespace()) ?>' } })"
     class="w-full text-sm text-gray-700 <?= $element->isRequired() ? 'required' : 'not-required' ?>"
     wire:key="postcode_eu_auto_complete_<?= $escaper->escapeHtmlAttr($form->getNamespace()) ?>"
     x-init="initialize()"
>
    <?= $element->getRenderer()->renderLabel($element) ?>
    <?= $element->getRenderer()->renderBefore($element) ?>

    <!-- Flex wrapper zodat icon en input inline staan -->
    <div class="flex items-center gap-2 w-full">
        <span class="text-gray-400 flex-shrink-0">
            <?= $iconsViewModel->searchHtml('', 20, 20, ['aria-hidden' => 'true']); ?>
        </span>
        <input
            class="<?= $escaper->escapeHtmlAttr($element->renderClass(['block w-full form-input py-1.5'])) ?>"
            placeholder="<?= $escaper->escapeHtmlAttr(__('Enter a location')) ?>"
            <?php if ($element->hasAttributes()): ?>
                <?= $element->renderAttributes($escaper) ?>
            <?php endif ?>
        />
        <?php if ($element->hasTooltip()): ?>
            <?= $element->getRenderer()->renderTooltip($element) ?>
        <?php endif ?>
    </div>

    <div class="pt-4">
        <p class="text-sm font-medium text-gray-700">
            <?= $escaper->escapeHtml(__('Start typing your address and completion will do the rest')) ?>
        </p>
    </div>

    <?= $element->getRenderer()->renderComment($element) ?>
    <?= $element->getRenderer()->renderAfter($element) ?>
</div>
