<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Form\AbstractEntityForm;
use Hyva\Checkout\Model\Form\EntityFieldInterface;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var EntityFieldInterface $postcodeElement */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var AbstractEntityForm $form */

$element = $block->getData('element');
$form = $element->getForm();

$postcodeField = $form->getField('aac_postcode');
$houseNumberField = $form->getField('aac_house_number');
$manualCompletionField = $form->getField('aac_manual_completion');
$manualPostcodeField = $form->getField('postcode');
$streetField = $form->getField('street');
$form->getField('street');

$manualCompletionValue = $manualCompletionField ? $manualCompletionField->getValue() : false;

$manualPostcodeField->setVisible((bool) $manualCompletionValue);

if ($streetField) {
    $relatives = $streetField->getRelatives();
    // We always want to show the streetname
    if (isset($relatives[0])) {
        $firstRelative = $relatives[0];
        $firstRelative->setVisible(true);
    }

    // And hide the housenumber if we are not in manual completion
    if (isset($relatives[1])) {
        $relatives[1]->setVisible((bool) $manualCompletionValue);
    }
}
?>
<div x-data="postcodeNLAddressAutoComplete($wire)"
     x-init="initialize()"
     class="w-full text-sm text-gray-500 space-y-4"
     wire:key="postcode_nl_auto_complete_<?= $escaper->escapeHtmlAttr($form->getNamespace()) ?>"
>
    <?= $block->getChildHtml('messenger') ?>

    <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
            <?= /* @noEscape */ $postcodeField->render() ?>
        </div>

        <div class="flex-1">
            <?= /* @noEscape */ $houseNumberField->render() ?>
        </div>
    </div>


    <div class="flex items-start md:mb-4">
        <?= /* @noEscape */ $manualCompletionField->render() ?>
    </div>
</div>
