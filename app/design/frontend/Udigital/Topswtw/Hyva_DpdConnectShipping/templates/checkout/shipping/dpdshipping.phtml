<?php

/**
 * Copyright Elgentos. All rights reserved.
 * https://elgentos.nl/
 */

use DpdConnect\ShippingHyva\ViewModel\CheckoutConfig;
use Hyva\Checkout\ViewModel\Checkout\ShippingSummary;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HyvaCsp;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var HyvaCsp $hyvaCsp */
/** @var ShippingSummary $shippingSummary */
/** @var CheckoutConfig $checkoutConfigViewModel */

$shippingSummary = $viewModels->require(ShippingSummary::class);
$checkoutConfigViewModel = $viewModels->require(CheckoutConfig::class);
$checkoutConfig = $checkoutConfigViewModel->getConfig();

$address = $shippingSummary->getShippingAddress();
?>
<script>
    function initDpdShipping(event) {
        return {
            error: null,
            googleKey: '<?= $escaper->escapeJs($checkoutConfig['dpd_parcelshop_google_key']) ?>',
            locale: '<?= $escaper->escapeJs($checkoutConfig['dpd_locale']) ?>' || 'nl',
            saveUrl: '<?= $escaper->escapeJs($checkoutConfig['dpd_parcelshop_save_url']) ?>',
            selectedParcelshop: null,
            selectedParcelshopHtml: null,
            token: '<?= $escaper->escapeJs($checkoutConfig['dpd_parcelshop_token']) ?>',
            useDpdKey: <?= !empty($checkoutConfig['dpd_parcelshop_use_dpd_key']) ? 'true' : 'false' ?>,
            validationError: null,

            init() {
                const script = document.createElement('script');
                script.async = true;
                script.src = 'https://api.dpdconnect.nl/parcelshop/map/js';

                script.onload = () => {
                    this.onScriptLoad();
                };

                document.head.appendChild(script);
            },
            onScriptLoad() {
                document.addEventListener('click', (event) => {
                    if (
                        event.target.classList.contains('btn-next') ||
                        (event.target.parentElement && event.target.parentElement.classList.contains('btn-next'))
                    ) {
                        this.handleButtonClick(event);
                    }
                }, {
                    capture: true
                });

                const parcelshop = sessionStorage.getItem('selectedParcelshop');

                if (parcelshop) {
                    this.selectParcelshop(JSON.parse(parcelshop));
                }
                else {
                    this.showMap();
                }
            },
            flatten(object) {
                const urlSearchParams = new URLSearchParams();

                for (const key in object) {
                    const value = object[key];

                    if (Array.isArray(value)) {
                        value.forEach((nestedValue, index) => {
                            for (const nestedKey in nestedValue) {
                                urlSearchParams.append(key + '[' + index + '][' + nestedKey + ']', nestedValue[nestedKey] ?? '');
                            }
                        });
                    }
                    else {
                        urlSearchParams.append(key, value ?? '');
                    }
                }

                return urlSearchParams;
            },

            handleButtonClick(event) {
                const parcelShopId = document.querySelector('.parcelshopId');

                const selectedValue = document.querySelector('input[name="shipping-method-option"]:checked')?.value;


                if (selectedValue !== 'dpdpickup_dpdpickup') {
                    return;
                }

                if (!(parcelShopId) || !(parcelShopId.value)) {
                    event.stopPropagation();
                    this.validationError = hyva.str('<?= $escaper->escapeJs(__('Please select a parcel shop')) ?>');
                }
            },

            handleWindowClick(event) {
                if (!event.target.classList.contains('dpd_connect_change_parcelshop')) {
                    return;
                }

                event.preventDefault();

                sessionStorage.removeItem('selectedParcelshop');

                this.selectedParcelshop = null;
                this.selectedParcelshopHtml = null;

                this.showMap();
            },

            selectParcelshop(parcelshop) {
                this.validationError = null;
                this.selectedParcelshop = parcelshop;

                sessionStorage.setItem('selectedParcelshop', JSON.stringify(this.selectedParcelshop));

                fetch(this.saveUrl, {
                    body: this.flatten(this.selectedParcelshop),
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    method: 'POST'
                }).then((response) => {
                    response.text().then((text) => {
                        this.selectedParcelshopHtml = text;
                    });
                });
            },

            showMap() {
                this.error = null;
                this.validationError = null;

                const shippingAddress = JSON.parse(this.$el.dataset.shippingAddress);

                if (!shippingAddress.postcode || !shippingAddress.country_id) {
                    this.error = hyva.str('No address entered. Please enter your address and try again.');

                    return;
                }

                /** @var DPDConnect */
                DPDConnect.onParcelshopSelected = this.selectParcelshop.bind(this);

                const searchAddress = shippingAddress.street[0] + ' ' +
                    shippingAddress.postcode + ' ' +
                    shippingAddress.city + ' ' +
                    shippingAddress.country_id;

                if (this.useDpdKey) {

                    /** @var DPDConnect */
                    DPDConnect.show(this.token, searchAddress, this.locale);
                }
                else {

                    /** @var DPDConnect */
                    DPDConnect.show(this.token, searchAddress, this.locale, this.googleKey);
                }
            },
            showConnectMap() {
                return !this.selectedParcelshopHtml && this.hasError();
            },
            hasError() {
                return this.error !== null;
            },
            hasParcelSelected() {
                return this.selectedParcelshop !== null;
            },
            getParcelShopHtml() {
                return this.selectedParcelshopHtml;
            }
        };
    }
</script>
<?php $hyvaCsp->registerInlineScript() ?>
