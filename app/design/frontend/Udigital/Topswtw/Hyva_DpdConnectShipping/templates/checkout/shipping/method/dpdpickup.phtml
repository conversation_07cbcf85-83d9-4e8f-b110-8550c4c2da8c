<?php

/**
 * Copyright Elgentos. All rights reserved.
 * https://elgentos.nl/
 */

use DpdConnect\ShippingHyva\ViewModel\CheckoutConfig;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Hyva\Checkout\ViewModel\Checkout\ShippingSummary;

/** @var ViewModelRegistry $viewModels */
/** @var ShippingSummary $shippingSummary */
/** @var CheckoutConfig $checkoutConfigViewModel */

/** @var Escaper $escaper */
$checkoutConfigViewModel = $viewModels->require(CheckoutConfig::class);
$shippingSummary = $viewModels->require(ShippingSummary::class);

$checkoutConfig = $checkoutConfigViewModel->getConfig();
$mapWidth  = (int) $checkoutConfig['dpd_googlemaps_width'];
$mapHeight = (int) $checkoutConfig['dpd_googlemaps_height'];

$address = $shippingSummary->getShippingAddress();
$addressData = [
    "postcode" => $address->getPostcode(),
    "city" => $address->getCity(),
    "country_id" => $address->getCountryId(),
    "street" => is_array($address->getStreet()) ? $address->getStreet() : []
];
?>

<div x-data="initDpdShipping"
     data-shipping-address='<?= $escaper->escapeHtmlAttr(json_encode($addressData)) ?>'
     x-on:click.window="handleWindowClick">

    <div id="dpd-connect-map-container"
         class="block"
         style="height: <?= $escaper->escapeHtmlAttr($mapHeight) ?>px;"
         x-cloak
         x-show="showConnectMap">
    </div>
    <div x-html="error" x-show="hasError"></div>
    <div id="dpd-connect-selected-container"
         x-cloak
         x-html="getParcelShopHtml"
         x-show="hasParcelSelected">
    </div>
    <form>
        <div class="field field-error field-reserved">
            <ul class="messages">
                <li x-html="validationError" x-show="validationError"></li>
            </ul>
        </div>
    </form>
</div>
