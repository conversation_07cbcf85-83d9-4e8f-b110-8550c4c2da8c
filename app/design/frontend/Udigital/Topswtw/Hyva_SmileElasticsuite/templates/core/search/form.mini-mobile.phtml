<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\Escaper;
use Magento\Search\Helper\Data as SearchHelper;

/**
 * Template for quick search mini form.
 * Overridden to manage template injection for the rendering of autocomplete results.
 *
 * @var \Smile\ElasticsuiteCore\Block\Search\Form\Autocomplete $block
 * @var SearchHelper $helper
 * @var Escaper $escaper
 * @var \Hyva\Theme\Model\ViewModelRegistry $viewModels
 * @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroicons
 */

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$helper        = $this->helper(SearchHelper::class);
$suggestionUrl = $helper->getResultUrl() . '?' . $helper->getQueryParamName() . '=';
$heroicons     = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
$templates     = json_decode($block->getJsonSuggestRenderers(), true);

?>

<script>
    function initMiniSearchComponentMobile() {
        "use strict";

        return {
            show:false,
            formSelector: "#search_mini_form_mobile",
            url: "<?= /* @escapeNotVerified */ $block->getUrl('search/ajax/suggest') ?>",
            destinationSelector: "#search_autocomplete_mobile",
            templates: <?= /* @noEscape */ $block->getJsonSuggestRenderers() ?>,
            priceFormat: <?= /* @noEscape */ $block->getJsonPriceFormat() ?>,
            minSearchLength: <?= /* @escapeNotVerified */ $helper->getMinQueryLength() ?>,
            searchResultsByType: {},
            currentRequest: null,

            /**
             * Get search results.
             */
            getSearchResults: function () {
                let value = document.querySelector('#search_mobile').value.trim();

                if (value.length < parseInt(this.minSearchLength, 10)) {
                    this.searchResultsByType = [];

                    return false;
                }

                let url = this.url + '?' + new URLSearchParams({
                    q: document.querySelector('#search_mobile').value,
                    _: Date.now()
                }).toString();

                if (this.currentRequest !== null) {
                    this.currentRequest.abort();
                }
                this.currentRequest = new AbortController();

                fetch(url, {
                    method: 'GET',
                    signal: this.currentRequest.signal,
                }).then((response) => {
                    if (response.ok) {
                        return response.json();
                    }
                }).then((data)  => {
                    this.show = data.length > 0;

                    this.searchResultsByType = data.reduce((acc, result) => {
                        if (! acc[result.type]) acc[result.type] = [];
                        acc[result.type].push(result);
                        return acc;
                    }, {});
                }).catch((error) => {
                    ;
                });
            },
        }
    }
</script>
<div id="elasticsuite-search-container" class="order-3 mx-auto text-blue-prussian search-header-top w-50%" x-show="true">
    <div x-data="initMiniSearchComponentMobile()" @click.away="show = false">
        <form class="form minisearch search-header-form" id="search_mini_form_mobile" action="<?= $escaper->escapeUrl($helper->getResultUrl()) ?>" method="get" role="search">
            <div class="flex relative">
                <label class="sr-only" for="search">
                    <?= $escaper->escapeHtmlAttr(__('What are you looking for?')) ?>
                </label>
                <input id="search_mobile"
                       x-on:input.debounce="getSearchResults()"
                       x-ref="searchInput"
                       type="search"
                       class="w-full input-search-header p-2 leading-normal transition appearance-none text-grey-800
                    focus:outline-none focus:border-transparent"
                       autocapitalize="off" autocomplete="off" autocorrect="off"
                       name="<?= $escaper->escapeHtmlAttr($helper->getQueryParamName()) ?>"
                       value="<?= $escaper->escapeHtmlAttr($helper->getEscapedQueryText()) ?>"
                       placeholder="<?= $escaper->escapeHtmlAttr(__('What are you looking for?')) ?>"
                       maxlength="<?= $escaper->escapeHtmlAttr($helper->getMaxQueryLength()) ?>"
                       @search-open.window.debounce.10="
                            $el.focus();
                            $el.select();
                       "
                />
                <button type="submit"
                        title="<?= $escaper->escapeHtml(__('Search')) ?>"
                        class="action search bg-transparent"
                        aria-label="Search"
                >
                    <?= /* @noEscape */  $heroiconsSolid->renderHtml(
                        'search-alt',
                        'fill-current text-blue-picton',
                        18,
                        18,
                        ['aria-hidden' => 'true']
                    );
                    ?>
                </button>
            </div>
            <div id="search_autocomplete_mobile" class="search-autocomplete relative w-full" x-show="show" style="display:none;">
                <div class="absolute bg-white border border-solid border-black z-50 w-full">
                    <template x-for="searchResultByType in Object.values(searchResultsByType)">
                        <div class="">
                            <template x-if="searchResultByType.hasOwnProperty(0) && templates[searchResultByType[0].type].title && templates[searchResultByType[0].type].titleRenderer === undefined">
                                <div class="font-bold pt-2 pl-2 text-center" x-text="templates[searchResultByType[0].type].title"></div>
                            </template>
                            <template x-if="searchResultByType.hasOwnProperty(0) && templates[searchResultByType[0].type].titleRenderer !== undefined">
                                <div class="font-bold pt-2 pl-2 text-center" x-text="window[templates[searchResultByType[0].type].titleRenderer](searchResultByType)"></div>
                            </template>

                            <template x-for="searchResult in searchResultByType">
                                <div class="hover:bg-gray-100">
                                    <?php foreach(json_decode($block->getJsonSuggestRenderers(), true) as $renderer): ?>
                                        <?= $block->getLayout()
                                            ->createBlock('Magento\Framework\View\Element\Template')
                                            ->setTemplate($renderer['template'])
                                            ->setData('suggestion_url', $suggestionUrl)
                                            ->toHtml()
                                        ?>
                                    <?php endforeach; ?>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>
            <?= $block->getChildHtml() ?>
        </form>
    </div>
</div>
