<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="back-botton" remove="true" />
        <referenceContainer name="page.wrapper" htmlClass="page-wrapper"/>
        <referenceBlock name="page.main.title" display="false"/>
        <referenceContainer name="main" htmlClass="column main main-category" />
        <!-- <referenceContainer name="content"> -->
            <!-- <referenceBlock name="sidebar.main" remove="true"/>
            <referenceBlock name="sidebar.additional" remove="true"/>
            <referenceBlock name="catalog.leftnav" remove="true"/> -->
        <!-- </referenceContainer > -->
        <referenceContainer name="columns.top">
            <referenceContainer name="category.view.container">
                <referenceBlock name="category.image" remove="true"/>
                <block class="Udigital\CategoryAttribute\Block\Category\View" name="categories.top" template="Magento_Catalog::category/top.phtml" before="category.description">
                    <arguments>
                        <argument name="image" xsi:type="object">Magento\Catalog\ViewModel\Category\Image</argument>
                        <argument name="output" xsi:type="object">Magento\Catalog\ViewModel\Category\Output</argument>
                        <argument name="theme_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Theme</argument>
                    </arguments>
                </block>
            </referenceContainer>
        </referenceContainer>
        <move element="category.description" destination="category.view.container" after="categories.top"/>
    </body>
</page>
