<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="product_list_item">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
            </arguments>
            <referenceBlock name="product.item.stockstatus">
                <arguments>
                    <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                </arguments>
            </referenceBlock>
        </referenceBlock>
    </body>
</page>
