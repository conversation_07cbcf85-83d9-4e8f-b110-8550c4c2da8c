<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page layout="1column"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <block class="Magento\Theme\Block\Html\Title" name="page.main.title" template="Magento_Catalog::product/title.phtml" />
        <attribute name="itemtype" value="" />
        <attribute name="itemscope" value="" />
        <referenceBlock name="back-botton" remove="true" />
        <referenceContainer name="page.wrapper" htmlClass="page-wrapper">
            <block class="Magento\Catalog\Block\Product\View"
                   name="product.info.data"
                   template="Magento_Catalog::product/view/structured-data.phtml"/>
        </referenceContainer>
        <referenceBlock name="related.post.tab" remove="true"/>

        <referenceBlock name="product.info.review">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
            </arguments>
        </referenceBlock>

        <move element="product.info.review" destination="product.detail.page"/>

        <referenceContainer name="product.info">
            <block class="Magento\Framework\View\Element\Template" name="product.info.usps" template="Magento_Catalog::product/view/usps.phtml" />

            <!-- <block class="Magento\Catalog\Block\Product\View\Attributes" name="product.attributes.merkfilterclasses" template="Magento_Catalog::product/view/merk_filterclasses.phtml" >
                <arguments>
                    <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                    <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
                </arguments>
            </block> -->
            <block class="Magento\Catalog\Block\Product\View\Attributes" name="product.attributes.tops" template="Magento_Catalog::product/view/attributes.phtml" before="-">
                <arguments>
                    <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                    <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
                    <argument translate="true" name="title" xsi:type="string">Specificaties</argument>
                    <argument name="sort_order" xsi:type="string">20</argument>
                </arguments>
            </block>
            <!-- <block class="Magento\Catalog\Block\Product\View\Attributes" name="product.info.faq.tops" template="Magento_Catalog::product/view/sections/faq-tab.phtml" group="detailed_info">
                <arguments>
                    <argument translate="true" name="title" xsi:type="string">FAQ</argument>
                    <argument name="sort_order" xsi:type="string">20</argument>
                </arguments>
            </block> -->
        </referenceContainer>

         <referenceBlock name="product.info.details">
            <block class="Magento\Catalog\Block\Product\View\Attributes" name="product.info.faq.tops" template="Magento_Catalog::product/view/sections/faq-tab.phtml" group="detailed_info">
                <arguments>
                    <argument name="sort_order" xsi:type="string">20</argument>
                    <argument name="cat" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
                </arguments>
            </block>
        </referenceBlock>

        <referenceBlock name="product.attributes">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
                <argument name="title" xsi:type="string" translate="true">More Information</argument>
                <argument name="sort_order" xsi:type="number">-10</argument>
                <argument name="attributes" xsi:type="array"></argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="product.info.stockstatus">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="product.info.price">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="product.info.sku">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="product.info.overview">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="product.info.description">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
