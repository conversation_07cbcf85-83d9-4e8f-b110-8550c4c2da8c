<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Modal;
use Magento\Framework\DB\Select;
use Magento\Framework\Escaper;
use Udigital\CategoryAttribute\Block\Category\View;

/** @var Escaper $escaper */
/** @var View $block */
/** @var ViewModelRegistry $viewModels */

$modalViewModel = $viewModels->require(Modal::class);
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
$heroicons = $viewModels->require(HeroiconsOutline::class);

$imgDefault            = $block->getDefaultImage();
$currentCategory       = $block->getCurrentCategory();
$allParentIds          = $currentCategory->getParentIds();
$keyIdTopsCatPage      = false;
foreach ($allParentIds as $parentId) {
    //$parent = $block->getCategoryById($parentId);
    //$isHelpPopupCategory = $parent->getIsHelpPopupCategory();
    if (!empty($isHelpPopupCategory)) {
        $keyIdTopsCatPage = true;
    }
}
$limit = 6;
$childCategoryData     = $block->getChildCategories($limit, ["name" => Select::SQL_ASC]);
$childCategories       = $childCategoryData['childOnlySize'];
$childCategoriesAll    = $childCategoryData['childOnlySort'];

$imgCategoryParent = $block->getImage()->getUrl($currentCategory);
$imgUrlParent      = empty($imgCategoryParent) ? $imgDefault : $imgCategoryParent;
$levelCurent       = $currentCategory->getLevel() - 1;
$currentName       = !empty($currentCategory->getDescription()) ?
    strip_tags($currentCategory->getDescription()) :
    $currentCategory->getName();

$typeMenu = $block->getThemeAction()->checkCategorizeCategoryMenu($currentCategory->getId(), true, 3);
$language = $block->getThemeAction()->getLanguage();
$exceptLang = ["nl", "en", "be"];
$isLower = in_array($language, $exceptLang) ? true : false;
?>
<div class="w-full mb-9.5" x-data="Object.assign({}, hyva.modal())">
    <div class="wrapper-category-second-level">
        <div class="flex items-center justify-start">
            <img width="100" class="category-image-view-top hidden md:block"
            src="<?= $imgUrlParent ?>" alt="<?= $escaper->escapeHtmlAttr($currentName) ?>">
                <h1 class="title-category-view-top"><?= $escaper->escapeHtmlAttr($currentName) ?></h1>
        </div>
        <?php if ($keyIdTopsCatPage !== false) { ?>
            <?php $modalTop = $modalViewModel->createModal()->withContent('
            <div class="relative w-full h-full md:h-auto text-blue-prussian text-justify">
                <!-- Modal header -->
                <div class="flex items-start justify-end md:pb-4 lg:pl-24 mb-2">
                    <button class="ml-6 lg:ml-24 -mt-4" @click="hide">
                        <span aria-hidden="true" class="text-blue-picton">
                            '. $heroiconsSolid->renderHtml('x', 'text-blue-picton stroke-current -mr-1', 20, 20) .'
                        </span><span class="sr-only">Close</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="pr-6 lg:px-25 body-content-scroll mobile-clear-scroll">
                    '. $block->getLayout()
                        ->createBlock('Magento\Cms\Block\Block')
                        ->setBlockId('overlay-keuzehulp')
                        ->toHtml() .'
                </div>
            </div>
            <script>
            </script>')->withContainerClasses(
                            'fixed',
                            'flex',
                            'justify-start',
                            'md:justify-end',
                            'items-end',
                            'md:items-center'
                        )
            ->withBlockName('Top Category')
            ->withDialogRefName('top-category')
            ->withAriaLabelledby('the-top-category')
            ->addDialogClass('modal-view-category'); ?>
        <button @click="<?= $modalTop->getShowJs() ?>" class="help-category btn btn-saffron">
            <span class="title md:pl-4 text-3.25 md:text-sm md:font-bold">
                <?= $escaper->escapeHtml(__("Decision aid"))  ?>
            </span>
            <span class="hidden md:block float-right">
                <?=
                    $heroicons->renderHtml(
                        'arrow-narrow-right',
                        'fill-current text-blue-prussian',
                        16,
                        16,
                        ['aria-hidden' => 'true']
                    );
                ?>
            </span>
        </button>
            <?= $modalTop // @codingStandardsIgnoreLine ?>
        <?php }?>
    </div>
</div>
<?php if ($levelCurent == 1): ?>
    <?php if (count($childCategories) > 0): ?>
        <?php
                /* all brand
                * for another store please check on translation
                *default
                */
        if ($typeMenu == "accessories") {
            $title_cat = $currentName;
        } else {
            if ($typeMenu == "diameters" && count($childCategoriesAll) <= $limit) {
                $title_cat = __('All diameters');
            } else {
                $title_cat = sprintf(
                    __("Most common %s $typeMenu"),
                    ($isLower ? strtolower($currentName) : $currentName)
                );
            }
        }
        ?>
        <div class="wrapper-categories-page w-full">
            <p class="title-category-view mb-1 md:mb-4">
                <?= $escaper->escapeHtml(__($title_cat)) ?>
            </p>
            <div class="max-md:flex max-md:pb-1.25 max-md:overflow-x-auto max-md:-mr-6 md:grid xl:grid-cols-6
            lg:grid-cols-4 md:grid-cols-3 md:gap-4">
                <?php
                $inc = 0;
                $count = count($childCategories);
                foreach ($childCategories as $child):
                    $inc++;
                    $imgCategory = $block->getImage()->getUrl($child);

                    $_imgUrl  = empty($imgCategory) ? $imgDefault : $imgCategory;
                    $_imgHtml = '<img src="'
                        . $escaper->escapeUrl($_imgUrl)
                        . '" alt="'
                        . $escaper->escapeHtmlAttr($child->getName())
                        . '" title="'
                        . $escaper->escapeHtmlAttr($child->getName())
                        . '" class="p-1 md:p-2 max-md:max-w-14.25" />';
                    $_imgHtml = $block->getOutput()->categoryAttribute($child, $_imgHtml, 'image');

                    ?>
                    <a href="<?= $escaper->escapeUrl($child->getUrl()) ?>"
                        class="block md:col-span-1 py-2 md:py-0 <?= $inc == 1 ?
                        'pr-2 md:pr-0' : ($inc == $count ?
                        'pl-2 mr-6.25 md:pl-0 md:mr-0' :
                        'px-2 md:px-0') ?>">
                        <div class="md:min-h-18.75 hover:bg-gray-50 md:grid md:border-gray-bright md:rounded-lg
                        md:border-solid md:bg-white md:bg-no-repeat md:bg-clip-padding border
                        border-white-azureish rounded-xl max-md:h-full max-md:min-w-35">
                            <div class="flex h-full max-md:flex-col max-md:justify-between">
                                <div class="md:w-20 md:min-w-20 flex items-center max-md:justify-center
                                max-md:h-full">
                                    <?= $_imgHtml ?>
                                </div>
                                <div class="max-md:flex max-md:items-end max-md:justify-center
                                max-md:pb-2 max-md:pt-1 px-2 md:my-auto md:hyphens-auto">
                                    <div class="text-3.25 text-blue-picton
                                    font-normal md:text-blue-prussian md:text-4.25 lg:text-base
                                    md:no-underline md:block max-lg:hyphens-auto break-words">
                                        <?= $child->getName() ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>

            <?php if (count($childCategoriesAll) > $limit): ?>
                <?php
                /* all brand
                * for another store please check on translation
                *default
                */
                $title_cat2 = sprintf(
                    __("All %s brands - from A to Z"),
                    ($isLower ? strtolower($currentName) : $currentName)
                );

                //cat kegelfilter / Cone filters:
                if ($typeMenu == "diameters") {
                    $title_cat2 = __('All diameters');
                } elseif ($typeMenu == "brands") {
                    $title_cat2 = sprintf(
                        __("All %s brands - from A to Z"),
                        ($isLower ? strtolower($currentName) : $currentName)
                    );
                } elseif ($typeMenu == "accessories") {
                    $title_cat2 = __("All"). " ".($isLower ? strtolower($currentName) : $currentName);
                } else {
                    $title_cat2 = sprintf(
                        __("All %s $typeMenu from A to Z"),
                        ($isLower ? strtolower($currentName) : $currentName)
                    );
                }
                ?>
            <p class="title-category-view mb-2 md:mb-4 mt-10">
                <?= $escaper->escapeHtml(__($title_cat2)) ?>
            </p>
            <div class="grid grid-cols-2 xl:grid-cols-6 lg:grid-cols-4 md:grid-cols-3 gap-4">
                <?php foreach ($childCategoriesAll as $child):
                    $imgDefault  = $block->getDefaultImage();
                    $imgCategory = $block->getImage()->getUrl($child);

                    $_imgUrl  = empty($imgCategory) ? $imgDefault : $imgCategory;
                    $_imgHtml = '<img class="p-2" width="72" src="'
                        . $escaper->escapeUrl($_imgUrl)
                        . '" alt="'
                        . $escaper->escapeHtmlAttr($child->getName())
                        . '" title="'
                        . $escaper->escapeHtmlAttr($child->getName())
                        . '" class="category-image-view" />';
                    $_imgHtml = $block->getOutput()->categoryAttribute($child, $_imgHtml, 'image');
                    ?>

                    <a href="<?= $escaper->escapeUrl($child->getUrl()) ?>" class="hidden md:block">
                        <div class="min-h-18.75 grid border border-gray-bright rounded-lg
                        border-solid hover:bg-gray-50 bg-white bg-no-repeat bg-clip-padding">
                            <div class="flex h-full">
                                <div class="w-20 min-w-20 flex items-center">
                                    <?= $_imgHtml ?>
                                </div>
                                <div class="my-auto px-2 hyphens-auto">
                                    <span class="title-category-image">
                                        <?= $child->getName() ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
                <?php
                $increment    = 0;
                $alphapets    = [];
                $isFirstBreak = false;
                $isLastBreak  = $isFirstBreak;
                $childSize    = count($childCategoriesAll);
                $divideAlp    = round($childSize / 2);
                foreach ($childCategoriesAll as $child):
                    $increment++;
                    $name       = $child->getName();
                    $subName    = substr($name, 0, 1);
                    $subName    = strtoupper($subName);
                    $isExistAlp = in_array($subName, $alphapets);
                    ?>
                    <?php if ($increment == 1 ||
                        $isFirstBreak):
                        $isFirstBreak = false; ?>
                        <div class="block md:hidden">
                    <?php endif; ?>
                        <?php if (!$isExistAlp): $alphapets[] = $subName; ?>
                            <div class="text-3.75 font-bold text-blue-prussian mt-3">
                                <?= $escaper->escapeHtml($subName) ?>
                            </div>
                        <?php endif; ?>
                        <a href="<?= $escaper->escapeUrl($child->getUrl()) ?>"
                            class="text-3.25 block text-blue-jeans my-1 hover:underline">
                            <?= $escaper->escapeHtml($name) ?>
                        </a>
                    <?php if ((!$isLastBreak &&
                        $increment >= $divideAlp &&
                        !$isExistAlp) ||
                        ($increment == $childSize)):
                        $isFirstBreak = true;
                        $isLastBreak = true; ?>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
<?php endif; ?>
