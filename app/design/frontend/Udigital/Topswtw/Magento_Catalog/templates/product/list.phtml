<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentCategory;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Block\Product\ListProduct;
use Magento\Catalog\Block\Product\ReviewRendererInterface;
use Magento\Framework\Escaper;
use Udigital\FilterModal\ViewModel\FilterViewModel;

/** @var ListProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var ProductPage $productViewModel */
/** @var CurrentCategory $currentCategoryViewModel */

$productViewModel         = $viewModels->require(ProductPage::class);
$productListItemViewModel = $viewModels->require(ProductListItem::class);
$currentCategoryViewModel = $viewModels->require(CurrentCategory::class);
$filterViewModel          = $viewModels->require(FilterViewModel::class);

$eagerLoadImagesCount = (int) ($block->getData('eager_load_images_count') ?? 3);
$productCollection = $block->getLoadedProductCollection();
?>
<?php if (!$productCollection->count()): ?>
    <div class="message info empty">
        <div>
            <?= $escaper->escapeHtml(__('We can\'t find products matching the selection.')) ?>
        </div>
    </div>
<?php else: ?>
    <section class="pt-6 custom-filter-sidebar">
        <?php
        if ($block->getMode() == 'grid') {
            $viewMode         = 'grid';
            $imageDisplayArea = 'category_page_grid';
            $showDescription  = !empty($block->getUrlAction()) ? $block->getUrlAction()->isShowDescriptionProduct() : false;
            $templateType     = ReviewRendererInterface::SHORT_VIEW;
        } else {
            $viewMode         = 'list';
            $imageDisplayArea = 'category_page_list';
            $showDescription  = !empty($block->getUrlAction()) ? $block->getUrlAction()->isShowDescriptionProduct() : false;
            $templateType     = ReviewRendererInterface::FULL_VIEW;
        }
        /**
         * Position for actions regarding image size changing in vde if needed
         */
        $pos = $block->getPositioned();
        ?>
        <div class="products wrapper mode-<?= /* @noEscape */$viewMode ?> products-<?= /* @noEscape */$viewMode ?>">
            <div class="mx-auto pt-4 pb-12 md:grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                <?php
                /** @var \Magento\Catalog\Model\Product $product */
                foreach (array_values($productCollection->getItems()) as $i => $product) {
                    if ($i < $eagerLoadImagesCount) {
                        $product->setData('image_custom_attributes', ['loading' => 'eager', 'fetchpriority' => 'high']);
                    }
                    echo $productListItemViewModel->getItemHtml(
                        $product,
                        $block,
                        $viewMode,
                        $templateType,
                        $imageDisplayArea,
                        $showDescription
                    );
                } ?>
            </div>
        </div>
        <?= $block->getChildBlock('toolbar')->setCollection($productCollection)->setIsBottom(true)->toHtml() ?>
    </section>
<?php endif; ?>
