<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
declare(strict_types=1);
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Catalog\Block\Product\AbstractProduct;
use Magento\Catalog\ViewModel\Product\OptionsData as ProductOptionsData;
use Magento\Framework\Escaper;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Catalog\Pricing\Price\TierPrice;
use Magento\Catalog\Model\Product;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
// phpcs:disable Generic.Files.LineLength.TooLong
/** @var AbstractProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
$productOptionsViewmodel = $viewModels->require(ProductOptionsData::class);

/** @var Product $product */
$product = $block->getData('product');
$imageDisplayArea = $block->getData('image_display_area');
$templateType = $block->getData('template_type');
$viewMode = $block->getData('view_mode');
$showDescription = $block->getData('show_description');
$showAddToWishlist = false;
$showAddToCompare = false;
$viewIsSuggested = $viewMode === 'suggested';

$tagProduct     = $block->getCategoryAction()->getTagProductByCategory($product); // lama
$categoriesName = $tagProduct["array"];
$categoryName   = $tagProduct["result"];

if (!$product) {
    return '';
}

$storeId = $product->getStoreId();
$nlEnStoreId = 2;
$beEnStoreId = 6;

$option_attr       = $product->getCustomAttribute("website_recommends");
$recommendedStatus = false;
if ($option_attr != null) {
    if (((bool)$option_attr->getValue())):
        $recommendedStatus = true;
    endif;
} else {
    $option_attr = $block->getProductAction()->getAttributeProduct($product, "website_recommends");
    if ($option_attr != null && ((bool)$option_attr->getValue())):
        $recommendedStatus = true;
    endif;
}
$option_attr_per_set = $block->getProductAction()->getAttributeProduct($product, "per_set");
$perSetStatus = ($option_attr_per_set != null && $option_attr_per_set == 1) ? 'set' : "product";
$productId = $product->getId();
$options   = $productOptionsViewmodel->getOptionsData($product);
$hideDetails       = $block->getData('hide_details') ?: false;
$hideRatingSummary = $block->getData('hide_rating_summary') ?: false;
$imageCustomAttributes = $product->getData('image_custom_attributes')
    ?? $block->getData('image_custom_attributes')
    ?? [];
$regularPrice = $productPriceViewModel->getPriceValue(RegularPrice::PRICE_CODE, $product);
$finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);
$tierPrices = $productPriceViewModel->getTierPrices(TierPrice::PRICE_CODE, $product);
if ($productPriceViewModel->displayPriceInclAndExclTax()) {
    $regularPriceExclTax = $productPriceViewModel->getPriceValueExclTax(RegularPrice::PRICE_CODE, $product);
    $finalPriceExclTax = $productPriceViewModel->getPriceValueExclTax(FinalPrice::PRICE_CODE, $product);
}
$displayTax = $productPriceViewModel->displayPriceIncludingTax();

$stock_status = $product->getInventoryStatus();
?>
<?php if ($product->getInventoryStatus() != 126): ?>
<form method="post"
    action="<?= $escaper->escapeUrl($productViewModel->getAddToCartUrl($product, ['useUencPlaceholder' => true])) ?>"
    class="item product product-item <?= $viewIsSuggested ? 'flex-col flex card-product' : 'item-product-list card-product-list' ?> product_addtocart_form ccc relative"
    x-data="initPriceBox_<?= (int)$product->getId() ?>()"
    x-bind="eventListeners_<?= (int)$product->getId() ?>"
    x-init="$watch('qty', (value) => qty = value <= 0 ? 1 : value)"
    <?php if ($product->getOptions()): ?>
    enctype="multipart/form-data"
    <?php endif; ?>
>
    <?php if (preg_match(strtolower('/tops filters?/i'), strtolower($product->getData('merk_filter') ?? ''))): ?>
        <div class="absolute top-2 left-2 bg-blue-prussian text-white px-1 sm:px-3 py-1 text-xs sm:text-base rounded">
            <?php if(in_array($storeId, [$beEnStoreId, $nlEnStoreId])) : ?>
                <?= $escaper->escapeHtml(__('Tops filters brand')) ?>
            <?php else: ?>
                <?= $escaper->escapeHtml(__('Tops Filter brand')) ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    <?= /** @noEscape */ $block->getBlockHtml('formkey') ?>
    <input type="hidden" name="product" value="<?= (int)$productId ?>"/>
    <?php foreach ($options as $optionItem): ?>
    <input type="hidden"
        name="<?= $escaper->escapeHtml($optionItem['name']) ?>"
        value="<?= $escaper->escapeHtml($optionItem['value']) ?>">
    <?php endforeach; ?>
<?php else: ?>
<div class="item product product-item <?= $viewIsSuggested ? 'card-product' : 'item-product-list card-product-list' ?>"
    x-data="initPriceBox_<?= (int)$product->getId() ?>()"
    x-bind="eventListeners_<?= (int)$product->getId() ?>"
    x-init="$watch('qty', (value) => qty = value <= 0 ? 1 : value)">
<?php endif; ?>
    <?php if ($recommendedStatus): ?>
        <div class="label-product-image<?= $viewIsSuggested ? '-list' : '' ?> <?= !$recommendedStatus ? 'is-not-active-label-product-image-list' : '' ?> ">
            <span class="title"><?= $escaper->escapeHtml(__("tops")) ?></span>&nbsp;<span><?= $escaper->escapeHtml(__("choice")) ?></span>
        </div>
    <?php endif; ?>
    <?php /* Product Image */ ?>
    <div class="<?= $viewIsSuggested ? 'item-product-wrapper-sugest' : 'item-product-wrapper' ?>">
        <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
            class="product photo <?= $viewIsSuggested ? 'block mx-auto' : 'product-item-photo md:mx-auto' ?> md:mb-3"
            tabindex="-1">
            <div class="content-image-shopping-cart<?= $viewIsSuggested ? '-sugest' : '' ?>">
                <?php if (!$viewIsSuggested): ?>
                    <?php if ($recommendedStatus): ?>
                        <div class="label-product-image-mobile  <?= !$recommendedStatus ? 'is-not-active-label-product-image-list' : '' ?> ">
                            <span class="title"><?= $escaper->escapeHtml(__("tops")) ?></span>&nbsp;<span><?= $escaper->escapeHtml(__("choice")) ?></span>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                <?= $block->getImage($product, $imageDisplayArea)
                    ->setTemplate('Magento_Catalog::product/list/image.phtml')
                    ->setData('custom_attributes', $imageCustomAttributes)
                    ->setProductId($productId)
                    ->toHtml(); ?>
            </div>
            <?php if (!$hideRatingSummary && !$viewIsSuggested): ?>
                <div class="item-product-reviews-mobile">
                    <?php $reviewsSummary = $block->getReviewsSummaryHtml($product, 'short', true) ?>
                    <?= $reviewsSummary ?>
                    <div class="text-blue-picton text-2.75 leading-4">
                        <?= !empty($product->getData('reviews_count')) ?
                            number_format((float)$product->getData('reviews_count') ?? 0, 0, null, ".") :
                            "0"; ?>
                        <?= $escaper->escapeHtml(__("reviews")) ?>
                    </div>
                </div>
            <?php endif; ?>
        </a>
        <div class="product-info flex flex-col  top-cart-shopping">
            <div class="<?= ($viewIsSuggested ? 'item-product-content-sugest' : 'item-product-content') . ($showDescription ? '-desc' : '') ?>">
                <?php $productNameStripped = $block->stripTags($product->getName(), null, true); ?>
                <div class="container-product-item-link md:mb-1 items-center justify-center text-primary font-semibold text-left leading-0 <?= $viewIsSuggested ? 'mt-2' : 'item-product-name' ?>">
                    <a class="product-item-link text-blue-prussian text-4.25 leading-6 md:text-xl"
                        href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>">
                        <?= /* @noEscape */ $block->getProductAction()->productAttribute($product, $product->getName(), 'name') ?>
                    </a>
                </div>
                <div class="w-full md:py-1 leading-tight mt-1 md:mt-0">
                    <span class="text-3.25 md:text-3.75 text-blue-cadet product-item-code">
                        <?php if (!empty($categoriesName)): ?>
                            <?= $escaper->escapeHtml(__($categoryName)) ?>
                            <span class="ml-2"><?= $escaper->escapeHtml(__(!empty($product->getData('supplier_trade_item_code')) ? $product->getData('supplier_trade_item_code') : "")) ?></span>
                        <?php else: ?>
                            <?= $escaper->escapeHtml(__(!empty($product->getData('supplier_trade_item_code')) ? $product->getData('supplier_trade_item_code') : "")) ?>
                        <?php endif; ?>
                    </span>
                </div>
                <?php $reviewsSummary = $block->getReviewsSummaryHtml($product, 'short', true) ?>
                <?php if ($reviewsSummary): ?>
                <div class="h-8 md:pt-1 md:pb-2 <?= $viewIsSuggested ? 'flex items-center justify-start' : 'item-product-reviews' ?>">

                    <?= $reviewsSummary ?>
                    <span class="text-blue-picton text-3.25 <?= !empty($reviewsSummary) ? 'ml-2' : '' ?>">
                        <?= !empty($product->getData('reviews_count')) ?
                            number_format((float)$product->getData('reviews_count') ?? 0, 0, null, ".") :
                            "0"; ?>
                        <?= $escaper->escapeHtml(__("reviews")) ?>
                    </span>
                </div>
                <?php endif; ?>
                <?php if ($showDescription): ?>
                    <?php $description = $productViewModel->getShortDescriptionForProduct($product) ;
                    if (!empty($description)): ?>
                        <div class="hidden md:block product-description-container w-full text-3.25 md:text-3.75 text-blue-prussian mt-1 md:mt-2 mb-3">
                            <?= /* @noEscape */ $description ?>
                        </div>
                        <div class="product-description-container w-full text-3.25 md:text-3.75 text-blue-prussian md:hidden"
                            :class="{ 'mt-1 md:mt-2 mb-3': !isLineThroughStock()}">
                            <?= /* @noEscape */ $description ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if ($product->isAvailable() && !$hideDetails): ?>
                    <?= $block->getProductDetailsHtml($product) ?>
                <?php else: ?>
                    <div></div>
                <?php endif; ?>
            </div>
            <div class="block md:hidden <?= $viewIsSuggested ? 'item-product-bottom-mobile' : '' ?>">
                <div class="pt-1 text-blue-picton flex justify-between items-end gap-1 top-cart-price">
                    <div class="price w-full">
                        <template x-if="!activeProductsPriceData && !isPriceHidden()">
                            <div class="price-container">
                                <?php if ($finalPrice < $regularPrice): ?>
                                    <div class="old-price mr-2 flex">
                                        <span id="product-price-<?= (int)$product->getId() ?>"
                                            class="price-wrapper title-font font-regular text-xs line-through text-white-azureish">
                                            <span x-html="hyva.formatPrice((<?= (float)$regularPrice ?> + getCustomOptionPrice()) * qty)">
                                                <?= /** @noEscape */ $productViewModel->format($regularPrice) ?>
                                            </span>
                                        </span>
                                    </div>
                                    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                                        <div class="old-price-excl-tax text-xs">
                                            <span class="font-regular line-through text-blue-picton">
                                                <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                                                <span class="price" x-html="hyva.formatPrice(<?= (float)$regularPriceExclTax ?> + getCustomOptionBasePrice())">
                                                    <?= /** @noEscape */ $productViewModel->format($regularPriceExclTax) ?>
                                                </span>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <template x-if="!activeProductsPriceData &&
                                    getFinalPriceAmount() < initialFinalPrice">
                                    <div class="old-price flex mr-2">
                                        <span id="product-price-<?= (int)$product->getId() ?>"
                                            class="price-wrapper title-font font-regular text-xs line-through text-white-azureish">
                                            <span x-html="hyva.formatPrice((initialFinalPrice + getCustomOptionPrice()) * qty)"></span>
                                        </span>
                                    </div>
                                </template>
                                <div class="final-price inline-block">
                                    <span id="product-price-<?= (int)$product->getId() ?>"
                                        class="price-wrapper title-font font-semibold text-2xl text-blue-picton">
                                        <span class="price" x-html="getFormattedFinalPriceWithQty()">
                                            <?= /** @noEscape */ $productViewModel->format($finalPrice) ?>
                                        </span>
                                    </span>
                                    <meta itemprop="price" content="<?= $escaper->escapeHtmlAttr($finalPrice) ?>">
                                    <meta itemprop="priceCurrency" content="<?= $escaper->escapeHtmlAttr($productViewModel->getCurrencyData()['code']) ?>">
                                </div>
                                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                                    <div class="final-price-excl-tax text-xs">
                                        <span class="font-regular text-blue-picton">
                                            <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                                            <span x-html="getFormattedBasePrice()">
                                                <?= /** @noEscape */ $productViewModel->format($finalPriceExclTax) ?>
                                            </span>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </template>
                        <template x-if="activeProductsPriceData &&
                            activeProductsPriceData.oldPrice &&
                            getFinalPriceAmount() < activeProductsPriceData[regularPriceKey].amount">
                            <div class="old-price flex mr-2">
                                <span id="product-price-<?= (int)$product->getId() ?>"
                                    class="price-wrapper title-font font-regular text-xs line-through text-white-azureish">
                                    <span x-html="hyva.formatPrice((activeProductsPriceData[regularPriceKey].amount + getCustomOptionPrice()) * qty)"></span>
                                </span>
                            </div>
                        </template>
                        <template x-if="activeProductsPriceData">
                            <div class="final-price inline-block">
                                <span id="product-price-<?= (int)$product->getId() ?>"
                                    class="price-wrapper title-font font-semibold text-2xl text-blue-picton">
                                    <span class="price" x-html="getFormattedFinalPriceWithQty()"></span>
                                </span>
                            </div>
                        </template>
                        <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                            <template x-if="activeProductsPriceData &&
                                activeProductsPriceData.oldPrice &&
                                getFinalPriceAmount() < activeProductsPriceData[regularPriceKey].amount">
                                <div class="old-price-excl-tax text-xs">
                                    <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                                    <span class="font-regular text-blue-picton">
                                        <span x-html="hyva.formatPrice(activeProductsPriceData['baseOldPrice'].amount + getCustomOptionBasePrice())"></span>
                                    </span>
                                </div>
                            </template>
                            <template x-if="activeProductsPriceData">
                                <div class="price-excl-taxinline-block text-xs">
                                    <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                                    <span class="font-regular text-blue-picton">
                                        <span x-html="getFormattedBasePrice()"></span>
                                    </span>
                                </div>
                            </template>
                        <?php endif; ?>
                        <small class="text-cadet-space flex text-2.75 leading-4 md:text-[13px]" :class="{ 'custom-top-perset': isLineThroughStock(), 'md:mt-0': !isLineThroughStock()}">
                            <span x-html="getFormattedFinalPrice()"></span>
                            <span class="pl-1"><?= __('per '.$perSetStatus)  ?></span>
                        </small>
                    </div>

                    <div class="flex items-end">
                        <div class="text-right w-full item-quantity">
                            <input type="number" name="qty" required readonly
                                x-model="qty"
                                x-on:change.debounce="updateInputItemQty"
                                title="Qty" class="w-20 mr-4">
                            <div class="item-quantity-nav">
                                <div class="item-quantity-button item-quantity-up"
                                    @click="updateItemQty('plus')"
                                >
                                    <?= $heroiconsSolid->renderHtml('chevron-up', 'fill-current text-blue-prussian mr-1.5 mt-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                                </div>
                                <div class="item-quantity-button item-quantity-down"
                                    @click="updateItemQty('minus')"
                                >
                                    <?= $heroiconsSolid->renderHtml('chevron-down', 'fill-current text-blue-prussian mr-1.5 mb-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-green-islamic item-product-add-to-cart"
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>">
                            <?= $heroiconsSolid->renderHtml('shopping-cart-alt', 'border-current inline', 23, 23); ?>
                        </button>
                    </div>
                </div>
                <div class="instock-percent w-full mt-2">
                    <div class="instock-percent-item mt-auto md:pb-2 flex justify-between items-center">
                        <div class="text-3.25 md:text-3.75" :class="{ 'custom-top': isLineThroughStock(), 'md:mt-4': !isLineThroughStock()}">
                            <div class="<?= $viewIsSuggested ? 'text-3.25' : 'text-2.75 leading-4' ?> mb-1">
                                <?= $block->getChildBlock('stockstatus')->setData('product', $product)->toHtml() ?>
                            </div>
                            <template x-if="(activeProductsPriceData && activeProductsPriceData.tierPrices.length > 0) ||
                                (!activeProductsPriceData && initialTierPrices.length > 0)">
                                <span class="percent-text text-2.75 leading-4 md:text-[13px] text-cadet-space" x-data="calculateDescriptionTierPrice()">
                                    <?= $escaper->escapeHtml(__('Take ')) ?>
                                    <span x-text="takeQty"></span>
                                    <?= $escaper->escapeHtml(__(' '.$perSetStatus.'s for a ')) ?>
                                    <span x-text="takeDiscount"></span>
                                    <?= $escaper->escapeHtml(__(' discount')) ?>
                                </span>
                            </template>
                        </div>
                        <div class="flex items-end">
                            <?php if ($showAddToWishlist): ?>
                                <button x-data="initWishlist()"
                                        @click.prevent="addToWishlist(<?= (int)$productId ?>)"
                                        aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Wish List')) ?>"
                                        type="button"
                                        class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex shrink-0 items-center justify-center text-gray-500 hover:text-red-600 ml-2">
                                    <?= $heroiconsSolid->heartHtml("w-5 h-5", 25, 25) ?>
                                </button>
                            <?php endif; ?>
                            <?php if ($showAddToCompare): ?>
                                <button x-data="initCompareOnProductList()"
                                        @click.prevent="addToCompare(<?= (int)$productId ?>)"
                                        aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Compare')) ?>"
                                        type="button"
                                        class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex shrink-0 items-center justify-center text-gray-500 hover:text-yellow-500 ml-2">
                                    <?= $heroicons->scaleHtml("w-5 h-5", 25, 25) ?>
                                </button>
                            <?php endif; ?>
                            <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                                <?= $addToBlock->setProduct($product)->getChildHtml() ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <template x-if="activeProductsPriceData && activeProductsPriceData.tierPrices.length > 0">
                        <div class="content-button-persen-item-product text-sm">
                            <template x-for="(tierPrice, index) in activeProductsPriceData.tierPrices">
                                <div @click="updateItemDiscountTierPrice(index, 'general', tierPrice.qty)" class="content-button py-0.5 "
                                    :class="{'md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && qty < tierPrice.qty,
                                            'active-persen md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && qty >= tierPrice.qty,
                                            'md:py-1 md:my-0.5' : index > 0 && index < activeProductsPriceData.tierPrices.length - 1 && qty < tierPrice.qty,
                                            'active-persen md:py-1 md:my-0.5' : index > 0 && index < activeProductsPriceData.tierPrices.length - 1 && qty >= tierPrice.qty,
                                            'md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == activeProductsPriceData.tierPrices.length - 1 && qty < tierPrice.qty,
                                            'active-persen md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == activeProductsPriceData.tierPrices.length - 1 && qty >= tierPrice.qty}">
                                    <span class="text-2.75 leading-4 md:text-sm" x-html="tierPrice.percentage + '%'"></span>
                                </div>
                            </template>
                        </div>
                    </template>
                    <template x-if="!activeProductsPriceData && initialTierPrices.length > 0">
                        <div class="content-button-persen-item-product text-sm">
                            <template x-for="(tierPrice, index) in initialTierPrices">
                                <div @click="updateItemDiscountTierPrice(index, 'init', tierPrice.price_qty)" class="content-button py-0.5 "
                                    :class="{'md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && qty < tierPrice.price_qty,
                                            'active-persen md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && qty >= tierPrice.price_qty,
                                            'md:py-1 md:my-0.5' : index > 0 && index < initialTierPrices.length - 1 && qty < tierPrice.price_qty,
                                            'active-persen md:py-1 md:my-0.5' : index > 0 && index < initialTierPrices.length - 1 && qty >= tierPrice.price_qty,
                                            'md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == initialTierPrices.length - 1 && qty < tierPrice.price_qty,
                                            'active-persen md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == initialTierPrices.length - 1 && qty >= tierPrice.price_qty}">
                                    <span class="text-2.75 leading-4 md:text-sm" x-html="getPercentTierPrice(tierPrice) + '%'"></span>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
    <div class="item-product-bottom">
        <div class="flex flex-col w-full">
            <div class="pt-1 text-blue-picton flex justify-between items-start gap-1 top-cart-price">
                <div class="price w-full">
                    <template x-if="!activeProductsPriceData && !isPriceHidden()">
                        <div class="price-container">
                            <?php if ($finalPrice < $regularPrice): ?>
                                <div class="old-price mr-2 flex">
                                    <span id="product-price-<?= (int)$product->getId() ?>"
                                        class="price-wrapper title-font font-regular text-xs line-through text-white-azureish">
                                        <span x-html="hyva.formatPrice((<?= (float)$regularPrice ?> + getCustomOptionPrice()) * qty)">
                                            <?= /** @noEscape */ $productViewModel->format($regularPrice) ?>
                                        </span>
                                    </span>
                                </div>
                                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                                    <div class="old-price-excl-tax text-xs">
                                        <span class="font-regular line-through text-blue-picton">
                                            <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                                            <span class="price" x-html="hyva.formatPrice(<?= (float)$regularPriceExclTax ?> + getCustomOptionBasePrice())">
                                                <?= /** @noEscape */ $productViewModel->format($regularPriceExclTax) ?>
                                            </span>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            <template x-if="!activeProductsPriceData &&
                                getFinalPriceAmount() < initialFinalPrice">
                                <div class="old-price flex mr-2">
                                    <span id="product-price-<?= (int)$product->getId() ?>"
                                        class="price-wrapper title-font font-regular text-xs line-through text-white-azureish">
                                        <span x-html="hyva.formatPrice((initialFinalPrice + getCustomOptionPrice()) * qty)"></span>
                                    </span>
                                </div>
                            </template>
                            <div class="final-price inline-block">
                                <span id="product-price-<?= (int)$product->getId() ?>"
                                    class="price-wrapper title-font font-semibold text-2xl text-blue-picton">
                                    <span class="price" x-html="getFormattedFinalPriceWithQty()">
                                        <?= /** @noEscape */ $productViewModel->format($finalPrice) ?>
                                    </span>
                                </span>
                            </div>
                            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                                <div class="final-price-excl-tax text-xs">
                                    <span class="font-regular text-blue-picton">
                                        <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                                        <span x-html="getFormattedBasePrice()">
                                            <?= /** @noEscape */ $productViewModel->format($finalPriceExclTax) ?>
                                        </span>
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </template>
                    <template x-if="activeProductsPriceData &&
                        activeProductsPriceData.oldPrice &&
                        getFinalPriceAmount() < activeProductsPriceData[regularPriceKey].amount">
                        <div class="old-price flex mr-2">
                            <span id="product-price-<?= (int)$product->getId() ?>"
                                class="price-wrapper title-font font-regular text-xs line-through text-white-azureish">
                                <span x-html="hyva.formatPrice((activeProductsPriceData[regularPriceKey].amount + getCustomOptionPrice()) * qty)"></span>
                            </span>
                        </div>
                    </template>
                    <template x-if="activeProductsPriceData">
                        <div class="final-price inline-block">
                            <span id="product-price-<?= (int)$product->getId() ?>"
                                class="price-wrapper title-font font-semibold text-2xl text-blue-picton">
                                <span class="price" x-html="getFormattedFinalPriceWithQty()"></span>
                            </span>
                        </div>
                    </template>
                    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                        <template x-if="activeProductsPriceData &&
                            activeProductsPriceData.oldPrice &&
                            getFinalPriceAmount() < activeProductsPriceData[regularPriceKey].amount">
                            <div class="old-price-excl-tax text-xs">
                                <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                                <span class="font-regular text-blue-picton">
                                    <span x-html="hyva.formatPrice(activeProductsPriceData['baseOldPrice'].amount + getCustomOptionBasePrice())"></span>
                                </span>
                            </div>
                        </template>
                        <template x-if="activeProductsPriceData">
                            <div class="price-excl-taxinline-block text-xs">
                                <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                                <span class="font-regular text-blue-picton">
                                    <span x-html="getFormattedBasePrice()"></span>
                                </span>
                            </div>
                        </template>
                    <?php endif; ?>
                    <small class="text-cadet-space flex text-2.75 leading-4 md:text-[13px] text-perset-desktop">
                        <span x-html="getFormattedFinalPrice()"></span>
                        <span class="pl-1"><?= __('per '.$perSetStatus)  ?></span>
                    </small>
                </div>
                <?php if ($product->getInventoryStatus() != 126): ?>
                <div class="flex items-end">
                    <div class="text-right w-full item-quantity">
                        <input type="number" name="qty" required  readonly
                            x-model="qty"
                            x-on:change.debounce="updateInputItemQty"
                            title="Qty" class="w-20 mr-4">
                        <div class="item-quantity-nav">
                            <div class="item-quantity-button item-quantity-up"
                                @click="updateItemQty('plus')"
                            >
                                <?= $heroiconsSolid->renderHtml('chevron-up', 'fill-current text-blue-prussian mr-1.5 mt-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                            </div>
                            <div class="item-quantity-button item-quantity-down"
                                @click="updateItemQty('minus')"
                            >
                                <?= $heroiconsSolid->renderHtml('chevron-down', 'fill-current text-blue-prussian mr-1.5 mb-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-green-islamic item-product-add-to-cart"
                            aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>">
                        <?= $heroiconsSolid->renderHtml('shopping-cart-alt', 'border-current inline', 23, 23); ?>
                    </button>
                </div>
                <?php endif; ?>
            </div>
            <?php if ($finalPrice < $regularPrice): ?>
                <div class="mt-2 md:mt-1.5 text-3.25 md:text-3.75" :class="{ 'is-product-discount': isLineThroughStock(), 'is-not-product-discount': !isLineThroughStock()}" >
                    <?= $block->getChildBlock('stockstatus')->setData('product', $product)->toHtml() ?>
                </div>
            <?php else: ?>
                <div class="item-product-status"
                    :class="{ 'is-product-discount': isLineThroughStock(), 'is-not-product-discount': !isLineThroughStock()}">
                    <?= $block->getChildBlock('stockstatus')->setData('product', $product)->toHtml() ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="instock-percent w-full">
            <div class="instock-percent-item mt-auto md:pb-2 flex justify-between items-center">
                <div class="in-stock">
                    <template x-if="(activeProductsPriceData && activeProductsPriceData.tierPrices.length > 0) ||
                        (!activeProductsPriceData && initialTierPrices.length > 0)">
                        <span class="percent-text text-2.75 leading-4 md:text-[13px] text-cadet-space text-perset-desktop" x-data="calculateDescriptionTierPrice()">
                            <?= $escaper->escapeHtml(__('Take ')) ?>
                            <span x-text="takeQty"></span>
                            <?= $escaper->escapeHtml(__(' '.$perSetStatus.'s for a ')) ?>
                            <span x-text="takeDiscount"></span>
                            <?= $escaper->escapeHtml(__(' discount')) ?>
                        </span>
                    </template>
                </div>
                <div class="flex items-end f">
                    <?php if ($showAddToWishlist): ?>
                        <button x-data="initWishlist()"
                                @click.prevent="addToWishlist(<?= (int)$productId ?>)"
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Wish List')) ?>"
                                type="button"
                                class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex shrink-0 items-center justify-center text-gray-500 hover:text-red-600 ml-2">
                            <?= $heroiconsSolid->heartHtml("w-5 h-5", 25, 25) ?>
                        </button>
                    <?php endif; ?>
                    <?php if ($showAddToCompare): ?>
                        <button x-data="initCompareOnProductList()"
                                @click.prevent="addToCompare(<?= (int)$productId ?>)"
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Compare')) ?>"
                                type="button"
                                class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex shrink-0 items-center justify-center text-gray-500 hover:text-yellow-500 ml-2">
                            <?= $heroicons->scaleHtml("w-5 h-5", 25, 25) ?>
                        </button>
                    <?php endif; ?>
                    <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                        <?= $addToBlock->setProduct($product)->getChildHtml() ?>
                    <?php endif; ?>
                </div>
            </div>
            <template x-if="activeProductsPriceData && activeProductsPriceData.tierPrices.length > 0">
                <div class="content-button-persen-item-product text-sm">
                    <template x-for="(tierPrice, index) in activeProductsPriceData.tierPrices">
                        <div @click="updateItemDiscountTierPrice(index, 'general', tierPrice.qty)" class="content-button py-0.5 "
                            :class="{'md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && qty < tierPrice.qty,
                                    'active-persen md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && qty >= tierPrice.qty,
                                    'md:py-1 md:my-0.5' : index > 0 && index < activeProductsPriceData.tierPrices.length - 1 && qty < tierPrice.qty,
                                    'active-persen md:py-1 md:my-0.5' : index > 0 && index < activeProductsPriceData.tierPrices.length - 1 && qty >= tierPrice.qty,
                                    'md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == activeProductsPriceData.tierPrices.length - 1 && qty < tierPrice.qty,
                                    'active-persen md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == activeProductsPriceData.tierPrices.length - 1 && qty >= tierPrice.qty}">
                            <span class="text-2.75 leading-4 md:text-sm" x-html="tierPrice.percentage + '%'"></span>
                        </div>
                    </template>
                </div>
            </template>
            <template x-if="!activeProductsPriceData && initialTierPrices.length > 0">
                <div class="content-button-persen-item-product text-sm">
                    <template x-for="(tierPrice, index) in initialTierPrices">
                        <div @click="updateItemDiscountTierPrice(index, 'init', tierPrice.price_qty)" class="content-button py-0.5 "
                            :class="{'md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && qty < tierPrice.price_qty,
                                    'active-persen md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && qty >= tierPrice.price_qty,
                                    'md:py-1 md:my-0.5' : index > 0 && index < initialTierPrices.length - 1 && qty < tierPrice.price_qty,
                                    'active-persen md:py-1 md:my-0.5' : index > 0 && index < initialTierPrices.length - 1 && qty >= tierPrice.price_qty,
                                    'md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == initialTierPrices.length - 1 && qty < tierPrice.price_qty,
                                    'active-persen md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == initialTierPrices.length - 1 && qty >= tierPrice.price_qty}">
                            <span class="text-2.75 leading-4 md:text-sm" x-html="getPercentTierPrice(tierPrice) + '%'"></span>
                        </div>
                    </template>
                </div>
            </template>
        </div>
    </div>
    <script>
        function initPriceBox_<?= (int)$product->getId() ?>()
        {
            const regularPriceInclTaxKey = 'oldPrice',
                regularPriceExclTaxKey = 'baseOldPrice',
                finalPriceInclTaxKey = 'finalPrice',
                finalPriceExclTaxKey = 'basePrice';
            function calculateCustomOptionPrices(activeCustomOptions, customOptionPrices) {
                return activeCustomOptions.reduce((priceAccumulator, activeCustomOptionId) => {
                    const customOptionPrice = customOptionPrices[activeCustomOptionId];
                    if (customOptionPrice) {
                        return Number.parseFloat(priceAccumulator) + Number.parseFloat(customOptionPrice);
                    }
                    return priceAccumulator;
                }, 0);
            }
            return {
                regularPriceKey: <?= $displayTax ? 'regularPriceInclTaxKey' : 'regularPriceExclTaxKey' ?>,
                finalPriceKey: <?= $displayTax ? 'finalPriceInclTaxKey' : 'finalPriceExclTaxKey' ?>,
                activeProductsPriceData: false,
                initialFinalPrice: <?= (float)$finalPrice ?>,
                defaultFinalPrice: <?= (float)$finalPrice ?>,
                calculatedFinalPrice: false,
                calculatedFinalPriceWithCustomOptions: false,
                initialTierPrices: <?= /** @noEscape */ json_encode($tierPrices, JSON_UNESCAPED_UNICODE) ?>,
                showRegularPriceLabel: <?= ($finalPrice < $regularPrice) ? 'true' : 'false' ?>,
                customOptionPrices: [],
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                initialBasePrice: <?= (float)$finalPriceExclTax ?>,
                calculatedBasePrice: false,
                customOptionBasePrices: [],
                calculatedBasePriceWithCustomOptions: false,
                <?php endif; ?>
                activeCustomOptions: [],
                qty: 1,
                takeQty : 1,
                takeDiscount : '0%',
                getFormattedPrice(price) {
                    return hyva.formatPrice(price)
                },
                updateCustomOptionActive(data) {
                    let activeCustomOptions = this.activeCustomOptions;
                    const customOptionId = data.customOptionId;
                    if (data.active) {
                        if (!activeCustomOptions.includes(customOptionId)) {
                            activeCustomOptions.push(data.customOptionId);
                        }
                    } else {
                        if (customOptionId && activeCustomOptions.includes(customOptionId)) {
                            let index = activeCustomOptions.indexOf(customOptionId);
                            activeCustomOptions.splice(index, 1);
                        }
                    }
                    this.calculateFinalPriceWithCustomOptions()
                },
                updateCustomOptionPrices(prices, basePrices) {
                    if (prices) {
                        this.customOptionPrices = prices;
                    }
                    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    if (basePrices) {
                        this.customOptionBasePrices = basePrices;
                    }
                    <?php endif; ?>
                    this.calculateFinalPriceWithCustomOptions();
                },
                isLineThroughStock() {
                    let isStock = false;
                    if (!this.activeProductsPriceData &&
                        this.getFinalPriceAmount() < this.initialFinalPrice) {
                        isStock = true;
                    } else if (this.activeProductsPriceData &&
                        this.activeProductsPriceData.oldPrice &&
                        this.getFinalPriceAmount() < this.activeProductsPriceData[this.regularPriceKey].amount){
                        isStock = true;
                    }
                    return isStock;
                },
                getPercentTierPrice(tierPrice) {
                    let percentageVal = tierPrice.percentage_value;
                    if(percentageVal == undefined) {
                        const price = tierPrice.price_excl_tax > tierPrice.price_incl_tax ?
                                    tierPrice.price_excl_tax : tierPrice.price_incl_tax;
                        percentageVal = (100 - (price / this.defaultFinalPrice) * 100);
                    }

                    const percentaceParts = percentageVal.toString().split(".");
                    const decimalLength = percentaceParts.length > 1
                        ? percentaceParts[1].length || 0
                        : 0;

                    if(decimalLength > 2) {
                        const multiplier = Math.pow(10, 1 || 0);
                        percentageVal = Math.round(percentageVal * multiplier) / multiplier;
                    }
                    const isFloat = (eval(percentageVal) - Number(percentageVal)) > 0 ? true : false;
                    return isFloat ? eval(percentageVal) : Number(percentageVal)
                },
                calculateFinalPrice() {
                    const findApplicableTierPrice = (initialPrice, withTax) => {
                        if (this.activeProductsPriceData && this.activeProductsPriceData.tierPrices) {
                            const key = withTax ? 'price' : 'basePrice'
                            const result = this.activeProductsPriceData.tierPrices.reduce((acc, tierPrice) => {
                                if (this.qty >= tierPrice.qty && tierPrice[key] < acc) {
                                    return tierPrice[key];
                                }
                                if(tierPrice[key] == undefined){
                                    if (this.qty >= tierPrice.qty && tierPrice.price < acc) {
                                        return tierPrice.price;
                                    }
                                }
                                return acc;
                            }, this.activeProductsPriceData[withTax ? finalPriceInclTaxKey : finalPriceExclTaxKey].amount);
                            return result;
                        } else {
                            const key = withTax ? 'price_incl_tax' : 'price_excl_tax';
                            return Object.values(this.initialTierPrices).reduce((acc, tierPrice) => {
                                if (this.qty >= tierPrice.price_qty && tierPrice[key] < acc) {
                                    return tierPrice[key];
                                }
                                return acc;
                            }, initialPrice);
                        }
                    }
                    this.calculatedFinalPrice = findApplicableTierPrice(this.initialFinalPrice, <?= $displayTax ? 'true' : 'false' ?>);
                    window.dispatchEvent(new CustomEvent("update-product-final-price", {detail: this.calculatedFinalPrice}));
                    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    this.calculatedBasePrice = findApplicableTierPrice(<?= (float) $finalPriceExclTax ?>, false);
                    window.dispatchEvent(new CustomEvent("update-product-base-price", {detail: {basePrice: this.calculatedBasePrice}}));
                    <?php endif; ?>
                },
                calculatePriceLabelVisibility() {
                    this.showRegularPriceLabel =
                        (this.calculatedFinalPrice === this.activeProductsPriceData[this.regularPriceKey].amount) &&
                        this.activeProductsPriceData.isMinimalPrice;
                },
                calculateFinalPriceWithCustomOptions() {
                    const finalPrice = this.calculatedFinalPrice || this.initialFinalPrice;
                    this.calculatedFinalPriceWithCustomOptions = finalPrice + this.getCustomOptionPrice();
                    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    const basePrice = this.calculatedBasePrice || this.initialBasePrice;
                    this.calculatedBasePriceWithCustomOptions = basePrice + this.getCustomOptionBasePrice();
                    <?php endif; ?>
                },
                getCustomOptionPrice() {
                    return calculateCustomOptionPrices(this.activeCustomOptions, this.customOptionPrices);
                },
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                getCustomOptionBasePrice() {
                    return calculateCustomOptionPrices(this.activeCustomOptions, this.customOptionBasePrices);
                },
                <?php endif; ?>
                getFormattedFinalPrice() {
                    return hyva.formatPrice(
                        this.calculatedFinalPriceWithCustomOptions ||
                        this.calculatedFinalPrice ||
                        this.initialFinalPrice
                    )
                },
                getFinalPriceAmount() {
                    return this.calculatedFinalPriceWithCustomOptions ||
                        this.calculatedFinalPrice ||
                        (this.activeProductsPriceData ? this.activeProductsPriceData[this.finalPriceKey].amount : false) ||
                        this.initialFinalPrice;
                },
                getFormattedFinalPriceWithQty() {
                    return hyva.formatPrice(
                        (this.calculatedFinalPriceWithCustomOptions ||
                        this.calculatedFinalPrice ||
                        this.initialFinalPrice) * this.qty
                    )
                },
                updateItemDiscountTierPrice(index, type, itemQty) {
                    this.changeTake(index, type, itemQty);
                    this.calculateFinalPrice();
                    this.calculateFinalPriceWithCustomOptions();
                },
                updateInputItemQty (e) {
                    let max = <?= ($stock_status == 138)? 5 : (($stock_status == 152)? 1: 9999)  ?>;
                    const qtyInput = e.target.value;
                    if (this.qty < max) {
                        this.qty = qtyInput;
                    }
                    this.changeTake();
                    this.calculateFinalPrice();
                    this.calculateFinalPriceWithCustomOptions();
                },
                updateItemQty(type) {
                    let max = <?= ($stock_status == 138)? 5 : (($stock_status == 152)? 1: 9999)  ?>;

                    if(type == 'plus'){
                        if(this.qty < max){
                            this.qty++;
                        }
                    }else{
                        this.qty--;
                    }
                    this.qty = this.qty == 0 ? 1 : this.qty;

                    this.changeTake();
                    this.calculateFinalPrice();
                    this.calculateFinalPriceWithCustomOptions();

                },
                changeTake(index = null, type = null, itemQty = 1) {
                    if(type){
                        index++
                        let itemTakeQty      = itemQty;
                        let itemTakeDiscount = '0%';
                        if(type == 'general'){
                            const idx = (index == this.activeProductsPriceData.tierPrices.length ? index-1 : index);
                            const tierPrice  = this.activeProductsPriceData.tierPrices[idx];
                            itemTakeQty = tierPrice.qty;
                            itemTakeDiscount = `${tierPrice.percentage}%`
                        }else if(type == 'init'){
                            const idx = (index == this.initialTierPrices.length ? index-1 : index);
                            const tierPrice  = this.initialTierPrices[idx];
                            const percentage = this.getPercentTierPrice(tierPrice);
                            itemTakeQty = tierPrice.price_qty;
                            itemTakeDiscount = `${percentage}%`
                        }
                        this.qty = itemQty;
                        this.takeQty = itemTakeQty;
                        this.takeDiscount = itemTakeDiscount
                    }else{
                        this.calculateDescriptionTierPrice()
                    }
                },
                calculateDescriptionTierPrice() {
                    this.qty             = this.qty < 1 ? 1 : this.qty;
                    const itemQty        = this.qty;
                    let itemTakeQty      = itemQty;
                    let itemTakeDiscount = '0%';
                    let idx = 0;
                    if (this.activeProductsPriceData) {
                        if (this.activeProductsPriceData.tierPrices.length > 0) {
                            this.activeProductsPriceData.tierPrices.forEach(function(tierPrice, index) {
                                if (itemQty >= tierPrice.qty) idx = (index+1);
                            });
                        }
                        idx = (idx == this.initialTierPrices.length ? idx-1 : idx);
                        if(this.initialTierPrices[idx] != null){
                            const tierPrice  = this.initialTierPrices[idx];
                            itemTakeQty = tierPrice.qty;
                            itemTakeDiscount = `${tierPrice.percentage}%`
                        }
                    }else if(this.initialTierPrices.length > 0) {
                        this.initialTierPrices.forEach(function(tierPrice, index) {
                            if (itemQty >= tierPrice.price_qty) idx = (index+1);
                        });
                        idx = (idx == this.initialTierPrices.length ? idx-1 : idx);
                        if(this.initialTierPrices[idx] != null){
                            const tierPrice  = this.initialTierPrices[idx];
                            const percentage = this.getPercentTierPrice(tierPrice);
                            itemTakeQty = tierPrice.price_qty;
                            itemTakeDiscount = `${percentage}%`
                        }
                    }
                    this.takeQty = itemTakeQty;
                    this.takeDiscount = itemTakeDiscount;
                },
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                getFormattedBasePrice() {
                    return hyva.formatPrice(
                        this.calculatedBasePriceWithCustomOptions ||
                        this.calculatedBasePrice ||
                        this.initialBasePrice
                    )
                },
                <?php endif; ?>
                isPriceHidden() {
                    const finalPrice = this.calculatedFinalPriceWithCustomOptions ||
                        this.calculatedFinalPrice ||
                        this.initialFinalPrice;
                    return <?= ($product->getInventoryStatus() != 126) ? 'false' : 'true' ?> && finalPrice === 0;
                },
                eventListeners_<?= (int)$product->getId() ?>: {
                    ['@update-prices-<?= (int)$product->getId() ?>.window'](event) {
                        this.activeProductsPriceData = event.detail;
                        this.changeTake();
                        this.calculateFinalPrice();
                        this.calculateFinalPriceWithCustomOptions();
                        this.calculatePriceLabelVisibility();
                    },
                    ['@update-qty-<?= (int)$product->getId() ?>.window'](event) {
                        this.qty = event.detail;
                        this.changeTake();
                        this.calculateFinalPrice();
                        this.calculateFinalPriceWithCustomOptions();
                    },
                    ['@update-custom-option-active.window'](event) {
                        this.updateCustomOptionActive(event.detail);
                    },
                    ['@update-custom-option-prices.window'](event) {
                        this.updateCustomOptionPrices(event.detail);
                    },
                    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    ['@update-custom-option-base-prices.window'](event) {
                        this.updateCustomOptionPrices(null, event.detail);
                    }
                    <?php endif; ?>
                }
            }
        }
    </script>
<?php if ($product->getInventoryStatus() != 126): ?>
</form>
<?php else: ?>
</div>
<?php endif; ?>
