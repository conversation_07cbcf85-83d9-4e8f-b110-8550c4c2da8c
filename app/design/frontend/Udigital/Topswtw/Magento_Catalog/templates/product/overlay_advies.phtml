<style>
    a.content-toggle {
        display: flex;
    }

    /* .con-plus {
        display: inline-block;
        text-decoration: none;
        cursor: pointer;
        margin-left: auto;
        color: black;
    }
    .con-plus:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 20px;
        line-height: inherit;
        color: #000;
        content: '\e622';
        font-family: 'icons-blank-theme';
        margin: 0;
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    } */
    .content-toggle p {
        font-weight: 700;
        font-size: 16px;
        width: 90%;
    }

    .toggle .a {
        font-size: 14px;
        margin: 0px 15px;
    }

    .toggle {
        margin: 16px 0px;
        border-bottom: solid 1px #A2AFBD;
        padding-bottom: 16px;
    }

    .bestellen-betalen {
        color: #16365B;
    }

    .bestellen-betalen.faq {
        margin-top: 25px;
    }

    .mx-merk {
        margin-left: 25%;
    }
    .make-modal .content-modal-attr p {
        text-align: left;
    }
    @media (max-width: 768px) {
        .toggle {
            margin: 10px 0px;
            border-bottom: solid 1px #A2AFBD;
            padding-bottom: 10px;
        }
        .mx-merk .content-modal-attr h2 {
            text-align: left;
            margin-bottom: 5px;
        }
        .make-modal .mx-merk .content-modal-attr p {
            width: 90%;
        }
        .make-modal .mx-merk .content-modal-attr a.content-toggle p {
            font-size: 15px;
        }
        [data-content-type='row'] > div {
            margin-bottom: 0.625rem;
            padding: 0.5rem;
        }
        .make-modal .mx-merk .content-modal-attr [data-content-type='text'] p,
        .make-modal .mx-merk .content-modal-attr [data-content-type='html'] p{
            font-size: 13px;
        }
    }
</style>