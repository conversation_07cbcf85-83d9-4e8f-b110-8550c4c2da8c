<style>
    a.content-toggle {
        display: flex;
    }
    .content-toggle p {
        font-weight: 700;
        font-size: 16px;
        width: 90%;
    }

    .toggle .a {
        font-size: 14px;
        margin: 0px 15px;
    }

    .toggle {
        margin: 16px 0px;
        border-bottom: solid 1px #A2AFBD;
        padding-bottom: 16px;
    }

    .bestellen-betalen {
        color: #16365B;
    }

    .bestellen-betalen.faq {
        margin-top: 25px;
    }

    .mx-bypass {
        margin-left: 25%;
    }
    .make-modal .content-modal-attr p {
        text-align: left;
    }

    .make-modal .mx-bypass div.modal-view-product-bypass[role="dialog"] {
        right: 10% !important;
        left: 29% !important;
    }
    @media (max-width: 1024px) {

        .make-modal .mx-bypass div.modal-view-product-bypass[role="dialog"] {
            right: 25% !important;
            left: -30% !important;
        }
        .toggle {
            margin: 10px 0px;
            border-bottom: solid 1px #A2AFBD;
            padding-bottom: 10px;
        }
        .mx-bypass .content-modal-attr h2 {
            text-align: left;
            margin-bottom: 5px;
        }
        .make-modal .mx-bypass .content-modal-attr p {
            width: 90%;
        }
        .make-modal .mx-bypass .content-modal-attr a.content-toggle p {
            font-size: 15px;
        }
        [data-content-type='row'] > div {
            margin-bottom: 0.625rem;
            padding: 0.5rem;
        }
        .make-modal .mx-bypass .content-modal-attr [data-content-type='text'] p,
        .make-modal .mx-bypass .content-modal-attr [data-content-type='html'] p{
            font-size: 13px;
        }
    }
    @media (min-width: 769px) and (max-width: 1024px){
        #mpblog-list-container .post-list-item {
            flex-basis: 52% !important;
        }
        #the-labelmx-bypass .body-content-modal {
            padding-left: 0px; 
         }
    }
</style>