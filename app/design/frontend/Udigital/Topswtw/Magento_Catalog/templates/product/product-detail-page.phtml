<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Catalog\Block\Product\View;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
/** @var View $block */

$product = $block->getProduct();
?>

<?= $block->getChildHtml('product.title') ?>
<?= $block->getChildHtml('product.title.breadcrumbs') ?>
<?= $block->getChildHtml('product.info.review') ?>

<div class="con-prod-detail">
    <section class="text-gray-700 body-font">
        <div class="flex pb-6 lg:flex-row flex-col items-center">
            <div class="grid grid-rows-auto grid-cols-1 md:gap-x-5 md:grid-cols-[42%_minmax(0,_1fr)] md:grid-rows-[min-content_minmax(0,_1fr)] lg:gap-x-10 lg:grid-cols-2 w-full">
                <?= $block->getChildHtml('product.media') ?>
                <?= $block->getChildHtml('product.info') ?>
            </div>
        </div>
    </section>
    <section>
        <?= $block->getChildHtml('product_options_wrapper_bottom') ?>
    </section>
    <?= $block->getChildHtml('product.info.details'); ?>
    <section>
        <div class="flex flex-wrap pb-4">
            <?= $block->getChildHtml('review_form') ?>
            <?= $block->getChildHtml('review_list') ?>
        </div>
        <?= $block->getChildHtml('related') ?>
        <?php  //$block->getChildHtml('upsell') ?>
    </section>
    <section class="blog-section">
        <?= $block->getChildHtml('home-blog') ?>
    </section>
</div>
