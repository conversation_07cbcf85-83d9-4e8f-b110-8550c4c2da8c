<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\Store;
use Magento\Catalog\Block\Product\ReviewRendererInterface as ProductReviewRenderer;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Store $viewModelStore */
$viewModelStore = $viewModels->require(Store::class);

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

$viewMode = 'suggested';
$imageDisplayArea = 'category_page_grid';
$showDescription = $block->getData('show_description');

$title = (string) $block->getTitle();
$items = $block->getItems() ?? [];
if (is_object($items) && $items instanceof Iterator) {
    $items = iterator_to_array($items);
}
if (!$itemCount = count($items)) {
    return '';
}

$sliderItemRenderer = $block->getLayout()->getBlock('product_list_item')
    ?: $block->getChildBlock('slider.item.template')
    ?: $block->getLayout()->createBlock(Template::class);

$hideRatingSummary = (bool) $block->getData('hide_rating_summary');
$hideDetails       = (bool) $block->getData('hide_details');

$sliderItemRenderer->setData('hide_details', $hideDetails);
$sliderItemRenderer->setData('hide_rating_summary', $hideRatingSummary);

// The slider item renderer block is often a shared instance.
// If a specific item template is set for this slider, the previously set template must be reset later
// so the item template is only replaced for the one slider it is specified on.
$sharedItemRendererTemplate = null;
$isSharedItemRenderer       = $sliderItemRenderer !== $block->getChildBlock('slider.item.template');
if ($isSharedItemRenderer && $block->getChildBlock('slider.item.template')) {
    $sharedItemRendererTemplate = $sliderItemRenderer->getTemplate();
    $sliderSpecificItemTemplate = $block->getChildBlock('slider.item.template')->getTemplate();
    $sliderItemRenderer->setTemplate($sliderSpecificItemTemplate);
}

?>
<script>
    'use strict';

    function initSliderComponent() {
        return {
            active: 0,
            itemCount: 0,
            getSlider() {
                return this.$root.querySelector('.js_slides');
            },
            pageSize: 4,
            pageFillers: 0,
            calcPageSize() {
                const slider = this.getSlider();
                if (slider) {
                    this.itemCount = slider.querySelectorAll('.js_slide').length;
                    this.pageSize = Math.round(slider.clientWidth / slider.querySelector('.js_slide').clientWidth);
                    this.pageFillers = (
                        this.pageSize * Math.ceil(this.itemCount / this.pageSize)
                    ) - this.itemCount;
                }
            },
            calcActive() {
                const slider = this.getSlider();
                if (slider) {
                    const sliderItems = this.itemCount + this.pageFillers;
                    const calculatedActiveSlide = slider.scrollLeft / (slider.scrollWidth / sliderItems);
                    this.active = Math.round(calculatedActiveSlide / this.pageSize) * this.pageSize;
                }
            },
            scrollPrevious() {
                this.scrollTo(this.active - this.pageSize);
            },
            scrollNext() {
                this.scrollTo(this.active + this.pageSize);
            },
            scrollTo(idx) {
                const slider = this.getSlider();
                if (slider) {
                    const slideWidth = slider.scrollWidth / (this.itemCount + this.pageFillers);
                    slider.scrollLeft = Math.floor(slideWidth) * idx;
                    this.active = idx;
                }
            }
        }
    }
</script>
<section class="<?=
    $escaper->escapeHtmlAttr($block->getData('maybe_purged_tailwind_section_classes'))
    ?: 'my-12 text-gray-700 body-font title-top-crossel';
?> "
         x-data="initSliderComponent()"
         x-init="calcPageSize();"
         @resize.window.debounce="calcPageSize(); $nextTick( function() { calcActive() })"
>
    <?php if ($items): ?>
        <div class="relative">
            <?php if ($title): ?>
                <div class="title-crossel">
                    <h3 class="text-2xl text-blue-picton font-medium text-gray-900 title-font l-title-text">
                        <?= $escaper->escapeHtml(__("Have you thought about this?")) ?>
                    </h3>
                </div>
            <?php endif; ?>
            <div class="flex-none relative w-full overflow-x-hidden focus-within:ring-2 ring-offset-2 active:ring-0 ring-blue-500/50 content-p-slide">
                <div class="relative flex flex-nowrap w-full max-[1024px]:overflow-auto js_slides snap md:px-1 xl:px-2 content-slide-cart "
                     @scroll.debounce="calcActive"
                >
                    <?php foreach ($items as $product): ?>
                        <div class="flex shrink-0 w-full mr-1 p-1 js_slide md:w-1/2 lg:w-1/3 xl:w-1/4 cart-left">
                            <?= /** @noEscape */ $productListItemViewModel->getItemHtmlWithRenderer(
                                $sliderItemRenderer,
                                $product,
                                $block,
                                $viewMode,
                                ProductReviewRenderer::SHORT_VIEW,
                                $imageDisplayArea,
                                $showDescription
                            ) ?>
                        </div>
                    <?php endforeach; ?>
                    
                </div>
            </div>
            <div class="max-[768px]:h-16 slider-indicator">
            <template x-if="itemCount > pageSize">
                <div class="flex items-center justify-center py-4 slider-none">
                    <button
                        aria-label="<?= $escaper->escapeHtml(__('Previous')) ?>"
                        tabindex="-1"
                        class="mr-4 text-black rounded-full outline-none focus:outline-none flex-none"
                        :class="{ 'opacity-25 pointer-events-none' : active === 0 }"
                        @click="scrollPrevious">
                        <?= $heroicons->chevronLeftHtml("w-5 h-5", 25, 25) ?>
                    </button>
                    <div class="flex flex-wrap w-full md:w-auto justify-center">
                    <?php for ($i=0; $i < $itemCount; $i++): ?>
                        <span class="shrink-0 block w-3 h-3 m-1 bg-black bg-opacity-25 rounded-full shadow cursor-pointer"
                                :class="{
                                    'bg-opacity-100': active === <?= (int) $i ?>,
                                    'bg-opacity-25': active !== <?= (int) $i ?>,
                                    'hidden': (pageSize !== 1 && !!(<?= (int) $i ?> % pageSize))
                                    }"
                                @click="scrollTo(<?= (int) $i ?>)">
                        </span>
                    <?php endfor; ?>
                    </div>
                    <button
                        aria-label="<?= $escaper->escapeHtml(__('Next')) ?>"
                        tabindex="-1"
                        class="ml-4 text-black rounded-full outline-none focus:outline-none flex-none"
                        :class="{ 'opacity-25 pointer-events-none' : active >= itemCount-pageSize }"
                        @click="scrollNext">
                        <?= $heroicons->chevronRightHtml("w-5 h-5", 25, 25) ?>
                    </button>
                </div>
            </template>
            </div>
        </div>
    <?php endif; ?>
</section>
<?php

if ($sharedItemRendererTemplate) {
    $sliderItemRenderer->setTemplate($sharedItemRendererTemplate);
}

?>
