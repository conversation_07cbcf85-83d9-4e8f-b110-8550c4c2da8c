<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Title;

/** @var Escaper $escaper */
/** @var Title $block */
/** @var ViewModelRegistry $viewModels */
/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

$cssClass = $block->getCssClass() ? ' ' . $block->getCssClass() : 'text-3xl';
$titleHtml = '';
if (trim((string)$block->getPageHeading())) {
    $titleHtml = $escaper->escapeHtml($block->getPageHeading());
}
if (trim((string)$productViewModel->getProduct()->getSubtitle())) {
    $titleHtml = trim($productViewModel->getProduct()->getSubtitle());
}

?>
<?php if ($titleHtml): ?>
    <div class="flex flex-col md:flex-row flex-wrap font-bold
    lg:mt-8 <?= /** @noEscape */ $cssClass ?> mb-mobile-text title-account">
        <h1 class="text-gray-900 page-title title-font title-cart-checkout"
            <?php if ($block->getId()): ?> id="<?= $escaper->escapeHtmlAttr($block->getId()) ?>" <?php endif; ?>>
            <?= /* @noEscape */ $titleHtml ?>
        </h1>
        <?= $block->getChildHtml() ?>
    </div>
<?php endif; ?>
