<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
?>

<button type="submit" form="product_addtocart_form" title="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>" class="btn btn-green-islamic btn-add-to-cart px-6 ml-1 mb-1 whitespace-nowrap" id="product-addtocart-button">
    <?= $heroiconsSolid->shoppingCartHtml('border-current'); ?>
    <span class="hidden sm:block md:hidden lg:block text-xl ml-3">
        <div class="hidden lg:block">
            <div class="text-4 screen-256.25:text-5 text-white text-left leading-4.5">
                <?= $block->getData('is_cart_configure') ?
                    $escaper->escapeHtml(__('Update item')) :
                    $escaper->escapeHtml(__('Add to Cart')) ?>
            </div>
        </div>
        <div class="block lg:hidden">
            <div class="text-4 screen-256.25:text-5 text-white text-left leading-4.5">
                <?= $block->getData('is_cart_configure') ?
                    $escaper->escapeHtml(__('Update item')) :
                    $escaper->escapeHtml(__('Add to Cart mobile')) ?>
            </div>
        </div>
    </span>
</button>

<?= $block->getChildHtml('', true) ?>
