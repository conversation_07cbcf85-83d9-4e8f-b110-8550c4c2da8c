<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Elgentos\Skwirrel\ViewModel\SkwirrelProductAttachment as ProductAttachment;
use Elgentos\Skwirrel\Model\SkwirrelProductAttachment as ProductAttachmentModel;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel           = $viewModels->require(ProductPage::class);
$productAttachmentViewModel = $viewModels->require(ProductAttachment::class);
/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
/** @var Product $product */
$product = $productViewModel->getProduct();
?>
<?php $attachmentsByProduct = $productAttachmentViewModel->getAttachmentsByProduct($product); ?>
<?php if (count($attachmentsByProduct->getItems())): ?>
    <ul class="magepsycho-product-attachments">
        <?php foreach ($attachmentsByProduct->getItems() as $productAttachment): ?>
            <?php if ($productAttachment->getTypeCode() === 'PVI') { continue;} ?>
            <?php /** @var ProductAttachmentModel $productAttachment */ ?>
            <?php $rawUrl = str_replace(
                '/file/download/',
                '/file/raw/',
                (string)$productAttachment->getSourceUrl()
            ); ?>
            <li>
                <a href="<?= $escaper->escapeHtml($rawUrl) ?>" class="pb-4" target="_blank" title="<?= $escaper->escapeHtml($productAttachment->getTitle() ?? '') ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="15px" height="21px" style="fill:#2EB6ED;" viewBox="0 0 384 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. -->
                        <path d="M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm76.45 211.36l-96.42 95.7c-6.65 6.61-17.39 6.61-24.04 0l-96.42-95.7C73.42 337.29 80.54 320 94.82 320H160v-80c0-8.84 7.16-16 16-16h32c8.84 0 16 7.16 16 16v80h65.18c14.28 0 21.4 17.29 11.27 27.36zM377 105L279.1 7c-4.5-4.5-10.6-7-17-7H256v128h128v-6.1c0-6.3-2.5-12.4-7-16.9z" />
                    </svg>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
<?php endif; ?>
