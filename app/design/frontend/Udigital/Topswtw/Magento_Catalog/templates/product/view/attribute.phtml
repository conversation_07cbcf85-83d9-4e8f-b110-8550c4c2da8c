<?php
/**
 * Product view template
 *
 */

use Magento\Catalog\Block\Product\View\Description;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;

/** @var Description $block */
/** @var Escaper $escaper */
$_product = $block->getProduct();

if (!$_product instanceof Product) {
    return;
}

$_call = $block->getAtCall();
$_code = $block->getAtCode();
$_className = $block->getCssClass();
$_attributeLabel = $block->getAtLabel();
$_attributeType = $block->getAtType();
$_attributeAddAttribute = $block->getAddAttribute();

$renderLabel = true;
// if defined as 'none' in layout, do not render
if ($_attributeLabel == 'none') {
    $renderLabel = false;
}

if ($_attributeLabel && $_attributeLabel == 'default') {
    $_attributeLabel = $_product->getResource()->getAttribute($_code)->getStoreLabel();
}
if ($_attributeType && $_attributeType == 'text') {
    $_attributeValue = ($block->getProductAction()->productAttribute($_product, $_product->$_call(), $_code))
        ? $_product->getAttributeText($_code)
        : '';
} else {
    $_attributeValue = $block->getProductAction()->productAttribute($_product, $_product->$_call(), $_code);
}
?>

<?php if ($_attributeValue):?>
<div class="product attribute <?= $escaper->escapeHtmlAttr($_className) ?>">----
    <?php if ($renderLabel):?>
        <strong class="type"><?= $escaper->escapeHtml($_attributeLabel) ?></strong>
    <?php endif; ?>
    <div class="value" <?= /* @noEscape */ $_attributeAddAttribute ?>><?= /* @noEscape */ $_attributeValue ?></div>
</div>
<?php endif; ?>
