<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Catalog\Block\Product\View\Attributes;
use Hyva\Theme\ViewModel\ProductAttributes;
use Magento\Catalog\Model\Product;
use Magento\Framework\View\Element\AbstractBlock;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\Model\ViewModelRegistry;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Attributes $block */

/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var Product $product */
$product = $block->getProduct();

$compositie_aantal_1 = $product->getCustomAttribute("compositie_aantal_1");
$compositie_type_1 = $product->getCustomAttribute("compositie_type_1");
$compositie_afmetingen_1 = $product->getCustomAttribute("compositie_afmetingen_1");
$compositie_filterklasse_1 = $product->getCustomAttribute("compositie_filterklasse_1");

$compositie_aantal_2 = $product->getCustomAttribute("compositie_aantal_2");
$compositie_type_2 = $product->getCustomAttribute("compositie_type_2");
$compositie_afmetingen_2 = $product->getCustomAttribute("compositie_afmetingen_2");
$compositie_filterklasse_2 = $product->getCustomAttribute("compositie_filterklasse_2");
$website_id = $block->getCategoryAction()->getStore()->getWebsiteId();
$text_free_shipping = ($website_id == 4 ) ? "Free shipping from €100" : "Free shipping from €50";
?>

<div class="w-full pr-6 items-center mb-3 min-[440px]:max-w-[439px]">
    <div class="card w-full border-b pt-3.75 pb-3.75 pl-5 mt-4 border-container last:border-0 last:mb-0 total-reviews bg-blue-mist rounded-xl min-[1025px]:max-w-[500px]">
        <div class="text-base grid md:grid-cols-[70%_25%] grid-cols-[65%_30%] gap-px place-content-between max-[375px]:grid-cols-[70%_35%]">
            <ol class="ol-stars-text">
                <li class="pl-1 flex items-start place-items-center my-1 text-blue-prussian">
                    <span class="mt-1"><?= /* @noEscape */ $heroiconsSolid->checkHtml("w-4 h-4 text-[#07C605]", 12, 12); ?></span>
                    <span class="ml-1 max-screen-110:text-[12px]">
                        <?=$escaper->escapeHtml(__("Choose your own discount")) ?>
                    </span>
                </li>
                <li class="pl-1 flex items-start place-items-center my-1 text-blue-prussian">
                    <span class="mt-1"><?= /* @noEscape */ $heroiconsSolid->checkHtml("w-4 h-4 text-[#07C605]", 12, 12); ?></span>
                    <span class="ml-1 max-screen-110:text-[12px]">
                        <?= $escaper->escapeHtml(__($text_free_shipping)) ?>
                    </span>
                </li>
                <li class="pl-1 flex items-start place-items-center my-1 text-blue-prussian">
                    <span class="mt-1"><?= /* @noEscape */ $heroiconsSolid->checkHtml("w-4 h-4 text-[#07C605]", 12, 12); ?></span>
                    <span class="ml-1 max-screen-110:text-[12px]">
                        <?= $escaper->escapeHtml(__("Free returns")) ?>
                    </span>
                </li>
            </ol>
            <div class="relative">
                <img
                    class="-mb-[15px] absolute bottom-0 max-w-[95px]"
                    src="<?= $block->getViewFileUrl('images/sven_home/afbeelding.png'); ?>"
                    alt="sven_home_nl_n-mobile" />
            </div>
        </div>
    </div>
</div>
<?php if ($attributes = $block->getAdditionalData()): ?>
    <?php
 // show attr custom
    if ($compositie_aantal_1 != null && $compositie_type_1 != null && $compositie_afmetingen_1 != null && $compositie_filterklasse_1 != null) {
        ?>
    <div class="flex pt-6 pb-3 mb-1 md:flex-row">
        <h3 class="text-gray-900 !text-2xl title-font font-bold text-left w-full" style="font-size: 24px;">
            <?= __('U ontvangt') . '&nbsp;' ?>
        </h3>
    </div>
    <div class="table-wrapper overflow-x-auto" id="product-attributes-u-ontvangt">
        <table class="additional-attributes text-[#3D3D3D] text-[16px] w-full">
            <?php
            // show attr custom
            if ($compositie_aantal_1 != null && $compositie_type_1 != null && $compositie_afmetingen_1 != null && $compositie_filterklasse_1 != null) {
                ?>
                <tr class="border-gray-300 last:border-b-0">
                    <th class="col label w-1/12 px-2 text-left text-gray-700 font-normal product-attribute-label" scope="row">
                        <?= $compositie_aantal_1->getValue() . __(" x"); ?>
                    </th>
                    <td class="col data w-5/12 pl-2 text-left text-gray-900 product-attribute-value">
                        <?= $compositie_type_1->getValue(); ?>
                    </td>
                    <td class="col data w-4/12 pl-2 text-left text-gray-900 product-attribute-value">
                        <?= $compositie_afmetingen_1->getValue(); ?>
                    </td>
                    <td class="col data w-1/12 pl-2 text-left text-gray-900 product-attribute-value">
                        <?= $compositie_filterklasse_1->getValue(); ?>
                    </td>
                </tr>
            <?php }
            if ($compositie_aantal_2 != null && $compositie_type_2 != null && $compositie_afmetingen_2 != null && $compositie_filterklasse_2 != null) { ?>
                <tr class="border-gray-300 last:border-b-0">
                    <th class="col label w-1/12 px-2 text-left text-gray-700 font-normal product-attribute-label" scope="row">
                        <?= $compositie_aantal_2->getValue() . __(" x"); ?>
                    </th>
                    <td class="col data w-5/12 pl-2 text-left text-gray-900 product-attribute-value">
                        <?= $compositie_type_2->getValue(); ?>
                    </td>
                    <td class="col data w-4/12 pl-2 text-left text-gray-900 product-attribute-value">
                        <?= $compositie_afmetingen_2->getValue(); ?>
                    </td>
                    <td class="col data w-1/12 pl-2 text-left text-gray-900 product-attribute-value">
                        <?= $compositie_filterklasse_2->getValue(); ?>
                    </td>
                </tr>
                <?php } ?>
            </table>
        </div>
       <?php } ?>
    <div class="flex pt-3 pb-3 mb-1 md:flex-row">
        <h3 class="text-gray-900 text-2xl title-font font-bold text-left w-full max-[1024px]:text-lg">
            <?= $escaper->escapeHtml(__('Specifications')) ?>
        </h3>
    </div>
    <div class="table-wrapper overflow-x-auto" id="product-attributes" x-data="{ showingall: false }">
        <table class="additional-attributes text-[#3D3D3D] text-[16px] w-full">
            <?php
            // show attr from xml catalog_product_view
            foreach ($block->getAttributes() as $attributeConfig) {
                $attribute = $attributesViewModel->getAttributeFromLayoutConfig($attributeConfig); ?>
                <?php if ($value = $attribute['value'] ?? null) { ?>
                    <tr class="border-b border-gray-300 last:border-b-0">
                        <th class="col label w-1/2 py-2 px-2 text-left text-gray-700 font-normal product-attribute-label" scope="row">
                            <?= $escaper->escapeHtml($attribute['label']) ?>
                        </th>
                        <td class="col data w-1/2 py-2 pl-2 text-left text-gray-900 product-attribute-value">
                            <?= $escaper->escapeHtml($value) ?>
                        </td>
                    </tr>
                <?php } ?>
            <?php } ?>
            <?php
            $showingcount = 0;
            $statusshowing = 'false';
            // show attr from dynamic attr aditional
            foreach ($attributes as $attribute):
                $statusshowing = ($showingcount < 3) ? "{'flex': true }" : "{'hidden': showingall ,'flex': !showingall }";
                ?>
                <tr class="border-b border-gray-300 last:border-b-0" :class="<?= $statusshowing; ?>">
                    <th class="col label w-1/2 py-2 px-2 text-left text-gray-700 font-normal product-attribute-label" scope="row">
                        <?= $escaper->escapeHtml($attribute['label']) ?>
                    </th>
                    <?php //dd($attribute['value']);
                    ?>
                    <td class="col data w-1/2 py-2 pl-2 text-left text-gray-900 product-attribute-value" data-th="<?= $escaper->escapeHtmlAttr($attribute['label']) ?>">
                        <?= /* @noEscape */
                        $block->getProductAction()->productAttribute($product, $attribute['value'], $attribute['code']) ?>
                    </td>
                </tr>
                <?php
                $showingcount += 1;
            endforeach; ?>
        </table>
        <div class="flex pt-1 pb-3 mb-1 md:flex-row toon-allen-spec">
            <a class="mt-4 text-blue-picton" @click="showingall = !showingall">
                <span :class="{'flex items-center': showingall, 'hidden' : !showingall}">
                    <?= $heroiconsSolid->renderHtml('plus', 'text-blue-picton', 16, 16); ?>
                    <?= $escaper->escapeHtml(__("Show all specifications")) ?></span>
                <span :class="{'flex items-center': !showingall, 'hidden' : showingall}">
                    <?= $heroiconsSolid->renderHtml('chevron-up', 'text-blue-picton', 20, 20); ?>
                    <?= $escaper->escapeHtml(__("Show less specifications")) ?>
                </span>
            </a>
        </div>
    </div>

<?php endif; ?>
