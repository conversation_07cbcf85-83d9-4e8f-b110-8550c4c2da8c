<?php

/**
 * Merk n Filterclass by attr configurable product list on $filterShowByAttr
 * 18 Aug 2023
 * <EMAIL>
 */

use Magento\Framework\Escaper;
use Hyva\Theme\Model\ViewModelRegistry;

/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var \Hyva\Theme\ViewModel\Modal $modalViewModel */
$modalViewModel = $viewModels->require(\Hyva\Theme\ViewModel\Modal::class);
?>
<?php
$_product = $block->getProduct();
$product = $_product;

$filterShowByAttr = ["merk_filter", "filter_klasse", "filterklasse_1", "filterklasse_2", "website_recommends", "specifiek_type", "bypass", "category_ids", "is_salable"];

$stockitem  = $block->getProductAction()->getStockItem($product->getId(), $product->getStore()->getWebsiteId());
$backorders = $stockitem->getBackorders();
$qty = (int)$stockitem->getQty();
$specifiek_type_label = $block->getProductAction()->getStoreSpecificAttributeLabel($product->getId(), "specifiek_type");
$merk_filter_label = $block->getProductAction()->getStoreSpecificAttributeLabel($product->getId(), "merk_filter");
$filter_klasse_label = $block->getProductAction()->getStoreSpecificAttributeLabel($product->getId(), "filter_klasse");
?>
<div>
    <?php
    $getInventoryStatus = ($stockitem->getIsInStock() ?? "");
    $merk_filterData = empty($product->getCustomAttribute("merk_filter")) ? '' : $product->getCustomAttribute("merk_filter")->getValue(); // ($product->getCustomAttribute("merk_filter")->getValue() ?? ""); //
    $filter_klasseData = empty($product->getCustomAttribute("filter_klasse")) ? '' : $product->getCustomAttribute("filter_klasse")->getValue(); // ($product->getCustomAttribute("filter_klasse")->getValue() ?? ""); //
    echo '<label prod="show" inv-status="'.$getInventoryStatus .'" merk_filter="' . $merk_filterData . '" filter_klasse="'. $filter_klasseData .'"></label>';
    // don't show filter if merk n filter_klasse not filled, n product not configure product
    if ($product->getCustomAttribute("merk_filter") != null && ($product->getCustomAttribute("filter_klasse") != null || $product->getCustomAttribute("filterklasse_1") != null) && $product->getTypeId() != 'configurable' && ($product->getInventoryStatus() != 126)):
        //store default product attr merk, filterklasse n etc to this array
        $productShowingArrayATTR = [];
        foreach ($filterShowByAttr as $ff => $xyz) {
            if ($xyz == "is_salable") {
                $ParentTemp = ($product->getInventoryStatus() != 126) ? 1 : 0;
                $productShowingArrayATTR[$xyz] = ($ParentTemp) ? 1 : 0; // 1 = product salelable, 0 = not
            } else {
                $ParentTemp = $product->getCustomAttribute($xyz);
                $productShowingArrayATTR[$xyz] = ($ParentTemp != null) ? $ParentTemp->getValue() : "-";
            }
            // get all filterklasse form same categori to array
            if ($xyz == "filter_klasse" && $productShowingArrayATTR[$xyz] == "-") {
                $productShowingArrayATTR[$xyz] = $product->getCustomAttribute('filterklasse_1')->getValue() . ' - ' . $product->getCustomAttribute('filterklasse_2')->getValue();
            }
        }
        $productShowingArrayATTR['storewebsite_id'] = $product->getStore()->getWebsiteId();
        $productShowingArrayATTR['productwebsite_ids'] = $product->getWebsiteIds();
        $_currentCat = $block->getCategoryAction()->getCurrentCategory();
        $_currentCatEntityId = [];
        // if ($_currentCat == null) {
            $_currentCatEntityId = $product->getCategoryIds();
            $productShowingArrayATTR['category_ids'] =  $_currentCatEntityId;
            // echo "_currentCat null . ". json_encode($product->getCategoryIds());
        // }
        // else{
        //     $_currentCatEntityId[0] = $_currentCat->getId();
        //     if($block->getCategoryAction()->getCatLevel($_currentCat->getId()) == 5 && empty($block->getProductAction()->getProductCollectionByCategories($productShowingArrayATTR['category_ids'])->getSize() )){
        //         $productShowingArrayATTR['category_ids'] =  $_currentCatEntityId;
        //         // echo "_currentCat not null with _currentCatEntityId ".json_encode($productShowingArrayATTR['category_ids']);
        //     }else{
        //         $productShowingArrayATTR['category_ids'] =  $product->getCategoryIds();
        //         // echo "_currentCat not null with product->getCategoryIds() ".json_encode($productShowingArrayATTR['category_ids']);
        //     }
            
        // }
        $productShowingArrayATTR['category_ids'] = $block->getCategoryAction()->getCatLevelStatus($productShowingArrayATTR['category_ids']);
        
        // echo "call categoryProductsSameLevel with id : ". json_encode($productShowingArrayATTR['category_ids']);
        // echo '<br> with level cat '.$block->getCategoryAction()->getCatLevel($productShowingArrayATTR['category_ids'][0]);
        // get Product on same category level ( cat index 0)
        $categoryProductsSameLevel = $block->getProductAction()->getProductCollectionByCategories($productShowingArrayATTR['category_ids']);
        $class_label_not_select = "swatch-option relative border-2 shadow-sm cursor-pointer select-none bg-container-lighter product-option-value-label rounded-lg border-container-darker";
        $class_label_selected = "swatch-option relative border-2 shadow-sm cursor-pointer select-none bg-container-lighter product-option-value-label rounded-lg border-container-darker border-container-lighter ring";

        $array_prodSameLevelCat = [];
        $iplus = 0;
        $option_attr_filterklasse = [];
        $option_attr_merk_filter = [];
        $option_attr_specifiek_type = [];
        foreach ($categoryProductsSameLevel as $productSameLevelCat) {
            $productType = $productSameLevelCat->getTypeId();
            $isSalable = ($productSameLevelCat->getInventoryStatus() != 126) ? 1 : 0; // 1 = product salelable, 0 = not
            // if prod not configure
            if ($productType != 'configurable') {
                // if prod same level with attr 'merk_filter' not null
                if ($productSameLevelCat->getCustomAttribute($filterShowByAttr[0]) != null) {
                    // check if product with same category view on this store view, on array of product WebsiteIds
                    if (($keycheck = array_search($block->getCategoryAction()->getStore()->getWebsiteId(), $productSameLevelCat->getWebsiteIds())) !== false) {
                        // check if same category or not with product view
                        $catID = [];
                        $diff = array_diff($productShowingArrayATTR['category_ids'], $productSameLevelCat->getCategoryIds());
                        if (empty($diff)) {
                            $catID = $productShowingArrayATTR['category_ids'];
                        } else {
                            continue;
                        }
                        if ($catID == $productShowingArrayATTR['category_ids']) {
                            // each filter attr
                            foreach ($filterShowByAttr as $fff => $xyza) {
                                // get value of attr
                                $SameCatProdTemp = $productSameLevelCat->getCustomAttribute($xyza);
                                $array_prodSameLevelCat[$iplus][$xyza] = ($SameCatProdTemp != null) ? $SameCatProdTemp->getValue() : "-";
                                // get all filterklasse form same categori to array
                                if ($xyza == "filter_klasse") {
                                    $temp_value = "";
                                    //if filter_klasse attr null, so set filterklasse_1 n _2
                                    if ($array_prodSameLevelCat[$iplus][$xyza] == "-") {
                                        if ($productSameLevelCat->getCustomAttribute('filterklasse_1')) {
                                            $temp_value = $productSameLevelCat->getCustomAttribute('filterklasse_1')->getValue() . ' - ' . $productSameLevelCat->getCustomAttribute('filterklasse_2')->getValue();
                                        }
                                    } else {
                                        $temp_value = $array_prodSameLevelCat[$iplus][$xyza];
                                    }
                                    $array_prodSameLevelCat[$iplus][$xyza] = $temp_value;
                                    if (!in_array($temp_value, $option_attr_filterklasse)) {
                                        $option_attr_filterklasse[$iplus] = $temp_value;
                                    }
                                }
                                if ($xyza == "merk_filter") {
                                    $temp_value = $array_prodSameLevelCat[$iplus][$xyza];
                                    $array_prodSameLevelCat[$iplus][$xyza] = $temp_value;
                                    if (!in_array($temp_value, $option_attr_merk_filter)) {
                                        $option_attr_merk_filter[$iplus] = $temp_value;
                                    }
                                }
                                if ($xyza == "specifiek_type") {
                                    $temp_value = $array_prodSameLevelCat[$iplus][$xyza];
                                    $array_prodSameLevelCat[$iplus][$xyza] = $temp_value;
                                    if (!in_array($temp_value, $option_attr_specifiek_type)) {
                                        $option_attr_specifiek_type[$iplus] = $temp_value;
                                    }
                                }
                            }
                            $array_prodSameLevelCat[$iplus]['catid'] = $catID;
                            $array_prodSameLevelCat[$iplus]['category_ids'] = $catID;
                            $array_prodSameLevelCat[$iplus]['storewebsite_id'] = $productSameLevelCat->getStore()->getWebsiteId();
                            $array_prodSameLevelCat[$iplus]['name'] = $productSameLevelCat->getName();
                            $array_prodSameLevelCat[$iplus]['url'] = $productSameLevelCat->getProductUrl();
                            $array_prodSameLevelCat[$iplus]['sku'] = $productSameLevelCat->getSku();
                            $array_prodSameLevelCat[$iplus]['is_salable'] = $isSalable;
                            $iplus++;
                        }
                    }
                }
            }
        }
        $quantities = array_column($array_prodSameLevelCat, 'is_salable');
        array_multisort($quantities, SORT_DESC, $array_prodSameLevelCat);
        rsort($array_prodSameLevelCat);
        $option_attr_filterklasse = array_values($option_attr_filterklasse);
        $option_attr_merk_filter = array_values($option_attr_merk_filter);
        $option_attr_specifiek_type = array_values($option_attr_specifiek_type);
        // echo "<br><br>product showing : ". json_encode(array_values($productShowingArrayATTR));
        // echo "<br><br><br>arr same level : " . json_encode($array_prodSameLevelCat);
        // echo "<br><br><br>attr merk option : ". json_encode($option_attr_merk_filter);
        // echo "<br><br><br>attr filterklasse option : ". json_encode($option_attr_filterklasse);
        // echo "<br><br><br>attr specifiek_type : ". json_encode($option_attr_specifiek_type);
        
        ?>
        <div class="relative mb-1">
            <div class="flex flex-col-reverse">
                <div class="swatch-attribute border-container overflow-x-auto overflow-y-auto pb-0 w-full min-h-14 ">
                    <div class="flex flex-col items-start py-4 sm:py-1 w-full border-gray-300 merk-filter-container max-[430px]:!pt-6.25" x-data="Object.assign({}, hyva.modal())">
                        <label class="basis-full text-left text-gray-700 label product-option-label flex flex-wrap h-11" for="attribute-merk">
                            <span class="font-bold md:font-bold title-filters text-lg max-[1024px]:text-base max-[1024px]:!text-[#16365B]" style="color: #002C4B;">
                                <?= _($merk_filter_label) ?>
                            </span>
                        </label>
                        <div class="basis-full w-3/4 sm:w-full text-left text-md text-gray-900 product-option-values">
                            <div class="flex items-center -mx-4 max-[1024px]:mx-0 space-x-4 swatch-attribute-options h-11" role="radiogroup">
                                <?php
                                $attr_code_to_view = 'merk_filter';
                                $option_attr = null;
                                if ($attr_id = $product->getCustomAttribute($attr_code_to_view)) {
                                    $option_attr = $option_attr_merk_filter;  ?>
                                    <div class="filter-option flex">
                                        <?php
                                        // option_attr = all data option attr (merk_filter)
                                        $count_merk_showing = 0;
                                        $prev_filter_klasse = "";
                                        $prev_filter_merk = "";
                                        $temp_merk_notselected = "";
                                        $count_array_prodSameLevelCat = count($option_attr_merk_filter);
                                        foreach ($option_attr as $option) {
                                            // array_prodSameLevelCat = list product in same level category
                                            foreach ($array_prodSameLevelCat as $x => $x_value) {
                                                // if (id merk = product->id merk), we want to search selected merk
                                                if ($option == $productShowingArrayATTR['merk_filter']) {
                                                    // if (array_prodSameLevelCat id filter_klasse = product->id filter_klasse)
                                                    if ($x_value['filter_klasse'] == $productShowingArrayATTR['filter_klasse']) {
                                                            // if (array_prodSameLevelCat sku filterklasse = product->sku) to make selected
                                                        if ($x_value['sku'] == $product->getSku()) {
                                                            echo '
                                                                <div class="relative contents border-b border-dotted border-[#002C4B] tooltip"
                                                                ' . 'fil="' . $x_value['filter_klasse'] . '" merk_filter="' . $x_value['merk_filter'] . '" specifiek_type="' . $x_value['specifiek_type'] . '" catid="' . json_encode($x_value['catid'], JSON_UNESCAPED_UNICODE) . '" is_salable="' . $x_value['is_salable']. '" bypass="' . $x_value['bypass'] . '">' .
                                                                '<a class="tooltip-a-width ' . $class_label_not_select . 'swatch-option relative border-2 shadow-sm cursor-pointer select-none bg-container-lighter product-option-value-label rounded-lg border-container-darker border-container-lighter ring "  style="position:relative;">
                                                                        <input type="radio" class="input-filter-option inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input" style="z-index:-1" value="6" aria-label="choose">
                                                                        <div class="filter-options whitespace-nowrap px-2" style="color: #002C4B">' . $option . '</div>';
                                                            if ((bool)$x_value['website_recommends']) {
                                                                echo
                                                                '<span class="invisible min-w-full text-center rounded px-[5px] absolute z-10 top-full left-0 text-sm flex items-center justify-center text-[#5DC2EF] m-0 atooltiptext ' . (strlen($option) < 12 ? '!w-[137px] !left-[-15px]' : '') . '">
                                                                                <div class="flex flex-nowrap place-content-center rounded-md px-3 py-1 tooltiptext-div" style="background-color: #002C4B;">'. $escaper->escapeHtml(__("tops")) .' <span class="text-white pl-[5px] max-[1024px]:text-xs">'. $escaper->escapeHtml(__("choice")) .'</span>
                                                                                    <button type="button" class="text-xs text-blue-picton ml-2" >
                                                                                            ' . $heroicons->informationCircleHtml('w-4 h-4') . '
                                                                                    </button>   
                                                                                </div>
                                                                            </span>';
                                                            }
                                                            echo '    
                                                                    </a>
                                                                </div>';
                                                            $count_merk_showing += 1;
                                                            continue;
                                                        }
                                                    }
                                                    $prev_filter_klasse = $x_value['filter_klasse'];
                                                    $prev_filter_merk = $x_value['merk_filter'];
                                                }
                                                // not selected
                                                elseif ($option == $x_value['merk_filter']) {
                                                    // echo "<br>merk : ". $option . " " . $x_value['merk_filter'] . ", filter_klasse : " . $x_value['filter_klasse'] . " " . $productShowingArrayATTR['filter_klasse'] . " count merk : " . $count_array_prodSameLevelCat;
                                                    $temp_merk_notselected =  '
                                                            <div class="relative contents border-b border-dotted border-[#002C4B] tooltip"
                                                            ' . 'fil="' . $x_value['filter_klasse'] . '" merk_filter="' . $x_value['merk_filter'] . '" specifiek_type="' . $x_value['specifiek_type'] . '" catid="' . json_encode($x_value['catid'], JSON_UNESCAPED_UNICODE) . '" is_salable="' . $x_value['is_salable']. '" bypass="' . $x_value['bypass'] . '">' .
                                                                        '<a href="' . $x_value['url'] . '" class="tooltip-a-width ' . $class_label_not_select . ' options active" style="position:relative;">
                                                                    <input type="radio" class="input-filter-option inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input" style="z-index:-1" value="6" aria-label="choose">
                                                                    <div class="filter-options whitespace-nowrap px-2" style="color: #002C4B;">' . $option . '</div>';
                                                    if ((bool)$x_value['website_recommends']) {
                                                        $temp_merk_notselected .=
                                                        '<span class="invisible min-w-full text-center rounded px-[5px] absolute z-10 top-full left-0 text-sm flex items-center justify-center text-[#5DC2EF] m-0 atooltiptext ' . (strlen($option) < 12 ? '!w-[137px] !left-[-15px]' : '') . '">
                                                                            <div class="flex flex-nowrap place-content-center rounded-md px-3 py-1 tooltiptext-div" style="background-color: #002C4B;">'. $escaper->escapeHtml(__("tops")) .' <span class="text-white pl-[5px] max-[1024px]:text-xs">'. $escaper->escapeHtml(__("choice")) .'</span>
                                                                                <button type="button" class="text-xs text-blue-picton ml-2" >
                                                                                        ' . $heroicons->informationCircleHtml('w-4 h-4') . '
                                                                                </button>   
                                                                            </div>
                                                                        </span>';
                                                    }
                                                                    $temp_merk_notselected .= '    
                                                                </a>
                                                            </div>';
                                                    if ($x_value['filter_klasse'] == $productShowingArrayATTR['filter_klasse']) {
                                                        if ($prev_filter_klasse != $productShowingArrayATTR['filter_klasse']) {
                                                            if ($x_value['is_salable'] == '1') {
                                                                echo $temp_merk_notselected;
                                                                $count_merk_showing += 1;
                                                                $prev_filter_klasse = $x_value['filter_klasse'];
                                                                $prev_filter_merk = $x_value['merk_filter'];
                                                                continue;
                                                            }
                                                        } else {
                                                            $prev_filter_klasse = $x_value['filter_klasse'];
                                                            $prev_filter_merk = $x_value['merk_filter'];
                                                            continue;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        // if merk count notshowing == count_array_prodSameLevelCat
                                        if ($count_merk_showing < $count_array_prodSameLevelCat) {
                                            echo $temp_merk_notselected;
                                        }
                                        ?>
                                    </div>
                                <?php }
                                ?>
                            </div>
                        </div>

                    </div>
                    <div class="flex flex-col items-start py-4 max-[425px]:!pb-0 sm:py-1 w-full border-gray-300 filterklasses-filter-container" x-data="Object.assign({}, hyva.modal())">
                        <label class="basis-full text-left text-gray-700 label product-option-label flex flex-wrap h-11" for="attribute-merk">
                            <span class="font-bold md:font-bold title-filters text-lg max-[1024px]:text-base max-[1024px]:!text-[#16365B]" style="color: #002C4B;">
                                <?= _($filter_klasse_label) ?>
                            </span>
                            <?php /** @var Hyva\Theme\ViewModel\HeroiconsOutline $heroicons */
                            $heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
                            ?>
                            <div class="make-modal">
                                <?php
                                $contentModal = '';
                                $classModal   = 'justify-center';
                                $classWidth = 'w-6/12';
                                $classMargin = 'mx-';
                                $dialogClass  = 'modal-view-product';
                                $classModal   = 'justify-center';
                                $classWidth = 'w-10/12';
                                $classMargin = 'mx-filterklasse';
                                $contentModal = $block->getLayout()
                                    ->createBlock('Magento\Cms\Block\Block')
                                    ->setBlockId('overlay-filterklasse-uitleg')
                                    ->toHtml();
                                $modal = $modalViewModel->createModal()->withContent('
                                            <div id="the-label' . $classMargin . '" class="relative w-full h-full md:h-auto text-blue-prussian text-justify">
                                                <!-- Modal header -->
                                                <div class="flex items-start justify-end pb-4 lg:pl-24">
                                                    <button class="close-button ml-6 lg:ml-44 -mt-4" @click="hide">
                                                        <span aria-hidden="true" class="s-close-button text-2xl !text-blue-picton font-bold text-blue-picton">&times;</span><span class="sr-only">Close</span>
                                                    </button>
                                                </div>
                                                <!-- Modal body -->
                                                <div class="body-content-modal pr-6 py-4 body-content-scroll content-modal-attr">
                                                    ' . $contentModal . '
                                                </div>
                                            </div>' . '<script>
    
                                            (() => {
    
                                                function myEventOverlayAdviesCallback() {
                                                    const svg_arrow = \'<svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" /></svg>\';
                                                    const arrow_down = document.getElementsByClassName("arrow-down");
                                                    for (let i = 0; i < arrow_down.length; i++) {
                                                        arrow_down[i].innerHTML = svg_arrow;
                                                    }
                                                }
                                                window.addEventListener(\'DOMContentLoaded\', myEventOverlayAdviesCallback);
                                                
                                                })();
                                            </script>')
                                    ->withContainerClasses('fixed', 'flex', $classModal, 'items-center', $classWidth, "!mx-auto ".$classMargin)
                                    ->withAriaLabelledby('the-label')
                                    ->addDialogClass($dialogClass); ?>
                                <button type="button" class="text-xs text-blue-picton ml-2" @click="<?= $escaper->escapeHtmlAttr($modal->getShowJs()) ?>">
                                    <?= $heroicons->informationCircleHtml('w-4 h-4') ?>
                                </button>
                                <?= /** @noEscape */ $modal; ?>
                            </div>
                        </label>
                        <div class="basis-full w-3/4 sm:w-full text-left text-md text-gray-900 product-option-values min-[1025px]:mb-2.5">
                            <div class="flex items-center -mx-4 max-[1024px]:mx-0 space-x-4 swatch-attribute-options h-11" role="radiogroup">
                                <?php
                                $attr_code_to_view = 'filter_klasse';
                                $option_attr = null;
                                $attr_filter_array = [];

                                // $attr_id = $product->getCustomAttribute($attr_code_to_view);
                                if ($productShowingArrayATTR['filter_klasse'] != null) {
                                    $option_attr = $option_attr_filterklasse;
                                    echo '<div class="filter-option flex">';
                                    // option_attr = all data option attr (filter_klasse)
                                    foreach ($option_attr as $option) {
                                        // array_prodSameLevelCat = list product in same level category
                                        $x_value_filterklasse_before = '';
                                        foreach ($array_prodSameLevelCat as $x => $x_value) {
                                            // if (id filter_klasse = product->id filter_klasse), we want to search selected filter_klasse
                                            if ($option == $productShowingArrayATTR['filter_klasse']) {
                                                // if (array_prodSameLevelCat id merk = product->id merk)
                                                if ($x_value['merk_filter'] == $productShowingArrayATTR['merk_filter']) {
                                                        // if (array_prodSameLevelCat sku merk = product->sku) to make selected
                                                    if ($x_value['sku'] == $product->getSku()) {
                                                        $x_value_filterklasse_before = $x_value['filter_klasse'];
                                                        $labeled[0] = "";
                                                        $labeled[1] = $option;
                                                        echo '
                                                                <div class="relative contents border-b border-dotted border-[#002C4B] tooltip"
                                                                ' . 'fil="' . $x_value['filter_klasse'] . '" merk_filter="' . $x_value['merk_filter'] . '" specifiek_type="' . $x_value['specifiek_type'] . '" catid="' . json_encode($x_value['catid'], JSON_UNESCAPED_UNICODE) . '" is_salable="' . $x_value['is_salable']. '" bypass="' . $x_value['bypass'] . '">' .
                                                            '<a class="tooltip-a-width ' . $class_label_not_select . 'swatch-option relative border-2 shadow-sm cursor-pointer select-none bg-container-lighter product-option-value-label rounded-lg border-container-darker border-container-lighter ring "  style="position:relative;">
                                                                        <input type="radio" class="input-filter-option inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input" style="z-index:-1" value="6" aria-label="choose">
                                                                        <div class="flex filter-options whitespace-nowrap px-2" style="color: #002C4B"><div class="labeled-hide-mobile">' .  $labeled[0] . '</div> ' . $labeled[1] . '</div>';
                                                        if ((bool)$x_value['website_recommends']) {
                                                            echo
                                                            '<span class="invisible min-w-full text-center rounded px-[5px] absolute z-10 top-full left-0 text-sm flex items-center justify-center text-[#5DC2EF] m-0 tooltiptext  ' . (strlen($option) < 12 ? '!w-[137px] !left-[-15px]' : '') . '">
                                                                                <div class="flex flex-nowrap place-content-center rounded-md px-3 py-1 tooltiptext-div" style="background-color: #002C4B;">'. $escaper->escapeHtml(__("tops")) .' <span class="text-white pl-[5px] max-[1024px]:text-xs">'. $escaper->escapeHtml(__("choice")) .'</span>
                                                                                    <button type="button" class="text-xs text-blue-picton ml-2" >
                                                                                        ' . $heroicons->informationCircleHtml('w-4 h-4') . '
                                                                                    </button>   
                                                                                </div>
                                                                            </span>';
                                                        }
                                                        echo '    
                                                                    </a>
                                                                </div>';
                                                        // continue because found it
                                                        continue;
                                                    }
                                                }
                                            }
                                            // not selected
                                            // $x_value_filterklasse_before for check if filterklase same name or not, is same continue next product
                                            elseif ($option == $x_value['filter_klasse'] && $x_value['filter_klasse'] != $x_value_filterklasse_before) {
                                                if ($x_value['merk_filter'] == $productShowingArrayATTR['merk_filter']) {
                                                    if ($x_value['catid'] == $productShowingArrayATTR['category_ids']) {
                                                        if ($x_value['is_salable'] == '1') {
                                                            $x_value_filterklasse_before = $x_value['filter_klasse'];
                                                            $labeled[0] = "";
                                                            $labeled[1] = $option;
                                                            echo '
                                                                    <div class="relative contents border-b border-dotted border-[#002C4B] tooltip"
                                                                    ' . 'fil="' . $x_value['filter_klasse'] . '" merk_filter="' . $x_value['merk_filter'] . '" specifiek_type="' . $x_value['specifiek_type'] . '" catid="' . json_encode($x_value['catid'], JSON_UNESCAPED_UNICODE) . '" is_salable="' . $x_value['is_salable']. '" bypass="' . $x_value['bypass'] . '">' .
                                                                '<a href="' . $x_value['url'] . '" class="tooltip-a-width ' . $class_label_not_select . ' options active" style="position:relative;">
                                                                            <input type="radio" class="input-filter-option inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input" style="z-index:-1" value="6" aria-label="choose">
                                                                            <div class="flex filter-options whitespace-nowrap px-2" style="color: #002C4B"><div class="labeled-hide-mobile">' .  $labeled[0] . '</div> ' . $labeled[1] . '</div>';
                                                            if ((bool)$x_value['website_recommends']) {
                                                                echo
                                                                '<span class="invisible min-w-full text-center rounded px-[5px] absolute z-10 top-full left-0 text-sm flex items-center justify-center text-[#5DC2EF] m-0 atooltiptext ' . (strlen($option) < 12 ? '!w-[137px] !left-[-15px]' : '') . '">
                                                                            <div class="flex flex-nowrap place-content-center rounded-md px-3 py-1 tooltiptext-div" style="background-color: #002C4B;">'. $escaper->escapeHtml(__("tops")) .' <span class="text-white pl-[5px] max-[1024px]:text-xs">'. $escaper->escapeHtml(__("choice")) .'</span>
                                                                                <button type="button" class="text-xs text-blue-picton ml-2" >
                                                                                        ' . $heroicons->informationCircleHtml('w-4 h-4') . '
                                                                                </button>   
                                                                            </div>
                                                                        </span>';
                                                            }
                                                            echo '    
                                                                    </a>
                                                                </div>';
                                                            continue;
                                                        } else {
                                                            $x_value_filterklasse_before = $x_value['filter_klasse'];
                                                            $labeled[0] = "";
                                                            $labeled[1] = $option;
                                                            echo '
                                                                    <div class="relative contents border-b border-dotted border-[#002C4B] tooltip"
                                                                    ' . 'fil="' . $x_value['filter_klasse'] . '" merk_filter="' . $x_value['merk_filter'] . '" specifiek_type="' . $x_value['specifiek_type'] . '" catid="' . json_encode($x_value['catid'], JSON_UNESCAPED_UNICODE) . '" is_salable="' . $x_value['is_salable']. '" bypass="' . $x_value['bypass'] . '">' .
                                                                '<a href="' . $x_value['url'] . '" style="margin-left:0px !important;" class="bg-slate-200 pointer-events-none tooltip-a-width ' . $class_label_not_select . ' options active" style="position:relative;">
                                                                            <input type="radio" class="input-filter-option inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input" style="z-index:-1" value="6" aria-label="choose">
                                                                            <div class="flex filter-options whitespace-nowrap px-2" style="color: #002C4B"><div class="labeled-hide-mobile">' .  $labeled[0] . '</div> ' . $labeled[1] . '</div>';
                                                            if ((bool)$x_value['website_recommends']) {
                                                                echo
                                                                '<span class="invisible min-w-full text-center rounded px-[5px] absolute z-10 top-full left-0 text-sm flex items-center justify-center text-[#5DC2EF] m-0 atooltiptext ' . (strlen($option) < 12 ? '!w-[137px] !left-[-15px]' : '') . '">
                                                                            <div class="flex flex-nowrap place-content-center rounded-md px-3 py-1 tooltiptext-div" style="background-color: #002C4B;">'. $escaper->escapeHtml(__("tops")) .' <span class="text-white pl-[5px] max-[1024px]:text-xs">'. $escaper->escapeHtml(__("choice")) .'</span>
                                                                                <button type="button" class="text-xs text-blue-picton ml-2" >
                                                                                        ' . $heroicons->informationCircleHtml('w-4 h-4') . '
                                                                                </button>   
                                                                            </div>
                                                                        </span>';
                                                            }
                                                            echo '    
                                                                    </a>
                                                                </div>';
                                                            continue;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    echo '</div><br>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    <?php
                    $attr_code_to_view = 'merk_filter';
                    $bypass_status = [
                        'yes' => [false, ''],
                        'no' => [false, '']
                    ];
                    $specifiek_type_status = [
                        'yes' => [false, ''],
                        'no' => [false, '']
                    ];
                    if ($attr_id = $product->getCustomAttribute($attr_code_to_view)) {
                        foreach ($array_prodSameLevelCat as $x => $x_value) {
                            if ($x_value['merk_filter'] == $productShowingArrayATTR['merk_filter']) {
                                if ($x_value['filter_klasse'] == $productShowingArrayATTR['filter_klasse']) {
                                    if ($x_value['catid'] == $productShowingArrayATTR['category_ids']) {
                                        if ($x_value['specifiek_type'] == $option_attr_specifiek_type[0]) {
                                            $specifiek_type_status['yes'][0] = true;
                                            $specifiek_type_status['yes'][1] = $x_value['url'];
                                            $specifiek_type_status['yes'][2] = $x_value['merk_filter'];
                                            $specifiek_type_status['yes'][3] = $x_value['filter_klasse'];
                                            $specifiek_type_status['yes'][4] = $x_value['catid'];
                                            $specifiek_type_status['yes'][5] = $x_value['specifiek_type'];
                                            $specifiek_type_status['yes'][6] = $x_value['is_salable'];
                                            $specifiek_type_status['yes'][7] = $x_value['bypass'];
                                        } elseif ($x_value['specifiek_type'] == $option_attr_specifiek_type[1]) {
                                            $specifiek_type_status['no'][0] = true;
                                            $specifiek_type_status['no'][1] = $x_value['url'];
                                            $specifiek_type_status['no'][2] = $x_value['merk_filter'];
                                            $specifiek_type_status['no'][3] = $x_value['filter_klasse'];
                                            $specifiek_type_status['no'][4] = $x_value['catid'];
                                            $specifiek_type_status['no'][5] = $x_value['specifiek_type'];
                                            $specifiek_type_status['no'][6] = $x_value['is_salable'];
                                            $specifiek_type_status['no'][7] = $x_value['bypass'];
                                        }
                                        if ($x_value['bypass'] == 1) {
                                            $bypass_status['yes'][0] = true;
                                            $bypass_status['yes'][1] = $x_value['url'];
                                            $bypass_status['yes'][2] = $x_value['merk_filter'];
                                            $bypass_status['yes'][3] = $x_value['filter_klasse'];
                                            $bypass_status['yes'][4] = $x_value['catid'];
                                            $bypass_status['yes'][5] = $x_value['specifiek_type'];
                                            $bypass_status['yes'][6] = $x_value['is_salable'];
                                            $bypass_status['yes'][7] = $x_value['bypass'];
                                        } elseif ($x_value['bypass'] == 0) {
                                            $bypass_status['no'][0] = true;
                                            $bypass_status['no'][1] = $x_value['url'];
                                            $bypass_status['no'][2] = $x_value['merk_filter'];
                                            $bypass_status['no'][3] = $x_value['filter_klasse'];
                                            $bypass_status['no'][4] = $x_value['catid'];
                                            $bypass_status['no'][5] = $x_value['specifiek_type'];
                                            $bypass_status['no'][6] = $x_value['is_salable'];
                                            $bypass_status['no'][7] = $x_value['bypass'];
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if ($productShowingArrayATTR["bypass"] != null && ($bypass_status['yes'][0] == true && $bypass_status['no'][0] == true)):
                        ?>
                        <div class="flex flex-col items-start py-4 max-[425px]:!pb-0 sm:py-1 w-full border-gray-300 bypass-filter-container" x-data="Object.assign({}, hyva.modal())">
                            <label class="basis-full text-left text-gray-700 label product-option-label flex flex-wrap h-11" for="attribute-merk">
                                <span class="font-bold md:font-bold title-filters text-lg max-[1024px]:text-base max-[1024px]:!text-[#16365B]" style="color: #002C4B;">
                                    <?= __("Bypass"); ?>
                                </span>
                                <?php /** @var Hyva\Theme\ViewModel\HeroiconsOutline $heroicons */
                                $heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
                                ?>
                                <div class="make-modal">
                                    <?php
                                    $contentModal = '';
                                    $classModal   = 'justify-center';
                                    $classWidth = 'w-6/12';
                                    $classMargin = 'mx-';
                                    $dialogClass  = 'modal-view-product-bypass';
                                    $classModal   = 'justify-center';
                                    $classWidth = 'w-10/12';
                                    $classMargin = 'mx-bypass';
                                    $contentModal = $block->getLayout()
                                        ->createBlock('Magento\Cms\Block\Block')
                                        ->setBlockId('overlay-bypass')
                                        ->toHtml();
                                    $modal = $modalViewModel->createModal()->withContent('
                                                    <div id="the-label' . $classMargin . '" class="relative w-full h-full md:h-auto text-blue-prussian text-justify">
                                                        <!-- Modal header -->
                                                        <div class="flex items-start justify-end pb-4 lg:pl-24">
                                                            <button class="close-button ml-6 lg:ml-44 -mt-4" @click="hide">
                                                                <span aria-hidden="true" class="s-close-button text-2xl !text-blue-picton font-bold text-blue-picton">&times;</span><span class="sr-only">Close</span>
                                                            </button>
                                                        </div>
                                                        <!-- Modal body -->
                                                        <div class="body-content-modal pr-6 md:py-4 body-content-scroll content-modal-attr">
                                                            ' . $contentModal . '
                                                        </div>
                                                    </div>' . '<script>
                                                    (() => {
                                                        function myEventOverlayAdviesCallback() {
                                                            const svg_arrow = \'<svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" /></svg>\';
                                                            const arrow_down = document.getElementsByClassName("arrow-down");
                                                            for (let i = 0; i < arrow_down.length; i++) {
                                                                arrow_down[i].innerHTML = svg_arrow;
                                                            }
                                                        }
                                                        window.addEventListener(\'DOMContentLoaded\', myEventOverlayAdviesCallback);
                                                        })();
                                                    </script>')
                                        ->withContainerClasses('fixed', 'flex', $classModal, 'items-center', $classWidth, $classMargin)
                                        ->withAriaLabelledby('the-label')
                                        ->addDialogClass($dialogClass); ?>
                                    <button type="button" class="text-xs text-blue-picton ml-2" @click="<?= $escaper->escapeHtmlAttr($modal->getShowJs()) ?>">
                                        <?= $heroicons->informationCircleHtml('w-4 h-4') ?>
                                    </button>
                                    <?= /** @noEscape */ $modal; ?>
                                </div>
                            </label>
                            <div class="basis-full w-3/4 sm:w-full text-left text-md text-gray-900 product-option-values min-[1024px]:mb-2.5">
                                <div class="flex items-center -mx-4 max-[1024px]:mx-0 space-x-4 swatch-attribute-options h-11" role="radiogroup">
                                    <?php
                                    $attr_code_to_view = 'bypass';
                                    $option_attr = null;
                                    $attr_filter_array = [];
                                    $option_attr = $product->getCustomAttribute($attr_code_to_view);
                                    $class_label_by_pass = $class_label_not_select;
                                    $class_met_bypass = '';
                                    $class_zonder_bypass = '';
                                    $class_selected = ' swatch-option relative border-2 shadow-sm cursor-pointer select-none bg-container-lighter product-option-value-label rounded-lg border-container-darker border-container-lighter ring ';
                                    $class_not_selected = ' options active';
                                    if ($option_attr->getValue() == "yes" || $option_attr->getValue() == "1") {
                                        $class_met_bypass = $class_selected;
                                        $class_zonder_bypass = $class_not_selected;
                                    } elseif ($option_attr->getValue() == "no" || $option_attr->getValue() == "0") {
                                        $class_met_bypass = $class_not_selected;
                                        $class_zonder_bypass = $class_selected;
                                    }

                                    echo '<div class="filter-option flex">';
                                    $labeled = 'Met bypass';
                                    if ($bypass_status['yes'][0]):
                                        echo '
                                            <div class="relative contents border-b border-dotted border-[#002C4B] tooltip"
                                            ' . 'fil="' . $bypass_status['yes'][3] . '" merk_filter="' . $bypass_status['yes'][2] . '" specifiek_type="' . $bypass_status['yes'][5] . '" catid="' . json_encode($bypass_status['yes'][4], JSON_UNESCAPED_UNICODE) . '" is_salable="' . $bypass_status['yes'][6] . '" bypass="' . $bypass_status['yes'][7] . '">' .
                                            '<a href="' . $bypass_status['yes'][1] . '" class="tooltip-a-width ' . $class_label_by_pass . $class_met_bypass . '"  style="position:relative;">
                                                <input type="radio" class="input-filter-option inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input" style="z-index:-1" value="6" aria-label="choose">
                                                <div class="flex filter-options whitespace-nowrap px-2" style="color: #002C4B">' . $labeled . '</div>';

                                        if ((bool)$x_value['website_recommends']) {
                                            echo
                                            '<span class="invisible min-w-full text-center rounded px-[5px] absolute z-10 top-full left-0 text-sm flex items-center justify-center text-[#5DC2EF] m-0 atooltiptext ' . (strlen($labeled) < 12 ? '!w-[137px] !left-[-15px]' : '') . '">
                                                                    <div class="flex flex-wrap place-content-center rounded-md px-3 py-1 tooltiptext-div" style="background-color: #002C4B;">'. $escaper->escapeHtml(__("tops")) .' <span class="text-white pl-[5px] max-[1024px]:text-xs">'. $escaper->escapeHtml(__("choice")) .'</span>
                                                                        <button type="button" class="text-xs text-blue-picton ml-2" >
                                                                                ' . $heroicons->informationCircleHtml('w-4 h-4') . '
                                                                        </button>   
                                                                    </div>
                                                                </span>';
                                        }
                                        echo '
                                                
                                                </a>
                                            </div>';
                                    endif;
                                    if ($bypass_status['no'][0]):
                                        $labeled ='Zonder bypass';
                                        echo '
                                            <div class="relative contents border-b border-dotted border-[#002C4B] tooltip"
                                            ' . 'fil="' . $bypass_status['no'][3] . '" merk_filter="' . $bypass_status['no'][2] . '" specifiek_type="' . $bypass_status['no'][5] . '" catid="' . json_encode($bypass_status['no'][4], JSON_UNESCAPED_UNICODE) . '" is_salable="' . $bypass_status['no'][6] .  '" bypass="' . $bypass_status['no'][7] . '">' .
                                            '<a href="' . $bypass_status['no'][1] . '" class="tooltip-a-width ' . $class_label_by_pass . $class_zonder_bypass . '" style="position:relative;">
                                                <input type="radio" class="input-filter-option inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input" style="z-index:-1" value="6" aria-label="choose">
                                                <div class="flex filter-options whitespace-nowrap px-2" style="color: #002C4B">' . $labeled . '</div>';
                                        if ((bool)$x_value['website_recommends']) {
                                            echo
                                            '<span class="invisible min-w-full text-center rounded px-[5px] absolute z-10 top-full left-0 text-sm flex items-center justify-center text-[#5DC2EF] m-0 atooltiptext ' . (strlen($labeled) < 12 ? '!w-[137px] !left-[-15px]' : '') . '">
                                                                    <div class="flex flex-wrap place-content-center rounded-md px-3 py-1 tooltiptext-div" style="background-color: #002C4B;">'. $escaper->escapeHtml(__("tops")) .' <span class="text-white pl-[5px] max-[1024px]:text-xs">'. $escaper->escapeHtml(__("choice")) .'</span>
                                                                        <button type="button" class="text-xs text-blue-picton ml-2" >
                                                                                ' . $heroicons->informationCircleHtml('w-4 h-4') . '
                                                                        </button>   
                                                                    </div>
                                                                </span>';
                                        }
                                        echo '
                                                </a>
                                            </div>';
                                    endif;
                                    echo '</div><br>';
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php
                    endif;
                    // SPesifik type
                    if ($productShowingArrayATTR["specifiek_type"] != null && ($specifiek_type_status['yes'][0] == true && $specifiek_type_status['no'][0] == true)):
                        ?>
                        <div class="flex flex-col items-start py-4 max-[425px]:!pb-0 sm:py-1 w-full border-gray-300 bypass-filter-container" x-data="Object.assign({}, hyva.modal())">
                            <label class="basis-full text-left text-gray-700 label product-option-label flex flex-wrap h-11" for="attribute-merk">
                                <span class="font-bold md:font-bold title-filters text-lg max-[1024px]:text-base max-[1024px]:!text-[#16365B]" style="color: #002C4B;">
                                    <?= _($specifiek_type_label); ?>
                                </span>
                            </label>
                            <div class="basis-full w-3/4 sm:w-full text-left text-md text-gray-900 product-option-values min-[1024px]:mb-2.5">
                                <div class="flex items-center -mx-4 max-[1024px]:mx-0 space-x-4 swatch-attribute-options h-11" role="radiogroup">
                                    <?php
                                    $attr_code_to_view = 'specifiek_type';
                                    $option_attr = null;
                                    $attr_filter_array = [];
                                    $option_attr = $product->getCustomAttribute($attr_code_to_view);
                                    $class_label_by_pass = $class_label_not_select;
                                    $class_met_bypass = '';
                                    $class_zonder_bypass = '';
                                    $class_selected = ' swatch-option relative border-2 shadow-sm cursor-pointer select-none bg-container-lighter product-option-value-label rounded-lg border-container-darker border-container-lighter ring ';
                                    $class_not_selected = ' options active';
                                    if ($specifiek_type_status['yes'][5] == $productShowingArrayATTR['specifiek_type']) {
                                        $class_met_bypass = $class_selected;
                                        $class_zonder_bypass = $class_not_selected;
                                    }
                                    if ($specifiek_type_status['no'][5] == $productShowingArrayATTR['specifiek_type']) {
                                        $class_met_bypass = $class_not_selected;
                                        $class_zonder_bypass = $class_selected;
                                    }

                                    echo '<div class="filter-option flex">';
                                    $labeled = $option_attr_specifiek_type[0];//'Met bypass';
                                    if ($specifiek_type_status['yes'][0]):
                                        echo '
                                            <div class="relative contents border-b border-dotted border-[#002C4B] tooltip"
                                            ' . 'fil="' . $specifiek_type_status['yes'][3] . '" merk_filter="' . $specifiek_type_status['yes'][2] . '" specifiek_type="' . $specifiek_type_status['yes'][5] . '" catid="' . json_encode($specifiek_type_status['yes'][4], JSON_UNESCAPED_UNICODE) . '" is_salable="' . $specifiek_type_status['no'][6] . '">' .
                                            '<a href="' . $specifiek_type_status['yes'][1] . '" class="tooltip-a-width ' . $class_label_by_pass . $class_met_bypass . '"  style="position:relative;">
                                                <input type="radio" class="input-filter-option inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input" style="z-index:-1" value="6" aria-label="choose">
                                                <div class="flex filter-options whitespace-nowrap px-2" style="color: #002C4B">' . $labeled . '</div>';

                                        if ((bool)$x_value['website_recommends']) {
                                            echo
                                            '<span class="invisible min-w-full text-center rounded px-[5px] absolute z-10 top-full left-0 text-sm flex items-center justify-center text-[#5DC2EF] m-0 atooltiptext ' . (strlen($labeled) < 12 ? '!w-[137px] !left-[-15px]' : '') . '">
                                                                    <div class="flex flex-wrap place-content-center rounded-md px-3 py-1 tooltiptext-div" style="background-color: #002C4B;">'. $escaper->escapeHtml(__("tops")) .' <span class="text-white pl-[5px] max-[1024px]:text-xs">'. $escaper->escapeHtml(__("choice")) .'</span>
                                                                        <button type="button" class="text-xs text-blue-picton ml-2" >
                                                                                ' . $heroicons->informationCircleHtml('w-4 h-4') . '
                                                                        </button>   
                                                                    </div>
                                                                </span>';
                                        }
                                        echo '
                                                
                                                </a>
                                            </div>';
                                    endif;
                                    if ($specifiek_type_status['no'][0]):
                                        $labeled = $option_attr_specifiek_type[1];//'Zonder bypass';
                                        echo '
                                            <div class="relative contents border-b border-dotted border-[#002C4B] tooltip"
                                            ' . 'fil="' . $specifiek_type_status['no'][3] . '" merk_filter="' . $specifiek_type_status['no'][2] . '" specifiek_type="' . $specifiek_type_status['no'][5] . '" catid="' . json_encode($specifiek_type_status['no'][4], JSON_UNESCAPED_UNICODE) . '" is_salable="' . $specifiek_type_status['no'][6] . '">' .
                                            '<a href="' . $specifiek_type_status['no'][1] . '" class="tooltip-a-width ' . $class_label_by_pass . $class_zonder_bypass . '" style="position:relative;">
                                                <input type="radio" class="input-filter-option inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input" style="z-index:-1" value="6" aria-label="choose">
                                                <div class="flex filter-options whitespace-nowrap px-2" style="color: #002C4B">' . $labeled . '</div>';
                                        if ((bool)$x_value['website_recommends']) {
                                            echo
                                            '<span class="invisible min-w-full text-center rounded px-[5px] absolute z-10 top-full left-0 text-sm flex items-center justify-center text-[#5DC2EF] m-0 atooltiptext ' . (strlen($labeled) < 12 ? '!w-[137px] !left-[-15px]' : '') . '">
                                                                    <div class="flex flex-wrap place-content-center rounded-md px-3 py-1 tooltiptext-div" style="background-color: #002C4B;">'. $escaper->escapeHtml(__("tops")) .' <span class="text-white pl-[5px] max-[1024px]:text-xs">'. $escaper->escapeHtml(__("choice")) .'</span>
                                                                        <button type="button" class="text-xs text-blue-picton ml-2" >
                                                                                ' . $heroicons->informationCircleHtml('w-4 h-4') . '
                                                                        </button>   
                                                                    </div>
                                                                </span>';
                                        }
                                        echo '
                                                </a>
                                            </div>';
                                    endif;
                                    echo '</div><br>';
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php
                    endif;
                    ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>