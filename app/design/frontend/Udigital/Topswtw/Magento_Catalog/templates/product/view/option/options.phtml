<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Catalog\Block\Product\View\Options;
use Magento\Catalog\Model\Product\Option;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Options $block */
/** @var ViewModelRegistry $viewModels */
/** @var Option $option */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

$product = $block->getProduct();

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

$displayTax = $productPriceViewModel->displayPriceIncludingTax();

$options = $block->decorateArray($block->getOptions());

?>
<?php if (count($options)): ?>
    <script>
        function initOptions() {
            return {
                optionConfig: <?= /* @noEscape */ $block->getJsonConfig() ?>,
                regularPriceKey: '<?= /* @noEscape */ $displayTax ? 'oldPrice' : 'baseOldPrice' ?>',
                finalPriceKey: '<?= /* @noEscape */ $displayTax ? 'finalPrice' : 'basePrice' ?>',
                productFinalPrice: false,
                customOptionPrices: {},
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                productBasePrice: false,
                customOptionBasePrices: {},
                <?php endif; ?>
                refs: [],
                getFormattedOptionPrice(optionId) {
                    if (this.customOptionPrices[optionId]) {
                        const showSign = true;
                        return hyva.formatPrice(this.customOptionPrices[optionId], showSign);
                    }
                    return false;
                },
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                getFormattedOptionBasePrice(optionId) {
                    if (this.customOptionBasePrices[optionId]) {
                        const showSign = true;
                        return hyva.formatPrice(this.customOptionBasePrices[optionId], showSign);
                    }
                    return false;
                },
                <?php endif; ?>
                updateOptionPrice(optionId, optionPriceEl) {
                    const container = document.createElement('div');
                    container.innerHTML = optionPriceEl.innerHTML;

                    const price = this.customOptionPrices[optionId],
                        priceEl = container.querySelector('.price-wrapper');
                    if (priceEl && price) {
                        priceEl.innerText = hyva.formatPrice(price);
                    }
                    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    const basePrice = this.customOptionBasePrices[optionId],
                        basePriceEl = container.querySelector('.price-excluding-tax .price');
                    if (basePriceEl && basePrice) {
                        basePriceEl.innerText = hyva.formatPrice(basePrice);
                    }
                    <?php endif; ?>
                    return container.innerHTML;
                },
                calculateOptionPrices() {
                    for (const [customOptionId, customOption] of Object.entries(this.optionConfig)) {
                        if (customOption.prices) {
                            this.customOptionPrices[customOptionId] = this.calculateOptionPrice(customOption,customOptionId);
                            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                            this.customOptionBasePrices[customOptionId] = this.calculateOptionBasePrice(customOption,customOptionId);
                            <?php endif; ?>
                        } else {
                            for (const [childCustomOptionId, childCustomOption] of Object.entries(customOption)) {
                                const key = customOptionId + '_' + childCustomOptionId;
                                this.customOptionPrices[key] = this.calculateOptionPrice(childCustomOption,customOptionId,childCustomOptionId);
                                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                                this.customOptionBasePrices[key] = this.calculateOptionBasePrice(childCustomOption,customOptionId,childCustomOptionId);
                                <?php endif; ?>
                            }
                        }
                    }

                    window.dispatchEvent(new CustomEvent("update-custom-option-prices", {detail: this.customOptionPrices}));

                    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    window.dispatchEvent(new CustomEvent("update-custom-option-base-prices", {detail: this.customOptionBasePrices}));
                    <?php endif; ?>
                },
                calculateOptionPrice(customOption, customOptionId, childCustomOptionId) {
                    const customOptionCode = customOptionId + (childCustomOptionId ? '-' + childCustomOptionId : '') ;

                    const optionElement = this.refs && this.refs['option-'+customOptionCode];

                    let price = customOption.prices[this.finalPriceKey].amount;

                    if (this.productFinalPrice &&
                        optionElement &&
                        optionElement.dataset.priceAmount &&
                        optionElement.dataset.priceType
                    ) {
                        price = optionElement.dataset.priceType !== 'percent' ?
                            optionElement.dataset.priceAmount :
                            this.productFinalPrice * (optionElement.dataset.priceAmount / 100)
                    }

                    return price;
                },
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                calculateOptionBasePrice(customOption, customOptionId, childCustomOptionId) {
                    const customOptionCode = customOptionId + (childCustomOptionId ? '-' + childCustomOptionId : '') ;

                    const optionElement = this.refs && this.refs['option-'+customOptionCode];

                    let basePrice = customOption.prices['basePrice'].amount;

                    if (this.productBasePrice &&
                        optionElement &&
                        optionElement.dataset.basePriceAmount &&
                        optionElement.dataset.priceType
                    ) {
                        basePrice = optionElement.dataset.priceType !== 'percent' ?
                            optionElement.dataset.basePriceAmount :
                            this.productBasePrice * (optionElement.dataset.priceAmount / 100)
                    }

                    return basePrice;
                },
                <?php endif; ?>
                updateCustomOptionValue($dispatch, customOptionId, target) {
                    let active;
                    switch (target.type) {
                        case 'text':
                        case 'textarea':
                        case 'file':
                        case 'time':
                        case 'datetime-local':
                        case 'date':
                            active = !!target.value;
                            break;
                        case 'hidden':
                            // when we're configuring an item in the cart that already has an attachment
                            // this is only needed on page-initialization
                            active = (target.value === 'save_old')
                        break;
                        case 'select-one':
                        case 'select-multiple':
                            Array.from(target.options).forEach(option => {
                                customOptionId = option.dataset.optionId;
                                active = option.selected;
                                $dispatch('update-custom-option-active', { customOptionId, active });
                            });
                            return;
                        break;
                        case 'radio':
                            target.checked && document.querySelectorAll('input[name="'+target.name+'"]')
                                .forEach(input => {
                                    // unset sibling radio buttons
                                    input.dataset &&
                                    input.dataset.optionId &&
                                    input !== target && $dispatch('update-custom-option-active',
                                        {
                                            customOptionId: input.dataset.optionId,
                                            active: false
                                        }
                                    );
                                });
                        // DO NOT BREAK
                        // Checkbox case should also run for Radio
                        case 'checkbox':
                            const requiredOptions = document
                                .querySelectorAll('input[name="'+target.name+'"][data-required]');
                            const checkedOptionsQty = Array.from(requiredOptions)
                                .filter(option => option.checked).length;

                            requiredOptions.forEach(input => {
                                if (checkedOptionsQty) {
                                    input.required = false;
                                    input.setCustomValidity('');
                                } else {
                                    input.required = false;
                                    input.setCustomValidity(input.dataset.validationMessage);
                                }
                            });
                            active = target.checked;
                        break;
                    }
                    $dispatch('update-custom-option-active', { customOptionId, active });
                },
                initSelectedOptions($dispatch) {
                    Array.from(document.querySelectorAll('.product-custom-option')).forEach(customOption => {
                        this.updateCustomOptionValue($dispatch, customOption.dataset.optionId, customOption);
                    })
                },
                eventListenersOptions: {
                    ['@update-product-final-price.window'](event) {
                        this.productFinalPrice = event.detail;
                        this.calculateOptionPrices();
                    },
                    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    ['@update-product-base-price.window'](event) {
                        this.productBasePrice = event.detail;
                        this.calculateOptionPrices();
                    }<?php endif; ?>
                }
            }
        }
    </script>
    <h3 class="text-gray-900 text-xl title-font font-medium mb-4">
        <?= $escaper->escapeHtml(__('Customizable Options')) ?>:
    </h3>
    <div x-data="initOptions()"
         x-init="
            refs = $refs;
            $nextTick(() => { calculateOptionPrices(); initSelectedOptions($dispatch); })
         "
         x-bind="eventListenersOptions"
         class="mb-6">
    <?php foreach ($options as $option):?>
        <?= $block->getOptionHtml($option) ?>
    <?php endforeach; ?>
    </div>
<?php endif; ?>
