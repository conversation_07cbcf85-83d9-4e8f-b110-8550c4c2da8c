<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Catalog\Block\Product\AbstractProduct;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\Catalog\Pricing\Price\TierPrice;
use Magento\Framework\Escaper;
use Tops\StockStatus\ViewModel\StockStatusViewModel;

/** @var AbstractProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Product $product */
$product = $block->getProduct();

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

$stockStatusViewModel = $viewModels->require(StockStatusViewModel::class);
$heroicons = $viewModels->require(HeroiconsSolid::class);

$regularPrice = $productPriceViewModel->getPriceValue(RegularPrice::PRICE_CODE, $product);
$finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);
$tierPrices = $productPriceViewModel->getTierPrices(TierPrice::PRICE_CODE, $product);
$option_attr = $block->getProductAction()->getAttributeOptionV2("inventory_status");
$option_attr_per_set = $block->getProductAction()->getAttributeProduct($product, "per_set");
$perSetStatus = ($option_attr_per_set != null && $option_attr_per_set == 1) ? 'set' : "product";

if ($productPriceViewModel->displayPriceInclAndExclTax()) {
    $regularPriceExclTax = $productPriceViewModel->getPriceValueExclTax(RegularPrice::PRICE_CODE, $product);
    $finalPriceExclTax = $productPriceViewModel->getPriceValueExclTax(FinalPrice::PRICE_CODE, $product);
}

$displayTax = $productPriceViewModel->displayPriceIncludingTax();

$stockitem  = $block->getProductAction()->getStockItem($product->getId(), $product->getStore()->getWebsiteId());
$stock_status = $product->getInventoryStatus();

?>
<script>
    function initPrice<?= (int)$product->getId() ?>() {

        <?php /* All four of these keys are used - they are rendered by PHP */ ?>
        const regularPriceInclTaxKey = 'oldPrice',
            regularPriceExclTaxKey = 'baseOldPrice',
            finalPriceInclTaxKey = 'finalPrice',
            finalPriceExclTaxKey = 'basePrice';

        function calculateCustomOptionPrices(activeCustomOptions, customOptionPrices) {
            return activeCustomOptions.reduce((priceAccumulator, activeCustomOptionId) => {
                const customOptionPrice = customOptionPrices[activeCustomOptionId];
                if (customOptionPrice) {
                    return Number.parseFloat(priceAccumulator) + Number.parseFloat(customOptionPrice);
                }
                return priceAccumulator;
            }, 0);
        }

        return {
            regularPriceKey: <?= $displayTax ? 'regularPriceInclTaxKey' : 'regularPriceExclTaxKey' ?>,
            finalPriceKey: <?= $displayTax ? 'finalPriceInclTaxKey' : 'finalPriceExclTaxKey' ?>,
            activeProductsPriceData: false,
            initialFinalPrice: <?= (float)$finalPrice ?>,
            calculatedFinalPrice: false,
            calculatedFinalPriceWithCustomOptions: false,
            initialTierPrices: <?=
                                /** @noEscape */
                                json_encode($tierPrices, JSON_UNESCAPED_UNICODE) ?>,
            showRegularPriceLabel: <?= ($finalPrice < $regularPrice) ? 'true' : 'false' ?>,
            customOptionPrices: [],
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                initialBasePrice: <?= (float)$finalPriceExclTax ?>,
                calculatedBasePrice: false,
                customOptionBasePrices: [],
                calculatedBasePriceWithCustomOptions: false,
            <?php endif; ?>
            activeCustomOptions: [],
            qty: 1,
            isbasePriceCustomShow: true,
            updateCustomOptionActive(data) {
                let activeCustomOptions = this.activeCustomOptions;
                const customOptionId = data.customOptionId;

                if (data.active) {
                    if (!activeCustomOptions.includes(customOptionId)) {
                        activeCustomOptions.push(data.customOptionId);
                    }
                } else {
                    if (customOptionId && activeCustomOptions.includes(customOptionId)) {
                        let index = activeCustomOptions.indexOf(customOptionId);
                        activeCustomOptions.splice(index, 1);
                    }
                }
                this.calculateFinalPriceWithCustomOptions()
            },
            updateCustomOptionPrices(prices, basePrices) {
                if (prices) {
                    this.customOptionPrices = prices;
                }

                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    if (basePrices) {
                        this.customOptionBasePrices = basePrices;
                    }
                <?php endif; ?>

                this.calculateFinalPriceWithCustomOptions();
            },
            calculateFinalPrice() {
                const findApplicableTierPrice = (initialPrice, withTax) => {
                    if (this.activeProductsPriceData && this.activeProductsPriceData.tierPrices) {
                        const key = withTax ? 'price' : 'basePrice'
                        return this.activeProductsPriceData.tierPrices.reduce((acc, tierPrice) => {
                            if (this.qty >= tierPrice.qty && tierPrice[key] < acc) {
                                return tierPrice[key];
                            }
                            if(tierPrice[key] == undefined){
                                if (this.qty >= tierPrice.qty && tierPrice.price < acc) {
                                    return tierPrice.price;
                                }
                            }
                            return acc;
                        }, this.activeProductsPriceData[withTax ? finalPriceInclTaxKey : finalPriceExclTaxKey].amount);

                    } else {
                        const key = withTax ? 'price_incl_tax' : 'price_excl_tax';
                        return Object.values(this.initialTierPrices).reduce((acc, tierPrice) => {
                            if (this.qty >= tierPrice.price_qty && tierPrice[key] < acc) {
                                return tierPrice[key];
                            }
                            return acc;
                        }, initialPrice);

                    }
                }

                this.calculatedFinalPrice = findApplicableTierPrice(this.initialFinalPrice, <?= $displayTax ? 'true' : 'false' ?>);
                window.dispatchEvent(new CustomEvent("update-product-final-price", {
                    detail: this.calculatedFinalPrice
                }));

                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    this.calculatedBasePrice = findApplicableTierPrice(<?= (float) $finalPriceExclTax ?>, false);
                    window.dispatchEvent(new CustomEvent("update-product-base-price", {
                        detail: {
                            basePrice: this.calculatedBasePrice
                        }
                    }));
                <?php endif; ?>
            },
            calculatePriceLabelVisibility() {
                this.showRegularPriceLabel =
                    (this.calculatedFinalPrice === this.activeProductsPriceData[this.regularPriceKey].amount) &&
                    this.activeProductsPriceData.isMinimalPrice;
            },
            calculateFinalPriceWithCustomOptions() {
                const finalPrice = this.calculatedFinalPrice || this.initialFinalPrice;
                this.calculatedFinalPriceWithCustomOptions = finalPrice + this.getCustomOptionPrice();
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    const basePrice = this.calculatedBasePrice || this.initialBasePrice;
                    this.calculatedBasePriceWithCustomOptions = basePrice + this.getCustomOptionBasePrice();
                <?php endif; ?>
            },
            getCustomOptionPrice() {
                return calculateCustomOptionPrices(this.activeCustomOptions, this.customOptionPrices);
            },
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                getCustomOptionBasePrice() {
                    return calculateCustomOptionPrices(this.activeCustomOptions, this.customOptionBasePrices);
                },
            <?php endif; ?>
            getFormattedFinalPrice() {
                return hyva.formatPrice(
                    this.calculatedFinalPriceWithCustomOptions ||
                    this.calculatedFinalPrice ||
                    this.initialFinalPrice
                )
            },
            getTotalFinalPrice() {
                const price = this.calculatedFinalPriceWithCustomOptions ||
                    this.calculatedFinalPrice ||
                    this.initialFinalPrice;
                const total = price * this.qty;
                let pricetemp = hyva.formatPrice(total);

                if(this.activeProductsPriceData &&
                    this.activeProductsPriceData.oldPrice &&
                    this.getFinalPriceAmount() < this.activeProductsPriceData[this.regularPriceKey].amount){
                    pricetemp += ` <span class="line-through text-white-azureish md:pl-2">${hyva.formatPrice((this.activeProductsPriceData[this.regularPriceKey].amount + this.getCustomOptionPrice()) * this.qty)}</span>`;
                }else if(!this.activeProductsPriceData &&
                    this.getFinalPriceAmount() < this.initialFinalPrice){
                        pricetemp += ` <span class="line-through text-white-azureish md:pl-7">${hyva.formatPrice((this.initialFinalPrice + this.getCustomOptionPrice()) * this.qty)}</span>`;
                }
                return pricetemp;
            },
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                getFormattedBasePrice() {
                    return hyva.formatPrice(
                        this.calculatedBasePriceWithCustomOptions ||
                        this.calculatedBasePrice ||
                        this.initialBasePrice
                    )
                },
            <?php endif; ?>
            isPriceHidden() {
                const finalPrice = this.calculatedFinalPriceWithCustomOptions ||
                    this.calculatedFinalPrice ||
                    this.initialFinalPrice;
                return <?= $product->isSaleable() ? 'false' : 'true' ?> && finalPrice === 0;
            },
            getFinalPriceAmount() {
                return this.calculatedFinalPriceWithCustomOptions ||
                    this.calculatedFinalPrice ||
                    (this.activeProductsPriceData ? this.activeProductsPriceData[this.finalPriceKey].amount : false) ||
                    this.initialFinalPrice;
            },
            updateItemQty() {
                this.changeTake();
                this.calculateFinalPrice();
                this.calculateFinalPriceWithCustomOptions();
            },
            changeTake() {
                this.qty             = this.qty < 1 ? 1 : this.qty;
                const itemQty        = this.qty;
                let itemTakeQty      = itemQty;
                let itemTakeDiscount = '0%';

                if (this.activeProductsPriceData) {
                    if (this.activeProductsPriceData.tierPrices.length > 0) {
                        this.activeProductsPriceData.tierPrices.forEach(function(tierPrice, index) {
                            if (itemQty >= tierPrice.qty) {
                                itemTakeQty = tierPrice.qty;
                                itemTakeDiscount = `${tierPrice.percentage}%`
                            }
                        });
                    }
                }else if(this.initialTierPrices.length > 0) {
                    this.initialTierPrices.forEach(function(tierPrice, index) {
                        const percentage = (eval(tierPrice.percentage_value) - Number(tierPrice.percentage_value)) > 0 ?
                            eval(tierPrice.percentage_value) : Number(tierPrice.percentage_value);
                        if (itemQty >= tierPrice.price_qty) {
                            itemTakeQty = tierPrice.price_qty;
                            itemTakeDiscount = `${percentage}%`
                        }
                    });
                }
                this.percent = itemTakeDiscount;
            },
            eventListeners_<?= (int)$product->getId() ?>: {
                ['@update-prices-<?= (int)$product->getId() ?>.window'](event) {
                    this.activeProductsPriceData = event.detail;
                    this.changeTake();

                    this.calculateFinalPrice();
                    this.calculateFinalPriceWithCustomOptions();
                    this.calculatePriceLabelVisibility();
                },
                ['@update-qty-<?= (int)$product->getId() ?>.window'](event) {
                    this.qty = event.detail;
                    this.changeTake();
                    this.calculateFinalPrice();
                    this.calculateFinalPriceWithCustomOptions();
                },
                ['@update-custom-option-active.window'](event) {
                    this.updateCustomOptionActive(event.detail);
                },
                ['@update-custom-option-prices.window'](event) {
                    this.updateCustomOptionPrices(event.detail);
                },
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>['@update-custom-option-base-prices.window'](event) {
                        this.updateCustomOptionPrices(null, event.detail);
                    }
                <?php endif; ?>
            }
        }
    }
</script>
<div x-data="initPrice<?= (int)$product->getId() ?>()" x-bind="eventListeners_<?= (int)$product->getId() ?>" class="price-box price-final_price flex flex-row flex-wrap lg:my-4">
    <template x-if="!activeProductsPriceData && !isPriceHidden()">
        <div class="price-container flex flex-row flex-wrap mb-4 screen-78.25:mb-0">
            <div class="final-price inline-block mr-4">
                <!-- <span class="price-label block">
                        <?= ($product->canConfigure() && is_int($product->getPrice())) ?
                            $escaper->escapeHtml(__('As low as')) . ':' :
                            '&nbsp;' ?>
                    </span> -->
                <span id="product-price-<?= (int)$product->getId() ?>" class="price-wrapper title-font font-bold text-2xl text-gray-900">
                    <span class="price text-blue-picton" :style="{'font-size': '28px'}" x-html="getTotalFinalPrice()">
                        <?=
                        /** @noEscape */
                        $productViewModel->format($finalPrice) ?>
                        <?php if ($finalPrice < $regularPrice): ?>
                            <span class="line-through text-white-azureish md:pl-2">
                                <?= /** @noEscape */ $productViewModel->format($regularPrice) ?>
                            </span>
                        <?php endif; ?>
                    </span>
                </span>
            </div>

            <?php if ($finalPrice < $regularPrice): ?>
                <div class="old-price mr-2 flex flex-wrap items-center mr-4">
                    <span id="product-price-<?= (int)$product->getId() ?>" :style="{'font-size':'28px'}" class="price-wrapper title-font font-bold text-xl line-through text-white-azureish">
                        <span class="price" x-html="hyva.formatPrice(<?= (float)$regularPrice ?> + getCustomOptionPrice())">
                            <?=
                            /** @noEscape */
                            $productViewModel->format($regularPrice) ?>
                        </span>
                    </span>
                </div>
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                    <div class="old-price-excl-tax">
                        <span class="font-bold line-through text-gray-900">
                            <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                            <span class="price" x-html="hyva.formatPrice(<?= (float)$regularPriceExclTax ?> + getCustomOptionBasePrice())">
                                <?=
                                /** @noEscape */
                                $productViewModel->format($regularPriceExclTax) ?>
                            </span>
                        </span>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                <div class="final-price-excl-tax">
                    <span class="font-regular text-gray-900">
                        <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                        <span class="price" x-html="getFormattedBasePrice()">
                            <?=
                            /** @noEscape */
                            $productViewModel->format($finalPriceExclTax) ?>
                        </span>
                    </span>
                </div>
            <?php endif; ?>
            <div class="flex flex-wrap per-set" :style="{ 'flex-basis': '100%'}">
                <span class="text-xs basepricecustom pr-1" x-text="getFormattedFinalPrice()" x-show="isbasePriceCustomShow">
                </span>
                <span class="text-xs">
                    <?= __('per '.$perSetStatus) ?>
                </span>
            </div>

            <?php
            $stockStatus = $stockStatusViewModel->getStockStatus(
                $block,
                $product,
                $stockitem
            );

            ?>
            <div class="flex flex-wrap text-xs items-center op-voorraad-desktop mt-4">
                <span class="<?= $escaper->escapeHtmlAttr($stockStatus['css_class']); ?> items-center flex">
                    <?php if (isset($stockStatus['icon'])): ?>
                    <?= /* @noEscape */ $heroicons->renderHtml($stockStatus['icon'], 'mr-1', 24, 24); ?>
                    <?php endif; ?>
                    <?= $escaper->escapeHtml($stockStatus['label']); ?>
                </span>
            </div>
        </div>
    </template>

    <template x-if="activeProductsPriceData">
        <div class="final-price flex inline-block mr-4">
            <?php if ($product->canConfigure() && is_int($product->getPrice())): ?>
                <!-- <span class="price-label block" :class="{ 'invisible' : !showRegularPriceLabel }">
                    <?= $escaper->escapeHtml(__('As low as')) ?>:
                </span> -->
            <?php endif; ?>
            <span id="product-price-<?= (int)$product->getId() ?>" class="price-wrapper title-font font-medium text-2xl text-blue-picton" :style="{'font-size': '28px'}">
                <span class="price" x-html="getTotalFinalPrice()"></span>
            </span>
            <div class="flex flex-wrap text-xs items-center ml-3"><span class="text-green-islamic"><?= $escaper->escapeHtml(__('In stock')) ?></span></div>
        </div>
    </template>
    <template x-if="activeProductsPriceData &&
        activeProductsPriceData.oldPrice &&
        activeProductsPriceData[finalPriceKey].amount < activeProductsPriceData[regularPriceKey].amount
    ">
        <div class="old-price flex mr-2">
            <span id="product-price-<?= (int)$product->getId() ?>" class="price-wrapper title-font font-bold text-xl line-through text-gray-900 flex flex-wrap mr-4 text-white-azureish" :style="{'font-size':'28px'}">
                <span class="price mr-1" x-html="hyva.formatPrice(activeProductsPriceData[regularPriceKey].amount + getCustomOptionPrice())"></span>
            </span>
            <div class="flex flex-wrap text-xs items-center op-voorraad-desktop"><span class="text-green-islamic"><?= $escaper->escapeHtml(__('In stock')) ?></span></div>
        </div>

    </template>
    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
        <template x-if="activeProductsPriceData &&
        activeProductsPriceData.oldPrice &&
        activeProductsPriceData[finalPriceKey].amount < activeProductsPriceData[regularPriceKey].amount
    ">
            <div class="old-price-excl-tax">
                <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                <span class="font-regular text-gray-900">
                    <span class="price" x-html="hyva.formatPrice(activeProductsPriceData['baseOldPrice'].amount + getCustomOptionBasePrice())"></span>
                </span>
            </div>
        </template>
        <template x-if="activeProductsPriceData">
            <div class="price-excl-taxinline-block">
                <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                <span class="font-regular text-gray-900">
                    <span class="price" x-html="getFormattedBasePrice()"></span>
                </span>
            </div>
        </template>
    <?php endif; ?>
    <template x-if="activeProductsPriceData">
        <div class="flex flex-wrap space-x-1 per-set" :style="{ 'flex-basis': '100%'}">
            <span class="text-xs pr-1" x-text="getFormattedFinalPrice()" x-show="isbasePriceCustomShow">
            </span>
            <span class="text-xs">
                <?= __('per '.$perSetStatus) ?>
            </span>
        </div>
    </template>
</div>
