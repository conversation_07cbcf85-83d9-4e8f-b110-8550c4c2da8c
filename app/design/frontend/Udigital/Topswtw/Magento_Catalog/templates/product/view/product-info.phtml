<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ProductAlert;
use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\Catalog\Pricing\Price\TierPrice;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var Product $product */
$product = $productViewModel->getProduct();

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

$regularPrice = $productPriceViewModel->getPriceValue(RegularPrice::PRICE_CODE, $product);
$finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);
$tierPrices = $productPriceViewModel->getTierPrices(TierPrice::PRICE_CODE, $product);
$isConfigurable = ($product->getTypeId() == "configurable");

/** @var ProductAlert $productAlertViewModel */
$productAlertViewModel = $viewModels->require(ProductAlert::class);

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var Product $product */
$product = $currentProduct->get();

$stock_status = $product->getInventoryStatus();

$signupLabel = __("Remember me by stock");
?>
<script>
    function initqtynew() {
        function findPathParam(key) {
            // get all path pairs after BASE_URL/front_name/action_path/action
            const baseUrl = (BASE_URL.substr(0, 2) === '//' ? 'http:' : '') + BASE_URL;
            const baseUrlParts = (new URL(baseUrl)).pathname.replace(/\/$/, '').split('/');
            const pathParts = window.location.pathname.split('/').slice(baseUrlParts.length + 3);
            for (let i = 0; i < pathParts.length; i += 2) {
                if (pathParts[i] === key && pathParts.length > i) {
                    return pathParts[i + 1];
                }
            }
        }

        return {
            qty: <?= $block->getProductDefaultQty() * 1 ?>,
            itemId: (new URLSearchParams(window.location.search)).get('id') || findPathParam('id'),
            productId: '<?= (int)$product->getId() ?>',
            showPercentTextMobile: false,
            showPercentTextDesktop: false,
            initialFinalPrice: <?= (float)$finalPrice ?>,
            defaultFinalPrice: <?= (float)$finalPrice ?>,
            initialTierPrices: <?=
                                /** @noEscape */
                                json_encode($tierPrices, JSON_UNESCAPED_UNICODE) ?>,
            isConfigurable: <?= $isConfigurable ? 'true' : 'false' ?>,
            // populate the qty when editing a product from the cart
            onGetCartData: function onGetCartData(data, $dispatch) {
                const cart = data && data.data && data.data.cart;
                if (this.itemId && cart && cart.items) {
                    const cartItem = cart.items.find((item) => {
                        return item.item_id === this.itemId && item.product_id === this.productId;
                    });
                    if (cartItem && cartItem.qty) {
                        this.qty = cartItem.qty;
                        $dispatch('update-qty-' + this.productId, this.qty);
                    }
                }
            },
            setTierPrice(event) {
                const tierPrices = event.detail.tierPrices;
                if (tierPrices != undefined) {
                    this.initialTierPrices = tierPrices;
                }
            },
            getPercentTierPrice(tierPrice) {
                let percentageVal = tierPrice.percentage_value;
                if (percentageVal == undefined) {
                    const price = tierPrice.price_excl_tax > tierPrice.price_incl_tax ?
                        tierPrice.price_excl_tax : tierPrice.price_incl_tax;
                    percentageVal = (100 - (price / this.defaultFinalPrice) * 100);
                }

                const percentaceParts = percentageVal.toString().split(".");
                const decimalLength = percentaceParts.length > 1
                    ? percentaceParts[1].length || 0
                    : 0;

                if (decimalLength > 2) {
                    const multiplier = Math.pow(10, 1 || 0);
                    percentageVal = Math.round(percentageVal * multiplier) / multiplier;
                }
                const isFloat = (eval(percentageVal) - Number(percentageVal)) > 0 ? true : false;
                return isFloat ? eval(percentageVal) : Number(percentageVal)
            },
            handleClickQtyPercenUpdate(tierPrice) {
                if (this.isConfigurable) {
                    const percentage = (eval(tierPrice.percentage) - Number(tierPrice.percentage)) > 0 ?
                        eval(tierPrice.percentage) : Number(tierPrice.percentage);
                    this.qty = tierPrice.qty;
                    this.percent = `${percentage}%`
                } else {
                    const percentage = this.getPercentTierPrice(tierPrice);
                    this.qty = tierPrice.price_qty;
                    this.percent = `${percentage}%`
                }
            }
        };
    }
</script>
<div class="order-2 w-full mb-2">
    <?= $block->getChildHtml('product.info.form') ?>
    <div class="flex flex-wrap flex-col items-start mb-4" x-data="initqtynew()"
    @update-prices-<?= (int)$product->getId() ?>.window="setTierPrice(event)">
        <div class="basis-full container-merk-filterclasses w-full">
            <?= $block->getChildHtml("product.attributes.merkfilterclasses") ?>
            <?= $block->getChildHtml("product.filterclasses") ?>
        </div>
        <div class="flex flex-wrap flex-col mt-2 items-start mb-4 basis-full price-quantity-cart-content
            max-lg:z-11 max-lg:fixed max-lg:bottom-0 max-lg:left-0
            max-lg:w-full max-lg:bg-white max-lg:mb-0 max-lg:py-4 px-4 lg:px-0">

            <div class="basis-full container-price">
                <?= $block->getChildHtml("product.info.price") ?>
            </div>
            <div class="grow">
                <div class="mb-4 lg:mb-0">
                    <template x-if="initialTierPrices.length > 0">
                        <div class="container-basetiertext basis-full mt-8 mb-2">
                            <div class="text-md" style="color: #002C4B">
                        <span class="basetiertext">
                            <?= $escaper->escapeHtml(__(
                                "Choose your quantity for the corresponding discount:"
                            )) ?>
                        </span>
                            </div>
                        </div>
                    </template>
                    <template x-if="initialTierPrices.length > 0">
                        <div class="container-percents flex mt-4 sm:mt-0">
                            <div x-data="{ percent: '0%' }">
                                <ol class="flex flex-wrap border-2 rounded-lg percent-status">
                                    <template x-for="(tierPrice, index) in initialTierPrices">
                                        <li class="percent-status-li">
                                            <template x-if="isConfigurable">
                                                <button x-on:click="handleClickQtyPercenUpdate(tierPrice)"
                                                        @click="$dispatch('update-qty-<?= (int)$product->getId() ?>', tierPrice.qty)"
                                                        class="btn box-shadow-none h-9 text-sm rounded-s-md max-lg:m-1 persen-5 hover:bg-blue-picton hover:text-white"
                                                        :class="{'bg-blue-picton text-white' : qty >= tierPrice.qty,
                                                    'bg-white text-blue-prussian' : qty < tierPrice.qty}">
                                                    <div class="hidden lg:block">
                                                        <div>
                                                            <span x-html="getPercentTierPrice(tierPrice) + '%'"></span>
                                                            <?= $escaper->escapeHtml(__(" from ")) ?>
                                                            <span x-html="tierPrice.qty"></span>
                                                            <?= $escaper->escapeHtml(__(" sets ")) ?>
                                                        </div>
                                                    </div>
                                                    <div class="block lg:hidden">
                                                        <div x-html="getPercentTierPrice(tierPrice) + '%'"></div>
                                                    </div>
                                                </button>
                                            </template>
                                            <template x-if="!isConfigurable">
                                                <button x-on:click="handleClickQtyPercenUpdate(tierPrice)"
                                                        @click="$dispatch('update-qty-<?= (int)$product->getId() ?>', tierPrice.price_qty)"
                                                        class="btn box-shadow-none h-9 text-sm rounded-s-md max-lg:m-1 persen-5 hover:bg-blue-picton hover:text-white"
                                                        :class="{'bg-blue-picton text-white' : qty >= tierPrice.price_qty,
                                                    'bg-white text-blue-prussian' : qty < tierPrice.price_qty}">
                                                    <div class="hidden lg:block">
                                                        <div>
                                                            <span x-html="getPercentTierPrice(tierPrice) + '%'"></span>
                                                            <?= $escaper->escapeHtml(__(" from ")) ?>
                                                            <span x-html="tierPrice.price_qty"></span>
                                                            <?= $escaper->escapeHtml(__(" sets ")) ?>
                                                        </div>
                                                    </div>
                                                    <div class="block lg:hidden">
                                                        <div x-html="getPercentTierPrice(tierPrice) + '%'"></div>
                                                    </div>
                                                </button>
                                            </template>
                                        </li>
                                    </template>
                                </ol>
                            </div>
                        </div>
                    </template>
                </div>
                <div class="flex mt-6 sm:mt-4 container-qty-cart !w-auto">
                    <?php if ($product->getInventoryStatus() != 126): ?>
                        <?= $block->getChildHtml("product.info.quantity") ?>
                        <div>
                            <?= $block->getChildHtml("product.info.addtocart") ?>
                        </div>
                        <?php else:
                            if ($product->getInventoryStatus() == 126): ?>
                            <div class="product alert stock">
                                <a href="<?= $escaper->escapeUrl($productAlertViewModel->getSaveUrl(
                                    $currentProduct->get(),
                                    'stock'
                                )) ?>"
                                title="<?= $escaper->escapeHtml($signupLabel) ?>"
                                class="action alert">
                                    <button type="button"
                                    class="btn btn-green-islamic btn-add-to-cart px-6 ml-1 mb-1 word"
                                    style="height: 54px;width: 319px;">
                                        <span class="text-white-400">
                                                <?= $heroicons->bellHtml('w-6 h-6 inline') ?>
                                        </span>
                                        <span class="fz-20 hidden sm:block md:hidden lg:block
                                        ml-3 button-notifyme">
                                                <?= $escaper->escapeHtml($signupLabel) ?>
                                        </span>

                                    </button>
                                </a>
                            </div>
                            <?php endif; ?>
                    <?php endif; ?>
                </div>

            </div>
        </div>
    </div>

    <?php if ($product->getInventoryStatus() != 126): ?>
        <div class="flex mt-4 justify-end">
            <?= $block->getChildHtml('addtocart.shortcut.buttons') ?>
        </div>
    <?php endif; ?>


    <?= /** @noEscape  */ $block->getChildHtml('product.info.usps') ?>

    <!-- =========================== -->

    <div class="my-2 flex">
        <?= $block->getChildHtml('product.info.review') ?>
    </div>

    <?php if ($shortDescription = $productViewModel->getShortDescription()) { ?>
        <!-- <div class="mb-4 leading-relaxed product-description prose">
            <?php // echo /* @noEscape */ $shortDescription ?></div> -->
    <?php } ?>
    <!-- <div class="flex flex-col sm:flex-row justify-between my-4">
        <?= $block->getChildHtml("product.info.stockstatus") ?>
        <?= $block->getChildHtml("alert.urls") ?>
    </div> -->

    <!-- <div id="product-details">
        <?php foreach ($block->getAttributes() as $attributeConfig) {
            $attribute = $attributesViewModel->getAttributeFromLayoutConfig($attributeConfig); ?>
            <?php if ($value = $attribute['value'] ?? null) { ?>
                <div class="flex border-t border-gray-300 py-2 last:mb-6 last:border-b
                    <?php // echo /* @noEscape */ $attribute['value'] ?: "" ?>">
                    <span class="w-1/2 text-left text-gray-700 product-detail-label">
                        <?= $escaper->escapeHtml($attribute['label']) ?>
                    </span>
                    <span class="w-1/2 ml-2 text-left text-gray-900 product-detail-value">
                        <?= $escaper->escapeHtml($value) ?>
                    </span>
                </div>
            <?php } ?>
        <?php } ?>
    </div> -->

    <!-- <div class="flex mt-4 justify-end">
        <?= $block->getChildHtml('product.info.addtowishlist'); ?>
        <?= $block->getChildHtml('product.info.addtocompare'); ?>
        <?= $block->getChildHtml('product.info.emailtofriend'); ?>
        <?= $block->getChildHtml('product.info.additional.actions'); ?>
    </div> -->

    <?php if ($tierPriceBlock = $block->getChildHtml("product.price.tier")): ?>
        <div class="py-4 my-2 tier-price-container">
            <?=
            /** @noEscape */
            $tierPriceBlock ?>
        </div>
    <?php endif; ?>

    <?= $block->getChildHtml("product.info.additional") ?>
</div>
