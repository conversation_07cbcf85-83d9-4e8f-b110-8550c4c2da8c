<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductStockItem;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Catalog\Block\Product\View;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;

// phpcs:disable Generic.WhiteSpace.ScopeIndent.Incorrect

/** @var View $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Product $product */
$product = $block->getProduct();

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var ProductStockItem $stockItemViewModel */
$stockItemViewModel = $viewModels->require(ProductStockItem::class);
$minSalesQty        = $stockItemViewModel->getMinSaleQty($product);
$maxSalesQty        = $stockItemViewModel->getMaxSaleQty($product);

$maxSalesQtyLength  = ($maxSalesQty ? strlen((string) $maxSalesQty) : 4)
    + (/* add one if decimal for separator */
    (int) $stockItemViewModel->isQtyDecimal($product));

$step = $stockItemViewModel->getQtyIncrements($product)
    ? $stockItemViewModel->getQtyIncrements($product)
    : null;

?>
<?php if ($block->shouldRenderQuantity()): ?>
    <script>
        // function initQtyField() {

        //     function findPathParam(key) {
        //         // get all path pairs after BASE_URL/front_name/action_path/action
        //         const baseUrl = (BASE_URL.substr(0, 2) === '//' ? 'http:' : '') + BASE_URL;
        //         const baseUrlParts = (new URL(baseUrl)).pathname.replace(/\/$/, '').split('/');
        //         const pathParts = window.location.pathname.split('/').slice(baseUrlParts.length + 3);
        //         for (let i = 0; i < pathParts.length; i += 2) {
        //             if (pathParts[i] === key && pathParts.length > i) {
        //                 return pathParts[i + 1];
        //             }
        //         }
        //     }

        //     return {
        //         qty: <?= $block->getProductDefaultQty() * 1 ?>,
        //         itemId: (new URLSearchParams(window.location.search)).get('id') || findPathParam('id'),
        //         productId: '<?= (int)$product->getId() ?>',
        //         <?php // populate the qty when editing a product from the cart ?>
        //         onGetCartData: function onGetCartData(data, $dispatch) {
        //             const cart = data && data.data && data.data.cart;
        //             if (this.itemId && cart && cart.items) {
        //                 const cartItem = cart.items.find((item) => {
        //                     return item.item_id === this.itemId && item.product_id === this.productId;
        //                 });
        //                 if (cartItem && cartItem.qty) {
        //                     this.qty = cartItem.qty;
        //                     $dispatch('update-qty-' + this.productId, this.qty);
        //                 }
        //             }
        //         }
        //     };
        // }
    </script>
    <div>
        
            <div class="mr-2 item-quantity" 
                x-init="$watch('qty', (value) => qty = value <= 0 ? 1 : value)">
                <label for="qty[<?= (int)$product->getId() ?>]"
                    class="sr-only"
                >
                    <?= $escaper->escapeHtml(__('Quantity')) ?>
                </label>

                <input name="qty"
                    @private-content-loaded.window="onGetCartData($event.detail, $dispatch)"
                    id="qty[<?= (int)$product->getId() ?>]"
                    form="product_addtocart_form"
                    <?php if ($stockItemViewModel->isQtyDecimal($product)): ?>
                    type="text"
                    pattern="[0-9](\.[0-9])?{0,<?= /** @noEscape */ $maxSalesQtyLength ?>}'"
                    inputmode="decimal"
                    <?php else: ?>
                    type="number"
                    pattern="[0-9]{0,<?= /** @noEscape */ $maxSalesQtyLength ?>}"
                    inputmode="numeric"
                    readonly
                    <?php if ($minSalesQty): ?>min="<?= /** @noEscape */ $minSalesQty ?>"<?php endif; ?>
                    <?php if ($maxSalesQty): ?>max="<?= /** @noEscape */ $maxSalesQty ?>"<?php endif; ?>
                    <?php if ($step): ?>step="<?= /** @noEscape */ $step ?>"<?php endif; ?>
                    <?php endif; ?>
                    value="1"
                    :value="qty"
                    class="w-20 mr-4"
                    x-model.number="qty"
                    @change="$dispatch('update-qty-<?= (int)$product->getId() ?>', qty)"
                    x-on:change="updateItemQty()"
                />

                <div class="item-quantity-nav">
                    <div class="item-quantity-button item-quantity-up"
                        @click="(qty < <?= ($product->getInventoryStatus() == 138)? 5 :(($product->getInventoryStatus() == 152)? 1: 9999); ?> ? qty++ : ''), $dispatch('update-qty-<?= (int)$product->getId() ?>', qty)"
                    >
                        <?= $heroiconsSolid->renderHtml('chevron-up', 'fill-current text-blue-prussian mr-1.5 mt-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                    </div>
                    <div class="item-quantity-button item-quantity-down"
                        @click="qty--, (qty > 0 ? $dispatch('update-qty-<?= (int)$product->getId() ?>', qty): '')"
                    >
                        <?= $heroiconsSolid->renderHtml('chevron-down', 'fill-current text-blue-prussian mr-1.5 mb-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                    </div>
                </div>
            </div>
        
    </div>
<?php endif; ?>
