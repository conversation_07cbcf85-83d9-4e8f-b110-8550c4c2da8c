<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Modal;
use Magento\Catalog\Block\Product\AbstractProduct;
use Magento\Catalog\Block\Product\ReviewRendererInterface;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;

/** @var $viewModels ViewModelRegistry */
/** @var $block AbstractProduct */
/** @var $escaper Escaper */

$product = $block->getProduct();
$total_review = $block->getProductAction()->getTotalReview();

if (!$total_review) {
    return;
}

$modalViewModel = $viewModels->require(Modal::class);
$heroicons = $viewModels->require(HeroiconsOutline::class);

if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
    $url = "https://";
} else {
    $url = "http://";
}
$url .= $_SERVER['HTTP_HOST'];
$url .= $_SERVER['REQUEST_URI'];
?>
<?php
    $modalShare = $modalViewModel->createModal()->withContent('
        <div class="relative w-full h-full md:h-auto text-blue-prussian text-justify">
            <!-- Modal header -->
            <div class="flex items-center justify-between pb-5 mb-3">
                <span class="text-xl font-semibold text-gray-90">
                    '. $escaper->escapeHtml(__('Share this article with others')) .'
                </span>
                <button class="ml-6 lg:ml-24 -mt-4" @click="hide">
                    <span aria-hidden="true" class="text-2xl font-bold text-blue-picton">&times;</span><span class="sr-only">Close</span>
                </button>
            </div>
            <!-- Modal body -->
            <div>
                <p class="text-sm mb-3">'. $escaper->escapeHtml(__('Copy this link. After this you can paste and share it.')) .'</p>
                <p class="text-sm">'. $escaper->escapeHtml(__('Link')) .'</sp>
                <div class="flex justify-between items-center gap-x-2">
                    <input type="text" value="'. $url .'" id="link" class="w-4/5 !bg-white-azureish" readonly>
                    <button id="btn_copy" class="btn btn-blue-prussian whitespace-nowrap px-4 py-2.5 rounded-md" @click="copyLink">'. __('Copy link') .'</button>
                </div>
            </div>
        </div>
    ')->withContainerClasses('fixed', 'flex', 'justify-center', 'items-center', 'mt-14')
    ->withAriaLabelledby('the-label')
    ->positionTop()
    ->addDialogClass('rounded md:w-2/5');
    ?>
<div class="review-share-container flex flex-wrap place-items-center">
    <?= $block->getReviewsSummaryHtml($block->getProduct(), ReviewRendererInterface::FULL_VIEW, true) ?>
    <span class="total-reviews-share text-blue-picton text-sm mr-[10px] max-[1024px]:text-xs pl-2"><?= $total_review; ?> <?= __(' reviews'); ?></span>
    <div class="flex items-center text-sm w-1/4 ml-2 text-blue-picton"  x-data="Object.assign({
            showShare() {
                const btn = document.getElementById('btn_copy');
                btn.classList.remove('btn-outlined-alt-blue-prussian');
                btn.classList.add('btn-blue-prussian');
                btn.innerText = '<?= $escaper->escapeHtml(__('Copy link')) ?>';
                this.show()
            },
            copyLink() {
                const self = this
                const copyText = document.getElementById('link');
                const btn = document.getElementById('btn_copy');
                copyText.select();
                copyText.setSelectionRange(0, 99999);
                document.execCommand('copy');
                btn.classList.remove('btn-blue-prussian');
                btn.classList.add('btn-outlined-alt-blue-prussian');
                btn.innerText = '✓ <?= $escaper->escapeHtml(__('Link copied')) ?>';
                setTimeout(function (){
                    self.hide()
                }, 700);
            }
        }, hyva.modal())
        ">
        <a class="link-share-product flex flex-wrap text-sm items-center">
            <span class="mr-1 md:text-3.5 text-3.25 text-blue-prussian">|</span>
            <button type="button" class="text-share flex items-center text-sm ml-2.5 mr-1" @click="showShare">
                <?= /** @noEscape */ $heroicons->renderHtml('share', 'product-share-icon mr-1 text-blue-picton', 16, 16); ?>
                <?= $escaper->escapeHtml(__('Share')); ?>
            </button>
            <?= /** @noEscape */ $modalShare ?>
        </a>
    </div>
</div>
