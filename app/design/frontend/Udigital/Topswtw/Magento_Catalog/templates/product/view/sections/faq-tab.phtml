<?php

/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * Product additional attributes template
 *
 */

use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Catalog\Block\Product\View\Attributes;
use Magento\Framework\Escaper;

/** @var Attributes $block */
/** @var Escaper $escaper*/
/** @var ViewModelRegistry $viewModels */

 $heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
 $heroicons = $viewModels->require(HeroiconsOutline::class);

$productViewModel = $viewModels->require(ProductPage::class);
$product          = $productViewModel->getProduct();
$categoryIds      = $product->getCategoryIds();


$category  = $block->getCat()->getFaqContentInProduct($categoryIds);
$faqContentCategory = !empty($category) ? $category->getFaqContentCategory() : [];

$arrayData = [];
$faqContentCategory = !empty($faqContentCategory) ? (is_string($faqContentCategory) ?
    json_decode($faqContentCategory) : $faqContentCategory) : "";

if (!empty($faqContentCategory)): ?>
    <div class="space-y-6">
        <div class="flex pt-6 pb-3 mb-1 md:flex-row product-faq-new">
            <h3 class="!text-gray-900 text-2xl title-font font-bold text-left w-full">
                <?= $escaper->escapeHtml(__('Frequently Asked Questions')) ?>
            </h3>
        </div>
        <div class="grid md:grid-cols-1 gap-4">
            <ul class="col-span-1 space-y-4">
                <?php
                $firstindex = 0;
                $showfisrtfaq = 'false';
                foreach ($faqContentCategory as $faq):
                    $arrayData[] = [
                        '@type' => 'Question',
                        'name' => $faq->title,
                        'acceptedAnswer' => [
                            '@type' => 'Answer',
                            'text' => $faq->description
                        ]
                    ];
                    $showfisrtfaq = ($firstindex == 0) ? 'true' :  'false';
                    ?>
                    <li class="md:text-blue-prussian text-[#16365B]" x-data="{ show: <?= $showfisrtfaq; ?> }">
                        <div class="cursor-pointer flex justify-between border-b border-blue-cadet pb-3" @click="show = !show">
                            <div class="font-bold faq-content-title"><?= $escaper->escapeHtml($faq->title) ?></div>
                            <div :class="{'hidden': show ,'block': !show }">
                                <?= $heroiconsSolid->renderHtml('chevron-down', 'text-blue-picton', 20, 20); ?>
                            </div>
                            <div :class="{'hidden': !show ,'block': show }">
                                <?= $heroiconsSolid->renderHtml('chevron-up', 'text-blue-picton', 20, 20); ?>
                            </div>
                        </div>
                        <div x-show.transition.in="show" class="py-4 faq-content-des">
                            <?= $escaper->escapeHtml($faq->description) ?>
                        </div>
                    </li>
                    <?php
                    $firstindex +=1;
                endforeach; ?>
            </ul>
        </div>
    </div>
<?php endif; ?>
