<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 *
 * this section for showing title and description on product page
 */

declare(strict_types=1);

/** @var Details $block */
/** @var Escaper $escaper */

use Magento\Catalog\Block\Product\View\Details;
use Magento\Framework\Escaper;

$titleRenderer = $block->getChildBlock('product.section.title.renderer');
$defaultTitleTemplate = $titleRenderer->getTemplate();

?>
<div class="grid lg:grid-cols-2 gap-8 gap-y-2 mb-12">
<?php
foreach ($block->getGroupSortedChildNames('detailed_info', '') as $sectionName) {
    $sectionBlock  = $block->getLayout()->getBlock($sectionName);
    $sectionHtml   = (string) $sectionBlock->toHtml();
    $titleTemplate = $sectionBlock->getData('title_template') ?? $defaultTitleTemplate;

    if (empty(trim($sectionHtml))) {
        continue;
    }
    ?>
    <section id="<?= $escaper->escapeHtmlAttr($sectionBlock->getNameInLayout()) ?>"
        <?php
        // just for product.info.faq.tops, make it grid column 1
        if ($escaper->escapeHtmlAttr($sectionBlock->getNameInLayout()) == "product.info.faq.tops"
            || $escaper->escapeHtmlAttr($sectionBlock->getNameInLayout()) == "product.attributes.attachments"
        ) { echo 'style="grid-column: 1;"'; } ?>
        >
        <?php
            // for product.attributes not showing title cause title showing on attributes.phtml
        if ($escaper->escapeHtmlAttr($sectionBlock->getNameInLayout()) != "product.attributes" && $escaper->escapeHtmlAttr($sectionBlock->getNameInLayout()) != "product.info.faq.tops") {
            echo $titleRenderer->setTemplate($titleTemplate)
                      ->assign('sectionBlock', $sectionBlock)
                      ->toHtml();
        }
        ?>
        <div class="w-full">
            <?= /** @noEscape  */ $sectionHtml ?>
        </div>
    </section>
<?php } ?>
</div>
