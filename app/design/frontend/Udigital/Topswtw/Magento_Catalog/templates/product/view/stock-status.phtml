<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Tops\StockStatus\ViewModel\StockStatusViewModel;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);
$stockStatusViewModel = $viewModels->require(StockStatusViewModel::class);
$heroicons = $viewModels->require(HeroiconsSolid::class);

/** @var Product $product */
$product = $block->hasData('product')
    ? $block->getData('product')
    : $currentProduct->get();

if (!$product || !$product->getId()) {
    return;
}

$stockItem  = $block->getProductAction()->getStockItem($product->getId(), $product->getStore()->getWebsiteId());

$stockStatus = $stockStatusViewModel->getStockStatus(
    $block,
    $product,
    $stockItem
);
?>
<?php if ($block->getParentBlock()->displayProductStockStatus()): ?>
    <div class="text-left">
        <p class="flex items-center justify-start align-middle available gap-x-2 stock"
                title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
            <span class="<?= $escaper->escapeHtmlAttr($stockStatus['css_class']); ?> flex justify-center items-center">
                <?php if (isset($stockStatus['icon'])): ?>
                    <?= /* @noEscape */ $heroicons->renderHtml($stockStatus['icon'], 'mr-1', 24, 24); ?>
                <?php endif; ?>
                <span class="leading-none"><?= $escaper->escapeHtml($stockStatus['label']); ?></span>
            </span>
        </p>
    </div>
<?php endif; ?>
