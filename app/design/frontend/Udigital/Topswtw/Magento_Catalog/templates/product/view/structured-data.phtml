<?php
/**
 * Copyright Elgentos. All rights reserved.
 * https://elgentos.nl/
 */

declare(strict_types=1);

use Magento\Catalog\Block\Product\View as CatalogProductView;
use Magento\Review\Block\Product\View as ReviewProductView;

/** @var CatalogProductView $block */

$product = $block->getProduct();

if (!$product) return;

$reviews = [];

$priceHelper = $block->getPriceHelper();
$price = $product->getFinalPrice();
$currency = $block->getCurrentCurrencyCode();
$image = $block->getImage($product, 'product_page_image_large')->getImageUrl();
$description = strip_tags($product->getDescription() ?? '');

$reviewCollection = $block->getLayout()
    ->createBlock(ReviewProductView::class)
    ->getReviewsCollection();
$reviewCollection->load()->addRateVotes();

$ratingSummary = $product->getRatingSummary(); // typically 0–100
$reviewsCount = $product->getReviewsCount();
$ratingValue = $ratingSummary ? round($ratingSummary / 20, 1) : null;

$data = [
    "@context" => "https://schema.org/",
    "@type" => "Product",
    "name" => $product->getName(),
    "sku" => $product->getSku(),
    "image" => $image,
    "description" => $description,
    "offers" => [
        "@type" => "Offer",
        "priceCurrency" => 'EUR',
        "price" => number_format($price, 2, '.', ''),
        "availability" => $product->isSaleable()
            ? "https://schema.org/InStock"
            : "https://schema.org/OutOfStock",
        "url" => $product->getProductUrl(),
    ]
];

if ($ratingValue && $reviewsCount) {
    $data['aggregateRating'] = [
        "@type" => "AggregateRating",
        "ratingValue" => $ratingValue,
        "reviewCount" => $reviewsCount
    ];
}

$reviews = [];
foreach ($reviewCollection as $review) {
    $votes = $review->getRatingVotes();
    $stars = 0;
    foreach ($votes as $vote) {
        $stars += (float)$vote->getPercent() / 20;
    }
    $ratingAvg = count($votes) ? round($stars / count($votes), 1) : 0;

    $reviews[] = [
        "@type" => "Review",
        "author" => [
            "@type" => "Person",
            "name" => $review->getNickname()
        ],
        "datePublished" => date('Y-m-d', strtotime($review->getCreatedAt())),
        "reviewBody" => $review->getDetail(),
        "name" => $review->getTitle(),
        "reviewRating" => [
            "@type" => "Rating",
            "ratingValue" => $ratingAvg,
            "bestRating" => "5"
        ]
    ];
}
if (!empty($reviews)) {
    $data['review'] = $reviews;
}
?>
<script type="application/ld+json">
<?= /** @noEscape */ json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) ?>
</script>
