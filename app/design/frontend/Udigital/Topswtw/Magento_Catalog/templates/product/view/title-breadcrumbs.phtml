<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Hyva\Theme\ViewModel\Navigation;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\CatalogUrlRewrite\Model\ProductUrlPathGenerator;
use Magento\Framework\Escaper;
use Hyva\Theme\Block\Catalog\Breadcrumbs;

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Breadcrumbs $block */

$storeConfigViewModel = $viewModels->require(StoreConfig::class);
$navigationViewModel = $viewModels->require(Navigation::class);
$currentProductViewModel = $viewModels->require(CurrentProduct::class);

$productUrlSuffix = $storeConfigViewModel->getStoreConfig(ProductUrlPathGenerator::XML_PATH_PRODUCT_URL_SUFFIX);

// Modules may disable the rendering of application/ld+json BreadcrumbList by setting this flag in layout XML
$skipLinkedDataJsonSchema = $block->getData('skip_ld_json_schema');

$product = $currentProductViewModel->exists() ? $currentProductViewModel->get() : null;

?>
<div class="title-crumbs" aria-label="Breadcrumb">

    <ol
        x-data="initBreadcrumbsProductTitle()"
        class="items list-reset mx-0 pb-4 rounded flex flex-wrap text-md relative"
    >
        <template x-for="item in breadcrumbs">
            <li class="item inline-flex whitespace-nowrap">
                <template x-if="item.name === 'product'">
                    <span class="text-blue-cadet" x-text="item.label"></span>
                </template>
                <template x-if="item.name !== 'product'">
                    <a :href="item.link" class="no-underline text-blue-cadet" x-text="item.label"></a>
                </template>
                <template x-if="item.name === 'category'">
                    <span aria-hidden="true" class="separator text-blue-cadet px-2"> - </span>
                </template>
            </li>
        </template>
        <li
            class="shrink-0 sticky inset-y-0 right-0 w-3 bg-gradient-to-l from-white"
            aria-hidden="true"
        >&ZeroWidthSpace;</li>
    </ol>
</div>
<script>
    function initBreadcrumbsProductTitle() {
        let excluded = ['c2057', 'c2324', 'c3851', 'c3852'];
        let productCategories = <?= /** @noEscape */ json_encode($product->getCategoryIds()) ?>;
        productCategories = productCategories.map(item => 'c' + item);

        const categories = <?= /** @noEscape */ json_encode($navigationViewModel->getCategories()) ?>;

        const breadcrumbs = [];

        function getProductCategoryUrl() {
            return document.referrer.split('?')[0]
        }

        const categoryUrl = getProductCategoryUrl();

        const category = Object.values(categories).find(cat => cat.url === categoryUrl);

        const validItems = productCategories
            .filter(item => item in categories && !excluded.includes(item))
            .map(item => categories[item]);

        const currentCategory = category ? category : (validItems.length > 0 ? validItems[0] : null);

        if (currentCategory) {
            let parts = currentCategory.path.split('/');
            // remove root category
            parts.splice(0, 2)
            for (let i in parts) {
                breadcrumbs.push({
                    name: 'category',
                    label: categories['c' + parts[i]]['name'],
                    title: '',
                    link: categories['c' + parts[i]]['url']
                });
            }
        }

        // add current product to breadcrumbs
        <?php if ($product): ?>
        breadcrumbs.push({
            name: 'product',
            label: '<?= $escaper->escapeJs($product->getData("supplier_trade_item_code")) ?>',
            title: '',
            link: BASE_URL + '<?= $escaper->escapeJs($product->getUrlKey()) ?>' + '<?= $escaper->escapeJs($productUrlSuffix) ?>'
        });
        <?php endif; ?>

        return {breadcrumbs};
    }
</script>
