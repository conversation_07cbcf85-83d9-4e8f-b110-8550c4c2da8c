<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var SvgIcons $svgIcons */

$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
$svgIcons = $viewModels->require(SvgIcons::class);
?>

<div class="flex flex-col space-y-4 mt-4">
    <div class="flex flex-row gap-3">
        <div class="h-6 w-6 grow-0">
            <?= /** @noEscape  */ $heroiconsSolid->renderHtml('check', 'h-6 w-6 text-green-islamic'); ?>
        </div>

        <p>
            <span class="font-semibold"><?= $escaper->escapeHtml(__('Free returns')); ?></span>
            <?= $escaper->escapeHtml(__('within 100 days')); ?>
        </p>
    </div>

    <div class="flex flex-row gap-3">
        <div class="h-6 w-6 grow-0">
            <?= /** @noEscape  */ $heroiconsSolid->renderHtml('check', 'h-6 w-6 text-green-islamic'); ?>
        </div>
        <p>
            <span class="font-semibold">99%</span>
            <?= $escaper->escapeHtml(__('Of customers recommend us')); ?>
        </p>
    </div>

    <div class="flex flex-row gap-3">
        <div class="h-6 w-6 grow-0">
            <?= /** @noEscape  */ $heroiconsSolid->renderHtml('check', 'h-6 w-6 text-green-islamic') ?>
        </div>
        <p>
            <?= $escaper->escapeHtml(__('Shop safely with')); ?>
            <span class="font-semibold"><?= $escaper->escapeHtml(__('E-commerce Quality Label')); ?></span>
        </p>
    </div>

    <div class="flex flex-row gap-3 lg:items-center">
        <div class="h-6 w-6 grow-0">
            <?= /** @noEscape  */ $heroiconsSolid->renderHtml('check', 'h-6 w-6 text-green-islamic'); ?>
        </div>
        <div class="lg:flex gap-3 items-center">
            <p class="font-semibold mb-4 lg:mb-0">
                <?= $escaper->escapeHtml(__('Pay securely with:')); ?>
            </p>
            <div class="flex gap-3">
                <?= /** @noEscape  */ $svgIcons->renderHtml('paypal', 'h-8 w-12') ?>
                <?= /** @noEscape  */ $svgIcons->renderHtml('klarna', 'h-8 w-12') ?>
                <?= /** @noEscape  */ $svgIcons->renderHtml('ideal', 'h-8 w-12') ?>
                <?= /** @noEscape  */ $svgIcons->renderHtml('visa', 'h-8 w-12') ?>
                <?= /** @noEscape  */$svgIcons->renderHtml('maestro', 'h-8 w-12') ?>
            </div>
        </div>
    </div>
</div>
