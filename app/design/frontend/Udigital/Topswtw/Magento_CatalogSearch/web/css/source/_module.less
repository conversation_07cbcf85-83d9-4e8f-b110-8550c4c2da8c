// @codingStandardsIgnoreFile
// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@autocomplete__background-color: @color-white;
@autocomplete__border: 1px solid @form-element-input__border-color;
@autocomplete-item__border: 1px solid @color-gray90;
@autocomplete-item__hover__color: @color-gray91;
@autocomplete-item-amount__color: @color-gray60;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    .search {
        .fieldset {
            .control {
                .addon {
                    input {
                        flex-basis: auto;
                        width: 100%;
                    }
                }
            }
        }
    }

    .block-search {
        margin-bottom: 10px!important;

        .block {
            &-title {
                display: none;
            }
        }

        .block-content {
            margin-bottom: 0;
        }

        .label {
            .lib-icon-font(
            @_icon-font-content: @icon-search,
            @_icon-font-size: 22px,
            @_icon-font-line-height: 28px,
            @_icon-font-margin: 0 @indent__s 0 0,
            @_icon-font-color: @header-icons-color,
            @_icon-font-color-hover: @header-icons-color-hover,
            @_icon-font-color-active: @header-icons-color-hover,
            @_icon-font-text-hide: true
            );
            display: inline-block;
            float: right;

            &.active {
                + .control {
                    input {
                        position: static;
                    }
                }
            }
        }

        .action.search {
            display: none;
        }

        .control {
            margin: 45px -15px 10px;
            padding: 11px 15px 10px;
            .lib-css(background-color, @color-blue-picton);
        }

        input {
            font-size: 16px;
            left: -300%;
            margin: 15px 0;
            position: static;
        }

        .nested {
            display: none;
        }
    }

    .search-autocomplete {
        display: none;
        margin-top: -15px;
        overflow: hidden;
        position: absolute;
        z-index: 3;
        &:extend(.abs-add-box-sizing all);

        ul {
            .lib-list-reset-styles();

            &:not(:empty) {
                .lib-css(background, @autocomplete__background-color);
                .lib-css(border, @autocomplete__border);
                border-top: 0;
            }

            li {
                .lib-css(border-top, @autocomplete-item__border);
                cursor: pointer;
                margin: 0;
                padding: @indent__xs @indent__xl @indent__xs @indent__s;
                position: relative;
                text-align: left;
                white-space: normal;

                &:first-child {
                    border-top: none;
                }

                &:hover,
                &.selected {
                    .lib-css(background, @autocomplete-item__hover__color);
                }

                .amount {
                    .lib-css(color, @autocomplete-item-amount__color);
                    position: absolute;
                    right: 7px;
                    top: @indent__xs;
                }
            }
        }
    }

    .form.search.advanced {
        .fields.range {
            .field {
                &:first-child {
                    position: relative;

                    .control {
                        padding-right: 25px;

                        &:after {
                            content: ' \2013 ';
                            display: inline-block;
                            position: absolute;
                            right: 0;
                            text-align: center;
                            top: 6px;
                            width: 25px;
                        }
                    }
                }

                &:last-child {
                    position: relative;

                    div.mage-error {
                        left: 0;
                        position: absolute;
                        top: 32px;
                    }
                }

                &.with-addon {
                    .control {
                        padding-right: 45px;
                    }
                }

                &.date {
                    &:extend(.abs-field-date all);
                }
            }
        }

        .group.price {
            .addon {
                .addafter {
                    background: none;
                    border: 0;
                    padding-top: 6px;
                    position: absolute;
                    right: 0;
                    top: 0;
                }
            }
        }
    }

    .search-terms {
        &:extend(.abs-reset-list all);
        line-height: 2em;

        > li {
            display: inline-block;
            margin-right: @indent__s;
        }
    }

    .search.found {
        margin-bottom: @indent__s;
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .block-search {
        margin-top: @indent__s;
        margin-bottom: 0;
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .block-search {
        padding-left: 10%;
        position: relative;
        display: inline-block;
        width: 30%;
        z-index: 4;

        .label {
            &:extend(.abs-visually-hidden-desktop all);
        }

        .control {
            border-top: 0;
            margin: 0;
            padding: 0;
            .lib-css(background-color, @color-blue-prussian);
        }

        .action.search {
            display: inline-block;
            .lib-button-icon(
            @_icon-font-content: @icon-search,
            @_icon-font-size: 16px,
            @_icon-font-text-hide: true,
            @_icon-font-color: @header-icons-color,
            @_icon-font-color-hover: @header-icons-color-hover,
            @_icon-font-line-height: 32px
            );
            .lib-button-reset();
            position: absolute;
            .lib-css(-webkit-text-stroke, 1px @header-icons-color);
            right: @indent__s;
            top: 0;
            z-index: 1;

            &:focus {
                &:before {
                    .lib-css(color, @color-gray20);
                }
            }
        }
    }

    .search-autocomplete {
        margin-top: 0;
    }

    .form.search.advanced {
        &:extend(.abs-forms-general-desktop all);
    }
}

.block-search .action.search[disabled]{
    pointer-events: none;
    opacity: 0.5;
}

.block-search {

    input {
        .lib-input-placeholder(@form-element-input-placeholder__color);
        margin: -5px 0 0 0;
        padding-right: 35px;
        position: static;
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border-radius: 8px;
        opacity: 1;
        height: 41px;
    }

    .action.search {
        display: inline-block;
        .lib-button-icon(
        @_icon-font-content: @icon-search,
        @_icon-font-size: 16px,
        @_icon-font-text-hide: true,
        @_icon-font-color: @header-icons-color,
        @_icon-font-color-hover: @header-icons-color-hover,
        @_icon-font-line-height: 32px
        );
        .lib-button-reset();
        position: absolute;
        .lib-css(-webkit-text-stroke, 1px @header-icons-color);
        right: 30px;
        top: 64px;
        z-index: 1;
    
        &:focus {
            &:before {
                .lib-css(color, @color-gray20);
            }
        }
    }
}

.form.minisearch .label{
    display:none!important;
}