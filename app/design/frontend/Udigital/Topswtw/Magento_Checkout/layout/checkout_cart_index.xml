<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content">
            <referenceBlock name="checkout.cart">
                <referenceContainer name="checkout.cart.items">
                    <referenceContainer name="checkout.cart.container">
                        <referenceBlock name="checkout.cart.wrapper">
                            <block name="checkout.cart.wrapper.loading" as="loading" template="Hyva_Theme::ui/loading.phtml"/>
                            <referenceContainer name="cart.summary">
                                <referenceBlock name="checkout.cart.shipping" remove="true"/>
                                <referenceContainer name="checkout.cart.totals.container">
                                    <referenceBlock name="checkout.cart.totals">
                                        <arguments>
                                            <argument name="cart_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Cart</argument>
                                        </arguments>
                                    </referenceBlock>
                                </referenceContainer>
                                <referenceBlock name="checkout.cart.methods.bottom">
                                    <arguments>
                                        <argument name="customer" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Customer</argument>
                                    </arguments>
                                </referenceBlock>
                            </referenceContainer>
                        </referenceBlock>
                    </referenceContainer>
                </referenceContainer>
            </referenceBlock>
        </referenceContainer>
    </body>
    <move element="cart.discount" destination="checkout.cart.totals.container" after="checkout.cart.totals" />

    <referenceBlock name="footer_other" remove="true"/>
</page>
