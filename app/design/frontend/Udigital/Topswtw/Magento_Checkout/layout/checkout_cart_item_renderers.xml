<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="checkout.cart.item.renderers.default">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="checkout.cart.item.renderers.simple">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="checkout.cart.item.renderers.bundle">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="checkout.cart.item.renderers.downloadable">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="checkout.cart.item.renderers.grouped">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="checkout.cart.item.renderers.configurable">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="checkout.cart.item.renderers.virtual">
            <arguments>
                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                <argument name="category_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
