<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Checkout\Block\Cart\Sidebar;
use Magento\Framework\Escaper;
use Magento\Framework\View\Helper\SecureHtmlRenderer;

/** @var Sidebar $block */
/** @var SecureHtmlRenderer $secureRenderer */
/** @var Escaper $escaper */

?>

<div data-block="minicart" class="minicart-wrapper">
    <a class="action showcart" href="<?= $escaper->escapeUrl($block->getShoppingCartUrl()) ?>"
       data-bind="scope: 'minicart_content'">
        <span class="text"><?= $escaper->escapeHtml(__('My Cart')) ?></span>
        <span class="counter qty minicart"
            data-bind="css: { minicart: !!getCartParam('summary_count') == false && !isLoading() }, blockLoader: isLoading">
            <span class="counter-number">
                <!-- ko if: getCartParam('summary_count') -->
                <!-- ko text: getCartParam('summary_count').toLocaleString(window.LOCALE) --><!-- /ko -->
                <!-- /ko -->

                <!-- ko ifnot: getCartParam('summary_count') -->
                <!-- ko text: 0 --><!-- /ko -->
                <!-- /ko -->
            </span>
            <span class="counter-label">
            <!-- ko if: getCartParam('summary_count') -->
                <!-- ko text: getCartParam('summary_count').toLocaleString(window.LOCALE) --><!-- /ko -->
                <!-- ko i18n: 'items' --><!-- /ko -->
            <!-- /ko -->

            <!-- ko ifnot: getCartParam('summary_count') -->
                <!-- ko text: 0 --><!-- /ko -->
            <!-- /ko -->
            </span>
        </span>
    </a>
    <?php if ($block->getIsNeedToDisplaySideBar()):?>
        <div class="block block-minicart"
             data-role="dropdownDialog"
             data-mage-init='{"dropdownDialog":{
                "appendTo":"[data-block=minicart]",
                "triggerTarget":".showcart",
                "timeout": "2000",
                "closeOnMouseLeave": false,
                "closeOnEscape": true,
                "triggerClass":"active",
                "parentClass":"active",
                "buttons":[]}}'>
            <div id="minicart-content-wrapper" data-bind="scope: 'minicart_content'">
                <!-- ko template: getTemplate() --><!-- /ko -->
            </div>
            <?= $block->getChildHtml('minicart.addons') ?>
        </div>
    <?php else: ?>
        <?php $scriptString = <<<script
            require(['jquery'], function ($) {
                $('a.action.showcart').on('click', function() {
                    $(document.body).trigger('processStart');
                });
            });
script;
        ?>
        <?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false); ?>
    <?php endif ?>
    <?= /* @noEscape */ $secureRenderer->renderTag('script', [], 'window.checkout = ' .
     /* @noEscape */ $block->getSerializedConfig(), false); ?>
    <script type="text/x-magento-init">
    {
        "[data-block='minicart']": {
            "Magento_Ui/js/core/app": <?= /* @noEscape */ $block->getJsLayout() ?>
        },
        "*": {
            "Magento_Ui/js/block-loader": "<?= $escaper->escapeJs(
                $escaper->escapeUrl($block->getViewFileUrl('images/loader-1.gif'))
            ) ?>"
        }
    }
    </script>
</div>
