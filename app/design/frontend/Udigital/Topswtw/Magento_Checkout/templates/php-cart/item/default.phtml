<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Checkout\Block\Cart\Item\Renderer;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Catalog\Pricing\Price\TierPrice;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Framework\Escaper;
use Tops\StockStatus\ViewModel\StockStatusViewModel;

/** @var Renderer $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

$_item = $block->getItem();
$product = $_item->getProduct();
$isVisibleProduct = $product->isVisibleInSiteVisibility();

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

/** @var StockStatusViewModel $stockStatusViewModel */
$stockStatusViewModel = $viewModels->require(StockStatusViewModel::class);

$uniqueId = '_' . uniqid();

$tagProduct     = $block->getCategoryAction()->getTagProductByCategory($product);
$categoryName   = $tagProduct["result"];

if ($product->getTypeId() == "configurable") {
    $children = $product->getTypeInstance()->getUsedProducts($product);
    foreach ($children as $child) {
        if ($product->getSku() == $child->getSku()) { $product = $child;
        }
    }
}
$finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);
$tierPrices = $productPriceViewModel->getTierPrices(TierPrice::PRICE_CODE, $product);

if ($productPriceViewModel->displayPriceInclAndExclTax()) {
    $regularPriceExclTax = $productPriceViewModel->getPriceValueExclTax(RegularPrice::PRICE_CODE, $product);
    $finalPriceExclTax = $productPriceViewModel->getPriceValueExclTax(FinalPrice::PRICE_CODE, $product);
}
$displayTax = $productPriceViewModel->displayPriceIncludingTax();
$productLoad = $block->getProductAction()->getProductId($product->getId());

$option_attr_per_set = $productLoad->getData("per_set");
$perSetStatus = ($option_attr_per_set !== null && $option_attr_per_set == 1) ? 'set' : "product";
$stock_status = $productLoad->getInventoryStatus();
$stockitem  = $block->getProductAction()->getStockItem($product->getId(), $product->getStore()->getWebsiteId());
$stockStatus = $stockStatusViewModel->getStockStatus(
    $block,
    $productLoad,
    $stockitem
);

$brand = $productLoad->getData('merk_filter');
?>


<div x-data="initCartPage_<?= /** @noEscape */ $product->getId() ?>()"
    x-init="initCart_<?= /** @noEscape */ $product->getId() ?>($dispatch)"
    x-bind="eventListenersCart_<?= /** @noEscape */ $product->getId() ?>">
    <div x-init="$watch('cartQty', (value) => cartQty = value <= 0 ? 1 : value)" class="w-cart-list">
        <div class="gap-2 grid-cols-3 cart-mobile min-[820px]:max-[1024px]:p-[17px]  min-[1280px]:max-[1280px]:p-[27px]">
            <div>
                <?= $block->getImage($block->getProductForThumbnail(), 'cart_page_product_thumbnail')->toHtml() ?>
            </div>
            <div class="col-span-2">
                <div class="md:grid">
                    <div class="grid gap-2 grid-cols-3">
                        <div class="col-span-2">
                            <p>
                                <a href="<?= $escaper->escapeUrl($block->getProductUrl()) ?>">
                                    <div class="content-title-product text-base title-mobile-cart">
                                        <?= $escaper->escapeHtml($block->getProductName()) ?>
                                    </div>
                                </a>
                            </p>
                        </div>
                        <div class="trash-mobile">
                            <?= /* @noEscape */ $block->getActions($_item) ?>
                        </div>
                        <div class="col-span-3 text-xs text-blue-cadet -mt-2.5 text-title-bottom">
                            <span><?= $escaper->escapeHtml(__($categoryName)) ?></span>
                            <span class="<?= !empty($categoryName) ? 'ml-2' : '' ?>"><?= $escaper->escapeHtml(!empty($productLoad->getData('supplier_trade_item_code')) ? $productLoad->getData('supplier_trade_item_code') : '') ?></span>
                        </div>
                        <div class="col-span-3 text-sm">
                            <span><?= $escaper->escapeHtml($brand); ?></span>
                        </div>
                        <div class="col-span-3 text-sm flex flex-wrap text-xs items-center op-voorraad-desktop mt-4">
                            <span class="<?= $escaper->escapeHtmlAttr($stockStatus['css_class']); ?> items-center flex">
                                <?php if (isset($stockStatus['icon'])): ?>
                                    <?= /* @noEscape */ $heroiconsSolid->renderHtml($stockStatus['icon'], 'mr-1', 24, 24); ?>
                                <?php endif; ?>
                                <?= $escaper->escapeHtml($stockStatus['label']); ?>
                            </span>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div class="control qty buttonIn flex mt-6">
                            <div class="text-right w-full minicart-item-quantity justify-start">
                                <input type="number" name="qty" required readonly
                                    x-model="cartQty"
                                    x-on:change.debounce="updateInputItemQty"
                                    title="Qty" class="w-20 mr-4 font-size-qty">
                                <div class="item-quantity-nav">
                                    <div class="item-quantity-button item-quantity-up"
                                        @click="updateItemQty('plus')"
                                    >
                                        <?= $heroiconsSolid->renderHtml('chevron-up', 'fill-current text-blue-prussian mr-1.5 mt-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                                    </div>
                                    <div class="item-quantity-button item-quantity-down"
                                        @click="updateItemQty('minus')"
                                    >
                                        <?= $heroiconsSolid->renderHtml('chevron-down', 'fill-current text-blue-prussian mr-1.5 mb-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="">
                            <div class="price-shoppingcart-list">
                                <b x-html="hyva.formatPrice(cartTotal)"></b>
                            </div>
                            <p class="price-perset"><span x-html="hyva.formatPrice(pricePerSet)"></span> <?= $escaper->escapeHtml(__('per '.$perSetStatus)) ?></p>
                        </div>
                    </div>
                    <div>
                        <template x-if="initialTierPrices.length > 0">
                            <p class="title-persen mt-4" x-data="calculateDescriptionTierPrice()">
                                <?= $escaper->escapeHtml(__('Take ')) ?>
                                <span x-text="takeQty"></span>
                                <?= $escaper->escapeHtml(__(' '.$perSetStatus.'s for a ')) ?>
                                <span x-text="takeDiscount"></span>
                                <?= $escaper->escapeHtml(__(' discount')) ?>
                            </p>
                        </template>
                        <template x-if="initialTierPrices.length > 0">
                            <div class="content-button-persen text-sm">
                                <template x-for="(tierPrice, index) in initialTierPrices">
                                    <div @click="updateItemDiscountTierPrice(index, true, tierPrice.price_qty)" class="content-button py-0.5 "
                                        :class="{'md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && cartQty < tierPrice.price_qty,
                                                'active-persen md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && cartQty >= tierPrice.price_qty,
                                                'md:py-1 md:my-0.5' : index > 0 && index < initialTierPrices.length - 1 && cartQty < tierPrice.price_qty,
                                                'active-persen md:py-1 md:my-0.5' : index > 0 && index < initialTierPrices.length - 1 && cartQty >= tierPrice.price_qty,
                                                'md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == initialTierPrices.length - 1 && cartQty < tierPrice.price_qty,
                                                'active-persen md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == initialTierPrices.length - 1 && cartQty >= tierPrice.price_qty}">
                                        <span class="text-2.75 leading-4 md:text-sm" x-html="getPercentTierPrice(tierPrice) + '%'"></span>
                                    </div>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-5 gap-1 pt-6 pb-6 pr-6 pl-6 border-2 shipping-cart max-[1280px]:hidden">
            <?= $block->getImage($block->getProductForThumbnail(), 'cart_page_product_thumbnail')->toHtml() ?>

            <div class="col-span-2">
                <div class="content-title-sku">
                    <p class="-mt-2">
                        <a href="<?= $escaper->escapeUrl($block->getProductUrl()) ?>">
                            <div class="content-title-product text-5.5 color-title-cart">
                                <b><?= $escaper->escapeHtml($block->getProductName()) ?></b>
                            </div>
                        </a>
                    </p>
                    <div class="grid grid-cols-3 gap-1">
                        <div class="col-span-3 text-base text-blue-cadet content-title-product">
                            <span><?= $escaper->escapeHtml(__($categoryName)) ?></span>
                            <span class="<?= !empty($categoryName) ? 'ml-2' : '' ?>"><?= $escaper->escapeHtml(!empty($productLoad->getData('supplier_trade_item_code')) ? $productLoad->getData('supplier_trade_item_code') : '') ?></span>
                        </div>
                    </div>

                    <div class="col-span-3 text-sm">
                        <span><?= $escaper->escapeHtml($brand); ?></span>
                    </div>
                </div>

                <div class="flex flex-wrap text-xs items-center op-voorraad-desktop mt-4">
                    <span class="<?= $escaper->escapeHtmlAttr($stockStatus['css_class']); ?> items-center flex">
                        <?php if (isset($stockStatus['icon'])): ?>
                            <?= /* @noEscape */ $heroiconsSolid->renderHtml($stockStatus['icon'], 'mr-1', 24, 24); ?>
                        <?php endif; ?>
                        <?= $escaper->escapeHtml($stockStatus['label']); ?>
                    </span>
                </div>
            </div>

            <div class="col-span-2 border-0 ml-auto flex flex-col justify-between pr-3">
                <div class="grid grid-cols-3 gap-1">
                    <div class="control qty flex mt-2 left-qty">
                        <div class="w-full item-quantity justify-start ">
                            <input type="number" name="qty" required readonly
                                x-model="cartQty"
                                x-on:change.debounce="updateInputItemQty"
                                title="Qty" class="w-20 mr-4">
                            <div class="item-quantity-nav">
                                <div class="item-quantity-button item-quantity-up"
                                    @click="updateItemQty('plus')"
                                >
                                    <?= $heroiconsSolid->renderHtml('chevron-up', 'fill-current text-blue-prussian mr-1.5 mt-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                                </div>
                                <div class="item-quantity-button item-quantity-down"
                                    @click="updateItemQty('minus')"
                                >
                                    <?= $heroiconsSolid->renderHtml('chevron-down', 'fill-current text-blue-prussian mr-1.5 mb-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-2 justify-between flex flex-row">
                        <div class="price-shoppingcart">
                            <b x-html="hyva.formatPrice(cartTotal)"></b>
                            <p class="text-3.25 text-cadet-space"><span x-html="hyva.formatPrice(pricePerSet)"></span> <?= $escaper->escapeHtml(__('per '.$perSetStatus)) ?></p>
                        </div>
                        <div class="content-trash-xd-destop mt-2">
                            <?= /* @noEscape */ $block->getActions($_item) ?>
                        </div>
                    </div>
                </div>

                <div>
                    <template x-if="initialTierPrices.length > 0">
                        <div class="grid grid-cols-3 gap-1 top-title-persen">
                            <div class="col-span-3 text-base text-blue-prussian"><?= $escaper->escapeHtml(__('Choose your quantity for the corresponding discount:')) ?></div>
                        </div>
                    </template>

                    <template x-if="initialTierPrices.length > 0">
                        <div class="grid grid-cols-3 gap-1">
                            <div class="content-button-persen col-span-3 text-sm">
                                <template x-for="(tierPrice, index) in initialTierPrices">
                                    <div @click="updateItemDiscountTierPrice(index, true, tierPrice.price_qty)" class="content-button py-0.5 "
                                         :class="{'md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && cartQty < tierPrice.price_qty,
                                                'active-persen md:py-1 md:my-0.5 md:ml-0.5 rounded-l-md' : index == 0 && cartQty >= tierPrice.price_qty,
                                                'md:py-1 md:my-0.5' : index > 0 && index < initialTierPrices.length - 1 && cartQty < tierPrice.price_qty,
                                                'active-persen md:py-1 md:my-0.5' : index > 0 && index < initialTierPrices.length - 1 && cartQty >= tierPrice.price_qty,
                                                'md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == initialTierPrices.length - 1 && cartQty < tierPrice.price_qty,
                                                'active-persen md:py-1 md:my-0.5 md:mr-0.5 rounded-r-md' : index == initialTierPrices.length - 1 && cartQty >= tierPrice.price_qty}">
                                        <span class="text-2.75 leading-4 md:text-sm" x-html="getPercentTierPrice(tierPrice) + '%'"></span>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function initCartPage_<?= /** @noEscape */ $product->getId() ?>()
    {
        const itemId = <?= $escaper->escapeHtml($_item->getId()) ?>;
        const cartQty = <?= $escaper->escapeHtmlAttr($block->getQty()) ?>;

        return {
            itemId,
            cartQty,
            cart: {},
            takeQty : cartQty,
            productId: '<?= $product->getId() ?>',
            takeDiscount : '0%',
            defaultFinalPrice: <?= (float)$finalPrice ?>,
            cartPriceFinal: <?= (float)$finalPrice ?>,
            pricePerSet: <?= (float)$finalPrice ?>,
            calculatedFinalPrice: false,
            calculatedFinalPriceWithCustomOptions: false,
            shippingMethod: window.checkoutConfig != null ? window.checkoutConfig.selectedShippingMethod : '',
            initialTierPrices : <?= json_encode($tierPrices, JSON_UNESCAPED_UNICODE) ?>,
            cartPrice : <?= $_item->getPrice() ?>,
            cartTotal : <?= $_item->getRowTotal() ?>,
            cartTotalReguler: <?= $_item->getPrice() ?> * cartQty,
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
            initialBasePrice: <?= (float)$finalPriceExclTax ?>,
            calculatedBasePrice: false,
            customOptionBasePrices: [],
            cartItems: [],
            calculatedBasePriceWithCustomOptions: false,
            <?php endif; ?>
            getPercentTierPrice(tierPrice) {
                let percentageVal = tierPrice.percentage_value;
                if(percentageVal == undefined) {
                    const price = tierPrice.price_excl_tax > tierPrice.price_incl_tax ?
                                tierPrice.price_excl_tax : tierPrice.price_incl_tax;
                    percentageVal = (100 - (price / this.defaultFinalPrice) * 100);
                }
                const decimalLength = percentageVal.toString().split(".")[1].length || 0
                if(decimalLength > 2) {
                    const multiplier = Math.pow(10, 1 || 0);
                    percentageVal = Math.round(percentageVal * multiplier) / multiplier;
                }
                const isFloat = (eval(percentageVal) - Number(percentageVal)) > 0 ? true : false;
                return isFloat ? eval(percentageVal) : Number(percentageVal)
            },
            initCart_<?= /** @noEscape */ $product->getId() ?>($dispatch){
                this.dispatch = $dispatch;
            },
            updateShippingMethod(shippingMethod) {
                this.shippingMethod = shippingMethod;
            },
            getData(data) {
                if (data.cart) {
                    this.cart = data.cart;
                    this.itemsCount = data.cart.items && data.cart.items.length || 0;
                    this.totalCartAmount = this.cart.summary_count;
                    this.setCartItem();
                }
                if (data.selectedShippingMethod) {
                    this.shippingMethod = data.selectedShippingMethod.data;
                }
                this.isLoading = false;
            },
            setCartItem() {
                this.cartItems = this.cart.items || [];
                const productId = this.productId;
                const filteredItem = this.cartItems.filter(function(el) {
                    return el.product_id === productId;
                });

                const lastItem = filteredItem.pop();
                this.cartQty = lastItem != undefined ? lastItem.qty : 0;
                this.takeQty = this.cartQty;

                this.cartPriceFinal = lastItem != undefined ? lastItem.product_price_value : 0;
                this.cartTotal = this.cartPriceFinal * this.cartQty;

                this.cartPrice = lastItem != undefined ? lastItem.final_price : 0
                this.cartTotalReguler = this.cartPrice * this.cartQty;

                this.pricePerSet = this.cartPriceFinal < this.cartPrice
                    ? this.cartPriceFinal : this.cartPrice;
            },
            eventListenersCart_<?= /** @noEscape */ $product->getId() ?>: {
                ['@update-shipping-method.window']($event) {
                    this.updateShippingMethod($event.detail.method)
                },
                ['@update-shipping-method-minicart.window']($event) {
                    this.updateShippingMethod($event.detail.method)
                },
                ['@private-content-loaded.window']($event) {
                    this.getData($event.detail.data)
                },
                ['@update-checkout-data.window']($event) {
                    this.getData($event.detail.data)
                }
            },
            calculateFinalPrice() {
                const findApplicableTierPrice = (initialPrice, withTax) => {
                    const key = withTax ? 'price_incl_tax' : 'price_excl_tax';
                    return Object.values(this.initialTierPrices).reduce((acc, tierPrice) => {
                        if (this.cartQty >= tierPrice.price_qty && tierPrice[key] < acc) {
                            return tierPrice[key];
                        }
                        return acc;
                    }, initialPrice);
                }
                this.calculatedFinalPrice = findApplicableTierPrice(this.defaultFinalPrice, <?= $displayTax ? 'true' : 'false' ?>);
                window.dispatchEvent(new CustomEvent("update-product-final-price", {detail: this.calculatedFinalPrice}));
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                this.calculatedBasePrice = findApplicableTierPrice(<?= (float) $finalPriceExclTax ?>, false);
                window.dispatchEvent(new CustomEvent("update-product-base-price", {detail: {basePrice: this.calculatedBasePrice}}));
                <?php endif; ?>
            },
            calculateFinalPriceWithCustomOptions() {
                const finalPrice = this.calculatedFinalPrice || this.defaultFinalPrice;
                this.calculatedFinalPriceWithCustomOptions = finalPrice;
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                const basePrice = this.calculatedBasePrice || this.initialBasePrice;
                this.calculatedBasePriceWithCustomOptions = basePrice;
                <?php endif; ?>
            },
            isNormalPrice(price, final_price) {
                const fix_price = parseFloat(price).toFixed(2);
                const fix_final_price = parseFloat(final_price).toFixed(2);
                return fix_final_price > fix_price ? true : false;
            },
            updateItemDiscountTierPrice(index, isInit, itemQty) {
                const qty = parseInt(itemQty);
                this.changeTake(index, isInit, qty);
                this.calculateFinalPrice();
                this.calculateFinalPriceWithCustomOptions();
                this.updateItemFromCart(qty);
            },
            changeTake(index = null, isInit, itemQty = 1) {
                if (isInit) {
                    index++
                    let itemTakeQty      = itemQty;
                    let itemTakeDiscount = '0%';

                    const idx = (index == this.initialTierPrices.length ? index-1 : index);
                    const tierPrice  = this.initialTierPrices[idx];
                    const percentage = this.getPercentTierPrice(tierPrice);
                    itemTakeQty = tierPrice.price_qty;
                    itemTakeDiscount = `${percentage}%`

                    this.cartQty = itemQty;
                    this.takeQty = itemTakeQty;
                    this.takeDiscount = itemTakeDiscount
                } else{
                    this.calculateDescriptionTierPrice()
                }
            },
            calculateDescriptionTierPrice() {
                this.cartQty         = this.cartQty < 1 ? 1 : this.cartQty;
                const itemQty        = this.cartQty;
                let itemTakeQty      = itemQty;
                let itemTakeDiscount = '0%';
                let idx = 0;
                if (this.initialTierPrices.length > 0) {
                    this.initialTierPrices.forEach(function(tierPrice, index) {
                        if (itemQty >= tierPrice.price_qty) idx = (index+1);
                    });
                    idx = (idx == this.initialTierPrices.length ? idx-1 : idx);
                    if(this.initialTierPrices[idx] != null){
                        const tierPrice  = this.initialTierPrices[idx];
                        const percentage = this.getPercentTierPrice(tierPrice);
                        itemTakeQty = tierPrice.price_qty;
                        itemTakeDiscount = `${percentage}%`
                    }
                }
                this.takeQty = itemTakeQty;
                this.takeDiscount = itemTakeDiscount;
            },
            updateInputItemQty (e) {
                let max = <?= ($stock_status == 138)? 5 : (($stock_status == 152)? 1: 9999)  ?>;
                const qtyInput = e.target.value;
                if (this.cartQty < max) {
                    this.cartQty = qtyInput;
                }
                this.changeTake();
                this.calculateFinalPrice();
                this.calculateFinalPriceWithCustomOptions();
                this.updateItemFromCart();
            },
            updateItemQty(type) {
                let max = <?= ($stock_status == 138)? 5 : (($stock_status == 152)? 1: 9999)  ?>;
                if (type == 'plus') {
                    if(this.cartQty < max){
                        this.cartQty++;
                    }
                } else {
                    this.cartQty--;
                }
                this.cartQty = this.cartQty == 0 ? 1 : this.cartQty;
                this.changeTake();
                this.calculateFinalPrice();
                this.calculateFinalPriceWithCustomOptions();
                this.updateItemFromCart();
            },
            updateItemFromCart(itemQty) {
                if (itemQty) this.cartQty = itemQty;
                this.isLoading = true;
                const formKey = hyva.getFormKey();
                const postUrl = BASE_URL + 'checkout/sidebar/updateItemQty/';
                const itemId = this.itemId;
                const qty = this.cartQty

                fetch(postUrl, {
                    "headers": {
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                    "body": "form_key=" + formKey + "&item_id=" + itemId+"&item_qty="+qty,
                    "method": "POST",
                    "mode": "cors",
                    "credentials": "include"
                }).then(response => {
                    if (response.redirected) {
                        window.location.href = response.url;
                    } else if (response.ok) {
                        return response.json();
                    } else {
                        window.dispatchMessages && window.dispatchMessages([{
                            type: 'warning',
                            text: '<?= $escaper->escapeJs(__('Could not update item from quote.')) ?>'
                        }]);
                        this.isLoading = false;
                    }
                }).then(result => {
                    this.isLoading = false;
                    if (result.success) {
                        const checkoutConfigData = result.checkout;
                        if (checkoutConfigData) {
                            this.dispatch('update-checkout-data', {data: checkoutConfigData});
                            if (checkoutConfigData.totalsData) {
                                this.dispatch('update-totals', {data: checkoutConfigData.totalsData});
                            }
                            if (checkoutConfigData.selectedShippingMethod) {
                                this.dispatch('update-shipping-method', {method: checkoutConfigData.selectedShippingMethod});
                                this.dispatch('update-shipping-method-minicart', {method: checkoutConfigData.selectedShippingMethod});
                            }
                        }
                    }
                    window.dispatchMessages && window.dispatchMessages([{
                        type: result.success ? 'success' : 'error',
                        text: result.success
                            ? '<?= $escaper->escapeJs(__('You updated the item.')) ?>'
                            : result.error_message
                    }], result.success ? 5000 : 0)
                });
            }
        }
    }
</script>
