<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Checkout\Block\Cart\Item\Renderer\Actions\Remove;
use Magento\Framework\Escaper;

/** @var Remove $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var HeroiconsSolid $heroicons */
$heroicons = $viewModels->require(HeroiconsSolid::class);

?>
<a href="#"
   title="<?= $escaper->escapeHtml(__('Remove item')) ?>"
   class="action action-delete flex items-center justify-between"
   x-data="{}"
   @click.prevent='hyva.postForm(<?= /* @noEscape */ $block->getDeletePostJson() ?>)'
>
    <?= $heroicons->renderHtml('trash-alt', 'w-5 h-5 text-blue-picton left-content'); ?>
</a>
