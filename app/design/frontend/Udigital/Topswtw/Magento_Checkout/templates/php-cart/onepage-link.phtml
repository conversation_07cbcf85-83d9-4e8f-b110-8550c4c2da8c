<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Checkout\Block\Onepage\Link;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Link $block */
?>
<?php if ($block->isPossibleOnepageCheckout()): ?>
    <a @click.prevent.stop="$dispatch('toggle-authentication',
        {url: '<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($block->getCheckoutUrl())) ?>'});"
        href="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($block->getCheckoutUrl())) ?>"
        title="<?= $escaper->escapeHtmlAttr(__('Proceed to Checkout')) ?>"
        class="btn btn-primary rounded text-lg font-medium py-4 px-10 my-4 checkout justify-center text-center padding-top-bottom text-mobile-checkout"
        id="checkout-link-button"
    >
        <?= $escaper->escapeHtml(__('Continue to order')) ?>
    </a>
<?php endif ?>
