<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Checkout\Block\Cart\Totals;
use Magento\Framework\Escaper;
use Magento\Store\Model\ScopeInterface;
use Hyva\Theme\ViewModel\Cart\CheckoutConfig;

/** @var Escaper $escaper */
/** @var Totals $block */
/** @var ViewModelRegistry $viewModels */

$storeConfigViewModel = $viewModels->require(StoreConfig::class);

/** @var CheckoutConfig $checkoutConfigViewModel */
$checkoutConfigViewModel = $viewModels->require(CheckoutConfig::class);

$totalsSort = $storeConfigViewModel->getStoreConfig('sales/totals_sort');
$serializedCheckoutConfig = !empty($block->getCartAction()->getItemsCount()) ? $checkoutConfigViewModel->getSerializedCheckoutConfig() : 'false';
?>
<script>
    const checkoutConfig = <?= /* @noEscape  */ $serializedCheckoutConfig ?>;
    function initCartTotals() {
        return {
            taxSummaryIsOpen: false,
            cart: {},
            checkoutConfig, 
            totalsData: checkoutConfig != null ? checkoutConfig.totalsData : [],
            valueCondition: checkoutConfig != null ? checkoutConfig.valueCondition : 0,
            quoteData: checkoutConfig != null ? checkoutConfig.quoteData : [],
            shippingMethod: checkoutConfig != null ? checkoutConfig.selectedShippingMethod : '',
            totalsSort: <?= /** @noEscape */ json_encode($totalsSort, JSON_UNESCAPED_UNICODE) ?>,
            isLoading: false,
            itemsCount: 0,
            totalCartAmount: 0,
            getData(data) {
                if (data.cart) {
                    this.cart = data.cart;
                    this.itemsCount = data.cart.items && data.cart.items.length || 0;
                    this.totalCartAmount = this.cart.summary_count;
                }
                this.isLoading = false;
                this.setData(data);
            },
            setData(data) {
                if (data.selectedshippingmethod) {
                    this.shippingMethod =  data.selectedshippingmethod.data;
                } else if (data.selectedShippingMethod) {
                    this.shippingMethod = data.selectedShippingMethod;
                }

                if (data.valuecondition) {
                    this.valueCondition = data.valuecondition.data;
                } else if (data.valueCondition) {
                    this.valueCondition = data.valueCondition;
                }

                if (data.totalsdata) {
                    this.totalsData = data.totalsdata.data;
                } else if (data.totalsData) {
                    this.totalsData = data.totalsData;
                }

                if (data.quotedata) {
                    this.quoteData = data.quotedata.data;
                } else if (data.quoteData) {
                    this.quoteData = data.quoteData;
                }
            },
            setCheckoutConfig(data) {
                this.checkoutConfig = data;
            },
            getSortedSegments() {
                const segments = this.totalsData.total_segments;
                if (this.totalsData) {
                    return Array.from(segments).sort((a,b) => {
                        const valueA = this.totalsSort[a.code] || 0;
                        const valueB = this.totalsSort[b.code] || 0;
                        return valueA - valueB;
                    })
                } else return [];
            },
            getTotals() {
                return this.totalsData
            },
            updateTotals(totalsData) {
                this.totalsData = totalsData
            },
            getFormattedPrice(price) {
                return hyva.formatPrice(price)
            },
            getFormattedShippingCost() {
                return this.getFormattedPrice(this.getShippingCost());
            },
            getShippingCost() {
                let cost = 0;
                if (this.checkoutConfig) {
                    const modeTax = this.checkoutConfig.reviewShippingDisplayMode;
                    if (modeTax === 'including') {
                        if (this.totalsData.shipping_incl_tax != undefined &&
                            this.totalsData.shipping_incl_tax != 0) {
                            cost = parseFloat(this.totalsData.shipping_incl_tax)
                        }
                        if (this.shippingMethod && cost == 0) {
                            cost = this.shippingMethod.price_incl_tax;
                        }
                    } else if (modeTax === 'excluding') {
                        if (this.totalsData.shipping_amount != undefined &&
                            this.totalsData.shipping_amount != 0) {
                            cost = parseFloat(this.totalsData.shipping_amount)
                        }
                        if (this.shippingMethod && cost == 0) {
                            cost = this.shippingMethod.price_excl_tax;
                        }
                    }
                }
                
                if (this.shippingMethod &&
                    this.shippingMethod.amount != undefined &&
                    this.shippingMethod.amount != 0 &&
                    cost == 0
                ) {
                    cost = this.shippingMethod.amount;
                }
                return cost;
            },
            getFeeRemaining() {
                const remaining = parseFloat(this.valueCondition) - parseFloat(this.cart.subtotalAmount);
                return hyva.formatPrice(remaining);
            },
            updateShippingMethod(shippingMethod) {
                this.shippingMethod = shippingMethod;
            },
            excludingTaxMessage: '<?= $escaper->escapeJs(__('Excl. Tax')) ?>',
            includingTaxMessage: '<?= $escaper->escapeJs(__('Incl. Tax')) ?>',
            eventListenersTotals: {
                ['@private-content-loaded.window']($event) {
                    this.getData($event.detail.data)
                },
                ['@update-checkout-data.window']($event) {
                    this.getData($event.detail.data)
                    this.setCheckoutConfig($event.detail.data);
                },
                ['@update-totals.window']($event) {
                    this.updateTotals($event.detail.data)
                },
                ['@update-shipping-method.window']($event) {
                    this.updateShippingMethod($event.detail.method)
                },
                ['@update-shipping-method-minicart.window']($event) {
                    this.updateShippingMethod($event.detail.method)
                },
                ['@update-totals-start.window']() {
                    this.isLoading = true;
                },
                ['@update-totals-end.window']() {
                    this.isLoading = false;
                }
            }
        }
    }
</script>
<div id="cart-totals"
     class="cart-totals relative"
     x-data="initCartTotals()"
     x-bind="eventListenersTotals"
>
    <?= $block->getBlockHtml('block-loader') ?>
    <template x-for="(segment, index) in getSortedSegments()" :key="index">
        <div>
            <?= $block->getChildHtml(); ?>
        </div>
    </template>
</div>
