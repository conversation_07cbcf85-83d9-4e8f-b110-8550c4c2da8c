<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Checkout\Block\Cart;
use Magento\Framework\Escaper;

/** @var Cart $block */
/** @var Escaper $escaper */
?>

<div class="w-full mobile-position-cart" x-data="{ isLoading: false }">
    <div class="w-full lg:w-1/4 float-left lg:float-right lg:order-2 top-0 lg:sticky mt-4 md:mt-0 order-position-cart mb-6 w-custom-shoppingcart pt-custom-shoppingcart">
        <?= $block->getChildHtml('cart.summary') ?>
    </div>
    <div class="w-full lg:w-3/4 float-left lg:order-1 lg:pr-8 w-custom-shoppingcart p-custom-shoppingcart">
        <?= $block->getChildHtml('form_before') ?>
        <?= $block->getChildHtml('cart-items') ?>
        <?= $block->getChildHtml('checkout_cart_widget') ?>
    </div>
    <div class="w-full lg:w-3/4 lg:pr-8">
        <?= $block->getChildHtml('cart.discount') ?>
    </div>
    <?= $block->getChildHtml('loading') ?>
</div>
