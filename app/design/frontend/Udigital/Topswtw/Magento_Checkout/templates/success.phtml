<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Checkout\Block\Onepage\Success;
use Magento\Cms\Block\Block;
use Magento\Framework\Escaper;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ReCaptcha;

/** @var Success $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var ReCaptcha $recaptcha */

$orderid = $block->getOrderId();
$Customer = $block->getProductMinor()->getOrder($orderid);
$customerData = $block->getProductMinor()->customerExists($Customer['email']);
$orderData = $block->getProductMinor()->getOrderByIncreamentId($block->getOrderId());
$shippingAddress = $orderData->getShippingAddress();

?>

    <div class="grid grid-cols-1 lg:grid-cols-3 min-[1024px]:max-[1279px]:block">
            <div class="gap-0 content-padding-text">
                    <div class="lg:p-3">
                        <!-- Bedankt voor je bestelling, < Voornaam >! -->
                        <p class="min-[1280px]:text-[20px] min-[1280px]:font-bold min-[1280px]:text-[#002C4B"" ><b><?= $block->getProductMinor()->getDynamicSuccessOne() ?> <br><?= $Customer['name'] ?>!</b></p>
                        <p class="mt-8 text-[#16365B] max-[1280px]:text-[#16365B] max-[1280px]:text-[15px] title-text-thankyoupage-mobile"><?= $block->getProductMinor()->getDynamicSuccessTwo() ?></p>
                        <ul class="mt-6 min-[1280px]:text-[#16365B] min-:text-[16px]  content-text-list-desktop">
                            <li class=" min-[1280px]:text-[#16365B] min-:text-[16px]"> <?= $block->getProductMinor()->getDynamicSuccessThree() ?> <?= "<b>".$Customer['email']."</b>" ?></li>
                            <li class=" min-[1280px]:text-[#16365B] min-:text-[16px]"> <?= $block->getProductMinor()->getDynamicSuccessFour() ?> <?= "<b>".$block->getOrderId()."</b>" ?> </li>
                            <li class=" min-[1280px]:text-[#16365B] min-:text-[16px]"> <?= $block->getProductMinor()->getDynamicSuccessFive() ?>  </li>
                        </ul>
                        <ul class="mt-6 max-[1280px]:text-[#16365B] max-[1280px]:text-[15px] content-text-list-mobile">
                            <li class="max-[1280px]:text-[#16365B] max-[1280px]:text-[15px]"> <?= $block->getProductMinor()->getDynamicSuccessThree() ?> <?= "<b>".$Customer['email']."</b>" ?></li>
                            <li class="max-[1280px]:text-[#16365B] max-[1280px]:text-[15px]"> <?= $block->getProductMinor()->getDynamicSuccessFour() ?> <?= "<b>".$block->getOrderId()."</b>" ?> </li>
                            <li class="max-[1280px]:text-[#16365B] max-[1280px]:text-[15px]"> <?= $block->getProductMinor()->getDynamicSuccessFive() ?>  </li>
                        </ul>
                        <p class="mt-6 max-[1280px]:text-[#16365B] max-[1280px]:text-[15px] min-[1280px]:text-[#16365B] min-:text-[16px]"> <?= $block->getProductMinor()->getDynamicSuccessSix() ?> </p>
                        <p class="mt-6 max-[1280px]:text-[#16365B] max-[1280px]:text-[15px] min-[1280px]:text-[#16365B] min-:text-[16px]"> <?= $block->getProductMinor()->getDynamicSuccessSeven() ?> </p>
                    </div>
            </div>

        <div class="gap-0 content-padding-text">
                <div class="content-form-desktop lg:p-3">
                    <?php if (!$block->getProductMinor()->isCustomerLoggedIn()) {

                        if ($customerData) {
                            $type = 'login';
                            $textLbl = 'Enter your email and password to see your orders.';
                            $textBtn = 'Login';
                            $urlAct = $block->getCustomerAction()->getPostActionUrl();

                        } else {
                            $type = 'register';
                            $textLbl = 'Enter your password and link this order to your account.';
                            $textBtn = 'Link account';
                            $urlAct = $block->getUrl('customer/account/createpost');
                        }

                        ?>
                    <p class="min-[1280px]:text-[20px] min-[1280px]:font-bold min-[1280px]:text-[#002C4B]"><b><?= $escaper->escapeHtml(__("Keep track of all your orders!")) ?> </b></p>
                    <p class="mt-6 min-[1280px]:text-[#002C4B] min-[1280px]:text-[16px]"><?= $escaper->escapeHtml(__($textLbl)) ?></p>

                    <form action="<?= $escaper->escapeUrl($urlAct); ?>" method="post" x-data="initCustomerLoginForm()" @submit.prevent="submitForm()" id="customer-login-form" class="mt-3">
                        <?= $block->getBlockHtml('formkey') ?>

                        <input class="!bg-[#dddddd]" readonly id="email-thankyou" type="email" <?= ($type=='login')?'name="login[username]"':'name="email"' ?>  placeholder="<?= $escaper->escapeHtml(__('Email')) ?>" value="<?= $Customer['email'] ?>">
                        <div class="input-icons">
                            <input required class="mt-6  min-[1280px]:w-[398px] min-[1280px]:rounded-[8px] min-[1280px]:border min-[1280px]:border-[#A2AFBD] min-[1280px]:text-[14px]" id="password-thankyou" <?= ($type=='login')?'name="login[password]"':'name="password"' ?>  type="password"  placeholder="<?= $escaper->escapeHtml(__('Password')) ?>">
                            <svg id="showpassword" x-on:click="showpassword($data)" x-data="{ action :  'show'}"  xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <svg id="hidepassword" x-on:click="showpassword($data)" x-data="{ action :  'hide' }"  xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                        </div>



                        <?php if ($type == 'register') { ?>

                            <div class="input-icons">
                                <input required class="mt-6 min-[1280px]:w-[398px] min-[1280px]:rounded-[8px] min-[1280px]:border min-[1280px]:border-[#A2AFBD] min-[1280px]:text-[14px]" id="password-confirm" name="password_confirmation" type="password"   placeholder="<?= $escaper->escapeHtml(__('Confirm Password')) ?>">
                                <svg id="showpasswordConfirm" x-on:click="showpasswordConfirm($data)" x-data="{ action :  'show' }"  xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <svg id="hidepasswordConfirm" x-on:click="showpasswordConfirm($data)" x-data="{ action :  'hide' }"  xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                            </div>

                            <input type="hidden" name="order_id"    value="<?= /** @noEscape  */ $orderData->getData('entity_id'); ?>">
                            <input type="hidden" name="firstname"   value="<?= $shippingAddress->getFirstname(); ?>">
                            <input type="hidden" name="lastname"    value="<?= $shippingAddress->getLastname(); ?>">
                            <input type="hidden" name="telephone"   value="<?= $shippingAddress->getTelephone(); ?>">
                            <input type="hidden" name="country_id"  value="<?= $shippingAddress->getCountryId(); ?>">
                            <input type="hidden" name="street[]"    value="this will removed by system">
                            <input type="hidden" name="street2[]"   value="<?= $shippingAddress->getStreet()[0]; ?>">
                            <input type="hidden" name="street3[]"   value="<?= (isset($shippingAddress->getStreet()[1]))?$shippingAddress->getStreet()[1]:''; ?>">
                            <input type="hidden" name="street3[]"   value="<?= (isset($shippingAddress->getStreet()[2]))?$shippingAddress->getStreet()[2]:''; ?>">
                            <input type="hidden" name="postcode"    value="<?= $shippingAddress->getPostcode(); ?>">
                            <input type="hidden" name="city"        value="<?= $shippingAddress->getCity(); ?>">
                            <input type="hidden" name="company"     value="<?= $shippingAddress->getCompany(); ?>">
                            <input type="hidden" name="vat_id"      value="<?= $shippingAddress->getVatId(); ?>">
                            <input type="hidden" name="kvk_nummer"  value="<?= $shippingAddress->getKvkNummer(); ?>">
                            <input type="hidden" name="create_address" value="1"/>
                            <input type="hidden" name="default_billing" value="1">
                            <input type="hidden" name="default_shipping" value="1">
                        <?php } ?>

                        <button  class="mt-4 button-email !bg-[#2EB6ED]"><?= $escaper->escapeHtml(__($textBtn)) ?></button>
                    </form>
                    <?php } ?>
                </div>
            </div>

            <div class="content-form-mobile gap-0" style="padding-right: 20px;padding-left: 20px;">
            <?php if (!$block->getProductMinor()->isCustomerLoggedIn()) {

                if ($customerData) {
                    $type = 'login';
                    $textLbl = 'Enter your email and password to see your orders.';
                    $textBtn = 'Login';
                    $urlAct = $block->getCustomerAction()->getPostActionUrl();

                } else {
                    $type = 'register';
                    $textLbl = 'Enter your password and link this order to your account.';
                    $textBtn = 'Link account';
                    $urlAct = $block->getUrl('customer/account/createpost');
                }

                ?>
                <p class="max-[1280px]:text-[17px] max-[1280px]:text-[#16365B] max-[1280px]:font-bold"><b><?= $escaper->escapeHtml(__("Keep track of all your orders!")) ?> </b></p>
                <p class="mt-6 max-[1280px]:text-[#002C4B] max-[1280px]:text-[15px]"><?= $escaper->escapeHtml(__($textLbl)) ?></p>

                    <form action="<?= $escaper->escapeUrl($urlAct); ?>" method="post" x-data="initCustomerLoginFormMobile()" @submit.prevent="submitFormMobile()" id="customer-login-form-mobile" class="mt-4">
                    <?= $block->getBlockHtml('formkey') ?>
                        <input class="!bg-[#dddddd]" id="email-thankyou" type="email" <?= ($type=='login')?'name="login[username]"':'name="email"' ?>  placeholder="<?= $escaper->escapeHtml(__('Email')) ?>" value="<?= $Customer['email'] ?>">
                        <div class="input-icons">
                            <input required class="mt-4 max-[1280px]:w-[80%] max-[1280px]:rounded-[8px] max-[1280px]:border max-[1280px]:border-[#A2AFBD] max-[1280px]:text-[12px] max-[1280px]:h-[32px]" type="password" id="password-thankyou-mobile" <?= ($type=='login')?'name="login[password]"':'name="password"' ?> placeholder="<?= $escaper->escapeHtml(__('Password')) ?>">
                            <svg id="showpassword-mobile" x-on:click="showpassword($data)" x-data="{ action :  'show' }"  xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <svg id="hidepassword-mobile" x-on:click="showpassword($data)" x-data="{ action :  'hide' }"  xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                        </div>

                        <?php if ($type == 'register') { ?>

                        <div class="input-icons">
                            <input required class="mt-4 max-[1280px]:w-[80%] max-[1280px]:rounded-[8px] max-[1280px]:border max-[1280px]:border-[#A2AFBD] max-[1280px]:text-[12px] max-[1280px]:h-[32px]    " id="password-confirm-mobile" name="password_confirmation" type="password"   placeholder="<?= $escaper->escapeHtml(__('Confirm Password')) ?>">
                            <svg id="showpasswordConfirm-mobile" x-on:click="showpasswordConfirm($data)" x-data="{ action :  'show' }"  xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg id="hidepasswordConfirm-mobile" x-on:click="showpasswordConfirm($data)" x-data="{ action :  'hide' }"  xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                        </svg>
                        </div>


                        <input type="hidden" name="order_id"    value="<?= /** @noEscape  */ $orderData->getData('entity_id'); ?>">
                        <input type="hidden" name="firstname"   value="<?= $shippingAddress->getFirstname(); ?>">
                        <input type="hidden" name="lastname"    value="<?= $shippingAddress->getLastname(); ?>">
                        <input type="hidden" name="telephone"   value="<?= $shippingAddress->getTelephone(); ?>">
                        <input type="hidden" name="country_id"  value="<?= $shippingAddress->getCountryId(); ?>">
                        <input type="hidden" name="street[]"    value="this will removed by system">
                        <input type="hidden" name="street2[]"   value="<?= $shippingAddress->getStreet()[0]; ?>">
                        <input type="hidden" name="street3[]"   value="<?= (isset($shippingAddress->getStreet()[1]))?$shippingAddress->getStreet()[1]:''; ?>">
                        <input type="hidden" name="street3[]"   value="<?= (isset($shippingAddress->getStreet()[2]))?$shippingAddress->getStreet()[2]:''; ?>">
                        <input type="hidden" name="postcode"    value="<?= $shippingAddress->getPostcode(); ?>">
                        <input type="hidden" name="city"        value="<?= $shippingAddress->getCity(); ?>">
                        <input type="hidden" name="company"     value="<?= $shippingAddress->getCompany(); ?>">
                        <input type="hidden" name="vat_id"      value="<?= $shippingAddress->getVatId(); ?>">
                        <input type="hidden" name="kvk_nummer"  value="<?= $shippingAddress->getKvkNummer(); ?>">
                        <input type="hidden" name="create_address" value="1"/>
                        <input type="hidden" name="default_billing" value="1">
                        <input type="hidden" name="default_shipping" value="1">
                        <?php } ?>

                        <button  class="mt-4 button-email !bg-[#2EB6ED]"><?= $escaper->escapeHtml(__($textBtn)) ?></button>
                    </form>
                <?php } ?>
            </div>
            <div class="rounded  gap-0 content-padding-text lg:p-3">
                <div class="right-sidebar-klantenservice">
                    <?= $this->getLayout()->createBlock(Block::class)
                        ->setBlockId('right-sidebar-klantenservice')
                        ->toHtml(); ?>
                </div>
            </div>
    </div>

<script>

    function initCustomerLoginForm() {
            return {
                errors: 0,
                hasCaptchaToken: 0,
                displayErrorMessage: false,
                errorMessages: [],
                setErrorMessages(messages) {
                    this.errorMessages = [messages]
                    this.displayErrorMessage = this.errorMessages.length
                },
                submitForm() {
                    // do not rename $form, the variable is the expected name in the recaptcha output
                    const $form = document.querySelector('#customer-login-form');


                    if (this.errors === 0) {
                        $form.submit();
                    }
                },
                showpassword({action}){

                    if (action == "hide") {

                        document.getElementById("showpassword").style.display = "block";
                        document.getElementById("hidepassword").style.display = "none";
                        document.getElementById("password-thankyou").type = "password";

                    }

                    if (action == "show") {

                        document.getElementById("showpassword").style.display = "none";
                        document.getElementById("hidepassword").style.display = "block";
                        document.getElementById("password-thankyou").type = "text";
                    }


                    if (action == "hide") {

                        document.getElementById("showpassword-mobile").style.display = "block";
                        document.getElementById("hidepassword-mobile").style.display = "none";
                        document.getElementById("password-thankyou-mobile").type = "password";

                    }

                    if (action == "show") {

                        document.getElementById("showpassword-mobile").style.display = "none";
                        document.getElementById("hidepassword-mobile").style.display = "block";
                        document.getElementById("password-thankyou-mobile").type = "text";
                    }
                },
                showpasswordConfirm({action}){

                    if (action == "hide") {

                        document.getElementById("showpasswordConfirm").style.display = "block";
                        document.getElementById("hidepasswordConfirm").style.display = "none";
                        document.getElementById("password-confirm").type = "password";

                    }

                    if (action == "show") {

                        document.getElementById("showpasswordConfirm").style.display = "none";
                        document.getElementById("hidepasswordConfirm").style.display = "block";
                        document.getElementById("password-confirm").type = "text";
                    }

                    if (action == "hide") {

                            document.getElementById("showpasswordConfirm-mobile").style.display = "block";
                            document.getElementById("hidepasswordConfirm-mobile").style.display = "none";
                            document.getElementById("password-confirm-mobile").type = "password";

                        }

                        if (action == "show") {

                            document.getElementById("showpasswordConfirm-mobile").style.display = "none";
                            document.getElementById("hidepasswordConfirm-mobile").style.display = "block";
                            document.getElementById("password-confirm-mobile").type = "text";
                        }

                }
            }
    }

    function initCustomerLoginFormMobile() {
            return {
                errors: 0,
                hasCaptchaToken: 0,
                showPassword: false,
                displayErrorMessage: false,
                errorMessages: [],
                setErrorMessages(messages) {
                    this.errorMessages = [messages]
                    this.displayErrorMessage = this.errorMessages.length
                },
                submitFormMobile() {
                    // do not rename $form, the variable is the expected name in the recaptcha output
                    const $form = document.querySelector('#customer-login-form-mobile');


                    if (this.errors === 0) {
                        $form.submit();
                    }
                },
                showpassword({action}){

                    if (action == "hide") {

                        document.getElementById("showpassword").style.display = "block";
                        document.getElementById("hidepassword").style.display = "none";
                        document.getElementById("password-thankyou").type = "password";

                    }

                    if (action == "show") {

                        document.getElementById("showpassword").style.display = "none";
                        document.getElementById("hidepassword").style.display = "block";
                        document.getElementById("password-thankyou").type = "text";
                    }


                    if (action == "hide") {

                        document.getElementById("showpassword-mobile").style.display = "block";
                        document.getElementById("hidepassword-mobile").style.display = "none";
                        document.getElementById("password-thankyou-mobile").type = "password";

                    }

                    if (action == "show") {

                        document.getElementById("showpassword-mobile").style.display = "none";
                        document.getElementById("hidepassword-mobile").style.display = "block";
                        document.getElementById("password-thankyou-mobile").type = "text";
                    }
                },
                showpasswordConfirm({action}){

                    if (action == "hide") {

                        document.getElementById("showpasswordConfirm").style.display = "block";
                        document.getElementById("hidepasswordConfirm").style.display = "none";
                        document.getElementById("password-confirm").type = "password";

                    }

                    if (action == "show") {

                        document.getElementById("showpasswordConfirm").style.display = "none";
                        document.getElementById("hidepasswordConfirm").style.display = "block";
                        document.getElementById("password-confirm").type = "text";
                    }

                    if (action == "hide") {

                            document.getElementById("showpasswordConfirm-mobile").style.display = "block";
                            document.getElementById("hidepasswordConfirm-mobile").style.display = "none";
                            document.getElementById("password-confirm-mobile").type = "password";

                        }

                        if (action == "show") {

                            document.getElementById("showpasswordConfirm-mobile").style.display = "none";
                            document.getElementById("hidepasswordConfirm-mobile").style.display = "block";
                            document.getElementById("password-confirm-mobile").type = "text";
                        }

                }
            }
    }

    // function showpassword({action}){

    //     if (action == "hide") {

    //         document.getElementById("showpassword").style.display = "block";
    //         document.getElementById("hidepassword").style.display = "none";
    //         document.getElementById("password-thankyou").type = "password";

    //     }

    //     if (action == "show") {

    //         document.getElementById("showpassword").style.display = "none";
    //         document.getElementById("hidepassword").style.display = "block";
    //         document.getElementById("password-thankyou").type = "text";
    //     }


    //     if (action == "hide") {

    //         document.getElementById("showpassword-mobile").style.display = "block";
    //         document.getElementById("hidepassword-mobile").style.display = "none";
    //         document.getElementById("password-thankyou-mobile").type = "password";

    //     }

    //     if (action == "show") {

    //         document.getElementById("showpassword-mobile").style.display = "none";
    //         document.getElementById("hidepassword-mobile").style.display = "block";
    //         document.getElementById("password-thankyou-mobile").type = "text";
    //     }
    // }

    function showpasswordConfirm({action}){

       if (action == "hide") {

           document.getElementById("showpasswordConfirm").style.display = "block";
           document.getElementById("hidepasswordConfirm").style.display = "none";
           document.getElementById("password-confirm").type = "password";

       }

       if (action == "show") {

           document.getElementById("showpasswordConfirm").style.display = "none";
           document.getElementById("hidepasswordConfirm").style.display = "block";
           document.getElementById("password-confirm").type = "text";
       }

       if (action == "hide") {

            document.getElementById("showpasswordConfirm-mobile").style.display = "block";
            document.getElementById("hidepasswordConfirm-mobile").style.display = "none";
            document.getElementById("password-confirm-mobile").type = "password";

        }

        if (action == "show") {

            document.getElementById("showpasswordConfirm-mobile").style.display = "none";
            document.getElementById("hidepasswordConfirm-mobile").style.display = "block";
            document.getElementById("password-confirm-mobile").type = "text";
        }

   }

</script>


