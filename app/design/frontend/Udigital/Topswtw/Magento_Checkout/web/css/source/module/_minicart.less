// @codingStandardsIgnoreFile
// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@minicart__border-color: @color-white-azureish;
@minicart__padding-horizontal: @indent__base;

@minicart-qty__height: 18px;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Minicart
    //  ---------------------------------------------

    .price-container{
        float: right;
    }

    .label-minicart{
        .lib-css(color, @color-blue-prussian);
        text-align: left;
        letter-spacing: 0px;
        opacity: 1;
        font-weight: @font-weight__bold;
        font-size: 15px;
    }

    .total-minicart{
        .lib-css(color, @color-blue-prussian);
        font-weight: @font-weight__bold;
        font-size: 19px;
    }

    .block-minicart {
        .items-total {
            float: left;
            margin: 20px @indent__s;

            .count {
                font-weight: @font-weight__bold;
            }
        }

        .subtotal {
            margin: @indent__base @indent__s @indent__s;
            text-align: right;

            .label {
                &:extend(.abs-colon all);
            }
        }

        .amount {
            .price-wrapper {
                &:first-child {
                    .price {
                        font-size: 19px;
                        font-weight: @font-weight__bold;
                        .lib-css(color, @color-blue-picton);
                    }
                }
            }
        }

        .subtitle {
            display: none;

            &.empty {
                display: block;
                font-size: 14px;
                padding: @indent__l 0 @indent__base;
                text-align: center;
            }
        }

        .text {
            &.empty {
                text-align: center;
            }
        }

        .block-content {
            > .actions {
                margin-top: 15px;
                text-align: center;

                > .primary {
                    margin: 0 @indent__s 15px;

                    .action {
                        &.primary {
                            &:extend(.abs-button-l all);
                            display: block;
                            margin-bottom: 15px;
                            width: 100%;

                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
        }

        .block-category-link,
        .block-product-link,
        .block-cms-link,
        .block-banners {
            margin: 15px 0 0;
            text-align: center;
        }
    }

    .minicart-wrapper {
        .lib-dropdown(
        @_toggle-selector: ~'.action.showcart',
        @_options-selector: ~'.block-minicart',
        @_dropdown-list-width: 320px,
        @_dropdown-list-position-right: 0,
        @_dropdown-list-pointer-position: right,
        @_dropdown-list-pointer-position-left-right: 26px,
        @_dropdown-list-z-index: 101,
        @_dropdown-toggle-icon-content: @icon-cart,
        @_dropdown-toggle-active-icon-content: @icon-cart,
        @_dropdown-list-item-padding: false,
        @_dropdown-list-item-hover: false,
        @_icon-font-position: before,
        @_icon-font-size: 20px,
        @_icon-font-line-height: 33px,
        @_icon-font-color: @minicart-icons-color,
        @_icon-font-color-hover: @minicart-icons-color-hover,
        @_icon-font-color-active: @minicart-icons-color
        );
        float: right;

        .block-minicart {
            .lib-css(padding, 25px 21px 10px 21px);

            .block-title {
                display: none;
            }
        }

        .product {
            .actions {
                float: right;
                margin: -28px 0 0;
                text-align: right;
                display: none;

                > .primary,
                > .secondary {
                    display: inline;
                }
            }
        }

        .action {
            &.close {
                .lib-button-icon(
                    @icon-remove,
                    @_icon-font-size: 12px,
                    @_icon-font-line-height: 12px,
                    @_icon-font-text-hide: true
                );
                .lib-button-reset();
                height: 40px;
                position: absolute;
                right: 0;
                top: 0;
                width: 40px;
                display: none;
            }

            &.showcart {
                white-space: nowrap;

                .text {
                    &:extend(.abs-visually-hidden all);
                }

                .counter.qty.minicart {
                    background : transparent!important;
                    font-weight: bold;
                }

                .counter.qty {
                    .lib-css(background, @active__color);
                    .lib-css(color, @page__background-color);
                    .lib-css(height, @minicart-qty__height);
                    .lib-css(line-height, @minicart-qty__height);
                    font-weight: bold;
                    border-radius: 10px;
                    display: inline-block;
                    margin: 6px 0 0 5px;
                    min-width: 18px;
                    overflow: hidden;
                    padding: 1.5px 1.5px;
                    text-align: center;
                    white-space: normal;

                    &.empty {
                        display: none;
                    }

                    .loader {
                        > img {
                            .lib-css(max-width, @minicart-qty__height);
                        }
                    }
                }

                .counter-label {
                    &:extend(.abs-visually-hidden all);
                }
            }
        }

        .minicart-widgets {
            margin-top: 15px;
        }
    }

    .minicart-items-wrapper {
        .lib-css(border-bottom, 1px solid @minicart__border-color);
        .lib-css(margin, 0 @indent__s);
        border-left: 0;
        border-right: 0;
        overflow-x: auto;
    }

    .minicart-items {
        .lib-list-reset-styles();

        .product-item {
            padding: @indent__base 0;

            &:not(:first-child) {
                .lib-css(border-top, 1px solid @minicart__border-color);
            }

            &:first-child {
                padding-top: 0;
            }

            > .product {
                &:extend(.abs-add-clearfix all);
            }
        }

        .product-image-wrapper {
            &:extend(.abs-reset-image-wrapper all);
        }

        .product-item-pricing {
            .label {
                display: inline-block;
            }
        }

        .price-minicart {
            margin-bottom: @indent__xs;
        }

        .message {
            margin-bottom: 0;
            margin-top: 10px;
        }

        .product {
            > .product-item-photo,
            > .product-image-container {
                float: left;
            }

            .toggle {
                .lib-icon-font(
                    @_icon-font-content: @icon-down,
                    @_icon-font-size: 16px,
                    @_icon-font-color: @color-blue-cadet,
                    @_icon-font-line-height: 16px,
                    @_icon-font-text-hide: false,
                    @_icon-font-position: after,
                    @_icon-font-display: block
                );
                cursor: pointer;
                position: relative;
                white-space: nowrap;
                font-size: 13px;
                .lib-css(color, @color-blue-cadet);

                &:after {
                    position: static;
                    right: @indent__base;
                    top: 0;
                    margin-left: 10px;
                    font-size: 13px;
                }
            }

            &.active {
                > .toggle {
                    .lib-icon-font-symbol(
                        @_icon-font-content: @icon-up,
                        @_icon-font-position: after
                    );
                }
            }
        }

        .product-item-name {
            font-weight: @font-weight__regular;
            margin: 0 0 @indent__s;

            a {
                .lib-css(color, @color-blue-prussian);
                text-decoration: none;
                font-weight: bold;
                font-size: 13px;
            }
        }

        .product-item-details {
            padding-left: 88px;

            .price {
                font-weight: @font-weight__bold;
                font-size: 19px;
            }

            .price-including-tax,
            .price-excluding-tax {
                display: inline-block !important;
                .lib-css(color, @color-blue-picton);
            }

            .weee[data-label] {
                .lib-font-size(11);

                .label {
                    &:extend(.abs-no-display all);
                }
            }

            .details-qty {
                margin-top: @indent__s;
            }

            .content{
                padding: 15px 0;
                .lib-css(color, @color-blue-cadet);
                font-size: 13px;
            }
        }

        .product.options {
            .tooltip.toggle {
                .lib-icon-font(
                    @icon-down,
                    @_icon-font-size: 28px,
                    @_icon-font-line-height: 28px,
                    @_icon-font-text-hide: true,
                    @_icon-font-margin: -3px 0 0 7px,
                    @_icon-font-position: after
                );

                .details {
                    display: none;
                }
            }
        }

        .details-qty,
        .price-minicart {
            .label {
                &:extend(.abs-colon all);
            }
        }

        .item-qty {
            text-align: center;
            width: 59px;
            height: 30px;
            border: 1px solid #002C4B;
            border-radius: 4px;
            opacity: 1;
        }

        .update-cart-item {
            .lib-font-size(11);
            font-weight: normal;
            margin-left: 5px;
            vertical-align: top;
            .lib-css(background, @color-blue-picton 0% 0% no-repeat padding-box);
            .lib-css(color, @color-blue-prussian);
            border-radius: 8px;
        }

        .subtitle {
            display: none;
        }

        .action {
            &.edit,
            &.delete {
                .lib-icon-font(
                    @icon-settings,
                    @_icon-font-size: 20px,
                    @_icon-font-line-height: 20px,
                    @_icon-font-text-hide: true,
                    @_icon-font-color: @color-gray19,
                    @_icon-font-color-hover: @color-gray19,
                    @_icon-font-color-active: @color-gray19
                );
            }

            &.delete {
                .lib-icon-font-symbol(
                    @_icon-font-content: @icon-trash
                );
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    .minicart-wrapper .block-minicart {
        width: 290px;
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .minicart-wrapper {
        margin-top: @indent__s;
        .lib-clearfix();
        .product {
            .actions {
                float: left;
                margin: 10px 0 0 0;
            }
        }
        .update-cart-item {
            float: right;
            margin-left: 0;
        }
    }
}

.minicart-wrapper {
    margin-top: 0!important;
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .minicart-wrapper {
        margin-top: -5px!important;
        margin-left: 13px;
        .lib-css(border,1px solid @color-blue-cadet);
        border-radius: 8px;
        padding: 3px 15px;

        .block-minicart:before,
        .block-minicart:after{
            display: none;
        }

        .block-minicart {
            .lib-css(background, @color-white 0% 0% no-repeat padding-box);
            .lib-css(border, 1px solid @color-blue-picton);
            box-shadow: 0px 3px 6px #00000029;
            right: -1px;
            width: 398px;
            margin-top: 10px;
            border-radius: 12px;
            opacity: 1;
        }
    }
}
