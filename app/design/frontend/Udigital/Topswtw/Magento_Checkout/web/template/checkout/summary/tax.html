<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!-- ko if: ifShowValue() && !ifShowDetails() -->
<tr class="totals-tax">
    <th data-bind="text: title" class="mark" scope="row"></th>
    <td data-bind="attr: {'data-th': title}" class="amount">
        <!-- ko if: isCalculated() -->
            <span class="price"
                  data-bind="text: getValue()"></span>
        <!-- /ko -->
        <!-- ko ifnot: isCalculated() -->
            <span class="not-calculated"
                  data-bind="text: getValue()"></span>
        <!-- /ko -->
    </td>
</tr>
<!-- /ko -->
<!-- ko if: ifShowValue() && ifShowDetails() -->
    <tr class="totals-tax-summary"
        data-bind="mageInit: {'toggleAdvanced':{'selectorsToggleClass': 'shown', 'baseToggleClass': 'expanded', 'toggleContainers': '.totals-tax-details'}}">
        <th data-bind="text: title" class="mark" scope="row"></th>
        <td data-bind="attr: {'data-th': title }" class="amount">
            <!-- ko if: isCalculated() -->
            <span class="price"
                  data-bind="text: getValue()"></span>
            <!-- /ko -->
            <!-- ko ifnot: isCalculated() -->
            <span class="not-calculated"
                  data-bind="text: getValue()"></span>
            <!-- /ko -->
        </td>
    </tr>
    <!-- ko foreach: getDetails() -->
        <!-- ko foreach: rates -->
        <tr class="totals-tax-details">
            <!-- ko if: percent -->
                <th class="mark" scope="row" data-bind="text: title + ' (' + percent + '%)'"></th>
            <!-- /ko -->
            <!-- ko if: !percent -->
                <th class="mark" scope="row" data-bind="text: title"></th>
            <!-- /ko -->
            <td class="amount">
                <!-- ko if: $parents[1].isCalculated() -->
                <span class="price"
                      data-bind="text: $parents[1].getTaxAmount($parents[0], percent), attr: {'data-th': title, 'rowspan': $parents[0].rates.length }"></span>
                <!-- /ko -->
                <!-- ko ifnot: $parents[1].isCalculated() -->
                <span class="not-calculated"
                      data-bind="text: $parents[1].getTaxAmount($parents[0], percent), attr: {'data-th': title, 'rowspan': $parents[0].rates.length }"></span>
                <!-- /ko -->
            </td>
        </tr>
        <!-- /ko -->
    <!-- /ko -->
<!-- /ko -->
