<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="block-title">
    <strong>
        <span class="text" translate="'My Cart'"></span>
        <span
            class="qty empty"
            text="getCartParam('summary_count')"
            data-bind="css: { empty: !!getCartParam('summary_count') == false },
                       attr: { title: $t('Items in Cart') }">
        </span>
    </strong>
</div>

<div class="block-content">
    <if args="getCartParam('summary_count')">
        <strong class="subtitle" translate="'Recently added item(s)'"></strong>
        <div data-action="scroll" class="minicart-items-wrapper">
            <ol id="mini-cart" class="minicart-items" data-bind="foreach: { data: getCartItems(), as: 'item' }">
                <each args="$parent.getRegion($parent.getItemRenderer(item.product_type))"
                      render="{name: getTemplate(), data: item, afterRender: function() {$parents[1].initSidebar()}}"></each>
            </ol>
        </div>
    </if>

    <ifnot args="getCartParam('summary_count')">
        <strong class="subtitle empty"
                translate="'You have no items in your shopping cart.'"></strong>
        <if args="getCartParam('cart_empty_message')">
            <p class="minicart empty text" text="getCartParam('cart_empty_message')"></p>
            <div class="actions">
                <div class="secondary">
                    <a class="action viewcart" data-bind="attr: {href: shoppingCartUrl}">
                        <span translate="'View and Edit Cart'"></span>
                    </a>
                </div>
            </div>
        </if>
    </ifnot>

    <button type="button"
            id="btn-minicart-close"
            class="action close"
            data-action="close"
            data-bind="
                attr: {
                    title: $t('Close')
                },
                click: closeMinicart()
            ">
        <span translate="'Close'"></span>
    </button>

    
    <if args="getCartParam('summary_count')">
        <div class="row mt-5 mx-3">
            <div class="offset-3 col-4">
                <!-- ko if: (getCartParam('summary_count') > 1) -->
                <span class="label-minicart" translate="'Items in Cart'"></span>
                <!--/ko-->
                <!-- ko if: (getCartParam('summary_count') === 1) -->
                    <span class="label-minicart" translate="'Item in Cart'"></span>
                <!--/ko-->
            </div>
            <div class="col-5 text-end">
                <span class="count total-minicart" if="maxItemsToDisplay < getCartLineItemsCount()" text="maxItemsToDisplay"></span>
                <translate args="'of'" if="maxItemsToDisplay < getCartLineItemsCount()"></translate>
                <span class="count total-minicart" text="getCartParam('summary_count').toLocaleString(window.LOCALE)"></span>
            </div>

            <each args="getRegion('subtotalContainer')" render=""></each>
            <each args="getRegion('extraInfo')" render=""></each>
        </div>

        <div class="row w-100 text-center mt-5">
            <div class="col-6">
                <div class="actions mt-3" if="getCartParam('summary_count')">
                    <div class="secondary">
                        <a class="action cart-secondary-topswtw viewcart " data-bind="attr: {href: shoppingCartUrl}">
                            <span translate="'Change shopping cart'"></span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="actions" if="getCartParam('possible_onepage_checkout')">
                    <div class="primary">
                        <button
                                id="top-cart-btn-checkout"
                                type="button"
                                class="action cart-primary-topswtw checkout"
                                data-action="close"
                                data-bind="
                                    attr: {
                                        title: $t(`I'm going to order`)
                                    },
                                    click: closeMinicart()
                                "
                                translate="`I'm going to order`">
                        </button>
                        <div data-bind="html: getCartParamUnsanitizedHtml('extra_actions')"></div>
                    </div>
                </div>
            </div>
        </div>
        
    </if>

    <div id="minicart-widgets" class="minicart-widgets" if="regionHasElements('promotion')">
        <each args="getRegion('promotion')" render=""></each>
    </div>
</div>
<each args="getRegion('sign-in-popup')" render=""></each>
