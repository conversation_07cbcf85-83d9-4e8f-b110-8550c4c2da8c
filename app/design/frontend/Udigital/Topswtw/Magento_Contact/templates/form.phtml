<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Contact\Block\ContactForm;
use Magento\Framework\Escaper;
use Hyva\Theme\ViewModel\ReCaptcha;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var ContactForm $block */
/** @var Escaper $escaper */
/** @var ReCaptcha $recaptcha|null */

$formId = 'contact';

// Do not replace this with $viewModels->require(ReCaptcha::class); that would break the dependency
// on the Magento_ReCaptchaContact module
$recaptcha = $block->getData('viewModelRecaptcha');

$cmsBlock = $block->getLayout()->createBlock('Magento\Cms\Block\Block');
?>
<div class="flex-columns-wrapper justify-between py-8 gap-4">
    <div class="lg:w-1/2 md:w-2/4 contact-information ">
        <?= $cmsBlock->setBlockId('contact-information')->toHtml(); ?>
    </div>

    <div class="lg:w-1/2 md:w-2/4">
        <form class="form contact custom opacity-100 w-4/5 ml-0 -mt-14 mt-2.5 mb-0 px-18 py-14 rounded-xl bg-white-azureish max-screen-110:w-full max-screen-110:mt-6.25 max-screen-110:mt-5 max-screen-110:mb-0 max-screen-110:mx-0 max-screen-110:p-0 max-screen-110:p-7.5 min-[768px]:max-[1144px]:w-full min-[768px]:max-[1144px]:pr-[20px] min-[768px]:max-[1144px]:pt-[20px] min-[768px]:max-[1144px]:pl-[20px] min-[768px]:max-[1144px]:pb-[20px]"
            action="<?= $escaper->escapeUrl($block->getFormAction()) ?>"
            id="<?= $escaper->escapeHtmlAttr($formId) ?>"
            method="post"
            x-data="initContactForm()"
            @submit.prevent="submitForm()"
        >
            <fieldset class="fieldset-contact-form mb-7.5">
                <div class="flex flex-col w-full">
                    <h2 class="sm:text-2xl text-xl font-medium title-font mb-4 title-contact-form text-blue-prussian text-xl font-bold max-screen-110:text-3.75">
                        <span><?= $escaper->escapeHtml(__('Contact form')) ?></span>
                    </h2>
                </div>
                <div class="flex flex-wrap -m-2">
                    <div class="field name required p-2 w-full text-left -mb-2.5">
                        <label class="label text-blue-prussian text-sm mb-0 max-screen-110:text-3" for="name"><span><?= $escaper->escapeHtml(
                            __('Name')
                        ) ?></span></label>
                        <input name="name" id="name" required title="<?= $escaper->escapeHtmlAttr(__('Name')) ?>"
                            value="<?= $escaper->escapeHtmlAttr(
                                $block->getContactAction()->getPostValue('name')
                                    ?: $block->getContactAction()->getUserName()
                            ) ?>"
                            class="mt-2 input input-light w-full border-0"
                            type="text"
                        />
                    </div>
                    <div class="field email required p-2 w-full text-left -mb-2.5">
                        <label class="label text-blue-prussian text-sm mb-0 max-screen-110:text-3" for="email"><span><?= $escaper->escapeHtml(
                            __('Email Address')
                        ) ?></span></label>
                        <input name="email" id="email" required title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                            value="<?= $escaper->escapeHtmlAttr(
                                $block->getContactAction()->getPostValue('email')
                                    ?: $block->getContactAction()->getUserEmail()
                            ) ?>"
                            class="mt-2 input input-light w-full border-0"
                            type="email"/>
                    </div>
                    <div class="field telephone p-2 w-full text-left -mb-2.5">
                        <label class="label text-blue-prussian text-sm mb-0 max-screen-110:text-3" for="telephone"><span><?= $escaper->escapeHtml(
                            __('Phone Number')
                        ) ?></span></label>
                        <input name="telephone" id="telephone"
                            title="<?= $escaper->escapeHtmlAttr(__('Phone Number')) ?>"
                            value="<?= $escaper->escapeHtmlAttr(
                                $block->getContactAction()->getPostValue('telephone')
                            ) ?>"
                            class="mt-2 input input-light w-full border-0"
                            type="text"/>
                    </div>
                    <div class="field comment required p-2 w-full text-left -mb-2.5">
                        <label class="label text-blue-prussian text-sm mb-0 max-screen-110:text-3" for="comment"><span><?= $escaper->escapeHtml(
                            __('Message:')
                        ) ?></span></label>
                        <textarea name="comment" id="comment" required
                                title="<?= $escaper->escapeHtmlAttr(__('What’s on your mind?')) ?>"
                                class="mt-2 input input-light border-0 w-full h-48" cols="5" rows="3"><?= $escaper->escapeHtml(
                                    $block->getContactAction()->getPostValue('comment')
                                ) ?></textarea>
                    </div>
                    <?= $block->getChildHtml('form.additional.info') ?>
                    <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CONTACT) : '' ?>
                </div>
            </fieldset>
            <div class="w-full">
                <?= $block->getBlockHtml('formkey') ?>
                <button type="submit" title="<?= $escaper->escapeHtmlAttr(__('Submit')) ?>" class="btn btn-primary contact-form  bg-blue-picton hover:bg-blue-picton text-blue-prussian hover:text-blue-prussian text-lg max-screen-110:px-7.5 max-screen-110:py-2">
                    <span><?= $escaper->escapeHtml(__('Send')) ?></span>
                </button>
            </div>
            <div class="w-full">
                <?= $block->getChildHtml('form.additional.after') ?>
                <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CONTACT) : '' ?>
            </div>
        </form>
    </div>

    <div class="lg:w-1/4 md:w-1/4 right-sidebar-klantenservice min-screen-110:w-1/5 min-screen-110:-mt-14">
        <?= $cmsBlock->setBlockId('right-sidebar-klantenservice')->toHtml(); ?>
    </div>
    <script>
        function initContactForm() {
            return {
                errors: 0,
                hasCaptchaToken: 0,
                submitForm() {
                    // Do not rename $form, the variable is expected to be declared in the recaptcha output
                    const $form = document.querySelector('#<?= $escaper->escapeJs($formId) ?>');
                    <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CONTACT) : '' ?>

                    if (this.errors === 0) {
                        $form.submit();
                    }
                }
            }
        }
    </script>
</div>
