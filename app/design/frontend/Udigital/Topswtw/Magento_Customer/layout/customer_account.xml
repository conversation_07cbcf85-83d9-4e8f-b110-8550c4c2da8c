<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" label="Customer My Account (All Pages)" design_abstraction="custom">
    <!-- remove Account Information in sidebar menu -->
    <referenceBlock name="customer-account-navigation-account-edit-link" remove="true"/> 
    <!-- remove Address Book in sidebar menu -->
    <referenceBlock name="customer-account-navigation-address-link" remove="true"/> 
    <!-- remove My Product Reviews in sidebar menu -->
    <referenceBlock name="customer-account-navigation-product-reviews-link" remove="true"/> 
    <!-- remove Stored Payment Methods in sidebar menu -->
    <referenceBlock name="customer-account-navigation-my-credit-cards-link" remove="true"/> 
    <!-- remove Newsletter Subscriptions in sidebar menu -->
    <referenceBlock name="customer-account-navigation-newsletter-subscriptions-link" remove="true"/> 
    <!-- remove My Wish List in sidebar menu -->
    <referenceBlock name="customer-account-navigation-wish-list-link" remove="true"/> 
    <!-- remove My Downloadable Products in sidebar menu -->
    <referenceBlock name="customer-account-navigation-downloadable-products-link" remove="true"/> 
    <body>
        <referenceContainer name="content">
            <referenceBlock name="sidebar.main" remove="false"/>
        </referenceContainer >
        <referenceBlock class="Magento\Framework\View\Element\Template" name="sidebar.main.account_nav"
                template="Magento_Customer::account/customername.phtml">
            <arguments>
                <argument name="block_title" xsi:type="string">My Account</argument>
                <argument name="block_css" xsi:type="string">account-nav</argument>
                <argument name="login" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Customer</argument>
            </arguments>
            <referenceBlock name="customer_account_navigation">
                <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-orders-link">
                    <arguments>
                        <argument name="path" xsi:type="string">sales/order/history</argument>
                        <argument name="label" xsi:type="string" translate="true">Orders</argument>
                        <argument name="sortOrder" xsi:type="number">230</argument>
                    </arguments>
                </block>
                <!-- <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-wishlist-link">
                    <arguments>
                        <argument name="path" xsi:type="string">wishlist</argument>
                        <argument name="label" xsi:type="string" translate="true">Wish List</argument>
                        <argument name="sortOrder" xsi:type="number">40</argument>
                    </arguments>
                </block> -->
                
                <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-external-link">
                    <arguments>
                        <argument name="path" xsi:type="string">returnless/external/link</argument>
                        <argument name="label" xsi:type="string" translate="true">Return</argument>
                        <argument name="sortOrder" xsi:type="number">30</argument>
                        <argument name="attributes" xsi:type="array">
                            <item name="target" xsi:type="string">_blank</item>
                        </argument>
                    </arguments>
                </block>

                <!-- <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-return-link" ifconfig="catalog/review/active">
                    <arguments>
                        <argument name="path" xsi:type="string">sales/order/return</argument>
                        <argument name="label" xsi:type="string" translate="true">Return</argument>
                        <argument name="sortOrder" xsi:type="number">30</argument>
                    </arguments>
                </block> -->
                <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-account-link">
                    <arguments>
                        <argument name="label" xsi:type="string" translate="true">Information</argument>
                        <argument name="path" xsi:type="string">customer/account</argument>
                        <argument name="sortOrder" xsi:type="number">20</argument>
                    </arguments>
                </block>
            </referenceBlock>
        </referenceBlock>
        
    </body>
</page>
