<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <update handle="hyva_form_validation"/>
    <head>
        <title>Account Information</title>
    </head>
    <body>
        <referenceContainer name="content">
            <block class="Magento\Customer\Block\Form\Edit" name="customer_edit" template="Magento_Customer::form/edit.phtml" cacheable="false">
                <container name="form.additional.info" as="form_additional_info"/>
            </block>
        </referenceContainer>
        <referenceContainer name="before.body.end">
            <block name="password.validate.js" template="Magento_Customer::js/password-validation.phtml"/>
        </referenceContainer>
    </body>
</page>
