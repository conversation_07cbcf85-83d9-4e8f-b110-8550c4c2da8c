<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <referenceBlock name="head.components">
            <block class="Magento\Framework\View\Element\Js\Components" name="customer_page_head_components" template="Magento_Customer::js/components.phtml"/>
        </referenceBlock>
        <referenceBlock name="customer-account-navigation-address-link">
            <arguments>
                <argument name="is_highlighted" xsi:type="boolean">true</argument>
            </arguments>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Magento\Customer\Block\Address\Edit" name="customer_address_edit" template="Magento_Customer::address/edit.phtml" cacheable="false">
                <arguments>
                    <argument name="product_action" xsi:type="object">Udigital\Minor\ViewModel\Product</argument>
                    <argument name="attribute_data" xsi:type="object">Magento\Customer\Block\DataProviders\AddressAttributeData</argument>
                    <argument name="post_code_config" xsi:type="object">Magento\Customer\Block\DataProviders\PostCodesPatternsAttributeData</argument>
                    <argument name="view_model" xsi:type="object">Magento\Customer\ViewModel\Address</argument>
                    <argument name="region_provider" xsi:type="object">Magento\Customer\ViewModel\Address\RegionProvider</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
