<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="header-content">
            <referenceBlock name="header.customer">
                <arguments>
                    <argument name="login" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Customer</argument>
                </arguments>
                <block name="header.customer.logged.in.links" class="Hyva\Theme\Block\SortableItems">
                    <block name="customer.header.orders.link" class="Hyva\Theme\Block\SortableItemInterface">
                        <arguments>
                            <argument name="label" xsi:type="string" translate="true">Orders</argument>
                            <argument name="path" xsi:type="string">sales/order/history</argument>
                            <argument name="sort_order" xsi:type="number">10</argument>
                        </arguments>
                    </block>
                    <block name="customer.header.return.link" class="Hyva\Theme\Block\SortableItemInterface">
                        <arguments>
                            <argument name="label" xsi:type="string" translate="true">Return</argument>
                            <argument name="path" xsi:type="string">returnless/external/link</argument>
                            <argument name="sort_order" xsi:type="number">20</argument>
                        </arguments>
                    </block>
                    <block name="customer.header.information.link" class="Hyva\Theme\Block\SortableItemInterface">
                        <arguments>
                            <argument name="label" xsi:type="string" translate="true">Information</argument>
                            <argument name="path" xsi:type="string">customer/account</argument>
                            <argument name="sort_order" xsi:type="number">30</argument>
                        </arguments>
                    </block>
                    <block name="customer.header.sign.out.delimiter" class="Hyva\Theme\Block\SortableItemInterface" template="Hyva_Theme::sortable-item/delimiter.phtml">
                        <arguments>
                            <argument name="sort_order" xsi:type="number">1990</argument>
                        </arguments>
                    </block>
                    <block name="customer.header.sign.out.link" class="Hyva\Theme\Block\SortableItemInterface">
                        <arguments>
                            <argument name="label" xsi:type="string" translate="true">Sign Out</argument>
                            <argument name="path" xsi:type="string">customer/account/logout</argument>
                            <argument name="sort_order" xsi:type="number">2000</argument>
                        </arguments>
                    </block>
                </block>
            </referenceBlock>
        </referenceBlock>
    </body>
</page>
