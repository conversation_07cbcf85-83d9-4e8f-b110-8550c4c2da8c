<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$customerName = $block->getLogin()->getCustomer()->getName();
$limitChar = 25;
if (strlen($customerName) > $limitChar) {
      $customerName = substr($customerName, 0, strrpos(substr($customerName, 0, $limitChar), ' ')) . '...';
}
?>
<style>
    .customername {
        border-left: 3px solid transparent;
        color: #002C4B;
        display: block;
        font-size: 20px;
        font-weight: 700;
        padding: 5px 18px 5px 15px;
    }
    .block.account-nav.card {
        /* background: none; */
        border: 1px solid #D5EAF0;
        border-radius: 12px;
        opacity: 1;
    }
    .block.account-nav.card .item .delimiter {
        border-top: 1px solid #D5EAF0;
    }
    .block.account-nav .text-lg.title {
        color: #002C4B;
        font-size: 20px;
        font-weight: 700;
    }
    .account-nav-content li.nav.item a {
        color: #002C4B;
        font-size: 14px;
        padding: 10px 0px;
    }
    .account-nav-content li.nav.item strong {
        color: #002C4B;
        font-size: 14px;
        padding: 10px 0px;
        font-weight: 700;
        text-decoration: none;
    }
    #maincontent {
        padding-bottom: 80px;
    }

    @media(max-width: 768px){
        .block.account-nav .title.account-nav-title .text-lg.title {
            font-size: 15px;
        }
        .block.account-nav.card.filter-option {
            margin-top: 15px;
        }
        .content.account-nav-content.pt-3.block {
            padding-top: 5px;
        }
        .delimiter.block.border-b.border-container{
            display: none;
        }
        .content.account-nav-content.block .nav.items li:last-child {
            margin-top: 10px;
        }
        .block.account-nav.card {
            background: none;
            border: 0px;
            border-radius: 12px;
            opacity: 1;
            --tw-shadow: 0 0px 0px 0;
            padding-left: 0px;
        }
        .block.account-nav.card .nav.item a {
            padding: 5px;
            font-size: 13px;
        }
        .block.account-nav.card .nav.item strong {
            padding: 5px;
            display: block;
            font-size: 13px;
        }
        .block.account-nav.card .nav.item strong::before {
            content: "> ";
            color: #5DC2EF;
        }
    }
</style>
<div class="block <?= $escaper->escapeHtmlAttr($block->getBlockCss()) ?> card filter-option py-4 px-8 mb-6"
     x-data="{ open: true }">
    <div class="title <?= $escaper->escapeHtmlAttr($block->getBlockCss()) ?>-title flex justify-between
        items-center cursor-pointer hover:text-secondary-darker border-container border-b pb-4"
         @click="open = !open"
         :class="{ 'border-b pb-4': open, 'border-0 pb-0': !open }"
    >
        <span class="text-lg title">
            <?= $escaper->escapeHtml(__($customerName)) ?>
        </span>
        <span class="px-1 py-1 border rounded border-container">
            <?= $heroicons->chevronDownHtml(
                'transition-transform duration-300 ease-in-out transform rotate-180',
                24,
                24,
                [":class" => "{ 'rotate-180': open, 'rotate-0': !open }"]
            ); ?>
        </span>
    </div>
    <div class="content <?= $escaper->escapeHtmlAttr($block->getBlockCss()) ?>-content pt-3 block"
         :class="{ 'hidden': !open , 'block': open }"
         id="<?= $escaper->escapeHtmlAttr($block->getBlockCss()) ?>">
        <?= $block->getChildHtml() ?>
    </div>
</div>
