<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Account\Dashboard\Address;
use Magento\Framework\Escaper;

/** @var Address $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$dataUserInfo = $block->getCustomerAction()->getInfoUser($block->getCustomer()->getDefaultShipping());

?>
<div class="block block-dashboard-addresses">
    <!-- remove title address - check : vendor/hyva-themes/magento2-default-theme/Magento_Customer/templates/account/dashboard/address.phtml -->
    <div class="block-content">
        <h2 class="text-cadet-space text-2xl font-bold mb-4"><?= $escaper->escapeHtml(__('Information')) ?></h2>
        <div class="flex flex-col justify-between gap-y-6">
            <div class="lg:w-1/2 w-full custom-card first-card">
                <div class="flex p-8 sm:flex-row flex-col h-full card border border-white-azureish rounded-lg">
                    <div class="grow">
                        <div class="flex justify-between">
                            <p class="text-cadet-space text-lg font-bold mb-3 info-title content-p-customer">
                                <spand><?= $escaper->escapeHtml(__('Personal information')) ?></spand>
                            </p>
                            <a
                                class="items-center md:text-sm text-secondary hover:text-secondary-darker text-blue-picton content-edit"
                                href="<?= $escaper->escapeUrl($block->getPrimaryBillingAddressEditUrl()) ?>"
                                data-ui-id="default-billing-edit-link"><span><?= $escaper->escapeHtml(
                                    __('Edit')
                                ) ?></span>
                            </a>
                        </div>
                        
                        <address class="not-italic">
                            <p><?=  $dataUserInfo['name'] ?></p>

                            <p><?= $dataUserInfo['company'] ?></p>

                            <?php if ($dataUserInfo['telepon'] != null) { ?>
                                <p>T:<?= $dataUserInfo['telepon']; ?></p>
                            <?php } ?>

                            <?php if ($dataUserInfo['vat'] != null) { ?>
                                <p>VAT: <?=  $dataUserInfo['vat'] ?></p>
                            <?php } ?>
                        </address>
                        
                    </div>
                </div>
            </div>
            <div class="lg:w-1/2 w-full custom-card">
                <div class="flex p-8 sm:flex-row flex-col h-full card border border-white-azureish rounded-lg">
                    <div class="grow">
                        <div class="flex justify-between">
                            <h2 class="text-cadet-space text-lg title-font font-bold mb-3 info-title">
                                <span><?= $escaper->escapeHtml(__('Address')) ?></span>
                            </h2>
                            <a
                                class="items-center md:text-sm text-secondary hover:text-secondary-darker text-blue-picton"
                                href="<?= $escaper->escapeUrl($block->getPrimaryShippingAddressEditUrl()) ?>"
                                data-ui-id="default-billing-edit-link"><span><?= $escaper->escapeHtml(
                                    __('Edit')
                                ) ?></span>
                            </a>
                        </div>
                        <address class="not-italic">
                            <p><?= $dataUserInfo['street'] ?></p>

                            <p><?= $dataUserInfo['city_and_postcode'] ?> </p>

                            <p><?= $dataUserInfo['country'] ?> </p>
                        </address>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
