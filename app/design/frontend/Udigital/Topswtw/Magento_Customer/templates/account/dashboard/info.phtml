<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Account\Dashboard\Info;
use Magento\Framework\Escaper;

/** @var Info $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<!-- <div class="mb-6 text-2xl block-title">Account Information</div> -->
<div class="flex flex-col justify-between mt-6">
    <div class="w-full custom-card contact-info lg:w-1/2">
        <div class="flex flex-col h-full p-8 sm:flex-row card border border-white-azureish rounded-lg">
            <!-- remove icon | check : vendor/hyva-themes/magento2-default-theme/Magento_Customer/templates/account/dashboard/info.phtml -->
            <div class="grow">
                <div class="flex justify-between">
                    <h2 class="mb-3 text-lg font-bold text-cadet-space title-font info-title content-text-bottom">
                        <span class="text-mobile-logged"><?= $escaper->escapeHtml(__('Sign-in Information')) ?></span>
                    </h2>
                    <a
                        class="md:text-sm text-secondary hover:text-secondary-darker text-blue-picton"
                        href="<?= $escaper->escapeUrl($block->getUrl('customer/account/edit')) ?>">
                        <span><?= $escaper->escapeHtml(__('Edit')) ?></span>
                    </a>
                </div>
                <p>
                    <?= $escaper->escapeHtml($block->getCustomer()->getEmail()) ?><br>
                </p>
                <input type="password" disabled value="password" class="type-password !border-0 !font-[22px] !p-0">
            </div>
        </div>
    </div>
    <!-- remove Newsletters | check : vendor/hyva-themes/magento2-default-theme/Magento_Customer/templates/account/dashboard/info.phtml -->
</div>
