<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Customer\Block\Address\Edit;
use Magento\Customer\ViewModel\Address as AddressViewModel;
use Magento\Framework\Escaper;
use Magento\Framework\View\Helper\SecureHtmlRenderer;

/** @var Edit $block */
/** @var Escaper $escaper */
/** @var SecureHtmlRenderer $secureRenderer */
/** @var ViewModelRegistry $viewModels */


/** @var AddressViewModel $addressViewModel */
$addressViewModel = $block->getViewModel();
$directoryViewModel = $viewModels->require(\Hyva\Theme\ViewModel\Directory::class);

$companyBlock = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Company::class);
$phoneBlock = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Telephone::class);
$faxBlock = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Fax::class);
$streetLabel = $block->getAttributeData()->getFrontendLabel('street');
$regionLabel = $block->getAttributeData()->getFrontendLabel('region');
$showOptionalRegions = $block->getConfig('general/region/display_all');

$apiKey = $block->getProductAction()->getApiKey();

/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Theme\ViewModel\Modal $modalViewModel */

$modalViewModel = $viewModels->require(\Hyva\Theme\ViewModel\Modal::class);


?>



<div class=""
    x-data="Object.assign({}, hyva.modal())"
    >
            <button @click="show" id="buttonValidation" class="text-filter-modal-edit">
            </button>
        <?= $modalViewModel->createModal()->withContent('
            <div class="relative w-full h-full md:h-auto text-blue-prussian text-justify" >
                <!-- Modal header -->
                <div class="flex items-start md:pb-4 pt-4 pl-4 mb-2 bg-filter-blue " >
                <span class="p-6 header-modal flex justify-between bg-filter-blue"><span class="text-xl text-white">BTW nummer validatie resultaat</span></span>
                </div>
                <!-- Modal body -->
                <div class="bg-white rounded shadow-lg w-full" x-data="clickHandler()">
                    <input type="hidden" id="country-btw">
                    
                    <div class="p-6 content-modal grid gap-4" >
                    Het geselecteerde land klopt niet bij het BTW nummer. Zullen we het land automatisch aanpassen?
                    </div>
                    <span class="p-6 flex justify-between footer-modal">
                    <button @click="hide" id="close-modal" class="filter-footer-text" >Cancel</button>
                    <button @click="yesClicked" class="filter-footer-text" >Yes</button>
                    </span>
                </div>
            </div>
            <script>
            </script>')->withContainerClasses('fixed', 'flex', 'justify-center', 'md:items-center', 'max-h-full')
        ->withAriaLabelledby('the-label')
        ->addDialogClass('modal-filter-home', 'test')
        ->removeDialogClass('p-10', 'overflow-auto', 'max-h-screen'); ?>
</div>

<div class="text-2xl title-cust-addr mb-5 text-blue-prussian">
    <?= $escaper->escapeHtml(__('Contact Information')) ?>
</div>
<form class="form-address-edit border border-gray-300 rounded-xl p-8 md:max-w-lg"
      x-data="Object.assign(initCustomerAddressEdit(), hyva.formValidation($root))"
      @private-content-loaded.window="onPrivateContentLoaded($event.detail.data)"
      @submit="onSubmit"
      action="<?= $escaper->escapeUrl($block->getSaveUrl()) ?>"
      method="post"
      id="form-validate"
      enctype="multipart/form-data"
      data-hasrequired="<?= $escaper->escapeHtmlAttr(__('* Required Fields')) ?>"
>
    <fieldset class="fieldset">
        <!-- <legend class="legend"><span><?= $escaper->escapeHtml(__('Contact Information')) ?></span></legend> -->
        <?= $block->getBlockHtml('formkey') ?>
        <input type="hidden" name="success_url" value="<?= $escaper->escapeUrl($block->getSuccessUrl()) ?>">
        <input type="hidden" name="error_url" value="<?= $escaper->escapeUrl($block->getErrorUrl()) ?>">

        <div class="md:grid grid-cols-1 gap-x-4 gap-y-0" x-data="{ accountType: '' }">
            <?= $block->getNameBlockHtml() ?>

            <div class="flex">
           

            <?php if ($phoneBlock->isEnabled()): ?>
                <div class="w-full">
                    <?= $phoneBlock->setTelephone($block->getAddress()->getTelephone())->toHtml() ?>
                </div>
            <?php endif ?>
            </div>
           

            <?php if ($faxBlock->isEnabled()): ?>
                <div class="field field-reserved w-full">
                    <?= $faxBlock->setFax($block->getAddress()->getFax())->toHtml() ?>
                </div>
            <?php endif ?>
        </div>
    </fieldset>
    <fieldset class="fieldset">
        <legend class="legend w-full"><span class="text-blue-prussian"><?= $escaper->escapeHtml(__('Address')) ?></span></legend>
        <div class="field field-reserved country w-full required">
            <label class="label" for="country">
                <span class="text-blue-prussian">
                    <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('country_id') ?>
                </span>
            </label>
            <div class="control">
                <?php $countries = $block
                    ->getCountryCollection()
                    ->setForegroundCountries($directoryViewModel->getTopCountryCodes())
                    ->toOptionArray();
                ?>
                <select name="country_id"
                        id="country"
                        title="Country"
                        required
                        class="form-select w-full"
                        x-ref="country_id"
                        @change="changeCountry($event.target)"
                >
                    <?php foreach ($countries as $country): ?>
                        <option name="<?= /** @noEscape */ $country['label'] ?>"
                                value="<?= /** @noEscape */ $country['value'] ?>"
                                data-is-zip-required="<?= (isset($country['is_zipcode_optional'])) ? '0' : '1' ?>"
                                data-is-region-required="<?= (isset($country['is_region_required'])) ? '1' : '0' ?>"
                                <?= ($block->getCountryId() ===  $country['value']) ? 'selected="selected"' : '' ?>
                        >
                            <?= /** @noEscape */ $country['label'] ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        


        
        <div class="street-additional w-full md:grid grid-cols-3 gap-x-2 gap-y-2 md:grid ">

                <div class="street w-full ">
                        <div class="field field-reserved required">
                            <label for="street_1" class="label">
                                <span class="text-blue-prussian"><?= /* @noEscape */ $escaper->escapeHtml(__('Street ')); ?></span>
                            </label>
                            <div class="control">
                                <input type="text"
                                    name="street[]"
                                    required
                                    value="<?= $escaper->escapeHtmlAttr($block->getStreetLine(1)) ?>"
                                    title="<?= /* @noEscape */ $escaper->escapeHtml(__('Street ')); ?>"
                                    id="street_1"
                                    class="form-input w-full"
                                />
                            </div>
                        </div>
                </div>
                <?php for ($i = 1, $n = $addressViewModel->addressGetStreetLines(); $i < $n; $i++): ?>
                    <div class="field additional <?= ($i == 1)?' required': '' ?>"  <?= ($i == 3)?' hidden': '' ?> >
                        <label class="label" for="street_<?= /* @noEscape */ $i + 1 ?>">
                            <span class="text-blue-prussian">
                                <?php
                                if ($i == 1) {
                                    echo  $escaper->escapeHtml(__('House Number'));
                                } else {
                                    echo  $escaper->escapeHtml(__('Additional'));
                                }
                                ?>
                            </span>
                        </label>
                        <div class="control">
                            <input type="text" name="street[]"
                                   value="<?= $escaper->escapeHtmlAttr($block->getStreetLine($i + 1)) ?>"
                                   title="<?= $escaper->escapeHtmlAttr(__('Street Address %1', $i + 1)) ?>"
                                   id="street_<?= /* @noEscape */ $i + 1 ?>"
                                   class="form-input w-full"
                            />
                        </div>
                    </div>
                <?php endfor; ?>
        </div>

        <div class="md:grid grid-cols-2 gap-4 postcode-city md:grid">

            <div class="field field-reserved zip w-full required">
                    <label class="label" for="zip">
                        <span class="text-blue-prussian">
                            <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>
                        </span>
                    </label>
                    <div class="control">
                        <input type="text"
                            name="postcode"
                            x-ref="postcode"
                            value="<?= $escaper->escapeHtmlAttr($block->getAddress()->getPostcode()) ?>"
                            title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                            id="zip"
                            :required="isZipRequired"
                            @change="onChange"
                            data-validate='{"postcode": true}'
                            class="form-input w-full">
                    </div>
            </div>
            <div class="field field-reserved city w-full">
                <label class="label" for="city">
                <span class="text-blue-prussian"><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?></span>
                </label>
                <div class="control">
                    <input type="text"
                           name="city"
                           required
                           value="<?= $escaper->escapeHtmlAttr($block->getAddress()->getCity()) ?>"
                           title="<?= $escaper->escapeHtmlAttr(__('City')) ?>"
                           class="form-input w-full"
                           id="city">
                </div>
            </div>

        </div>


        
        <div class=" w-full">

            <div class="field field-reserved region hidden w-full"
                 x-cloak
                 x-show="(hasAvailableRegions() && isRegionRequired) || showOptionalRegions">
                <label class="label" for="region_id">
                    <span class="text-blue-prussian"><?= /* @noEscape */ $regionLabel ?></span>
                </label>
                <div class="control">
                    <template x-if="hasAvailableRegions() && (isRegionRequired || showOptionalRegions)">
                        <select id="region_id" name="region_id"
                                title="<?= /* @noEscape */ $regionLabel ?>"
                                class="form-select validate-select region_id"
                                x-ref="region_id"
                                x-model="selectedRegion"
                                @change="$nextTick(() => $refs.region.value = availableRegions[selectedRegion].name)"
                        >
                            <option value=""><?= $escaper->escapeHtml(__('Please select a region, state or province.')) ?></option>
                            <template x-for="regionId in Object.keys(availableRegions)">
                                <?php /* in alpine v3, if the bound props update, the template body gets evaluated before the template condition */ ?>
                                <?php /* because of this it is required to check if availableRegions[regionId] is set */ ?>
                                <option :value="regionId"
                                        :name="availableRegions[regionId] && availableRegions[regionId].name"
                                        x-text="availableRegions[regionId] && availableRegions[regionId].name"
                                        :selected="selectedRegion === regionId"
                                >
                                </option>
                            </template>
                        </select>
                    </template>
                    <input :type="hasAvailableRegions() && (isRegionRequired || showOptionalRegions) ? 'hidden' : 'text'"
                           id="region"
                           name="region"
                           x-ref="region"
                           value="<?= $escaper->escapeHtmlAttr($block->getRegion()) ?>"
                           title="<?= /* @noEscape */ $regionLabel ?>"
                           class="form-input"
                           :required="isRegionRequired"
                    />
                </div>
            </div>

            <?php if ($companyBlock->isEnabled()): ?>
                <?php if ($block->getCustomer()->getTaxvat()): ?>
                    <?php if ($addressViewModel->addressIsVatAttributeVisible()): ?>
                    <div class="w-full">
                        <?= $companyBlock->setCompany($block->getAddress()->getCompany())->toHtml() ?>
                    </div>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endif ?>

            <?php if ($addressViewModel->addressIsVatAttributeVisible()): ?>
                <?php if ($block->getCustomer()->getTaxvat()): ?>
                
                <div class="field field-reserved taxvat w-full" id="taxvatdiv">
                    <label class="label" for="vat_id">
                    <span class="text-blue-prussian"><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?></span>
                    </label>
                    <div class="control">
                        <input type="text"
                               name="vat_id"
                               value="<?= $escaper->escapeHtmlAttr($block->getCustomer()->getTaxvat()) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?>"
                               class="form-input disabled bg-gray-400 w-full"
                               @change="onChange"
                                data-validate='{"vat_id": true}'
                               id="vat_id"
                               disabled >
                    </div>
                </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>

        <div class="md:grid grid-cols-1 gap-x-1 gap-y-1" >
           
            

            
        </div>
        
        <?php $isDefaultMessages = array_filter([
            $block->isDefaultBilling() ? __("It's a default billing address. ") : null,
            $block->isDefaultShipping() ? __("It's a default shipping address.") : null,
        ]) ?>

        <?php if ($isDefaultMessages): ?>
           
        <?php endif; ?>
        <?php if (! $block->isDefaultBilling() && $block->canSetAsDefaultBilling()): ?>
            <div class="field choice set billing">
                <input type="checkbox" id="primary_billing" name="default_billing" value="1" class="checkbox">
                <label class="label" for="primary_billing">
                    <span class="text-blue-prussian"><?= $escaper->escapeHtml(__('Use as my default billing address')) ?></span>
                </label>
            </div>
        <?php else: ?>
            <input type="hidden" name="default_billing" value="1"/>
        <?php endif; ?>

        <?php if (! $block->isDefaultShipping() && $block->canSetAsDefaultShipping()): ?>
            <div class="field choice set shipping">
                <input type="checkbox" id="primary_shipping" name="default_shipping" value="1" class="checkbox">
                <label class="label" for="primary_shipping">
                    <span class="text-blue-prussian"><?= $escaper->escapeHtml(__('Use as my default shipping address')) ?></span>
                </label>
            </div>
        <?php else: ?>
            <input type="hidden" name="default_shipping" value="1">
        <?php endif; ?>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit"
                    class="action save primary"
                    data-action="save-address"
                    title="<?= $escaper->escapeHtmlAttr(__('Save Address')) ?>">
                <span><?= $escaper->escapeHtml(__('Save Address')) ?></span>
            </button>
        </div>
        <div class="secondary">
            <a class="action back" href="<?= $escaper->escapeUrl($block->getUrl('customer/account/')) ?>">
                <span><?= $escaper->escapeHtml(__('Go back')) ?></span>
            </a>
        </div>
    </div>
</form>

<script>

const script = document.createElement('script')
script.src = 'https://maps.googleapis.com/maps/api/js?key=<?= $apiKey ?>&libraries=places&callback=googleReady';
script.type = 'text/javascript';
document.head.append(script);

function googleReady() {
    document.dispatchEvent(new Event('google:init'));
}

function clickHandler() {
            return {
                yesClicked() {
                    document.querySelector('#country').value = document.querySelector('#country-btw').value;
                    document.getElementById('close-modal').click();
                }
            };
        }

function initCustomerAddressEdit() {
    return {
        directoryData: {},
        availableRegions: {},
        messageTime: 5000,
        fieldsNames: [],
        selectedRegion: '<?= $escaper->escapeJs($block->getRegion() ?: 0) ?>',
        isZipRequired: true,
        isRegionRequired: true,
        showOptionalRegions: <?= $showOptionalRegions ? 'true' : 'false' ?>,
        onPrivateContentLoaded(data) {
            this.directoryData = data['directory-data'] || {};

            <?php if ($block->getCountryId()): ?>
                this.setCountry(this.$refs['country_id'], '<?= $escaper->escapeJs($block->getRegion()) ?>');
            <?php endif; ?>

        },
        setRegionInputValue(regionName) {
            this.$nextTick(() => {
                const regionInputElement = this.$refs['region'];
                if (regionInputElement) {
                    regionInputElement.value = regionName;
                }
            })
        },
        setCountry(countrySelectElement, initialRegion) {
            const selectedOption = countrySelectElement.options[countrySelectElement.selectedIndex];
            const countryCode = countrySelectElement.value;
            const countryData = this.directoryData[countryCode] || false;

            if (!countryData) {
                this.setRegionInputValue('');
                return;
            }

            this.isZipRequired = selectedOption.dataset.isZipRequired === '1';
            this.isRegionRequired = selectedOption.dataset.isRegionRequired === '1';

            this.availableRegions = countryData.regions || {};

            const initialRegionId = Object.keys(this.availableRegions).filter(regionId => this.availableRegions[regionId].name === initialRegion)[0];
            this.selectedRegion = initialRegionId || '0';
            this.setRegionInputValue(initialRegionId && this.availableRegions[initialRegionId].name || '');

        },
        changeCountry(countrySelectElement, initialRegion) {
            this.setCountry(countrySelectElement, initialRegion);

            this.validateCountryDependentFields();
        },
        validateCountryDependentFields() {
            this.$nextTick(() => {
                <?php /* Reinitialize validation rules for fields that depend on the country */ ?>
                this.fields['postcode'] && this.removeMessages(this.fields['postcode'])
                this.fields['region'] && this.removeMessages(this.fields['region'])
                delete this.fields['postcode'];
                delete this.fields['region'];
                <?php /* Initialize country_id, too, because the postcode validation depends on it */ ?>
                this.setupField(this.$refs['country_id']);
                this.setupField(this.$refs['postcode']);
                this.setupField(this.$refs['region']);

                this.fields['postcode'] && this.validateField(this.fields['postcode']);
                this.fields['region'] && this.validateField(this.fields['region']);
            })
        },
        hasAvailableRegions() {
            return Object.keys(this.availableRegions).length > 0;
        },
        init() {
                    
            // this.element = this.$el.querySelector("#street_1"); uncomment this to active search address autocomplete function

            if (this.element === null) {
                console.error("Cannot find Google Places Autocomplete input [x-ref=\"googleAutocomplete\"]");
                return;
            }

            if (typeof window.google === 'undefined') {
                document.addEventListener('google:init', () => {
                    this.initAutocomplete();
                });
            } else {
                this.initAutocomplete();
            }
        },
        initAutocomplete() {
            this.autocomplete = new window.google.maps.places.Autocomplete(this.element, {
                types: ['address']
            });
            window.google.maps.event.addListener(this.autocomplete, 'place_changed', ()=>this.handleResponse(this.autocomplete.getPlace()));
        },
        handleResponse(placeResultData) {
            
            const placeData = placeResultData.address_components;
                    let parent = this;
                    placeData.forEach(function (item, index) {
                        if (item.types[0] == 'street_number'){
                            let separated = parent.separateNumberAndLetter(item.short_name);
                            if (!separated.error) {
                                document.getElementById('street_2').value = separated.number;
                                document.getElementById('street_3').value = separated.letter;
                            } else {
                                document.getElementById('street_2').value = item.short_name;
                            }
                        }
                        if (item.types[0] == 'route') document.getElementById('street_1').value = item.short_name;
                        if (item.types[0] == 'postal_code' || item.types[0] == 'postal_code_prefix') document.getElementById('zip').value = item.long_name;
                        if (item.types[0] == 'administrative_area_level_2') document.getElementById('city').value = item.long_name;
                        if (item.types[0] == 'administrative_area_level_1') document.getElementById('region').value = item.long_name;
                        if (item.types[0] == 'country') document.getElementById('country').value = item.short_name;
                    });

        },
        separateNumberAndLetter(str) {
                    const matches = str.match(/^(\d+)([A-Za-z]+)$/);
                    if (matches) {
                        return { number: parseInt(matches[1], 10), letter: matches[2] };
                    } else {
                        return { error: 'No match found' };
                    }
                }
    }
}

window.addEventListener('DOMContentLoaded', () => {
    const url = '<?= $escaper->escapeUrl($block->getBaseUrl()) ?>';

    hyva.formValidation.addRule('telephone', (value, options) => {
        const phoneNumber = value.trim().replace(' ', '');
        if (phoneNumber && phoneNumber.length < (options.minlength || 3)) {
            return '<?= $escaper->escapeJs(__('The telephone number is too short.')) ?>';
        }

        return true;
    });

    const postCodeSpecs = <?= /* @noEscape */ $block->getPostCodeConfig()->getSerializedPostCodes() ?>;

    hyva.formValidation.addRule('postcode', (postCode, options, field, context) => {
        context.removeMessages(field, 'postcode-warning')
        const countryId = (context.fields['country_id'] && context.fields['country_id'].element.value),
              validatedPostCodeExamples = [],
              countryPostCodeSpecs = countryId && postCodeSpecs ? postCodeSpecs[countryId] : false;

        if (! postCode || ! countryPostCodeSpecs) return true;

        for (const postCodeSpec of Object.values(countryPostCodeSpecs)) {
            if (new RegExp(postCodeSpec.pattern).test(postCode)) return true;
            validatedPostCodeExamples.push(postCodeSpec.example);
        }
        if (validatedPostCodeExamples) {
            context.addMessages(field, 'postcode-warning', [
                '<?= $escaper->escapeJs(__('Provided Zip/Postal Code seems to be invalid.')) ?>',
                '<?= $escaper->escapeJs(__('Example: ')) ?>' + validatedPostCodeExamples.join('; ') + '. ',
                '<?= $escaper->escapeJs(__('If you believe it is the right one you can ignore this notice.')) ?>'
            ]);
        }

        return true;
    });


    hyva.formValidation.addRule('vat_id', (value, options, field, context) => {

        if(value.length > 1){
            return new Promise(resolve => {
                // show the user form validation is ongoing, maybe show a spinner
                field.element.disabled = true;

                fetch(url +'euvat/vatnumber/validation/'+ '?form_key=' + hyva.getFormKey()+'&vat_number='+value+'&handle=customer_account_create', {
                    method: "GET",
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                    },
                })
                .then(response => response.json())
                .then(result => {
                
                        if (result.vat_is_valid == true) {

                            if(result.vat_request_country_code !== document.querySelector('#country').value){
                                document.getElementById('buttonValidation').click();
                                document.querySelector('#country-btw').value = result.vat_request_country_code;
                            }
                            
                            let valtax = document.getElementById("vat_id").value;
                            document.getElementById("taxvatdiv").innerHTML += 
                            "<ul id='tax-mes' class='messages' aria-live='polite'><li data-msg-field='vat_id' style='color: green;'>"+result.request_message+"</li></ul>";
                            document.getElementById("vat_id").disabled = false;
                            document.getElementById("vat_id").value = valtax;
                            resolve(true);
                        }else{
                            resolve(result.request_message);
                        }
                })
                .finally(() => {
                    // indicate validation has finished, remove spinner if shown
                    field.element.disabled = false;
                });
            });
        }

        return true;
        });
})
</script>
