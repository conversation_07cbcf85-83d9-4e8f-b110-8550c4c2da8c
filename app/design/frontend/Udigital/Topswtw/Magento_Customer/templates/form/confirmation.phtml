<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
?>
<div class="container mb-12">
    <div class="w-full my-8">
        <form action="" method="post"
              class="form-confirmation mx-auto w-[30%] p-[30px] border border-blue-100 rounded-lg min-h-[300px] opacity-100 max-md:w-3/4 max-[480px]:w-full max-[480px]:p-5 max-lg:w-3/5 max-xl:w-2/ max-2xl:w-2/5">
            <?= $block->getLayout()->getBlock('formkey')->toHtml() ?>

            <p class="text-2xl text-blue-prussian font-bold mb-4">
                <?= $escaper->escapeHtml(__('Send confirmation link')) ?>
            </p>

            <fieldset class="fieldset">
                <p>
                    <?= $escaper->escapeHtml(__('Enter your email address and you will receive a confirmation link.')) ?>
                </p>
                <div
                    class="flex-row md:flex justify-between gap-1.5 mt-2 items-baseline">
                    <input type="email" name="email"
                           placeholder="<?= $escaper->escapeHtml(__('Email')) ?>"
                           id="email_address"
                           class="form-input inline-flex w-full"
                           value="<?= $escaper->escapeHtmlAttr($block->getEmail()) ?>"
                           autocomplete="off" required/>
                </div>
            </fieldset>
            <div class="actions-toolbar">
                <div class="primary">
                    <button type="submit"
                            class="action bg-blue-picton py-2 text-center rounded-lg">
                        <span><?= $escaper->escapeHtml(__('Send a confirmation link')) ?></span>
                    </button>
                </div>
                <div class="secondary">
                    <a href="<?= $escaper->escapeUrl($block->getLoginUrl()) ?>"
                       class="action back">
                        <span><?= $escaper->escapeHtml(__('Back to Sign In')) ?></span>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
