<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Form\Edit as CustomerEdit;
use Magento\Customer\Block\Widget\Name;
use Magento\Framework\Escaper;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var CustomerEdit $block */
/** @var Escaper $escaper */
/** @var ReCaptcha|null $recaptcha */
/** @var HeroiconsSolid $heroicons */
/** @var ViewModelRegistry $viewModels */

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');
$heroIcons = $viewModels->require(HeroiconsSolid::class);
$minimumPasswordLength = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();

?>
<p class="title-edit-address">Bewerk accountgegevens</p>
<form class="form form-edit-account cart-content-adress"
      action="<?= $escaper->escapeUrl($block->getUrl('customer/account/editPost')) ?>"
      method="post" id="form-validate"
      enctype="multipart/form-data"
      x-data="Object.assign(hyva.formValidation($el), initForm())"
      @submit.prevent="submitForm"
      autocomplete="off">
    <fieldset class="fieldset info">
        <?= $block->getBlockHtml('formkey') ?>
        <legend class="legend s-font-form"><span><?= $escaper->escapeHtml(__('Account Information')) ?></span></legend>
        <br>
        <div class="content-address">
        <?= $block->getLayout()->createBlock(Name::class)->setObject($block->getCustomer())->toHtml() ?>
        <?php $dob = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Dob::class) ?>
        <?php $taxvat = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Taxvat::class) ?>
        <?php $gender = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Gender::class) ?>
        
        <?php if ($dob->isEnabled()): ?>
            <?= $dob->setDate($block->getCustomer()->getDob())->toHtml() ?>
        <?php endif ?>
        <?php if ($taxvat->isEnabled()): ?>
            <?= $taxvat->setTaxvat($block->getCustomer()->getTaxvat())->toHtml() ?>
        <?php endif ?>
        <?php if ($gender->isEnabled()): ?>
            <?= $gender->setGender($block->getCustomer()->getGender())->toHtml() ?>
        <?php endif ?>
        </div>
        <?php

        ?>
        <div class="field field-reserved kvk_nummer required">
            <label class="label" for="kvk_nummer"><span><?= $escaper->escapeHtml(__('KVK Nummer')) ?></span></label>
            <div class="control">
                <input type="text" name="kvk_nummer" id="kvk_nummer" 
                        value="<?=  $block->getKvkNummer() ?>"
                        title="<?= $escaper->escapeHtmlAttr(__('KVK Nummer')) ?>"
                        class="form-input"/>
            </div>
        </div>

        <div class="field choice">
            <input type="checkbox" name="change_email" id="change-email" value="1"
                   title="<?= $escaper->escapeHtmlAttr(__('Change Email')) ?>"
                   x-on:change="showEmailField = !showEmailField"
                   class="checkbox"/>
            <label class="label" for="change-email">
                <span><?= $escaper->escapeHtml(__('Change Email')) ?></span>
            </label>
        </div>

        <template x-if="showEmailField">
            <div class="field field-reserved email required">
                <label class="label" for="email"><span><?= $escaper->escapeHtml(__('Email')) ?></span></label>
                <div class="control">
                    <input type="email" name="email" id="email" autocomplete="email" required
                           value="<?= $escaper->escapeHtmlAttr($block->getCustomer()->getEmail()) ?>"
                           title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                           class="form-input w-72 border border-gray-400 rounded-lg opacity-100 text-sm mb-5"/>
                           <label class="label" for="current-password">
                
                <span><?= $escaper->escapeHtml(__('Current Password')) ?></span>
                </label>
                <div class="control flex items-center">
                    <div class="input-icon">
                        <input :type="showPasswordCurrent ? 'text' : 'password'" type="password" class="form-input w-72 border border-gray-400 rounded-lg opacity-100 text-sm mb-5"
                            name="current_password" id="current-password"
                            data-input="current-password"
                            required
                            autocomplete="off"/>
                        <div x-on:click="showPasswordCurrent = !showPasswordCurrent"
                            class="cursor-pointer px-4 icon-class i-password-content"
                            :aria-label="showPasswordCurrent ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                       
                            <template x-if="!showPasswordCurrent">
                                <?= $heroIcons->eyeHtml('w-5 h-5'); ?>
                            </template>

                            <template x-if="showPasswordCurrent">
                                <?= $heroIcons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </div>
                    </div>
                </div>
                </div>
            </div>
            
        </template>

        <div class="field choice">
            <input type="checkbox" name="change_password" id="change-password" value="1"
                   title="<?= $escaper->escapeHtmlAttr(__('Change Password')) ?>"
                   x-on:change="showPasswordFields = !showPasswordFields"
                <?php if ($block->getChangePassword()): ?> checked="checked"<?php endif; ?>
                   class="checkbox"/>
            <label class="label" for="change-password">
                <span><?= $escaper->escapeHtml(__('Change Password')) ?></span>
            </label>
        </div>


        <template x-if="showPasswordFields">
            <div class="field field-reserved password current required">
                <label class="label" for="current-password">
                    <span><?= $escaper->escapeHtml(__('Current Password')) ?></span>
                </label>
                <div class="control flex items-center">
                    <div class="input-icon">
                        <input :type="showPasswordCurrent ? 'text' : 'password'" type="password" class="form-input w-72 border border-gray-400 rounded-lg opacity-100 text-sm mb-5"
                           name="current_password" id="current-password"
                           data-input="current-password"
                           required
                           autocomplete="off"/>
                        <div x-on:click="showPasswordCurrent = !showPasswordCurrent"
                            class="cursor-pointer px-4 icon-class i-password-content"
                            :aria-label="showPasswordCurrent ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                            >
                            <template x-if="!showPasswordCurrent">
                                <?= $heroIcons->eyeHtml('w-5 h-5'); ?>
                            </template>

                            <template x-if="showPasswordCurrent">
                                <?= $heroIcons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <template x-if="showPasswordFields">
            <div>
                <div class="field field-reserved">
                    <label class="label" for="password"><span>
                            <?= $escaper->escapeHtml(__('New Password')) ?>
                    </span></label>
                    <div class="control flex items-center content-form-p">
                        <div class="input-icon">
                            <input :type="showPasswordNew ? 'text' : 'password'" type="password" class="form-input w-72 border border-gray-400 rounded-lg opacity-100 text-sm mb-5" name="password" id="password" required
                                data-validate='{"password-strength": {"minCharacterSets": <?= (int) $passwordMinCharacterSets ?>}}'
                                @input="onChange"
                                minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>" autocomplete="off"/>
                            <div x-on:click="showPasswordNew = !showPasswordNew"
                                class="cursor-pointer px-4 icon-class i-password-content"
                                :aria-label="showPasswordNew ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                            >
                                <template x-if="!showPasswordNew">
                                    <?= $heroIcons->eyeHtml('w-5 h-5'); ?>
                                </template>
                                <template x-if="showPasswordNew">
                                    <?= $heroIcons->eyeOffHtml('w-5 h-5'); ?>
                                </template>
                            </div>
                        </div>
                        <div id="password-strength-meter-container" data-role="password-strength-meter" aria-live="polite">
                            <div id="password-strength-meter" class="password-strength-meter">
                                <?= $escaper->escapeHtml(__('Password Strength')) ?>:
                                <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                                    <?= $escaper->escapeHtml(__('No Password')) ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="field field-reserved">
                    <label class="label" for="password-confirmation">
                        <span><?= $escaper->escapeHtml(__('Confirm New Password')) ?></span>
                    </label>
                    <div class="control flex items-center content-form-p">
                        <div class="input-icon">
                            <input :type="showPasswordConfirm ? 'text' : 'password'" type="password" class="form-input w-72 border border-gray-400 rounded-lg opacity-100 text-sm mb-5"
                                name="password_confirmation" id="password-confirmation"
                                data-validate='{"equalTo": "password"}'
                                @input="onChange"
                                autocomplete="off"/>
                            <div x-on:click="showPasswordConfirm = !showPasswordConfirm"
                                class="cursor-pointer px-4 icon-class i-password-content"
                                :aria-label="showPasswordConfirm ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                            >
                                <template x-if="!showPasswordConfirm">
                                    <?= $heroIcons->eyeHtml('w-5 h-5'); ?>
                                </template>
                                <template x-if="showPasswordConfirm">
                                    <?= $heroIcons->eyeOffHtml('w-5 h-5'); ?>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </fieldset>
    <?= $block->getChildHtml('form_additional_info') ?>
    <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_EDIT) : '' ?>
    <div class="actions-toolbar">
        <div class="s-font-button">
            <button type="submit" class="btn action save primary s-font-button bg-blue-picton" title="<?= $escaper->escapeHtmlAttr(__('Save')) ?>">
                <span><?= $escaper->escapeHtml(__('Save')) ?></span>
            </button>
        </div>
        <div class="secondary">
           
        </div>
    </div>
</form>
<div class="w-full">
    <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_EDIT) : '' ?>
</div>
<script>
    function initForm() {
        return {
            errors: 0,
            hasCaptchaToken: 0,
            displayErrorMessage: false,
            errorMessages: [],
            showEmailField: false,
            showPasswordNew: false,
            showPasswordConfirm: false,
            showPasswordCurrent: false,
            showPasswordFields: <?= $block->getChangePassword() ? $escaper->escapeJs('true') : $escaper->escapeJs('false') ?>,
            setErrorMessages(messages) {
                this.errorMessages = [messages]
                this.displayErrorMessage = this.errorMessages.length
            },
            submitForm() {
                this.validate()
                    .then(() => {
                        // Do not rename $form, the variable is expected to be declared in the recaptcha output
                        const $form = document.querySelector('#form-validate');
                        <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_EDIT) : '' ?>

                        if (this.errors === 0) {
                            $form.submit();
                        }
                    })
                    .catch((invalid) => {
                        if (invalid.length > 0) {
                            invalid[0].focus();
                        }
                    })
            }
        }
    }
</script>
