<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\ForgotPasswordButton;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Account\Forgotpassword;
use Magento\Framework\Escaper;

/** @var Forgotpassword $block */
/** @var Escaper $escaper */
/** @var ReCaptcha $recaptcha */
/** @var ForgotPasswordButton $forgotPasswordButtonViewModel */
/** @var ViewModelRegistry $viewModels */

$forgotPasswordButtonViewModel = $viewModels->require(ForgotPasswordButton::class);
$formId = 'user_forgotpassword';

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');
?>
<div class="container mb-12">
    <div class="w-full my-8">
        <form action="<?= $escaper->escapeUrl($block->getUrl('*/*/forgotpasswordpost')) ?>"
              method="post"
              id="<?= $escaper->escapeHtmlAttr($formId) ?>"
              x-data="initPasswordForm()"
              class="form-password-forget mx-auto w-[30%] p-[30px] border border-blue-100 rounded-lg min-h-[300px] opacity-100 max-md:w-3/4 max-[480px]:w-full max-[480px]:p-5 max-lg:w-3/5 max-xl:w-2/ max-2xl:w-2/5"
              @submit.prevent="submitForm();">
            <?= $block->getBlockHtml('formkey') ?>
            <input type="hidden" name="formId" value="<?= $escaper->escapeHtmlAttr($formId) ?>"/>
            <?= $block->getChildHtml('form_fields_before') ?>
            <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_FORGOT_PASSWORD) : '' ?>
            <fieldset class="fieldset mb-5 w-full">
                <!-- <div class="text-secondary-darker mb-8"><?= $escaper->escapeHtml(
                    __('Please enter your email address below to receive a password reset link.')
                ) ?></div> -->
                <div class="field title mb-[20px] text-2xl text-cadet-space font-bold max-[480px]:pl-[30px] max-[480px]:mt-[-25px] max-[480px]:ml-[-35px] max-[480px]:text-lg"><?= $block->getProductAction()->getDynamicTitle() ?></div>
                <div class="field note mb-[20px] mt-1 text-[16px] text-blue-prussian text-left max-[480px]:text-xs"><?= $block->getProductAction()->getDynamicDesc() ?></div>
                <div class="field email required">
                    <!-- <label for="email_address" class="label"><span><?= $escaper->escapeHtml(
                        __('Email')
                    ) ?></span></label> -->
                    <div class="control">
                        <input type="email" name="email" alt="email" id="email_address" class="placeholder-gray-500 form-input w-full border border-gray-400 rounded-md opacity-100 text-sm text-blue-prussian pl-5 max-[480px]:text-xs" required
                               placeholder="<?= $escaper->escapeHtml(__('Email Address')) ?>" value="<?= $escaper->escapeHtmlAttr($block->getEmailValue()) ?>">
                    </div>
                </div>
                <?= $block->getChildHtml('form_additional_info') ?>
                <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_FORGOT_PASSWORD) : '' ?>
            </fieldset>
            <div class="actions-toolbar !border-t-0 !mt-0 !pt-0">
                <div class="w-full">
                    <button type="submit" class="action submit bg-blue-picton !block w-full h-12 rounded-md opacity-100 text-blue-prussian font-normal text-lg text-center disabled:opacity-75 max-[480px]:text-sm"
                        <?php if ($forgotPasswordButtonViewModel->disabled()): ?> disabled="disabled" data-recaptcha-btn<?php endif; ?>
                    >
                        <span class="text-center text-[20px] text-blue-prussian max-[480px]:text-xs"><?= $escaper->escapeHtml(__('Send')) ?></span>
                    </button>
                </div>
                <!-- <div class="secondary">
                    <a class="action back"
                       href="<?= $escaper->escapeUrl($block->getLoginUrl()) ?>"><span><?= $escaper->escapeHtml(__('Go back')) ?></span></a>
                </div> -->
            </div>
        </form>
        <script>
            function initPasswordForm() {
                return {
                    errors: 0,
                    hasCaptchaToken: 0,
                    submitForm() {
                        // Do not rename $form, the variable is expected to be declared in the recaptcha output
                        const $form = document.querySelector('#<?= $escaper->escapeJs($formId) ?>');
                        <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_FORGOT_PASSWORD) : '' ?>

                        if (this.errors === 0) {
                            $form.submit();
                        }
                    }
                }
            }
        </script>
    </div>
</div>