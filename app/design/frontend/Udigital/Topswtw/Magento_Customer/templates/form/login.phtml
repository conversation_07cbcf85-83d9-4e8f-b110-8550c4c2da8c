<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\LoginButton;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Form\Login;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Login $block */
/** @var LoginButton $loginButtonViewModel */
/** @var ViewModelRegistry $viewModels */
/** @var ReCaptcha $recaptcha */
/** @var HeroiconsOutline $heroiconsoutline */
/** @var HeroiconsSolid $heroiconssolid */

$heroiconsoutline = $viewModels->require(HeroiconsOutline::class);
$heroiconssolid = $viewModels->require(HeroiconsSolid::class);
$loginButtonViewModel = $viewModels->require(LoginButton::class);

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');
?>
<div class="w-full md:w-96 md:min-h-80 border border-white-azureish rounded-lg p-6">
    <div aria-labelledby="block-customer-login-heading">
        <form class="form form-login"
              action="<?= $escaper->escapeUrl($block->getPostActionUrl()) ?>"
              method="post"
              x-data="initCustomerLoginForm()"
              @submit.prevent="submitForm()"
              id="customer-login-form">
            <?= $block->getBlockHtml('formkey') ?>
            <fieldset class="fieldset login relative">
                <legend>
                    <h2 class="text-2xl text-blue-prussian font-bold">
                        <?= $escaper->escapeHtml(__('Log in')) ?>
                    </h2>
                </legend>
                <div class="text-blue-prussian mb-4">
                    <?= $escaper->escapeHtml(
                        __('Log in and order quickly and securely.')
                    ) ?>
                </div>
                <div class="field email required mb-4">
                    <!-- <label class="label" for="email"><span><?= $escaper->escapeHtml(__('Email')) ?></span></label> -->
                    <div class="control">
                        <input name="login[username]"
                               class="form-input w-full"
                               required
                               value="<?= $escaper->escapeHtmlAttr($block->getUsername()) ?>"
                            <?php if ($block->isAutocompleteDisabled()): ?>
                                autocomplete="off"
                            <?php endif; ?>
                               id="email"
                               type="email"
                               title="<?= $escaper->escapeHtmlAttr(__('Email Address')) ?>"
                               placeholder="<?= $escaper->escapeHtmlAttr(__('Email Address')) ?>"/>
                    </div>
                </div>
                <div class="field password required mb-4">
                    <!-- <label for="pass" class="label"><span><?= $escaper->escapeHtml(__('Password')) ?></span></label> -->
                    <div class="control flex items-center">
                        <input name="login[password]"
                               class="form-input w-full"
                               required
                               :type="showPassword ? 'text' : 'password'"
                            <?php if ($block->isAutocompleteDisabled()): ?>
                                autocomplete="off"
                            <?php endif; ?>
                               id="pass"
                               title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                               placeholder="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"/>
                        <div x-on:click="showPassword = !showPassword"
                             class="cursor-pointer absolute right-4"
                             :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                            >
                            <template x-if="!showPassword">
                                <?= $heroiconssolid->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPassword">
                                <?= $heroiconssolid->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </div>
                    </div>
                </div>

                <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_LOGIN) : '' ?>
                <?= $block->getChildHtml('form_additional_info') ?>

                <a class="text-blue-picton text-sm" href="<?= $escaper->escapeUrl($block->getForgotPasswordUrl()) ?>"><span><?= $escaper->escapeHtml(__('Forgot Password?')) ?></span></a>

                <div class="actions-toolbar flex justify-between pt-6 pb-2 items-center">
                    <button type="submit" class="w-full bg-blue-picton py-2 text-center rounded-lg" name="send"
                        <?php if ($loginButtonViewModel->disabled()): ?> disabled="disabled" data-recaptcha-btn<?php endif; ?>
                    >
                        <span><?= $escaper->escapeHtml(__('Log in')) ?></span>
                    </button>
                </div>
                <div>
                    <template x-if="displayErrorMessage">
                        <p class="text-red flex items-center">
                            <span class="inline-block w-8 h-8 mr-3">
                                <?= $heroiconsoutline->exclamationCircleHtml(); ?>
                            </span>
                            <template x-for="errorMessage in errorMessages">
                                <span x-html="errorMessage"></span>
                            </template>
                        </p>
                    </template>
                </div>
            </fieldset>
        </form>
    </div>
    <div class="w-full">
        <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_LOGIN) : '' ?>
    </div>
    <script>
        function initCustomerLoginForm() {
            return {
                errors: 0,
                hasCaptchaToken: 0,
                showPassword: false,
                displayErrorMessage: false,
                errorMessages: [],
                setErrorMessages(messages) {
                    this.errorMessages = [messages]
                    this.displayErrorMessage = this.errorMessages.length
                },
                submitForm() {
                    // do not rename $form, the variable is the expected name in the recaptcha output
                    const $form = document.querySelector('#customer-login-form');
                    <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_LOGIN) : '' ?>

                    if (this.errors === 0) {
                        $form.submit();
                    }
                }
            }
        }
    </script>
</div>