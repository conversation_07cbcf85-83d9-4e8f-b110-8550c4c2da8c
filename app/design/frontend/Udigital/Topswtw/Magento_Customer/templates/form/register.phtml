<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\CreateAccountButton;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Modal;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Form\Register;
use Magento\Customer\Block\Widget\Dob;
use Magento\Customer\Block\Widget\Gender;
use Magento\Customer\Block\Widget\Name as NameWidget;
use Magento\Customer\Block\Widget\Taxvat;
use Magento\Customer\Helper\Address;
use Magento\Framework\Escaper;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Register $block */
/** @var Escaper $escaper */
/** @var ReCaptcha $recaptcha */
/** @var CreateAccountButton $createAccountButtonViewModel */
/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsSolid $heroicons */

/** @var Address $addressHelper */
$addressHelper = $block->getData('addressHelper');
$formData = $block->getFormData();
$formId = 'accountcreate';


// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');
$heroIcons = $viewModels->require(HeroiconsSolid::class);
$createAccountButtonViewModel = $viewModels->require(CreateAccountButton::class);
$region = $block->getAttributeData()->getFrontendLabel('region');
$selectRegion = 'Please select a region, state or province.';
$showOptionalRegions = $block->getConfig('general/region/display_all');
$regionLabel = $block->getAttributeData()->getFrontendLabel('region');
$minimumPasswordLength = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();


/** @var ViewModelRegistry $viewModels */
/** @var Modal $modalViewModel */

$modalViewModel = $viewModels->require(Modal::class);
$apiKey = $block->getSwissupData()->getApiKey();
?>

<div class=""
    x-data="Object.assign({}, hyva.modal())"
    >
            <button @click="show" id="buttonValidation" class="text-filter-modal-register">
            </button>
        <?= $modalViewModel->createModal()->withContent('
            <div class="relative w-full h-full md:h-auto text-blue-prussian text-justify" >
                <!-- Modal header -->
                <div class="flex items-start md:pb-4 pt-4 pl-4 mb-2 bg-filter-blue " >
                <span class="p-6 header-modal flex justify-between bg-filter-blue"><span class="text-xl text-white">BTW nummer validatie resultaat</span></span>
                </div>
                <!-- Modal body -->
                <div class="bg-white rounded shadow-lg w-full" x-data="clickHandler()">
                    <input type="hidden" id="country-btw">

                    <div class="p-6 content-modal grid gap-4" >
                    Het geselecteerde land klopt niet bij het BTW nummer. Zullen we het land automatisch aanpassen?
                    </div>
                    <span class="p-6 flex justify-between footer-modal">
                    <button @click="hide" id="close-modal" class="filter-footer-text" >Cancel</button>
                    <button @click="yesClicked" class="filter-footer-text" >Yes</button>
                    </span>
                </div>
            </div>
            <script>
            </script>')->withContainerClasses('fixed', 'flex', 'justify-center', 'md:items-center', 'max-h-full')
        ->withAriaLabelledby('the-label')
        ->addDialogClass('modal-filter-home', 'test')
        ->removeDialogClass('p-10', 'overflow-auto', 'max-h-screen'); ?>
</div>



<div class="mb-8 container-register max-md:mt-10 w-full max-[480px]:pl-4  max-[480px]:pr-4 " x-data="{ account: 'personal',accountType: '' }">
    <?php /* Extensions placeholder */ ?>
    <?= $block->getChildHtml('customer.form.register.extra') ?>
    <form class="form create account form-create-account"
          action="<?= $escaper->escapeUrl($block->getPostActionUrl()) ?>"
          x-data="Object.assign(hyva.formValidation($el), initForm())"
          <?php if ($block->getShowAddressFields()): ?>
          @private-content-loaded.window="onPrivateContentLoaded(event.detail.data)"
          <?php endif; ?>
          id="<?= $escaper->escapeHtmlAttr($formId) ?>"
          @submit.prevent="submitForm()"
          method="post" id="form-validate" enctype="multipart/form-data" autocomplete="off">
        <?= /* @noEscape */ $block->getBlockHtml('formkey'); ?>
        <?= $block->getChildHtml('form_fields_before') ?>
        <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_CREATE) : '' ?>


        <span class="register-title-top text-left font-bold text-2xl text-blue-prussian max-md:hidden">
            <?= $escaper->escapeHtml(__('Create an account')) ?>
        </span>

        <div class="md:grid grid-cols-2 gap-4 formdiv  mt-[-10px] max-md:block">
            <fieldset class="my-8 card border border-gray-300 rounded-lg shadow-none p-8 max-md:border-none max-md:rounded-none max-md:shadow-none max-md:p-0">
                <span class="register-title desktop text-left font-bold text-xl text-blue-prussian max-md:hidden max-md:text-lg max-md:font-bold">
                    <?= $escaper->escapeHtml(__('Personal Info')) ?>
                </span>
                <span class="register-title mobile text-left font-bold text-xl text-blue-prussian md:hidden max-md:text-lg max-md:font-bold">
                    <?= $escaper->escapeHtml(__('Opening an account')) ?>
                </span>
                <input type="hidden" name="success_url" value="<?= $escaper->escapeUrl($block->getSuccessUrl()) ?>">
                <input type="hidden" name="error_url" value="<?= $escaper->escapeUrl($block->getErrorUrl()) ?>">
                <input type="hidden" name="create_address" value="1"/>
                <input type="hidden" name="default_billing" value="1">
                <input type="hidden" name="default_shipping" value="1">

                <div class="flex radio-type md:pt-6 md:pb-2.5 max-md:pt-5 max-md:pb-2.5">

                    <div class="flex mr-4">

                        <label for="personal-radio" class="radio-label-register fontSize-3.5 text-blue-prussian font-public-sans max-md:text-xs max-md:pt-1 max-md:pl-4 " >
                            <input class="field radio personal" id="personal-radio" type="radio" name="account_type" value="personal" x-model="account" @click="accountType = 'personal-radio'">
                            <i class="checked personal icon md:mr-[4px] md:mt-[0px] max-md:mr-1"></i>
                            <i class="notchecked personal icon"></i>
                            <?= $escaper->escapeHtml(__('Personal')) ?>
                        </label><br>
                    </div>
                    <div class="flex mr-4">
                        <label for="company-radio" class="radio-label-register fontSize-3.5 text-blue-prussian font-public-sans max-md:text-xs max-md:pt-1 max-md:pl-4">
                            <input class="field radio company" id="company-radio" type="radio" name="account_type" value="company" x-model="account" @click="accountType = 'company-radio'">
                            <i class="checked company icon"></i>
                            <i class="notchecked company icon"></i>
                            <?= $escaper->escapeHtml(__('Business')) ?>
                        </label><br>
                    </div>
                </div>



                <?= $block->getLayout()->createBlock(NameWidget::class)->setObject($block->getFormData())->setForceUseCustomerAttributes(true)->toHtml() ?>


                <?php if ($block->isNewsletterEnabled()): ?>
                    <!-- <div class="field choice newsletter">
                        <input type="checkbox" name="is_subscribed"
                               title="<?= $escaper->escapeHtmlAttr(__('Sign Up for Newsletter')) ?>" value="1"
                               id="is_subscribed"
                                <?php if ($block->getFormData()->getIsSubscribed()): ?>
                                    checked="checked"
                                <?php endif; ?>
                               class="checkbox">
                        <label for="is_subscribed" class="label">
                            <span>
                                <?= $escaper->escapeHtml(__('Sign Up for Newsletter')) ?>
                            </span>
                        </label>
                    </div> -->
                    <?php /* Extensions placeholder */ ?>
                    <?= $block->getChildHtml('customer.form.register.newsletter') ?>
                <?php endif ?>



                <?php $dob = $block->getLayout()->createBlock(Dob::class) ?>
                <?php if ($dob->isEnabled()): ?>
                    <?= $dob->setDate($block->getFormData()->getDob())->toHtml() ?>
                <?php endif ?>
                <input type="hidden" name="create_address" value="1"/>



                <?php $telephone = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Telephone::class) ?>
                <?php if ($telephone->isEnabled()): ?>
                    <?= $telephone->setTelephone($block->getFormData()->getTelephone())->toHtml() ?>
                <?php endif ?>

                <?php $fax = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Fax::class) ?>
                <?php if ($fax->isEnabled()): ?>
                    <?= $fax->setFax($block->getFormData()->getFax())->toHtml() ?>
                <?php endif ?>


                <div class="field field-reserved country w-full required">
                    <label class="label fontSize-3.5 text-blue-prussian font-public-sans" for="country">
                    <span class="max-md:text-xs">
                        <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('country_id') ?>
                    </span>
                    </label>
                    <div class="control">
                        <?php $countries = $block
                            ->getCountryCollection()
                            ->setForegroundCountries($block->getTopDestinations())
                            ->toOptionArray();
                        ?>
                        <select name="country_id"
                                id="country"
                                title="Country"
                                required
                                class="form-select w-full text-blue-prussian mb-5 md:text-sm max-md:text-xs"
                                x-ref="country_id"
                                @change="changeCountry($event.target)"
                        >
                            <?php foreach ($countries as $country): ?>
                                <option <?php if ($country['value'] == 'NL') { echo 'selected';} ?> name="<?= /** @noEscape */ $country['label'] ?>"
                                        value="<?= /** @noEscape */ $country['value'] ?>"
                                        data-is-zip-required="<?= (isset($country['is_zipcode_optional'])) ? '0' : '1' ?>"
                                        data-is-region-required="<?= (isset($country['is_region_required'])) ? '1' : '0' ?>"
                                    <?= ($block->getCountryId() ===  $country['value']) ? 'selected="selected"' : '' ?>
                                >
                                    <?= /** @noEscape */ $country['label'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <?php $streetValidationClass =
                    $addressHelper->getAttributeValidationClass(
                        'street'
                    ); ?>
                <div class="field field-reserved">
                    <label for="street_1" class="label fontSize-3.5 text-blue-prussian font-public-sans"><span class="max-md:text-xs"><?= /* @noEscape */
                            $escaper
                            ->escapeHtmlAttr(__('Street and house number')) ?></span></label>
                    <div class="control">
                        <div class="flex flex-col relative justify-center">
                            <div class="absolute z-[9] pl-2 text-gray-400">
                                <?= $heroIcons->searchHtml('w-6 h-6'); ?>
                            </div>
                            <input type="text" name="street[]"
                                value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getStreet(0)) ?>"
                                title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?>"
                                id="street_1"
                                placeholder="<?= /* @noEscape */ $escaper->escapeHtmlAttr(__('Enter street and house number and select your address from the list.')) ?>"
                                class="form-input  focus:ring-green-500 <?= $escaper->escapeHtmlAttr($streetValidationClass) ?>  !mb-0 fontSize-3.5 !pl-8 w-full border border-gray-400 rounded-md  max-md:text-xs  ">
                        </div>

                    </div>
                </div>

                <div class="field field-reserved choice manual-address-toggle flex flex-wrap mb-5">
                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            name="manualAddressEnabled"
                            id="manualAddressEnabled"
                            x-model="manualAddressEnabled"
                            @change="toggleAddressFields(manualAddressEnabled)"
                            required
                            class="checkbox md:mr-2.5 max-md:mr-0.5"
                        />
                        <label for="manualAddressEnabled" class="label fontSize-3.5 text-blue-prussian font-public-sans">
                            <span class="nobold">
                                <?= $escaper->escapeHtml(__('Enter address manually')) ?>
                            </span>
                        </label>
                    </div>
                </div>



                <div x-show="manualAddressEnabled" x-cloak>
                    <div class="field street w-full mt-5">
                        <div class="control">
                        <div class="nested flex">
                                <?php $streetValidationClass =
                                    trim(str_replace('required-entry', '', $streetValidationClass)); ?>
                                <?php for ($i = 2, $n = $addressHelper->getStreetLines(); $i <= $n; $i++): ?>
                                    <div class="field field-reserved additional div_street_<?= (int) $i ?> <?= ($i == 2 || $i == 3)?' mr-2 w-1/2 required':''; ?> <?= ($i == 4)?'w-1/2 min-[769px]:max-[785px]:w-1/4 min-[786px]:max-[953px]:w-1/3 ':''; ?> <?= ($i == 3)?'min-[769px]:max-[953px]:w-10/12 ':''; ?>">
                                        <label class="label fontSize-3.5 text-blue-prussian font-public-sans min-[769px]:max-[953px]:!text-xs min-[769px]:max-[953px]:after:!m-0" for="street_<?= /* @noEscape */ $i ?>">
                                            <span class="max-md:text-xs">
                                                <?php
                                                if ($i == 2) {
                                                    echo  $escaper->escapeHtml(__('Street'));
                                                } elseif ($i == 3) {
                                                    echo  $escaper->escapeHtml(__('House Number'));
                                                } else {
                                                    echo  $escaper->escapeHtml(__('Additional'));
                                                }
                                                ?>
                                            </span>
                                        </label>
                                        <div class="control">
                                            <input type="text" <?= ($i == 2)?'name="street2[]"':'name="street3[]"'; ?>
                                                value="<?= $escaper->escapeHtmlAttr($block
                                                    ->getFormData()
                                                    ->getStreetLine($i - 1)) ?>"
                                                title="<?= $escaper
                                                    ->escapeHtmlAttr(__('Street Address %1', $i)) ?>"
                                                id="street_<?= (int) $i ?>"
                                                required
                                                <?= ($i == 2)?'required':''; ?>
                                                class="form-input focus:ring-green-500 w-full border border-gray-400 rounded-md   max-md:text-xs  <?= $escaper
                                                    ->escapeHtmlAttr($streetValidationClass) ?>"
                                            >
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                    <div class="md:grid grid-cols-2 gap-4">
                        <div class="field field-reserved zip w-full required">
                            <label class="label fontSize-3.5 text-blue-prussian font-public-sans" for="zip">
                            <span class="max-md:text-xs">
                                <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>
                            </span>
                            </label>
                            <div class="control">
                                <input type="text"
                                    name="postcode"
                                    x-ref="postcode"
                                    value=""
                                    title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                                    id="zip"
                                    :required="isZipRequired"
                                    @change="onChange"
                                    data-validate='{"postcode": true}'
                                class="form-input validate-zip-international focus:ring-green-500 w-full border border-gray-400 rounded-md   max-md:text-xs
                                <?= $escaper->escapeHtmlAttr($addressHelper->getAttributeValidationClass('postcode')) ?>">
                            </div>
                        </div>

                        <div class="field field-reserved w-full required">
                            <label for="city" class="label fontSize-3.5 text-blue-prussian font-public-sans">
                                <span class="max-md:text-xs">
                                    <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>
                                </span>
                            </label>
                            <div class="control">
                                <input type="text" name="city"
                                    value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getCity()) ?>"
                                    title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>"
                                    class="form-input focus:ring-green-500 <?= $escaper->escapeHtmlAttr($addressHelper->getAttributeValidationClass('city')) ?> w-full border border-gray-400 rounded-md   max-md:text-xs "
                                    required
                                    id="city">

                            </div>
                        </div>
                    </div>
                </div>
                <div class="field field-reserved region w-full hidden"
                    x-cloak
                    x-show="(hasAvailableRegions() && isRegionRequired) || showOptionalRegions" >
                    <label class="label fontSize-3.5 text-blue-prussian font-public-sans" for="region_id">
                        <span><?= /* @noEscape */ $regionLabel ?></span>
                    </label>
                    <div class="control">
                        <template x-if="hasAvailableRegions() && (isRegionRequired || showOptionalRegions)">
                            <select id="region_id" name="region_id"
                                    title="<?= /* @noEscape */ $regionLabel ?>"
                                    class="form-select validate-select region_id"
                                    x-ref="region_id"
                                    x-model="selectedRegion"
                                    @change="$refs.region.value = availableRegions[selectedRegion].name"
                            >
                                <option value=""><?= $escaper->escapeHtml(__('Please select a region, state or province.')) ?></option>
                                <template x-for="regionId in Object.keys(availableRegions)">
                                    <?php /* in alpine v3, if the bound props update, the template body gets evaluated before the template condition */ ?>
                                    <?php /* because of this it is required to check if availableRegions[regionId] is set */ ?>
                                    <option :value="regionId"
                                            :name="availableRegions[regionId] && availableRegions[regionId].name"
                                            x-text="availableRegions[regionId] && availableRegions[regionId].name"
                                            :selected="selectedRegion === regionId"
                                    >
                                    </option>
                                </template>
                            </select>
                        </template>
                        <input :type="hasAvailableRegions() && (isRegionRequired || showOptionalRegions) ? 'hidden' : 'text'"
                            id="region"
                            name="region"
                            x-ref="region"
                            value="<?= $escaper->escapeHtmlAttr($block->getRegion()) ?>"
                            title="<?= /* @noEscape */ $regionLabel ?>"
                            class="form-input"
                            :required="isRegionRequired"
                        />
                    </div>
                </div>



                <?php $addressAttributes = $block->getChildBlock('customer_form_address_user_attributes'); ?>
                <?php if ($addressAttributes): ?>
                    <?php $addressAttributes->setEntityType('customer_address'); ?>
                    <?php $addressAttributes->setFieldIdFormat('address:%1$s')->setFieldNameFormat('address[%1$s]'); ?>
                    <?php $block->restoreSessionData($addressAttributes->getMetadataForm(), 'address'); ?>
                    <?= $addressAttributes->setShowContainer(false)->toHtml() ?>
                <?php endif; ?>
                <input type="hidden" name="default_billing" value="1">
                <input type="hidden" name="default_shipping" value="1">

                <div x-show="account == 'company'">

                    <?php $company = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Company::class) ?>
                    <?php if ($company->isEnabled()): ?>
                        <?= $company->setCompany($block->getFormData()->getCompany())->toHtml() ?>
                    <?php endif ?>


                    <?php $taxvat = $block->getLayout()->createBlock(Taxvat::class) ?>
                    <?php if ($taxvat->isEnabled()): ?>
                        <?= $taxvat->setTaxvat($block->getFormData()->getTaxvat())->toHtml() ?>
                    <?php endif ?>

                    <?php if ($addressHelper->isVatAttributeVisible()): ?>
                        <?php $_vatidValidationClass = $addressHelper->getAttributeValidationClass('vat_id'); ?>
                        <div class="field field-reserved taxvat" id="taxvatdiv">
                            <label class="label fontSize-3.5 text-blue-prussian font-public-sans" for="vat_id">
                                <span class="max-md:text-xs"><?= __('Transferring VAT from abroad?') ?></span>
                            </label>
                            <div class="control">
                                <input type="text"
                                    name="vat_id"
                                    value="<?= $escaper->escapeHtmlAttr($formData->getVatId()) ?>"
                                    title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?>"
                                    class="input-text focus:ring-green-500 <?= $escaper->escapeHtmlAttr($_vatidValidationClass) ?> w-full border border-gray-400 rounded-md  max-md:text-xs  "
                                    @change="onChange"
                                    data-validate='{"vat_id": true}'
                                    id="vat_id">
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="field" >
                        <label for="kvk_nummer" class="label fontSize-3.5 text-blue-prussian font-public-sans"><span class="max-md:text-xs"><?= __('KVK nummer') ?></span></label>
                        <div class="control">
                            <input type="text" name="kvk_nummer" id="kvk_nummer" value="<?= $escaper->escapeHtml($block->getFormData()->getKvkNummer()) ?>" title="<?= __('KVK nummer') ?>" class="form-input focus:ring-green-500 w-full border border-gray-400 rounded-md   max-md:text-xs " >
                        </div>
                    </div>




                    <?php $gender = $block->getLayout()->createBlock(Gender::class) ?>
                    <?php if ($gender->isEnabled()): ?>
                        <?= $gender->setGender($block->getFormData()->getGender())->toHtml() ?>
                    <?php endif ?>
                    <?= $block->getChildHtml('fieldset_create_info_additional') ?>





                </div>
            </fieldset>
            <fieldset class="my-8 card border border-gray-300 rounded-lg shadow-none p-8 login-card max-md:mt-[-1.75rem] max-md:border-none max-md:rounded-none max-md:shadow-none max-md:p-0">

                <span class="register-title text-left font-bold text-xl text-blue-prussian max-md:text-lg max-md:font-bold"> <?= $escaper->escapeHtml(__('Sign-in Information')) ?></span>
                <div class="field field-reserved required pt-4 mb-0">
                    <label for="email_address" class="label fontSize-3.5 text-blue-prussian font-public-sans">
                        <span class="max-md:text-xs">
                            <?= $escaper->escapeHtml(__('Email')) ?>
                        </span>
                    </label>
                    <div class="control">
                        <input type="email" name="email" autocomplete="email" id="email_address" required
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getEmail()) ?>"
                               title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>" class="form-input focus:ring-green-500 w-full border border-gray-400 rounded-md  max-md:text-xs  " />
                    </div>
                </div>
                <div class="md:grid grid-cols-2 gap-4 sign-in-info">
                <div class="field field-reserved required div-password flex flex-col relative">
                    <label for="password" class="label fontSize-3.5 text-blue-prussian font-public-sans min-[769px]:max-[1050px]:h-11"><span class="max-md:text-xs"><?= $escaper->escapeHtml(__('Password')) ?></span></label>
                    <div class="control flex items-center">
                        <?php $minimumPasswordLength = $block->getMinimumPasswordLength() ?>
                        <input :type="showPassword ? 'text' : 'password'" type="password" id="password" name="password"
                               title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                               minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>"
                               class="form-input w-full border border-gray-400 rounded-md   focus:ring-green-500  max-md:text-xs "
                               required
                               data-validate='{"password-strength": {"minCharacterSets": <?= (int) $passwordMinCharacterSets ?>}}'
                               @input="onChange"
                               autocomplete="off">
                        <div x-on:click="showPassword = !showPassword"
                             class="cursor-pointer px-4 show-password absolute z-[9] right-0"
                             :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPassword">
                                <?= $heroIcons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPassword">
                                <?= $heroIcons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </div>
                        <!-- <div id="password-strength-meter-container" data-role="password-strength-meter" aria-live="polite">
                            <div id="password-strength-meter" class="password-strength-meter">
                                <?= $escaper->escapeHtml(__('Password Strength')) ?>:
                                <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                                    <?= $escaper->escapeHtml(__('No Password')) ?>
                                </span>
                            </div>
                        </div> -->
                    </div>
                </div>
                <div class="field field-reserved required div-password flex flex-col relative">
                    <label for="password-confirmation" class="label fontSize-3.5 text-blue-prussian font-public-sans min-[769px]:max-[1050px]:h-11">
                        <span class="max-md:text-xs">
                            <?= $escaper->escapeHtml(__('Confirm Password')) ?>
                        </span>
                    </label>
                    <div class="control flex items-center">
                        <input :type="showPasswordConfirm ? 'text' : 'password'" type="password"
                               name="password_confirmation"
                               title="<?= $escaper->escapeHtmlAttr(__('Confirm Password')) ?>"
                               id="password-confirmation"
                               data-validate='{"equalTo": "password"}'
                               @input="onChange"
                               required
                               class="form-input w-full border border-gray-400 rounded-md   focus:ring-green-500  max-md:text-xs "
                               autocomplete="off">
                        <div x-on:click="showPasswordConfirm = !showPasswordConfirm"
                             class="cursor-pointer px-4 show-password absolute z-[9] right-0"
                             :aria-label="showPasswordConfirm ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPasswordConfirm">
                                <?= $heroIcons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPasswordConfirm">
                                <?= $heroIcons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </div>
                    </div>
                </div>
                </div>
                <?= $block->getChildHtml('form_additional_info') ?>
                <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_CREATE) : '' ?>

                <div class="field field-reserved choice termncondition flex flex-wrap mb-5">
                    <div class="flex">
                        <input type="checkbox" name="termncondition"
                            title="<?= $escaper->escapeHtmlAttr(__('I agree to the Terms and Conditions.')) ?>" value="1"
                            id="termncondition"
                            required
                            data-validate='{"termncondition": true}'
                            class="checkbox md:mr-2.5 max-md:mr-0.5">
                    <label for="termncondition" class="label fontSize-3.5 text-blue-prussian font-public-sans">
                        <span class="nobold">
                            <?= $escaper->escapeHtml(__('I agree to the')) ?> <a target="_blank" href="<?= $block->getBaseUrl().'algemene-voorwaarden' ?>"><u><?= strtolower($escaper->escapeHtml(__('Terms and Conditions'))) ?></u></a>
                        </span>
                    </label>
                    </div>
                </div>

                <div class=" flex">
                    <div class="parent-button max-md:w-full">
                        <button type="submit" class="action submit primary btn btn-primary disabled:opacity-75 register-button shadow-none w-52 grid max-md:w-full bg-blue-picton"
                                title="<?= $escaper->escapeHtmlAttr(__('Create an Account')) ?>"
                                <?php if ($createAccountButtonViewModel->disabled()): ?> disabled="disabled" data-recaptcha-btn<?php endif; ?>>
                                <span class="font-sans text-lg text-blue-prussian max-md:text-base"><?= $escaper->escapeHtml(__('Create an Account')) ?></span>
                        </button>
                    </div>
                    <!-- <div class="secondary ml-4 self-center">
                        <a class="action back"
                        href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                            <span>
                                <?= $escaper->escapeHtml(__('Back')) ?>
                            </span>
                        </a>
                    </div> -->
                </div>
            </fieldset>
        </div>

        <?php
        // $this->setShowAddressFields(1);
        if ($block->getShowAddressFields()): ?>
            <fieldset class="my-8 card border border-gray-300 rounded-lg shadow-none p-8 max-md:border-none max-md:rounded-none max-md:shadow-none max-md:p-0">
                <legend class="contents">
                    <span>
                        <?= $escaper->escapeHtml(__('Address Information')) ?>
                    </span>
                </legend>
                <input type="hidden" name="create_address" value="1"/>

                <?php $company = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Company::class) ?>
                <?php if ($company->isEnabled()): ?>
                    <?= $company->setCompany($block->getFormData()->getCompany())->toHtml() ?>
                <?php endif ?>

                <?php $telephone = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Telephone::class) ?>
                <?php if ($telephone->isEnabled()): ?>
                    <?= $telephone->setTelephone($block->getFormData()->getTelephone())->toHtml() ?>
                <?php endif ?>

                <?php $fax = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Fax::class) ?>
                <?php if ($fax->isEnabled()): ?>
                    <?= $fax->setFax($block->getFormData()->getFax())->toHtml() ?>
                <?php endif ?>
                <?php $streetValidationClass =
                    $addressHelper->getAttributeValidationClass(
                        'street'
                    ); ?>
                <div class="field field-reserved street required mt-5">
                    <label for="street_1" class="label fontSize-3.5 text-blue-prussian font-public-sans"><span class="max-md:text-xs"><?= /* @noEscape */
                            $block->getAttributeData()->getFrontendLabel('street') ?></span></label>
                    <div class="control">
                        <input type="text" name="street[]"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getStreet(0)) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?>"
                               id="street_1"
                               class="form-input <?= $escaper->escapeHtmlAttr($streetValidationClass) ?> focus:ring-green-500 w-full border border-gray-400 rounded-md  max-md:text-xs  "
                                autocomplete="chrome-off" >
                        <div class="nested">
                            <?php $streetValidationClass =
                                trim(str_replace('required-entry', '', $streetValidationClass)); ?>
                            <?php for ($i = 2, $n = $addressHelper->getStreetLines(); $i <= $n; $i++): ?>
                                <div class="field additional">
                                    <label class="label fontSize-3.5 text-blue-prussian font-public-sans" for="street_<?= /* @noEscape */ $i ?>">
                                        <span class="max-md:text-xs"><?= $escaper->escapeHtml(__('Address')) ?></span>
                                    </label>
                                    <div class="control">
                                        <input type="text" name="street[]"
                                               value="<?= $escaper->escapeHtmlAttr($block
                                                   ->getFormData()
                                                   ->getStreetLine($i - 1)) ?>"
                                               title="<?= $escaper
                                                   ->escapeHtmlAttr(__('Street Address %1', $i)) ?>"
                                               id="street_<?= (int) $i ?>"
                                               class="form-input focus:ring-green-500 max-md:text-xs  <?= $escaper
                                                   ->escapeHtmlAttr($streetValidationClass) ?> w-full border border-gray-400 rounded-md  "
                                        >
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>

                <div class="field field-reserved required">
                    <label for="city" class="label fontSize-3.5 text-blue-prussian font-public-sans">
                        <span>
                            <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>
                        </span>
                    </label>
                    <div class="control">
                        <input type="text" name="city"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getCity()) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>"
                               class="form-input focus:ring-green-500 max-md:text-xs <?= $escaper->escapeHtmlAttr($addressHelper->getAttributeValidationClass('city')) ?> w-full border border-gray-400 rounded-md  "
                               id="city">

                    </div>
                </div>

                <div class="field field-reserved region w-full"
                      x-cloak
                      x-show="(hasAvailableRegions() && isRegionRequired) || showOptionalRegions"
                >
                    <label class="label fontSize-3.5 text-blue-prussian font-public-sans" for="region_id">
                        <span><?= /* @noEscape */ $regionLabel ?></span>
                    </label>
                    <div class="control">
                        <template x-if="hasAvailableRegions() && (isRegionRequired || showOptionalRegions)">
                            <select id="region_id" name="region_id"
                                    title="<?= /* @noEscape */ $regionLabel ?>"
                                    class="form-select validate-select region_id"
                                    x-ref="region_id"
                                    x-model="selectedRegion"
                                    @change="$refs.region.value = availableRegions[selectedRegion].name"
                            >
                                <option value=""><?= $escaper->escapeHtml(__('Please select a region, state or province.')) ?></option>
                                <template x-for="regionId in Object.keys(availableRegions)">
                                    <?php /* in alpine v3, if the bound props update, the template body gets evaluated before the template condition */ ?>
                                    <?php /* because of this it is required to check if availableRegions[regionId] is set */ ?>
                                    <option :value="regionId"
                                            :name="availableRegions[regionId] && availableRegions[regionId].name"
                                            x-text="availableRegions[regionId] && availableRegions[regionId].name"
                                            :selected="selectedRegion === regionId"
                                    >
                                    </option>
                                </template>
                            </select>
                        </template>
                        <input :type="hasAvailableRegions() && (isRegionRequired || showOptionalRegions) ? 'hidden' : 'text'"
                               id="region"
                               name="region"
                               x-ref="region"
                               value="<?= $escaper->escapeHtmlAttr($block->getRegion()) ?>"
                               title="<?= /* @noEscape */ $regionLabel ?>"
                               class="form-input"
                               :required="isRegionRequired"
                        />
                    </div>
                </div>

                <div class="field field-reserved zip w-full">
                    <label class="label fontSize-3.5 text-blue-prussian font-public-sans" for="zip">
                    <span>
                        <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>
                    </span>
                    </label>
                    <div class="control">
                        <input type="text"
                               name="postcode"
                               x-ref="postcode"
                               value=""
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                               id="zip"
                               :required="isZipRequired"
                               @change="onChange"
                               data-validate='{"postcode": true}'
                        class="form-input focus:ring-green-500 validate-zip-international w-full border border-gray-400 rounded-md  max-md:text-xs
                        <?= $escaper->escapeHtmlAttr($addressHelper->getAttributeValidationClass('postcode')) ?>">
                    </div>
                </div>

                <div class="field field-reserved country w-full">
                    <label class="label fontSize-3.5 text-blue-prussian font-public-sans" for="country">
                    <span>
                        <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('country_id') ?>
                    </span>
                    </label>
                    <div class="control">
                        <?php $countries = $block
                            ->getCountryCollection()
                            ->setForegroundCountries($block->getTopDestinations())
                            ->toOptionArray();
                        ?>
                        <select name="country_id"
                                id="country"
                                title="Country"
                                required
                                class="form-select w-full text-blue-prussian mb-5 md:text-sm max-md:text-xs"
                                x-ref="country_id"
                                @change="changeCountry($event.target)"
                        >
                            <?php foreach ($countries as $country): ?>
                                <option name="<?= /** @noEscape */ $country['label'] ?>"
                                        value="<?= /** @noEscape */ $country['value'] ?>"
                                        data-is-zip-required="<?= (isset($country['is_zipcode_optional'])) ? '0' : '1' ?>"
                                        data-is-region-required="<?= (isset($country['is_region_required'])) ? '1' : '0' ?>"
                                    <?= ($block->getCountryId() ===  $country['value']) ? 'selected="selected"' : '' ?>
                                >
                                    <?= /** @noEscape */ $country['label'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <?php $addressAttributes = $block->getChildBlock('customer_form_address_user_attributes'); ?>
                <?php if ($addressAttributes): ?>
                    <?php $addressAttributes->setEntityType('customer_address'); ?>
                    <?php $addressAttributes->setFieldIdFormat('address:%1$s')->setFieldNameFormat('address[%1$s]'); ?>
                    <?php $block->restoreSessionData($addressAttributes->getMetadataForm(), 'address'); ?>
                    <?= $addressAttributes->setShowContainer(false)->toHtml() ?>
                <?php endif; ?>
                <input type="hidden" name="default_billing" value="1">
                <input type="hidden" name="default_shipping" value="1">
            </fieldset>
        <?php endif; ?>

    </form>

    <script>

        const script = document.createElement('script')
        script.src = 'https://maps.googleapis.com/maps/api/js?key=<?= $apiKey ?>&libraries=places&callback=googleReady';
        script.type = 'text/javascript';
        document.head.append(script);

        const url = '<?= $escaper->escapeUrl($block->getBaseUrl()) ?>';
        function googleReady() {
            document.dispatchEvent(new Event('google:init'));
        }

        function initForm() {
            return {
                errors: 0,
                hasCaptchaToken: 0,
                showPassword: false,
                showPasswordConfirm: false,
                manualAddressEnabled: false,
                submitForm() {
                    this.validate()
                        .then(() => {
                            // Do not rename $form, the variable is expected to be declared in the recaptcha output
                            const $form = document.querySelector('#<?= $escaper->escapeJs($formId) ?>');
                            <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_CREATE) : '' ?>

                            if (this.errors === 0) {
                                $form.submit();
                            }
                        })
                        .catch((invalid) => {
                            if (invalid.length > 0) {
                                invalid[0].focus();
                            }
                        });
                },
                <?php //if ($block->getShowAddressFields()): ?>
                directoryData: {},
                availableRegions: {},
                selectedRegion: <?= $escaper->escapeJs($block->getRegion() ?: 0) ?>,
                isZipRequired: true,
                isRegionRequired: false,
                showOptionalRegions: <?= $showOptionalRegions ? 'true' : 'false' ?>,
                onPrivateContentLoaded(data) {
                    this.directoryData = data['directory-data'] || {};

                    <?php if ($block->getCountryId()): ?>
                    this.setCountry(this.$refs['country_id'], '<?= $escaper->escapeJs($block->getRegion()) ?>');
                    <?php endif; ?>

                },
                setRegionInputValue(regionName) {
                    this.$nextTick(() => {
                        const regionInputElement = this.$refs['region'];
                        if (regionInputElement) {
                            regionInputElement.value = regionName;
                        }
                    })
                },
                setCountry(countrySelectElement, initialRegion) {
                    const selectedOption = countrySelectElement.options[countrySelectElement.selectedIndex];
                    const countryCode = countrySelectElement.value;
                    const countryData = this.directoryData[countryCode] || false;

                    if (!countryData) {
                        this.setRegionInputValue('');
                        return;
                    }

                    this.isZipRequired = selectedOption.dataset.isZipRequired === '1';
                    this.isRegionRequired = selectedOption.dataset.isRegionRequired === '1';

                    this.availableRegions = countryData.regions || {};

                    const initialRegionId = Object.keys(this.availableRegions).filter(regionId => this.availableRegions[regionId].name === initialRegion)[0];
                    this.selectedRegion = initialRegionId || '0';
                    this.setRegionInputValue(initialRegionId && this.availableRegions[initialRegionId].name || '');

                },
                changeCountry(countrySelectElement, initialRegion) {
                    this.setCountry(countrySelectElement, initialRegion);

                    this.validateCountryDependentFields();

                    if (this.autocomplete) {
                        this.autocomplete.unbindAll();
                        window.google.maps.event.clearInstanceListeners(this.element);
                    }

                    this.autocomplete = new google.maps.places.Autocomplete(this.element, {
                        types: ['address'],
                        componentRestrictions: { country: document.querySelector('#country').value }
                    });

                    google.maps.event.addListener(this.autocomplete, 'place_changed', ()=>this.handleResponse(this.autocomplete.getPlace()));
                },
                validateCountryDependentFields() {
                        this.$nextTick(() => {
                            <?php /* Reinitialize validation rules for fields that depend on the country */ ?>
                            this.fields['postcode'] && this.removeMessages(this.fields['postcode'])
                            this.fields['region'] && this.removeMessages(this.fields['region'])
                            delete this.fields['postcode'];
                            delete this.fields['region'];
                            <?php /* Initialize country_id, too, because the postcode validation depends on it */ ?>
                            this.setupField(this.$refs['country_id']);
                            this.setupField(this.$refs['postcode']);
                            this.setupField(this.$refs['region']);

                            this.fields['postcode'] && this.validateField(this.fields['postcode']);
                            this.fields['region'] && this.validateField(this.fields['region']);
                        })
                    },
                hasAvailableRegions() {
                    return Object.keys(this.availableRegions).length > 0;
                },
                init() {

                    this.element = this.$el.querySelector("#street_1");

                    if (this.element === null) {
                        console.error("Cannot find Google Places Autocomplete input [x-ref=\"googleAutocomplete\"]");
                        return;
                    }

                    this.toggleAddressFields(false);

                    if (typeof window.google === 'undefined') {
                        document.addEventListener('google:init', () => {
                            this.initAutocomplete();
                        });
                    } else {
                        this.initAutocomplete();
                    }
                },
                initAutocomplete() {
                    this.autocomplete = new window.google.maps.places.Autocomplete(this.element, {
                        types: ['address'],
                        componentRestrictions: { country: document.querySelector('#country').value }
                    });
                    window.google.maps.event.addListener(this.autocomplete, 'place_changed', ()=>this.handleResponse(this.autocomplete.getPlace()));
                },
                handleResponse(placeResultData) {
                    const placeData = placeResultData.address_components;
                    let parent = this;
                    placeData.forEach(function (item, index) {
                        if (item.types[0] == 'street_number'){
                            let separated = parent.separateNumberAndLetter(item.short_name);
                            if (!separated.error) {
                                document.getElementById('street_2').value = item.short_name;
                                document.getElementById('street_3').value = separated.number;
                                document.getElementById('street_4').value = separated.letter;
                            } else {
                                document.getElementById('street_3').value = item.short_name;
                            }
                        }
                        console.log(item.types[0]);

                        if (item.types[0] == 'route') document.getElementById('street_2').value = item.short_name;
                        if (item.types[0] == 'postal_code' || item.types[0] == 'postal_code_prefix') document.getElementById('zip').value = item.long_name;
                        if (item.types[0] == 'administrative_area_level_2') document.getElementById('city').value = item.long_name;
                        if (item.types[0] == 'administrative_area_level_1') document.getElementById('region').value = item.long_name;
                        if (item.types[0] == 'country') document.getElementById('country').value = item.short_name;
                    });

                    this.toggleAddressFields();
                },
                separateNumberAndLetter(str) {
                    const matches = str.match(/^(\d+)([A-Za-z]+)$/);
                    if (matches) {
                        return { number: parseInt(matches[1], 10), letter: matches[2] };
                    } else {
                        return { error: 'No match found' };
                    }
                },
                toggleAddressFields(enable = true) {
                    this.manualAddressEnabled = enable;
                }
            <?php //endif; ?>
            }
        }


        function clickHandler() {
            return {
                yesClicked() {
                    document.querySelector('#country').value = document.querySelector('#country-btw').value;
                    document.getElementById('close-modal').click();
                }
            };
        }

        window.addEventListener('DOMContentLoaded', () => {


            hyva.formValidation.addRule('termncondition', (value, options) => {
                if (value && value.length < 1) {
                    return '<?= $escaper->escapeJs(__('You have to check our terms and conditions before continue.')) ?>';
                }

                return true;
            });

            hyva.formValidation.addRule('telephone', (value, options) => {
                const phoneNumber = value.trim().replace(' ', '');
                if (phoneNumber && phoneNumber.length < (options.minlength || 3)) {
                    return '<?= $escaper->escapeJs(__('The telephone number is too short.')) ?>';
                }

                return true;
            });

            const postCodeSpecs = <?= /* @noEscape */ $block->getPostCodeConfig()->getSerializedPostCodes() ?>;

            hyva.formValidation.addRule('postcode', (postCode, options, field, context) => {
                context.removeMessages(field, 'postcode-warning')
                const countryId = (context.fields['country_id'] && context.fields['country_id'].element.value),
                    validatedPostCodeExamples = [],
                    countryPostCodeSpecs = countryId && postCodeSpecs ? postCodeSpecs[countryId] : false;

                if (! postCode || ! countryPostCodeSpecs) return true;

                for (const postCodeSpec of Object.values(countryPostCodeSpecs)) {
                    if (new RegExp(postCodeSpec.pattern).test(postCode)) return true;
                    validatedPostCodeExamples.push(postCodeSpec.example);
                }
                if (validatedPostCodeExamples) {
                    context.addMessages(field, 'postcode-warning', [
                        '<?= $escaper->escapeJs(__('Provided Zip/Postal Code seems to be invalid.')) ?>',
                        '<?= $escaper->escapeJs(__('Example: ')) ?>' + validatedPostCodeExamples.join('; ') + '. ',
                        '<?= $escaper->escapeJs(__('If you believe it is the right one you can ignore this notice.')) ?>'
                    ]);
                }

                return true;
            });


            hyva.formValidation.addRule('vat_id', (value, options, field, context) => {

                if(value.length > 1){
                    return new Promise(resolve => {
                        // show the user form validation is ongoing, maybe show a spinner
                        field.element.disabled = true;

                        fetch(url +'euvat/vatnumber/validation/'+ '?form_key=' + hyva.getFormKey()+'&vat_number='+value+'&handle=customer_account_create', {
                            method: "GET",
                            headers: {
                                "X-Requested-With": "XMLHttpRequest",
                            },
                        })
                        .then(response => response.json())
                        .then(result => {

                                if (result.vat_is_valid == true) {

                                    if(result.vat_request_country_code !== document.querySelector('#country').value){
                                        document.getElementById('buttonValidation').click();
                                        document.querySelector('#country-btw').value = result.vat_request_country_code;
                                    }

                                    let valtax = document.getElementById("vat_id").value;
                                    document.getElementById("taxvatdiv").innerHTML +=
                                    "<ul id='tax-mes' class='messages' aria-live='polite'><li data-msg-field='vat_id' style='color: green;'>"+result.request_message+"</li></ul>";
                                    document.getElementById("vat_id").disabled = false;
                                    document.getElementById("vat_id").value = valtax;
                                    resolve(true);
                                }else{
                                    resolve(result.request_message);
                                }
                        })
                        .finally(() => {
                            // indicate validation has finished, remove spinner if shown
                            field.element.disabled = false;
                        });
                    });
                }

                return true;
            });

        })
    </script>
</div>
