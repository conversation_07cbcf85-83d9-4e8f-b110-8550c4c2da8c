<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Customer\Block\Account\Resetpassword;
use Magento\Framework\Escaper;

/** @var Resetpassword $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

$heroIcons                = $viewModels->require(HeroiconsSolid::class);
$minimumPasswordLength    = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();
?>
<div class="container mb-12">
    <div class="w-full my-8">
        <form action="<?= $escaper->escapeUrl(
            $block->getUrl(
                '*/*/resetpasswordpost',
                [
                    '_query' => [
                        'id' => $block->getRpCustomerId(),
                        'token' => $block->getResetPasswordLinkToken()
                    ]
                ]
            )
        ) ?>"
              x-data="Object.assign(hyva.formValidation($el), {showPassword: false, showPasswordConfirm: false})"
              @submit="onSubmit"
              method="post"
            <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?>
              id="form-validate"
              class="form-password-forget mx-auto w-[30%] p-[30px] border border-blue-100 rounded-lg min-h-[300px] opacity-100 max-md:w-3/4 max-[480px]:w-full max-[480px]:p-5 max-lg:w-3/5 max-xl:w-2/ max-2xl:w-2/5">
            <fieldset class="fieldset w-full">
                <?= /** @noEscape  */ $block->getBlockHtml('formkey'); ?>

                <p class="text-2xl text-blue-prussian font-bold mb-4">
                    <?= $escaper->escapeHtml(__('Reset Password')) ?>
                </p>

                <div class="field field-reserved">
                    <div class="control relative my-2">
                        <input :type="showPassword ? 'text' : 'password'"
                               type="password"
                               class="form-input w-full" name="password"
                               id="password" required
                               minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>"
                               @input="onChange"
                               data-validate='{"password-strength": {"minCharacterSets": <?= (int)$passwordMinCharacterSets ?>}}'
                               autocomplete="off"
                               title="<?= $escaper->escapeHtmlAttr(__('New Password')) ?>"
                               placeholder="<?= $escaper->escapeHtmlAttr(__('New Password')) ?>"/>
                        <div x-on:click="showPassword = !showPassword"
                             class="cursor-pointer absolute inset-y-0 right-0 flex items-center pr-4"
                             :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPassword">
                                <?= /** @noEscape  */ $heroIcons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPassword">
                                <?= /** @noEscape  */ $heroIcons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </div>
                    </div>
                </div>

                <div class="field field-reserved">
                    <div class="control relative">
                        <input :type="showPasswordConfirm ? 'text' : 'password'"
                               type="password"
                               class="form-input w-full"
                               name="password_confirmation"
                               id="password-confirmation"
                               data-validate='{"equalTo": "password"}'
                               @input="onChange"
                               autocomplete="off"
                               title="<?= $escaper->escapeHtmlAttr(__('Confirm New Password')) ?>"
                               placeholder="<?= $escaper->escapeHtmlAttr(__('Confirm New Password')) ?>"/>
                        <div
                            x-on:click="showPasswordConfirm = !showPasswordConfirm"
                            class="cursor-pointer absolute inset-y-0 right-0 flex items-center pr-4"
                            :aria-label="showPasswordConfirm ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPasswordConfirm">
                                <?= /** @noEscape  */ $heroIcons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPasswordConfirm">
                                <?= /** @noEscape  */ $heroIcons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </div>
                    </div>
                </div>
            </fieldset>

            <div class="flex justify-between pb-2 items-center">
                <button type="submit"
                        class="w-full bg-blue-picton py-2 text-center rounded-lg">
                    <span><?= $escaper->escapeHtml(__('Set a New Password')) ?></span>
                </button>
            </div>
        </form>
    </div>
</div>
