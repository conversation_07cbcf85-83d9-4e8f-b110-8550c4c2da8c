<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\CustomerRegistration;
use Magento\Customer\Model\Account\Redirect;
use Magento\Framework\Escaper;

/** @var Customer $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var CustomerRegistration $customerRegistration */
$customerRegistration = $viewModels->require(CustomerRegistration::class);
?>

<div x-data="{ open: false }" class="relative flex items-center justify-center ml-1 sm:ml-3 lg:border-[1px] border-blue-cadet rounded-lg lg:px-4 md:py-2 h-10.25">
    <a
        href="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($block->getUrl('customer/account'))) ?>"
        id="customer-menu"
        class="flex text-white hover:text-blue-picton items-center"
        @click.prevent="open = true"
        :aria-expanded="open ? 'true' : 'false'"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Customer Menu')) ?>"
    >
        <?= $heroiconsSolid->renderHtml('person', 'text-white hover:text-blue-picton', 20, 20); ?>
        <?php if ($block->customerLoggedIn()): ?>
            <span class="ml-3 xl:block max-md:hidden text-sm font-semibold">
                <?= $escaper->escapeHtml($block->getLogin()->getCustomerName()) ?>
            </span>
            <span class="hidden xl:block">
                <?=  $heroicons->renderHtml('chevron-down', 'ml-2 py-1', 24, 24, ['aria-hidden' => 'true']); ?>
            </span>
        <?php else: ?>
            <span class="ml-3 hidden xl:block text-sm font-semibold">
                <?= $escaper->escapeHtml(__('Sign In')) ?>
            </span>
        <?php endif; ?> 
    </a>
    <nav x-data="initCompareHeader()" 
        @private-content-loaded.window="receiveCompareData($event.detail.data)"
        class="absolute right-0 top-full z-20 py-2 mt-2 px-1 overflow-auto origin-top-right rounded-sm
        shadow-lg sm:w-48 lg:mt-3 bg-container-lighter"
            x-cloak x-show="open"
            @click.outside="open = false"
            aria-labelledby="customer-menu"
    >
        <?php if ($block->customerLoggedIn()): ?>
            <?= $block->getChildHtml('header.customer.logged.in.links') ?>
        <?php else: ?>
            <?= $block->getChildHtml('header.customer.logged.out.links') ?>
        <?php endif; ?>
    </nav>
</div>

<script>
    function initHeader () {
        return {
            searchOpen: true,
            cart: {},
            getData(data) {
                if (data.cart) { this.cart = data.cart }
            }
        }
    }
    function initCompareHeader() {
        return {
            compareProducts: null,
            itemCount: 0,
            receiveCompareData(data) {
                if (data['compare-products']) {
                    this.compareProducts = data['compare-products'];
                    this.itemCount = this.compareProducts.count;
                }
            }
        }
    }
</script>