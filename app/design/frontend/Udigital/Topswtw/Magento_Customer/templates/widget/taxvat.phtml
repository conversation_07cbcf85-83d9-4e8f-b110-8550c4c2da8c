<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Customer\Block\Widget\Taxvat;
use Magento\Customer\Helper\Address as AddressHelper;
use Magento\Framework\Escaper;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var Taxvat $block */
?>
<div class="field field-reserved taxvat <?= $block->isRequired() ? 'required' : ''; ?>">
    <label class="label  fontSize-3.5 text-blue-prussian font-public-sans" for="<?= $escaper->escapeHtmlAttr(__('BTW nummer')) ?>"><span><?= $escaper->escapeHtml(__('BTW nummer')) ?></span><asd/label>
    <div class="control">
        <input type="text"
               id="<?= $escaper->escapeHtmlAttr($block->getFieldId('taxvat')) ?>"
               name="<?= $escaper->escapeHtmlAttr($block->getFieldName('taxvat')) ?>"
               value="<?= $escaper->escapeHtmlAttr($block->getTaxvat()) ?>"
               title="<?= $escaper->escapeHtmlAttr($block->getStoreLabel('taxvat')) ?>"
               class="form-input focus:ring-green-500  max-md:text-xs <?= $escaper->escapeHtmlAttr($block->getAttributeValidationClass('taxvat')) ?>"
            <?= $block->isRequired() ? ' required' : '' ?>
        >
    </div>
</div>
