<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Customer\Block\Widget\Telephone;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Telephone $block */
?>

<div class="field field-reserved telephone <?= $block->isRequired() ? 'required' : '' ?>">
    <label for="telephone" class="label  fontSize-3.5 text-blue-prussian font-public-sans">
        <span class="max-md:text-xs"><?= $escaper->escapeHtml(__('Phone Number')) ?></span>
    </label>
    <div class="control">
        <input type="tel"
               name="telephone"
               id="telephone"
               <?php if ($block->isRequired()): ?>required<?php endif; ?>
               @input.debounce="onChange"
               data-validate='{"telephone": true}'
               value="<?= $escaper->escapeHtmlAttr($block->getTelephone()) ?>"
               title="<?= $escaper->escapeHtmlAttr(__('Phone Number')) ?>"
               class="form-input w-full  focus:ring-green-500  max-md:text-xs <?= $escaper->escapeHtmlAttr(
                   $block->getAttributeValidationClass('telephone')
               ) ?>"
        >
    </div>
</div>
