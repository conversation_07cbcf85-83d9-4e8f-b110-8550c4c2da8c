<?xml version="1.0"?>
<layout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_layout.xsd">
    <referenceContainer name="page.wrapper">
        <referenceContainer name="product.info.details.after.wrapper">
            <referenceContainer name="product.info.details.after.wrapper.columns">
                <referenceContainer name="product.info.details.after.wrapper.main">
                    <referenceBlock name="product.attributes.wrapper">
                        <referenceBlock name="product.attributes.exclude.pagebuilder">
                            <arguments>
                                <argument name="product_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                            </arguments>
                        </referenceBlock>
                    </referenceBlock>
                </referenceContainer>
            </referenceContainer>
        </referenceContainer>
    </referenceContainer>
</layout>
