<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Block\Product\ListProduct;
use Magento\Catalog\Block\Product\ReviewRendererInterface;

/** @var ListProduct $block */
/** @var ViewModelRegistry $viewModels */

$productViewModel    = $viewModels->require(ProductPage::class);
$productListItemViewModel = $viewModels->require(ProductListItem::class);

?>
<?php if ($block->getProductCollection() && $block->getProductCollection()->getSize()): ?>
    <?php
    $type = 'widget-product-carousel';
    $mode = 'grid';
    $image = 'new_products_content_widget_grid';
    $items = $block->getProductCollection()->getItems();
    $templateType = ReviewRendererInterface::SHORT_VIEW;
    $description = false;
    ?>
    <div class="glider-contain product-items <?= /* @noEscape */ $type ?>">
        <div data-role="glider-content"
             class="mx-auto grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 relative overflow-y-hidden overflow-x-scroll">
            <?php foreach ($items as $item): ?>
                <?= $productListItemViewModel->getItemHtml($item, $block, $mode, $templateType, $image, $description) ?>
            <?php endforeach ?>
        </div>
        <?= $block->getBlockHtml('pagebuilder.carousel.nav') ?>
    </div>
<?php endif ?>
