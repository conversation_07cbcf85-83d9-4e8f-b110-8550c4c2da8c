// @codingStandardsIgnoreFile
.icon-star{
    .lib-icon-font(
      @_icon-font-content:@icon-star,
      @_icon-font-size: 13px,
      @_icon-font-line-height: 13px,
      @_icon-font-margin: -4px -1px 0 0,
      @_icon-font-text-hide: true,
      @_icon-font-color: #F9B734

  );
    margin-right: 5px;
}
.add-cart{
  .lib-icon-font(
      @_icon-font-content:@icon-cart,
      @_icon-font-size: 20px,
      @_icon-font-line-height: 13px,
      @_icon-font-margin: -4px -1px 0 0,
      @_icon-font-text-hide: true,
      @_icon-font-color: white

  );
}
.price-container{
  float: left;
  margin-left: 10px;
  font-size: 13px;
}

.content-button-persen{
  border:0px solid;
  width:290px;
  height:auto;
  font-size: 16px;
  color: #A2AFBD;
  display:flex;
  border: 1px solid #D5EAF0;
  border-radius: 8px;
  margin-top:10px
}

.content-button{
  width:114px;
  color:#16365B;
  text-align: center;
  cursor: pointer;
  
}

.active-persen, .content-button:hover {
  background: #2EB6ED 0% 0% no-repeat padding-box;
  width:114px;
  color:#FFFFFF;
  text-align: center;
  border-radius: 6px;
  cursor: pointer;
}   