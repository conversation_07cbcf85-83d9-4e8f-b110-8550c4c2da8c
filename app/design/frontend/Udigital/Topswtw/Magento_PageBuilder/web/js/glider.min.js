/* @preserve
    _____ __ _     __                _
   / ___// /(_)___/ /___  ____      (_)___
  / (_ // // // _  // -_)/ __/_    / /(_-<
  \___//_//_/ \_,_/ \__//_/  (_)__/ //___/
                              |___/

  Version: 1.7.4
  Author: <PERSON> (pickykneee)
  Website: https://nickpiscitelli.com
  Documentation: http://nickpiscitelli.github.io/Glider.js
  License: MIT License
  Release Date: October 25th, 2018

*/
!function(e){"function"==typeof define&&define.amd?define(e):"object"==typeof exports?module.exports=e():e()}(function(){var a="undefined"!=typeof window?window:this,e=a.Glider=function(e,t){var i=this;if(e._glider)return e._glider;if(i.ele=e,i.ele.classList.add("glider"),(i.ele._glider=i).opt=Object.assign({},{slidesToScroll:1,slidesToShow:1,resizeLock:!0,duration:.5,easing:function(e,t,i,o,r){return o*(t/=r)*t+i}},t),i.animate_id=i.page=i.slide=0,i.arrows={},i._opt=i.opt,i.opt.skipTrack)i.track=i.ele.children[0];else for(i.track=document.createElement("div"),i.ele.appendChild(i.track);1!==i.ele.children.length;)i.track.appendChild(i.ele.children[0]);i.track.classList.add("glider-track"),i.init(),i.resize=i.init.bind(i,!0),i.event(i.ele,"add",{scroll:i.updateControls.bind(i)}),i.event(a,"add",{resize:i.resize})},t=e.prototype;return t.init=function(e,t){var i=this,o=0,r=0;i.slides=i.track.children,[].forEach.call(i.slides,function(e,t){e.classList.add("glider-slide"),e.setAttribute("data-gslide",t)}),i.containerWidth=i.ele.clientWidth;var s,l=i.settingsBreakpoint();t=t||l,"auto"!==i.opt.slidesToShow&&void 0===i.opt._autoSlide||(s=i.containerWidth/i.opt.itemWidth,i.opt._autoSlide=i.opt.slidesToShow=i.opt.exactWidth?s:Math.floor(s)),"auto"===i.opt.slidesToScroll&&(i.opt.slidesToScroll=Math.floor(i.opt.slidesToShow)),i.itemWidth=i.opt.exactWidth?i.opt.itemWidth:i.containerWidth/i.opt.slidesToShow,[].forEach.call(i.slides,function(e){e.style.height="auto",e.style.width=i.itemWidth+"px",o+=i.itemWidth,r=Math.max(e.offsetHeight,r)}),i.track.style.width=o+"px",i.trackWidth=o,i.isDrag=!1,i.preventClick=!1,i.opt.resizeLock&&i.scrollTo(i.slide*i.itemWidth,0),(l||t)&&(i.bindArrows(),i.buildDots(),i.bindDrag()),i.updateControls(),i.emit(e?"refresh":"loaded")},t.bindDrag=function(){var t=this;t.mouse=t.mouse||t.handleMouse.bind(t);var e=function(){t.mouseDown=void 0,t.ele.classList.remove("drag"),t.isDrag&&(t.preventClick=!0),t.isDrag=!1},e={mouseup:e,mouseleave:e,mousedown:function(e){e.preventDefault(),e.stopPropagation(),t.mouseDown=e.clientX,t.ele.classList.add("drag")},mousemove:t.mouse,click:function(e){t.preventClick&&(e.preventDefault(),e.stopPropagation()),t.preventClick=!1}};t.ele.classList.toggle("draggable",!0===t.opt.draggable),t.event(t.ele,"remove",e),t.opt.draggable&&t.event(t.ele,"add",e)},t.buildDots=function(){var e=this;if(e.opt.dots){if("string"==typeof e.opt.dots?e.dots=document.querySelector(e.opt.dots):e.dots=e.opt.dots,e.dots){e.dots.innerHTML="",e.dots.classList.add("glider-dots");for(var t=0;t<Math.ceil(e.slides.length/e.opt.slidesToShow);++t){var i=document.createElement("button");i.dataset.index=t,i.setAttribute("aria-label","Page "+(t+1)),i.className="glider-dot "+(t?"":"active"),e.event(i,"add",{click:e.scrollItem.bind(e,t,!0)}),e.dots.appendChild(i)}}}else e.dots&&(e.dots.innerHTML="")},t.bindArrows=function(){var i=this;i.opt.arrows?["prev","next"].forEach(function(e){var t=i.opt.arrows[e];t&&("string"==typeof t&&(t=document.querySelector(t)),t&&(t._func=t._func||i.scrollItem.bind(i,e),i.event(t,"remove",{click:t._func}),i.event(t,"add",{click:t._func}),i.arrows[e]=t))}):Object.keys(i.arrows).forEach(function(e){e=i.arrows[e];i.event(e,"remove",{click:e._func})})},t.updateControls=function(e){var n=this;e&&!n.opt.scrollPropagate&&e.stopPropagation();var t=n.containerWidth>=n.trackWidth;n.opt.rewind||(n.arrows.prev&&(n.arrows.prev.classList.toggle("disabled",n.ele.scrollLeft<=0||t),n.arrows.prev.classList.contains("disabled")?n.arrows.prev.setAttribute("aria-disabled",!0):n.arrows.prev.setAttribute("aria-disabled",!1)),n.arrows.next&&(n.arrows.next.classList.toggle("disabled",Math.ceil(n.ele.scrollLeft+n.containerWidth)>=Math.floor(n.trackWidth)||t),n.arrows.next.classList.contains("disabled")?n.arrows.next.setAttribute("aria-disabled",!0):n.arrows.next.setAttribute("aria-disabled",!1))),n.slide=Math.round(n.ele.scrollLeft/n.itemWidth),n.page=Math.round(n.ele.scrollLeft/n.containerWidth);var a=n.slide+Math.floor(Math.floor(n.opt.slidesToShow)/2),d=Math.floor(n.opt.slidesToShow)%2?0:a+1;1===Math.floor(n.opt.slidesToShow)&&(d=0),n.ele.scrollLeft+n.containerWidth>=Math.floor(n.trackWidth)&&(n.page=n.dots?n.dots.children.length-1:0),[].forEach.call(n.slides,function(e,t){var i=e.classList,o=i.contains("visible"),r=n.ele.scrollLeft,s=n.ele.scrollLeft+n.containerWidth,l=n.itemWidth*t,e=l+n.itemWidth;[].forEach.call(i,function(e){/^left|right/.test(e)&&i.remove(e)}),i.toggle("active",n.slide===t),a===t||d&&d===t?i.add("center"):(i.remove("center"),i.add([t<a?"left":"right",Math.abs(t-(!(t<a)&&d||a))].join("-")));s=Math.ceil(l)>=r&&Math.floor(e)<=s;i.toggle("visible",s),s!==o&&n.emit("slide-"+(s?"visible":"hidden"),{slide:t})}),n.dots&&[].forEach.call(n.dots.children,function(e,t){e.classList.toggle("active",n.page===t)}),e&&n.opt.scrollLock&&(clearTimeout(n.scrollLock),n.scrollLock=setTimeout(function(){clearTimeout(n.scrollLock),.02<Math.abs(n.ele.scrollLeft/n.itemWidth-n.slide)&&(n.mouseDown||n.trackWidth>n.containerWidth+n.ele.scrollLeft&&n.scrollItem(n.getCurrentSlide()))},n.opt.scrollLockDelay||250))},t.getCurrentSlide=function(){var e=this;return e.round(e.ele.scrollLeft/e.itemWidth)},t.scrollItem=function(e,t,i){i&&i.preventDefault();var o,r=this,s=e;return++r.animate_id,e=!0===t?(e*=r.containerWidth,Math.round(e/r.itemWidth)*r.itemWidth):("string"==typeof e&&(o="prev"===e,e=r.opt.slidesToScroll%1||r.opt.slidesToShow%1?r.getCurrentSlide():r.slide,o?e-=r.opt.slidesToScroll:e+=r.opt.slidesToScroll,r.opt.rewind&&(i=r.ele.scrollLeft,e=o&&!i?r.slides.length:!o&&i+r.containerWidth>=Math.floor(r.trackWidth)?0:e)),e=Math.max(Math.min(e,r.slides.length),0),r.slide=e,r.itemWidth*e),r.scrollTo(e,r.opt.duration*Math.abs(r.ele.scrollLeft-e),function(){r.updateControls(),r.emit("animated",{value:s,type:"string"==typeof s?"arrow":t?"dot":"slide"})}),!1},t.settingsBreakpoint=function(){var e=this,t=e._opt.responsive;if(t){t.sort(function(e,t){return t.breakpoint-e.breakpoint});for(var i=0;i<t.length;++i){var o=t[i];if(a.innerWidth>=o.breakpoint)return e.breakpoint!==o.breakpoint&&(e.opt=Object.assign({},e._opt,o.settings),e.breakpoint=o.breakpoint,!0)}}var r=0!==e.breakpoint;return e.opt=Object.assign({},e._opt),e.breakpoint=0,r},t.scrollTo=function(t,i,o){var r=this,s=(new Date).getTime(),l=r.animate_id,n=function(){var e=(new Date).getTime()-s;r.ele.scrollLeft=r.ele.scrollLeft+(t-r.ele.scrollLeft)*r.opt.easing(0,e,0,1,i),e<i&&l===r.animate_id?a.requestAnimationFrame(n):(r.ele.scrollLeft=t,o&&o.call(r))};a.requestAnimationFrame(n)},t.removeItem=function(e){var t=this;t.slides.length&&(t.track.removeChild(t.slides[e]),t.refresh(!0),t.emit("remove"))},t.addItem=function(e){this.track.appendChild(e),this.refresh(!0),this.emit("add")},t.handleMouse=function(e){var t=this;t.mouseDown&&(t.isDrag=!0,t.ele.scrollLeft+=(t.mouseDown-e.clientX)*(t.opt.dragVelocity||3.3),t.mouseDown=e.clientX)},t.round=function(e){var t=1/(this.opt.slidesToScroll%1||1);return Math.round(e*t)/t},t.refresh=function(e){this.init(!0,e)},t.setOption=function(t,e){var i=this;i.breakpoint&&!e?i._opt.responsive.forEach(function(e){e.breakpoint===i.breakpoint&&(e.settings=Object.assign({},e.settings,t))}):i._opt=Object.assign({},i._opt,t),i.breakpoint=0,i.settingsBreakpoint()},t.destroy=function(){function e(t){t.removeAttribute("style"),[].forEach.call(t.classList,function(e){/^glider/.test(e)&&t.classList.remove(e)})}var t=this,i=t.ele.cloneNode(!0);i.children[0].outerHTML=i.children[0].innerHTML,e(i),[].forEach.call(i.getElementsByTagName("*"),e),t.ele.parentNode.replaceChild(i,t.ele),t.event(a,"remove",{resize:t.resize}),t.emit("destroy")},t.emit=function(e,t){t=new a.CustomEvent("glider-"+e,{bubbles:!this.opt.eventPropagate,detail:t});this.ele.dispatchEvent(t)},t.event=function(e,t,i){var o=e[t+"EventListener"].bind(e);Object.keys(i).forEach(function(e){o(e,i[e])})},e});
