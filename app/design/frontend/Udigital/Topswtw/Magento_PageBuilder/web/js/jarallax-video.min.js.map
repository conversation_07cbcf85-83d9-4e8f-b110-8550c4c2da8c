{"version": 3, "sources": ["jarallax-video.js"], "names": ["modules", "installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "callback", "document", "readyState", "attachEvent", "addEventListener", "global", "win", "window", "self", "this", "_typeof", "obj", "iterator", "constructor", "g", "Function", "e", "__webpack_exports__", "video_worker__WEBPACK_IMPORTED_MODULE_0__", "global__WEBPACK_IMPORTED_MODULE_1__", "global__WEBPACK_IMPORTED_MODULE_1___default", "lite_ready__WEBPACK_IMPORTED_MODULE_2__", "lite_ready__WEBPACK_IMPORTED_MODULE_2___default", "_jarallax_video_esm__WEBPACK_IMPORTED_MODULE_3__", "a", "VideoWorker", "jarall<PERSON>", "querySelectorAll", "global__WEBPACK_IMPORTED_MODULE_0__", "global__WEBPACK_IMPORTED_MODULE_0___default", "_defineProperties", "target", "props", "length", "descriptor", "configurable", "writable", "Deferred", "doneCallbacks", "failCallbacks", "execute", "list", "args", "Array", "slice", "apply", "resolve", "_len", "arguments", "_key", "reject", "_len2", "_key2", "done", "push", "fail", "ID", "YoutubeAPIadded", "VimeoAPIadded", "loadingYoutubePlayer", "loadingVimeoPlayer", "loadingYoutubeDefer", "loadingVimeoDefer", "url", "options", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_classCallCheck", "options_default", "autoplay", "loop", "mute", "volume", "showContols", "startTime", "endTime", "extend", "videoID", "parseURL", "loadAPI", "init", "protoProps", "staticProps", "_len3", "_key3", "out", "keys", "for<PERSON>ach", "match", "videoFormats", "result", "ready", "Youtube", "Vimeo", "Local", "split", "val", "type", "userEventsList", "_this", "_this2", "_len4", "_key4", "start", "player", "playVideo", "seekTo", "YT", "PlayerState", "PLAYING", "getPlayerState", "setCurrentTime", "getPaused", "then", "paused", "play", "currentTime", "pauseVideo", "pause", "setVolume", "$video", "muted", "unMute", "undefined", "getVolume", "isMuted", "availableSizes", "step", "tempImg", "request", "videoImage", "Image", "onload", "naturalWidth", "width", "concat", "src", "XMLHttpRequest", "open", "onreadystatechange", "response", "status", "JSON", "parse", "responseText", "thumbnail_large", "send", "getVideo", "onAPIready", "hiddenDiv", "ytStarted", "ytProgressInterval", "firstInit", "div", "playerOptionsString", "vmStarted", "locStarted", "createElement", "style", "display", "playerOptions", "host", "videoId", "playerVars", "autohide", "rel", "playsinline", "iv_load_policy", "modestbranding", "controls", "showinfo", "disablekb", "events", "onReady", "fire", "getDuration", "setInterval", "onStateChange", "data", "ENDED", "PAUSED", "getCurrentTime", "clearInterval", "onError", "setAttribute", "playerID", "append<PERSON><PERSON><PERSON>", "body", "Player", "getElementById", "videoWidth", "parseInt", "getAttribute", "videoHeight", "dnt", "id", "autopause", "transparent", "badge", "byline", "portrait", "title", "background", "encodeURIComponent", "getVideoWidth", "getVideoHeight", "height", "on", "seconds", "element", "source", "tag", "head", "getElementsByTagName", "vimeoInterval", "loaded", "onYouTubeIframeAPIReady", "jarallaxVideo", "Jarallax", "defOnScroll", "defCoverImage", "defInitImg", "defCanInitParallax", "defDest<PERSON>", "onScroll", "isVideoInserted", "video", "videoLazyLoading", "isElementInViewport", "disable<PERSON><PERSON><PERSON>", "$parent", "parentNode", "css", "position", "image", "top", "left", "right", "bottom", "max<PERSON><PERSON><PERSON>", "maxHeight", "pointerEvents", "transformStyle", "backfaceVisibility", "<PERSON><PERSON><PERSON><PERSON>", "margin", "zIndex", "$item", "tagName", "$container", "<PERSON><PERSON><PERSON><PERSON>", "coverImage", "h", "w", "ml", "mt", "imageData", "node", "nodeName", "container", "marginTop", "marginLeft", "initImg", "defaultResult", "videoSrc", "defaultInitImgResult", "canInitParallax", "videoLoop", "videoStartTime", "videoEndTime", "videoVolume", "resetDefaultImage", "$default_item", "clipContainer", "<PERSON><PERSON><PERSON><PERSON>", "disableParallax", "speed", "oldOnScroll", "videoPlayOnlyVisible", "videoError", "videoEnded", "isVisible", "getImageURL", "bgImage", "curStyle", "background-image", "background-position", "background-size", "destroy"], "mappings": ";;;;;IAAA,SAAAA,GAEA,IAAAC,EAAA,GAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAC,QAGA,IAAAC,EAAAJ,EAAAE,GAAA,CACAG,EAAAH,EACAI,GAAA,EACAH,QAAA,IAUA,OANAJ,EAAAG,GAAAK,KAAAH,EAAAD,QAAAC,EAAAA,EAAAD,QAAAF,GAGAG,EAAAE,GAAA,EAGAF,EAAAD,QAKAF,EAAAO,EAAAT,EAGAE,EAAAQ,EAAAT,EAGAC,EAAAS,EAAA,SAAAP,EAAAQ,EAAAC,GACAX,EAAAY,EAAAV,EAAAQ,IACAG,OAAAC,eAAAZ,EAAAQ,EAAA,CAAAK,YAAA,EAAAC,IAAAL,KAKAX,EAAAiB,EAAA,SAAAf,GACA,oBAAAgB,QAAAA,OAAAC,aACAN,OAAAC,eAAAZ,EAAAgB,OAAAC,YAAA,CAAAC,MAAA,WAEAP,OAAAC,eAAAZ,EAAA,aAAA,CAAAkB,OAAA,KAQApB,EAAAqB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAApB,EAAAoB,IACA,EAAAE,EAAA,OAAAF,EACA,GAAA,EAAAE,GAAA,iBAAAF,GAAAA,GAAAA,EAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAzB,EAAAiB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,UAAA,CAAAT,YAAA,EAAAK,MAAAA,IACA,EAAAE,GAAA,iBAAAF,EAAA,IAAA,IAAAM,KAAAN,EAAApB,EAAAS,EAAAe,EAAAE,EAAA,SAAAA,GAAA,OAAAN,EAAAM,IAAAC,KAAA,KAAAD,IACA,OAAAF,GAIAxB,EAAA4B,EAAA,SAAAzB,GACA,IAAAQ,EAAAR,GAAAA,EAAAoB,WACA,WAAA,OAAApB,EAAA,SACA,WAAA,OAAAA,GAEA,OADAH,EAAAS,EAAAE,EAAA,IAAAA,GACAA,GAIAX,EAAAY,EAAA,SAAAiB,EAAAC,GAAA,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGA9B,EAAAiC,EAAA,GAIAjC,EAAAA,EAAAkC,EAAA,GAnFA,CAsFA,CACA,CACA,CAEA,SAAA/B,EAAAD,GAEAC,EAAAD,QAAA,SAAAiC,GACA,aAAAC,SAAAC,YAAA,gBAAAD,SAAAC,WAEAF,EAAA7B,OACA8B,SAAAE,YAEAF,SAAAE,YAAA,qBAAA,WACA,gBAAAF,SAAAC,YAAAF,EAAA7B,SAEA8B,SAAAG,kBAEAH,SAAAG,iBAAA,mBAAAJ,KAMA,SAAAhC,EAAAD,EAAAF,IAEA,SAAAwC,GAAA,IAGAC,EADA,oBAAAC,OACAA,YACA,IAAAF,EACAA,EACA,oBAAAG,KACAA,KAEA,GAGAxC,EAAAD,QAAAuC,IACAnC,KAAAsC,KAAA5C,EAAA,KAIA,SAAAG,EAAAD,GAEA,SAAA2C,EAAAC,GAAA,OAAAD,EAAA,mBAAA3B,QAAA,iBAAAA,OAAA6B,SAAA,SAAAD,GAAA,cAAAA,GAAA,SAAAA,GAAA,OAAAA,GAAA,mBAAA5B,QAAA4B,EAAAE,cAAA9B,QAAA4B,IAAA5B,OAAAa,UAAA,gBAAAe,IAAAA,GAEA,IAEAG,EAAA,WACA,OAAAL,KADA,GAIA,IAEAK,EAAAA,GAAA,IAAAC,SAAA,cAAA,GACA,MAAAC,GAEA,YAAA,oBAAAT,OAAA,YAAAG,EAAAH,WAAAO,EAAAP,QAMAvC,EAAAD,QAAA+C,GAGA,CAEA,SAAA9C,EAAAD,EAAAF,GAEAG,EAAAD,QAAAF,EAAA,IAKA,SAAAG,EAAAiD,EAAApD,gBAGAA,EAAAiB,EAAAmC,GACA,IAAAC,EAAArD,EAAA,GACAsD,EAAAtD,EAAA,GACAuD,EAAAvD,EAAA4B,EAAA0B,GACAE,EAAAxD,EAAA,GACAyD,EAAAzD,EAAA4B,EAAA4B,GACAE,EAAA1D,EAAA,GAMAuD,EAAAI,EAAAC,YAAAL,EAAAI,EAAAC,aAAAP,EAAA,QACAxC,OAAA6C,EAAA,QAAA7C,GAEA4C,GAAAA,CAAA,gBACA,IAAAF,EAAAI,EAAAE,UACAN,EAAAI,EAAAE,SAAAzB,SAAA0B,iBAAA,6BAMA,SAAA3D,EAAAiD,EAAApD,gBAGAA,EAAAiB,EAAAmC,GACApD,EAAAS,EAAA2C,EAAA,UAAA,WAAA,OAAAQ,IACA,IAAAG,EAAA/D,EAAA,GACAgE,EAAAhE,EAAA4B,EAAAmC,GACA,SAAAlB,EAAAC,GAAA,OAAAD,EAAA,mBAAA3B,QAAA,iBAAAA,OAAA6B,SAAA,SAAAD,GAAA,cAAAA,GAAA,SAAAA,GAAA,OAAAA,GAAA,mBAAA5B,QAAA4B,EAAAE,cAAA9B,QAAA4B,IAAA5B,OAAAa,UAAA,gBAAAe,IAAAA,GAIA,SAAAmB,EAAAC,EAAAC,GAAA,IAAA,IAAA/D,EAAA,EAAAA,EAAA+D,EAAAC,OAAAhE,IAAA,CAAA,IAAAiE,EAAAF,EAAA/D,GAAAiE,EAAAtD,WAAAsD,EAAAtD,aAAA,EAAAsD,EAAAC,cAAA,EAAA,UAAAD,IAAAA,EAAAE,UAAA,GAAA1D,OAAAC,eAAAoD,EAAAG,EAAA3C,IAAA2C,IAOA,SAAAG,IACA5B,KAAA6B,cAAA,GACA7B,KAAA8B,cAAA,GAGAF,EAAAzC,UAAA,CACA4C,QAAA,SAAAC,EAAAC,GACA,IAAAzE,EAAAwE,EAAAR,OAGA,IAFAS,EAAAC,MAAA/C,UAAAgD,MAAAzE,KAAAuE,GAEAzE,GAEAwE,IADAxE,GACA4E,MAAA,KAAAH,IAGAI,QAAA,WACA,IAAA,IAAAC,EAAAC,UAAAf,OAAAS,EAAA,IAAAC,MAAAI,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IACAP,EAAAO,GAAAD,UAAAC,GAGAxC,KAAA+B,QAAA/B,KAAA6B,cAAAI,IAEAQ,OAAA,WACA,IAAA,IAAAC,EAAAH,UAAAf,OAAAS,EAAA,IAAAC,MAAAQ,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IACAV,EAAAU,GAAAJ,UAAAI,GAGA3C,KAAA+B,QAAA/B,KAAA8B,cAAAG,IAEAW,KAAA,SAAArD,GACAS,KAAA6B,cAAAgB,KAAAtD,IAEAuD,KAAA,SAAAvD,GACAS,KAAA8B,cAAAe,KAAAtD,KAGA,IAAAwD,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,IAAAxB,EACAyB,EAAA,IAAAzB,EAEAZ,EAAA,WACA,SAAAA,EAAAsC,EAAAC,IAtDA,SAAAC,EAAAC,GAAA,KAAAD,aAAAC,GAAA,MAAA,IAAAC,UAAA,qCAuDAC,CAAA3D,KAAAgB,GAEA,IAAAjB,EAAAC,KACAD,EAAAuD,IAAAA,EACAvD,EAAA6D,gBAAA,CACAC,UAAA,EACAC,MAAA,EACAC,MAAA,EACAC,OAAA,IACAC,aAAA,EAEAC,UAAA,EACAC,QAAA,GAEApE,EAAAwD,QAAAxD,EAAAqE,OAAA,GAAArE,EAAA6D,gBAAAL,GAEAxD,EAAAsE,QAAAtE,EAAAuE,SAAAhB,GAEAvD,EAAAsE,UACAtE,EAAAgD,GAAAA,EACAA,GAAA,EACAhD,EAAAwE,UACAxE,EAAAyE,QAzEA,IAAAf,EAAAgB,EAAAC,EAi3BA,OAj3BAjB,EA+EAzC,GA/EAyD,EA+EA,CAAA,CACA3F,IAAA,SACAN,MAAA,WACA,IAAA,IAAAmG,EAAApC,UAAAf,OAAAS,EAAA,IAAAC,MAAAyC,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IACA3C,EAAA2C,GAAArC,UAAAqC,GAGA,IAAAC,EAAA5C,EAAA,IAAA,GAUA,OATAhE,OAAA6G,KAAA7C,GAAA8C,QAAA,SAAAvH,GACAyE,EAAAzE,IAIAS,OAAA6G,KAAA7C,EAAAzE,IAAAuH,QAAA,SAAAjG,GACA+F,EAAA/F,GAAAmD,EAAAzE,GAAAsB,OAGA+F,IAEA,CACA/F,IAAA,WACAN,MAAA,SAAA8E,GAoCA,IA/BA0B,EAQAA,EAOAC,EACAC,EACAC,EAcAC,MA/BAJ,EA+BA1B,EA/BA0B,MADA,iEAEA,KAAAA,EAAA,GAAAxD,SAAAwD,EAAA,GA+BAK,MAxBAL,EAwBA1B,EAxBA0B,MADA,kJAEAA,EAAA,KAAAA,EAAA,GAwBAM,GAlBAL,EAkBA3B,EAlBAiC,MAAA,iCACAL,EAAA,GACAC,EAAA,EACAF,EAAAF,QAAA,SAAAS,GAEA,IAAAR,EAAAQ,EAAAR,MAAA,6BAEAA,GAAAA,EAAA,IAAAA,EAAA,KAEAE,EAAA,QAAAF,EAAA,GAAA,MAAAA,EAAA,IAAAA,EAAA,GACAG,EAAA,OAGAA,GAAAD,GAOA,OAAAE,GACApF,KAAAyF,KAAA,UACAL,GAGAC,GACArF,KAAAyF,KAAA,QACAJ,KAGAC,IACAtF,KAAAyF,KAAA,QACAH,KAKA,CACAxG,IAAA,UACAN,MAAA,WACA,QAAAwB,KAAAqE,UAGA,CACAvF,IAAA,KACAN,MAAA,SAAAV,EAAAyB,GACAS,KAAA0F,eAAA1F,KAAA0F,gBAAA,IAEA1F,KAAA0F,eAAA5H,KAAAkC,KAAA0F,eAAA5H,GAAA,KAAA+E,KAAAtD,KAEA,CACAT,IAAA,MACAN,MAAA,SAAAV,EAAAyB,GACA,IAAAoG,EAAA3F,KAEAA,KAAA0F,gBAAA1F,KAAA0F,eAAA5H,KAIAyB,EAGAS,KAAA0F,eAAA5H,GAAAiH,QAAA,SAAAS,EAAA1G,GACA0G,IAAAjG,IACAoG,EAAAD,eAAA5H,GAAAgB,IAAA,YAJAkB,KAAA0F,eAAA5H,MASA,CACAgB,IAAA,OACAN,MAAA,SAAAV,GAGA,IAFA,IAAA8H,EAAA5F,KAEA6F,EAAAtD,UAAAf,OAAAS,EAAA,IAAAC,MAAA,EAAA2D,EAAAA,EAAA,EAAA,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IACA7D,EAAA6D,EAAA,GAAAvD,UAAAuD,GAGA9F,KAAA0F,qBAAA,IAAA1F,KAAA0F,eAAA5H,IACAkC,KAAA0F,eAAA5H,GAAAiH,QAAA,SAAAS,GAEAA,GACAA,EAAApD,MAAAwD,EAAA3D,OAKA,CACAnD,IAAA,OACAN,MAAA,SAAAuH,GACA,IAAAhG,EAAAC,KAEAD,EAAAiG,SAIA,YAAAjG,EAAA0F,MAAA1F,EAAAiG,OAAAC,iBACA,IAAAF,GACAhG,EAAAiG,OAAAE,OAAAH,GAAA,GAGA3E,EAAAL,EAAAoF,GAAAC,YAAAC,UAAAtG,EAAAiG,OAAAM,kBACAvG,EAAAiG,OAAAC,aAIA,UAAAlG,EAAA0F,YACA,IAAAM,GACAhG,EAAAiG,OAAAO,eAAAR,GAGAhG,EAAAiG,OAAAQ,YAAAC,KAAA,SAAAC,GACAA,GACA3G,EAAAiG,OAAAW,UAKA,UAAA5G,EAAA0F,YACA,IAAAM,IACAhG,EAAAiG,OAAAY,YAAAb,GAGAhG,EAAAiG,OAAAU,QACA3G,EAAAiG,OAAAW,WAIA,CACA7H,IAAA,QACAN,MAAA,WACA,IAAAuB,EAAAC,KAEAD,EAAAiG,SAIA,YAAAjG,EAAA0F,MAAA1F,EAAAiG,OAAAa,YACAzF,EAAAL,EAAAoF,GAAAC,YAAAC,UAAAtG,EAAAiG,OAAAM,kBACAvG,EAAAiG,OAAAa,aAIA,UAAA9G,EAAA0F,MACA1F,EAAAiG,OAAAQ,YAAAC,KAAA,SAAAC,GACAA,GACA3G,EAAAiG,OAAAc,UAKA,UAAA/G,EAAA0F,OACA1F,EAAAiG,OAAAU,QACA3G,EAAAiG,OAAAc,YAIA,CACAhI,IAAA,OACAN,MAAA,WACA,IAAAuB,EAAAC,KAEAD,EAAAiG,SAIA,YAAAjG,EAAA0F,MAAA1F,EAAAiG,OAAAjC,MACAhE,EAAAiG,OAAAjC,OAGA,UAAAhE,EAAA0F,MAAA1F,EAAAiG,OAAAe,WACAhH,EAAAiG,OAAAe,UAAA,GAGA,UAAAhH,EAAA0F,OACA1F,EAAAiH,OAAAC,OAAA,MAGA,CACAnI,IAAA,SACAN,MAAA,WACA,IAAAuB,EAAAC,KAEAD,EAAAiG,SAIA,YAAAjG,EAAA0F,MAAA1F,EAAAiG,OAAAjC,MACAhE,EAAAiG,OAAAkB,SAGA,UAAAnH,EAAA0F,MAAA1F,EAAAiG,OAAAe,WACAhH,EAAAiG,OAAAe,UAAAhH,EAAAwD,QAAAS,QAGA,UAAAjE,EAAA0F,OACA1F,EAAAiH,OAAAC,OAAA,MAGA,CACAnI,IAAA,YACAN,MAAA,SAAA,GACA,IAAAwF,EAAA,EAAAzB,UAAAf,aAAA2F,IADA,GAAA,EAEApH,EAAAC,KAEAD,EAAAiG,QAAAhC,IAIA,YAAAjE,EAAA0F,MAAA1F,EAAAiG,OAAAe,WACAhH,EAAAiG,OAAAe,UAAA/C,GAGA,UAAAjE,EAAA0F,MAAA1F,EAAAiG,OAAAe,WACAhH,EAAAiG,OAAAe,UAAA/C,GAGA,UAAAjE,EAAA0F,OACA1F,EAAAiH,OAAAhD,OAAAA,EAAA,QAGA,CACAlF,IAAA,YACAN,MAAA,SAAAe,GACA,IAAAQ,EAAAC,KAEAD,EAAAiG,QAKA,YAAAjG,EAAA0F,MAAA1F,EAAAiG,OAAAoB,WACA7H,EAAAQ,EAAAiG,OAAAoB,aAGA,UAAArH,EAAA0F,MAAA1F,EAAAiG,OAAAoB,WACArH,EAAAiG,OAAAoB,YAAAX,KAAA,SAAAzC,GACAzE,EAAAyE,KAIA,UAAAjE,EAAA0F,MACAlG,EAAA,IAAAQ,EAAAiH,OAAAhD,SAfAzE,GAAA,KAkBA,CACAT,IAAA,WACAN,MAAA,SAAAe,GACA,IAAAQ,EAAAC,KAEAD,EAAAiG,QAKA,YAAAjG,EAAA0F,MAAA1F,EAAAiG,OAAAqB,SACA9H,EAAAQ,EAAAiG,OAAAqB,WAGA,UAAAtH,EAAA0F,MAAA1F,EAAAiG,OAAAoB,WACArH,EAAAiG,OAAAoB,YAAAX,KAAA,SAAAzC,GACAzE,IAAAyE,KAIA,UAAAjE,EAAA0F,MACAlG,EAAAQ,EAAAiH,OAAAC,QAfA1H,EAAA,QAkBA,CACAT,IAAA,cACAN,MAAA,SAAAe,GACA,IAQA+H,EACAC,EACAC,EAmBAC,EA7BA1H,EAAAC,KAEAD,EAAA2H,WACAnI,EAAAQ,EAAA2H,aAIA,YAAA3H,EAAA0F,OACA6B,EAAA,CAAA,gBAAA,YAAA,YAAA,KACAC,EAAA,GACAC,EAAA,IAAAG,OAEAC,OAAA,WAEA,OAAA5H,KAAA6H,cAAA7H,KAAA8H,QAAAP,IAAAD,EAAA9F,OAAA,GAEAzB,EAAA2H,WAAA,8BAAAK,OAAAhI,EAAAsE,QAAA,KAAA0D,OAAAT,EAAAC,GAAA,QACAhI,EAAAQ,EAAA2H,cAGAH,GAAA,EACAvH,KAAAgI,IAAA,8BAAAD,OAAAhI,EAAAsE,QAAA,KAAA0D,OAAAT,EAAAC,GAAA,UAIAC,EAAAQ,IAAA,8BAAAD,OAAAhI,EAAAsE,QAAA,KAAA0D,OAAAT,EAAAC,GAAA,SAGA,UAAAxH,EAAA0F,QACAgC,EAAA,IAAAQ,gBACAC,KAAA,MAAA,kCAAAH,OAAAhI,EAAAsE,QAAA,UAAA,GAEAoD,EAAAU,mBAAA,WACA,IAGAC,EAHA,IAAApI,KAAAP,YACA,KAAAO,KAAAqI,QAAArI,KAAAqI,OAAA,MAEAD,EAAAE,KAAAC,MAAAvI,KAAAwI,cACAzI,EAAA2H,WAAAU,EAAA,GAAAK,gBACAlJ,EAAAQ,EAAA2H,cAMAD,EAAAiB,OACAjB,EAAA,SAIA,CACA3I,IAAA,YACAN,MAAA,SAAAe,GACAS,KAAA2I,SAAApJ,KAEA,CACAT,IAAA,WACAN,MAAA,SAAAe,GACA,IAAAQ,EAAAC,KAEAD,EAAAiH,OACAzH,EAAAQ,EAAAiH,QAKAjH,EAAA6I,WAAA,WACA,IAAAC,EA+BAC,EACAC,EA6EAC,EAGAC,EA2CAC,EAkCAC,EAmFAC,EA9QArJ,EAAAiH,UACA6B,EAAArJ,SAAA6J,cAAA,QACAC,MAAAC,QAAA,QAIA,YAAAxJ,EAAA0F,OACA1F,EAAAyJ,cAAA,CAEAC,KAAA,mCACAC,QAAA3J,EAAAsE,QACAsF,WAAA,CACAC,SAAA,EACAC,IAAA,EACAhG,SAAA,EAEAiG,YAAA,IAIA/J,EAAAwD,QAAAU,cACAlE,EAAAyJ,cAAAG,WAAAI,eAAA,EACAhK,EAAAyJ,cAAAG,WAAAK,eAAA,EACAjK,EAAAyJ,cAAAG,WAAAM,SAAA,EACAlK,EAAAyJ,cAAAG,WAAAO,SAAA,EACAnK,EAAAyJ,cAAAG,WAAAQ,UAAA,GAMApK,EAAAyJ,cAAAY,OAAA,CACAC,QAAA,SAAA9J,GAEAR,EAAAwD,QAAAQ,KACAxD,EAAAe,OAAAyC,OACAhE,EAAAwD,QAAAS,QACAzD,EAAAe,OAAAyF,UAAAhH,EAAAwD,QAAAS,QAIAjE,EAAAwD,QAAAM,UACA9D,EAAA4G,KAAA5G,EAAAwD,QAAAW,WAGAnE,EAAAuK,KAAA,QAAA/J,GAGAR,EAAAwD,QAAAO,OAAA/D,EAAAwD,QAAAY,UAEApE,EAAAwD,QAAAY,QAAApE,EAAAiG,OAAAuE,cADA,IAKAC,YAAA,WACAzK,EAAAqH,UAAA,SAAApD,GACAjE,EAAAwD,QAAAS,SAAAA,IACAjE,EAAAwD,QAAAS,OAAAA,EACAjE,EAAAuK,KAAA,eAAA/J,OAGA,MAEAkK,cAAA,SAAAlK,GAEAR,EAAAwD,QAAAO,MAAAvD,EAAAmK,OAAAtJ,EAAAL,EAAAoF,GAAAC,YAAAuE,OACA5K,EAAA4G,KAAA5G,EAAAwD,QAAAW,WAGA4E,GAAAvI,EAAAmK,OAAAtJ,EAAAL,EAAAoF,GAAAC,YAAAC,UACAyC,EAAA,EACA/I,EAAAuK,KAAA,UAAA/J,IAGAA,EAAAmK,OAAAtJ,EAAAL,EAAAoF,GAAAC,YAAAC,SACAtG,EAAAuK,KAAA,OAAA/J,GAGAA,EAAAmK,OAAAtJ,EAAAL,EAAAoF,GAAAC,YAAAwE,QACA7K,EAAAuK,KAAA,QAAA/J,GAGAA,EAAAmK,OAAAtJ,EAAAL,EAAAoF,GAAAC,YAAAuE,OACA5K,EAAAuK,KAAA,QAAA/J,GAIAA,EAAAmK,OAAAtJ,EAAAL,EAAAoF,GAAAC,YAAAC,QACA0C,EAAAyB,YAAA,WACAzK,EAAAuK,KAAA,aAAA/J,GAEAR,EAAAwD,QAAAY,SAAApE,EAAAiG,OAAA6E,kBAAA9K,EAAAwD,QAAAY,UACApE,EAAAwD,QAAAO,KACA/D,EAAA4G,KAAA5G,EAAAwD,QAAAW,WAEAnE,EAAA+G,UAGA,KAEAgE,cAAA/B,IAGAgC,QAAA,SAAAxK,GACAR,EAAAuK,KAAA,QAAA/J,MAGAyI,GAAAjJ,EAAAiH,WAGAiC,EAAAzJ,SAAA6J,cAAA,QACA2B,aAAA,KAAAjL,EAAAkL,UACApC,EAAAqC,YAAAjC,GACAzJ,SAAA2L,KAAAD,YAAArC,IAGA9I,EAAAiG,OAAAjG,EAAAiG,QAAA,IAAA5E,EAAAL,EAAAoF,GAAAiF,OAAArL,EAAAkL,SAAAlL,EAAAyJ,eAEAR,IACAjJ,EAAAiH,OAAAxH,SAAA6L,eAAAtL,EAAAkL,UAEAlL,EAAAuL,WAAAC,SAAAxL,EAAAiH,OAAAwE,aAAA,SAAA,KAAA,KACAzL,EAAA0L,YAAAF,SAAAxL,EAAAiH,OAAAwE,aAAA,UAAA,KAAA,MAKA,UAAAzL,EAAA0F,OACA1F,EAAAyJ,cAAA,CAEAkC,IAAA,EACAC,GAAA5L,EAAAsE,QACAuH,UAAA,EACAC,YAAA,EACAhI,SAAA9D,EAAAwD,QAAAM,SAAA,EAAA,EACAC,KAAA/D,EAAAwD,QAAAO,KAAA,EAAA,EACAmD,MAAAlH,EAAAwD,QAAAQ,KAAA,EAAA,GAGAhE,EAAAwD,QAAAS,SACAjE,EAAAyJ,cAAAxF,OAAAjE,EAAAwD,QAAAS,QAIAjE,EAAAwD,QAAAU,cACAlE,EAAAyJ,cAAAsC,MAAA,EACA/L,EAAAyJ,cAAAuC,OAAA,EACAhM,EAAAyJ,cAAAwC,SAAA,EACAjM,EAAAyJ,cAAAyC,MAAA,EACAlM,EAAAyJ,cAAA0C,WAAA,GAGAnM,EAAAiH,SACAkC,EAAA,GACAjL,OAAA6G,KAAA/E,EAAAyJ,eAAAzE,QAAA,SAAAjG,GACA,KAAAoK,IACAA,GAAA,KAGAA,GAAA,GAAAnB,OAAAjJ,EAAA,KAAAiJ,OAAAoE,mBAAApM,EAAAyJ,cAAA1K,OAIAiB,EAAAiH,OAAAxH,SAAA6J,cAAA,UACAtJ,EAAAiH,OAAAgE,aAAA,KAAAjL,EAAAkL,UACAlL,EAAAiH,OAAAgE,aAAA,MAAA,kCAAAjD,OAAAhI,EAAAsE,QAAA,KAAA0D,OAAAmB,IACAnJ,EAAAiH,OAAAgE,aAAA,cAAA,KACAjL,EAAAiH,OAAAgE,aAAA,qBAAA,IACAjL,EAAAiH,OAAAgE,aAAA,kBAAA,IACAnC,EAAAqC,YAAAnL,EAAAiH,QACAxH,SAAA2L,KAAAD,YAAArC,IAGA9I,EAAAiG,OAAAjG,EAAAiG,QAAA,IAAA5E,EAAAL,EAAAsE,MAAA+F,OAAArL,EAAAiH,OAAAjH,EAAAyJ,eAEAzJ,EAAAwD,QAAAW,WAAAnE,EAAAwD,QAAAM,UACA9D,EAAAiG,OAAAO,eAAAxG,EAAAwD,QAAAW,WAIAnE,EAAAiG,OAAAoG,gBAAA3F,KAAA,SAAAqB,GACA/H,EAAAuL,WAAAxD,GAAA,OAEA/H,EAAAiG,OAAAqG,iBAAA5F,KAAA,SAAA6F,GACAvM,EAAA0L,YAAAa,GAAA,MAIAvM,EAAAiG,OAAAuG,GAAA,aAAA,SAAAhM,GACA4I,IACApJ,EAAAuK,KAAA,UAAA/J,GACA4I,EAAA,GAGApJ,EAAAuK,KAAA,aAAA/J,GAEAR,EAAAwD,QAAAY,SACApE,EAAAwD,QAAAY,SAAA5D,EAAAiM,SAAAzM,EAAAwD,QAAAY,UACApE,EAAAwD,QAAAO,KACA/D,EAAA4G,KAAA5G,EAAAwD,QAAAW,WAEAnE,EAAA+G,WAKA/G,EAAAiG,OAAAuG,GAAA,OAAA,SAAAhM,GACAR,EAAAuK,KAAA,OAAA/J,GAEAR,EAAAwD,QAAAW,WAAA,IAAA3D,EAAAiM,SACAzM,EAAA4G,KAAA5G,EAAAwD,QAAAW,aAGAnE,EAAAiG,OAAAuG,GAAA,QAAA,SAAAhM,GACAR,EAAAuK,KAAA,QAAA/J,KAEAR,EAAAiG,OAAAuG,GAAA,QAAA,SAAAhM,GACAR,EAAAuK,KAAA,QAAA/J,KAEAR,EAAAiG,OAAAuG,GAAA,SAAA,SAAAhM,GACAR,EAAAuK,KAAA,QAAA/J,KAEAR,EAAAiG,OAAAuG,GAAA,eAAA,SAAAhM,GACAR,EAAAuK,KAAA,eAAA/J,KAEAR,EAAAiG,OAAAuG,GAAA,QAAA,SAAAhM,GACAR,EAAAuK,KAAA,QAAA/J,MAYA,UAAAR,EAAA0F,OACA1F,EAAAiH,SACAjH,EAAAiH,OAAAxH,SAAA6J,cAAA,SAEAtJ,EAAAwD,QAAAU,cACAlE,EAAAiH,OAAAiD,UAAA,GAIAlK,EAAAwD,QAAAQ,KACAhE,EAAAiH,OAAAC,OAAA,EACAlH,EAAAiH,OAAAhD,SACAjE,EAAAiH,OAAAhD,OAAAjE,EAAAwD,QAAAS,OAAA,KAIAjE,EAAAwD,QAAAO,OACA/D,EAAAiH,OAAAlD,MAAA,GAIA/D,EAAAiH,OAAAgE,aAAA,cAAA,IACAjL,EAAAiH,OAAAgE,aAAA,qBAAA,IACAjL,EAAAiH,OAAAgE,aAAA,KAAAjL,EAAAkL,UACApC,EAAAqC,YAAAnL,EAAAiH,QACAxH,SAAA2L,KAAAD,YAAArC,GACA5K,OAAA6G,KAAA/E,EAAAsE,SAAAU,QAAA,SAAAjG,GAjCA,IAAA2N,EAAAzE,EAAAvC,EACAiH,EADAD,EAkCA1M,EAAAiH,OAlCAgB,EAkCAjI,EAAAsE,QAAAvF,GAlCA2G,EAkCA,SAAAsC,OAAAjJ,IAjCA4N,EAAAlN,SAAA6J,cAAA,WACArB,IAAAA,EACA0E,EAAAjH,KAAAA,EACAgH,EAAAvB,YAAAwB,MAkCA3M,EAAAiG,OAAAjG,EAAAiG,QAAAjG,EAAAiH,OAEAjH,EAAAiG,OAAArG,iBAAA,UAAA,SAAAY,GACA6I,GACArJ,EAAAuK,KAAA,UAAA/J,GAGA6I,EAAA,IAEArJ,EAAAiG,OAAArG,iBAAA,aAAA,SAAAY,GACAR,EAAAuK,KAAA,aAAA/J,GAEAR,EAAAwD,QAAAY,SACApE,EAAAwD,QAAAY,SAAAnE,KAAA4G,aAAA7G,EAAAwD,QAAAY,UACApE,EAAAwD,QAAAO,KACA/D,EAAA4G,KAAA5G,EAAAwD,QAAAW,WAEAnE,EAAA+G,WAKA/G,EAAAiG,OAAArG,iBAAA,OAAA,SAAAY,GACAR,EAAAuK,KAAA,OAAA/J,KAEAR,EAAAiG,OAAArG,iBAAA,QAAA,SAAAY,GACAR,EAAAuK,KAAA,QAAA/J,KAEAR,EAAAiG,OAAArG,iBAAA,QAAA,SAAAY,GACAR,EAAAuK,KAAA,QAAA/J,KAEAR,EAAAiG,OAAArG,iBAAA,iBAAA,WAEAI,EAAAuL,WAAAtL,KAAAsL,YAAA,KACAvL,EAAA0L,YAAAzL,KAAAyL,aAAA,IACA1L,EAAAuK,KAAA,SAEAvK,EAAAwD,QAAAM,UACA9D,EAAA4G,KAAA5G,EAAAwD,QAAAW,aAGAnE,EAAAiG,OAAArG,iBAAA,eAAA,SAAAY,GACAR,EAAAqH,UAAA,SAAApD,GACAjE,EAAAwD,QAAAS,OAAAA,IAEAjE,EAAAuK,KAAA,eAAA/J,KAEAR,EAAAiG,OAAArG,iBAAA,QAAA,SAAAY,GACAR,EAAAuK,KAAA,QAAA/J,MAIAhB,EAAAQ,EAAAiH,YAGA,CACAlI,IAAA,OACAN,MAAA,WACAwB,KACAiL,SAAA,eAAAlD,OADA/H,KACA+C,MAEA,CACAjE,IAAA,UACAN,MAAA,WAGA,IAAAwE,IAAAC,EAAA,CAIA,IAuBA0J,EACAC,EAxBA5E,EAAA,GAQA,GANA,YARAhI,KAQAyF,MAAAzC,IACAA,EAAA,EACAgF,EAAA,sCAIA,UAdAhI,KAcAyF,OAAAxC,EAAA,CAGA,GAFAA,EAAA,OAEA,IAAA7B,EAAAL,EAAAsE,MACA,OAGA2C,EAAA,yCAGAA,IAKA2E,EAAAnN,SAAA6J,cAAA,UACAuD,EAAApN,SAAAqN,qBAAA,QAAA,GACAF,EAAA3E,IAAAA,EACA4E,EAAA1B,YAAAyB,GAEAA,EADAC,EAAA,SAGA,CACA9N,IAAA,aACAN,MAAA,SAAAe,GACA,IA0BAuN,EAxBA,YAFA9M,KAEAyF,YAEA,IAAArE,EAAAL,EAAAoF,IAAA,IAAA/E,EAAAL,EAAAoF,GAAA4G,QAAA7J,EASA,WAAAjD,EAAAmB,EAAAL,EAAAoF,KAAA,IAAA/E,EAAAL,EAAAoF,GAAA4G,OACAxN,IAEA6D,EAAAR,KAAA,WACArD,OAXA2D,EAAA,EAEApD,OAAAkN,wBAAA,WACAlN,OAAAkN,wBAAA,KACA5J,EAAAf,QAAA,QACA9C,OAYA,UAvBAS,KAuBAyF,YACA,IAAArE,EAAAL,EAAAsE,OAAAlC,OASA,IAAA/B,EAAAL,EAAAsE,MACA9F,IAEA8D,EAAAT,KAAA,WACArD,OAZA4D,EAAA,EACA2J,EAAAtC,YAAA,gBACA,IAAApJ,EAAAL,EAAAsE,QACAyF,cAAAgC,GACAzJ,EAAAhB,QAAA,QACA9C,MAEA,MAWA,UA3CAS,KA2CAyF,MACAlG,SA52BA8B,EAAAoC,EAAAtE,UAAAsF,GAAAC,GAAArD,EAAAoC,EAAAiB,GAi3BA1D,EAh0BA,IAu0BA,SAAAzD,EAAAiD,EAAApD,gBAGAA,EAAAiB,EAAAmC,GACApD,EAAAS,EAAA2C,EAAA,UAAA,WAAA,OAAAyM,IACA,IAAAxM,EAAArD,EAAA,GACAsD,EAAAtD,EAAA,GACAuD,EAAAvD,EAAA4B,EAAA0B,GAGA,SAAAuM,IACA,IAMAC,EAEAC,EA+CAC,EAsCAC,EAkBAC,EAmIAC,EAlPAtM,EAAA,EAAAsB,UAAAf,aAAA2F,IAAA5E,UAAA,GAAAA,UAAA,GAAA5B,EAAAI,EAAAE,cAEA,IAAAA,IAIAiM,EAAAjM,EAAAb,YAEA+M,EAAAD,EAAA/N,UAAAqO,SAEAN,EAAA/N,UAAAqO,SAAA,WACA,IAAAzN,EAAAC,KACAmN,EAAA/K,MAAArC,GACAA,EAAA0N,kBAAA1N,EAAA2N,OAAA3N,EAAAwD,QAAAoK,mBAAA5N,EAAA6N,qBAAA7N,EAAAwD,QAAAsK,iBAGA9N,EAAA0N,iBAAA,EACA1N,EAAA2N,MAAA/E,SAAA,SAAA+E,GACA,IAAAI,EAAAJ,EAAAK,WACAhO,EAAAiO,IAAAN,EAAA,CACAO,SAAAlO,EAAAmO,MAAAD,SACAE,IAAA,MACAC,KAAA,MACAC,MAAA,MACAC,OAAA,MACAxG,MAAA,OACAwE,OAAA,OACAiC,SAAA,OACAC,UAAA,OACAC,cAAA,OACAC,eAAA,cACAC,mBAAA,SACAC,WAAA,oBACAC,OAAA,EACAC,QAAA,IAEA/O,EAAAiH,OAAA0G,EAEA,UAAA3N,EAAA2N,MAAAjI,OACA1F,EAAAmO,MAAAlG,IACAjI,EAAAiH,OAAAgE,aAAA,SAAAjL,EAAAmO,MAAAlG,KACAjI,EAAAmO,MAAAa,OAAA,QAAAhP,EAAAmO,MAAAa,MAAAC,SAAAjP,EAAAmO,MAAAa,MAAA/G,KACAjI,EAAAiH,OAAAgE,aAAA,SAAAjL,EAAAmO,MAAAa,MAAA/G,MAKAjI,EAAAmO,MAAAe,WAAA/D,YAAAwC,GAEAI,EAAAC,WAAAmB,YAAApB,OAMAV,EAAAF,EAAA/N,UAAAgQ,WAEAjC,EAAA/N,UAAAgQ,WAAA,WACA,IAKAC,EACAC,EACAC,EACAC,EARAxP,EAAAC,KACAwP,EAAApC,EAAAhL,MAAArC,GACA0P,IAAA1P,EAAAmO,MAAAa,OAAAhP,EAAAmO,MAAAa,MAAAW,SA6BA,OA3BAF,GAAAzP,EAAA2N,OAAA+B,IAAA,WAAAA,GAAA,UAAAA,KAEAJ,GADAD,EAAAI,EAAAtB,MAAA5B,QACAvM,EAAAmO,MAAApG,MAAA/H,EAAAmO,MAAA5B,OACAgD,GAAAE,EAAAG,UAAA7H,MAAAuH,GAAA,EACAE,EAAAC,EAAAtB,MAAA0B,UAEAJ,EAAAG,UAAA7H,MAAAuH,IAEAD,GADAC,EAAAG,EAAAG,UAAA7H,OACA/H,EAAAmO,MAAA5B,OAAAvM,EAAAmO,MAAApG,MACAwH,EAAA,EACAC,IAAAC,EAAAtB,MAAA5B,OAAA8C,GAAA,GAIA,WAAAK,IACAL,GAAA,IACAG,GAAA,KAGAxP,EAAAiO,IAAAjO,EAAAiH,OAAA,CACAc,MAAA,GAAAC,OAAAsH,EAAA,MACAQ,WAAA,GAAA9H,OAAAuH,EAAA,MACAhD,OAAA,GAAAvE,OAAAqH,EAAA,MACAQ,UAAA,GAAA7H,OAAAwH,EAAA,SAIAC,GAIAnC,EAAAH,EAAA/N,UAAA2Q,QAEA5C,EAAA/N,UAAA2Q,QAAA,WACA,IAAA/P,EAAAC,KACA+P,EAAA1C,EAAAjL,MAAArC,GAMA,OAJAA,EAAAwD,QAAAyM,WACAjQ,EAAAwD,QAAAyM,SAAAjQ,EAAAgP,MAAAvD,aAAA,wBAAA,MAGAzL,EAAAwD,QAAAyM,UACAjQ,EAAAkQ,qBAAAF,GACA,GAGAA,GAGAzC,EAAAJ,EAAA/N,UAAA+Q,gBAEAhD,EAAA/N,UAAA+Q,gBAAA,WACA,IAAAnQ,EAAAC,KACA+P,EAAAzC,EAAAlL,MAAArC,GAEA,IAAAA,EAAAwD,QAAAyM,SACA,OAAAD,EAIA,IAAArC,EAAA,IAAAjN,EAAA,QAAAV,EAAAwD,QAAAyM,SAAA,CACAnM,UAAA,EACAC,KAAA/D,EAAAwD,QAAA4M,UACAlM,aAAA,EACAC,UAAAnE,EAAAwD,QAAA6M,gBAAA,EACAjM,QAAApE,EAAAwD,QAAA8M,cAAA,EACAtM,KAAAhE,EAAAwD,QAAA+M,YAAA,EAAA,EACAtM,OAAAjE,EAAAwD,QAAA+M,aAAA,IAGA,SAAAC,IACAxQ,EAAAmO,MAAAsC,gBACAzQ,EAAAmO,MAAAa,MAAAhP,EAAAmO,MAAAsC,cACAzQ,EAAAmO,MAAAa,MAAAzF,MAAAC,QAAA,QAEAxJ,EAAAoP,aACApP,EAAA0Q,gBACA1Q,EAAAyN,YAIA,GAAAE,EAAAgD,UAYA,GARA1Q,KAAAuD,QAAAoN,oBACAZ,GAAA,EACAhQ,EAAAmO,MAAAD,SAAA,WACAlO,EAAAwD,QAAAkC,KAAA,SACA1F,EAAAwD,QAAAqN,MAAA,GAIAb,GAoEA,GAhDArC,EAAAnB,GAAA,QAAA,WACA,IACAsE,EADA9Q,EAAAwD,QAAAuN,sBACAD,EAAA9Q,EAAAyN,SAEAzN,EAAAyN,SAAA,WACAqD,EAAAzO,MAAArC,GAEAA,EAAAgR,aAAAhR,EAAAwD,QAAA4M,YAAApQ,EAAAwD,QAAA4M,WAAApQ,EAAAiR,cACAjR,EAAAkR,YACAvD,EAAA/G,OAEA+G,EAAA5G,WAKA4G,EAAA/G,SAGA+G,EAAAnB,GAAA,UAAA,WACAxM,EAAAmO,MAAAsC,cAAAzQ,EAAAmO,MAAAa,MACAhP,EAAAmO,MAAAa,MAAAhP,EAAAiH,OAEAjH,EAAAmO,MAAApG,MAAA/H,EAAA2N,MAAApC,YAAA,KACAvL,EAAAmO,MAAA5B,OAAAvM,EAAA2N,MAAAjC,aAAA,IACA1L,EAAAoP,aACApP,EAAA0Q,gBACA1Q,EAAAyN,WAEAzN,EAAAmO,MAAAsC,gBACAzQ,EAAAmO,MAAAsC,cAAAlH,MAAAC,QAAA,UAGAmE,EAAAnB,GAAA,QAAA,WACAxM,EAAAiR,YAAA,EAEAjR,EAAAwD,QAAA4M,WAEAI,MAGA7C,EAAAnB,GAAA,QAAA,WACAxM,EAAAgR,YAAA,EAEAR,MAEAxQ,EAAA2N,MAAAA,GAEA3N,EAAAkQ,uBAEAlQ,EAAAmO,MAAAlG,IAAA,iFAEA,UAAA0F,EAAAjI,MAKA,OAJAiI,EAAAwD,YAAA,SAAA5N,GACAvD,EAAAmO,MAAAiD,QAAA,QAAApJ,OAAAzE,EAAA,MACAvD,EAAAyE,UAEA,OA5EAzE,EAAAkQ,sBACAvC,EAAAwD,YAAA,SAAA5N,GAEA,IAAA8N,EAAArR,EAAAgP,MAAAvD,aAAA,SAEA4F,GACArR,EAAAgP,MAAA/D,aAAA,gCAAAoG,GAIArR,EAAAiO,IAAAjO,EAAAgP,MAAA,CACAsC,mBAAA,QAAAtJ,OAAAzE,EAAA,MACAgO,sBAAA,SACAC,kBAAA,YAqEA,OAAAxB,GAIAxC,EAAAL,EAAA/N,UAAAqS,QAEAtE,EAAA/N,UAAAqS,QAAA,WACA,IAAAzR,EAAAC,KAEAD,EAAAmO,MAAAsC,gBACAzQ,EAAAmO,MAAAa,MAAAhP,EAAAmO,MAAAsC,qBACAzQ,EAAAmO,MAAAsC,eAGAjD,EAAAnL,MAAArC", "file": "jarallax-video.min.js", "sourcesContent": ["/*!\n * Name    : Video Background Extension for Jarallax\n * Version : 1.0.1\n * Author  : nK <https://nkdev.info>\n * GitHub  : https://github.com/nk-o/jarallax\n */\n/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 6);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */,\n/* 1 */,\n/* 2 */\n/***/ (function(module, exports) {\n\nmodule.exports = function (callback) {\n  if (document.readyState === 'complete' || document.readyState === 'interactive') {\n    // Already ready or interactive, execute callback\n    callback.call();\n  } else if (document.attachEvent) {\n    // Old browsers\n    document.attachEvent('onreadystatechange', function () {\n      if (document.readyState === 'interactive') callback.call();\n    });\n  } else if (document.addEventListener) {\n    // Modern browsers\n    document.addEventListener('DOMContentLoaded', callback);\n  }\n};\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/* WEBPACK VAR INJECTION */(function(global) {var win;\n\nif (typeof window !== \"undefined\") {\n  win = window;\n} else if (typeof global !== \"undefined\") {\n  win = global;\n} else if (typeof self !== \"undefined\") {\n  win = self;\n} else {\n  win = {};\n}\n\nmodule.exports = win;\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(4)))\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports) {\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nvar g; // This works in non-strict mode\n\ng = function () {\n  return this;\n}();\n\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) === \"object\") g = window;\n} // g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\n\nmodule.exports = g;\n\n/***/ }),\n/* 5 */,\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\nmodule.exports = __webpack_require__(7);\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var video_worker__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8);\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3);\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(global__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lite_ready__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2);\n/* harmony import */ var lite_ready__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lite_ready__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _jarallax_video_esm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9);\n\n\n\n // add video worker globally to fallback jarallax < 1.10 versions\n\nglobal__WEBPACK_IMPORTED_MODULE_1___default.a.VideoWorker = global__WEBPACK_IMPORTED_MODULE_1___default.a.VideoWorker || video_worker__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\nObject(_jarallax_video_esm__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(); // data-jarallax-video initialization\n\nlite_ready__WEBPACK_IMPORTED_MODULE_2___default()(function () {\n  if ('undefined' !== typeof global__WEBPACK_IMPORTED_MODULE_1___default.a.jarallax) {\n    global__WEBPACK_IMPORTED_MODULE_1___default.a.jarallax(document.querySelectorAll('[data-jarallax-video]'));\n  }\n});\n\n/***/ }),\n/* 8 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return VideoWorker; });\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(global__WEBPACK_IMPORTED_MODULE_0__);\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n // Deferred\n// thanks http://stackoverflow.com/questions/18096715/implement-deferred-object-without-using-jquery\n\nfunction Deferred() {\n  this.doneCallbacks = [];\n  this.failCallbacks = [];\n}\n\nDeferred.prototype = {\n  execute: function execute(list, args) {\n    var i = list.length;\n    args = Array.prototype.slice.call(args);\n\n    while (i) {\n      i -= 1;\n      list[i].apply(null, args);\n    }\n  },\n  resolve: function resolve() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    this.execute(this.doneCallbacks, args);\n  },\n  reject: function reject() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    this.execute(this.failCallbacks, args);\n  },\n  done: function done(callback) {\n    this.doneCallbacks.push(callback);\n  },\n  fail: function fail(callback) {\n    this.failCallbacks.push(callback);\n  }\n};\nvar ID = 0;\nvar YoutubeAPIadded = 0;\nvar VimeoAPIadded = 0;\nvar loadingYoutubePlayer = 0;\nvar loadingVimeoPlayer = 0;\nvar loadingYoutubeDefer = new Deferred();\nvar loadingVimeoDefer = new Deferred();\n\nvar VideoWorker = /*#__PURE__*/function () {\n  function VideoWorker(url, options) {\n    _classCallCheck(this, VideoWorker);\n\n    var self = this;\n    self.url = url;\n    self.options_default = {\n      autoplay: false,\n      loop: false,\n      mute: false,\n      volume: 100,\n      showContols: true,\n      // start / end video time in seconds\n      startTime: 0,\n      endTime: 0\n    };\n    self.options = self.extend({}, self.options_default, options); // check URL\n\n    self.videoID = self.parseURL(url); // init\n\n    if (self.videoID) {\n      self.ID = ID;\n      ID += 1;\n      self.loadAPI();\n      self.init();\n    }\n  } // Extend like jQuery.extend\n  // eslint-disable-next-line class-methods-use-this\n\n\n  _createClass(VideoWorker, [{\n    key: \"extend\",\n    value: function extend() {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n\n      var out = args[0] || {};\n      Object.keys(args).forEach(function (i) {\n        if (!args[i]) {\n          return;\n        }\n\n        Object.keys(args[i]).forEach(function (key) {\n          out[key] = args[i][key];\n        });\n      });\n      return out;\n    }\n  }, {\n    key: \"parseURL\",\n    value: function parseURL(url) {\n      // parse youtube ID\n      function getYoutubeID(ytUrl) {\n        // eslint-disable-next-line no-useless-escape\n        var regExp = /.*(?:youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=)([^#\\&\\?]*).*/;\n        var match = ytUrl.match(regExp);\n        return match && 11 === match[1].length ? match[1] : false;\n      } // parse vimeo ID\n\n\n      function getVimeoID(vmUrl) {\n        // eslint-disable-next-line no-useless-escape\n        var regExp = /https?:\\/\\/(?:www\\.|player\\.)?vimeo.com\\/(?:channels\\/(?:\\w+\\/)?|groups\\/([^\\/]*)\\/videos\\/|album\\/(\\d+)\\/video\\/|video\\/|)(\\d+)(?:$|\\/|\\?)/;\n        var match = vmUrl.match(regExp);\n        return match && match[3] ? match[3] : false;\n      } // parse local string\n\n\n      function getLocalVideos(locUrl) {\n        // eslint-disable-next-line no-useless-escape\n        var videoFormats = locUrl.split(/,(?=mp4\\:|webm\\:|ogv\\:|ogg\\:)/);\n        var result = {};\n        var ready = 0;\n        videoFormats.forEach(function (val) {\n          // eslint-disable-next-line no-useless-escape\n          var match = val.match(/^(mp4|webm|ogv|ogg)\\:(.*)/);\n\n          if (match && match[1] && match[2]) {\n            // eslint-disable-next-line prefer-destructuring\n            result['ogv' === match[1] ? 'ogg' : match[1]] = match[2];\n            ready = 1;\n          }\n        });\n        return ready ? result : false;\n      }\n\n      var Youtube = getYoutubeID(url);\n      var Vimeo = getVimeoID(url);\n      var Local = getLocalVideos(url);\n\n      if (Youtube) {\n        this.type = 'youtube';\n        return Youtube;\n      }\n\n      if (Vimeo) {\n        this.type = 'vimeo';\n        return Vimeo;\n      }\n\n      if (Local) {\n        this.type = 'local';\n        return Local;\n      }\n\n      return false;\n    }\n  }, {\n    key: \"isValid\",\n    value: function isValid() {\n      return !!this.videoID;\n    } // events\n\n  }, {\n    key: \"on\",\n    value: function on(name, callback) {\n      this.userEventsList = this.userEventsList || []; // add new callback in events list\n\n      (this.userEventsList[name] || (this.userEventsList[name] = [])).push(callback);\n    }\n  }, {\n    key: \"off\",\n    value: function off(name, callback) {\n      var _this = this;\n\n      if (!this.userEventsList || !this.userEventsList[name]) {\n        return;\n      }\n\n      if (!callback) {\n        delete this.userEventsList[name];\n      } else {\n        this.userEventsList[name].forEach(function (val, key) {\n          if (val === callback) {\n            _this.userEventsList[name][key] = false;\n          }\n        });\n      }\n    }\n  }, {\n    key: \"fire\",\n    value: function fire(name) {\n      var _this2 = this;\n\n      for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n        args[_key4 - 1] = arguments[_key4];\n      }\n\n      if (this.userEventsList && 'undefined' !== typeof this.userEventsList[name]) {\n        this.userEventsList[name].forEach(function (val) {\n          // call with all arguments\n          if (val) {\n            val.apply(_this2, args);\n          }\n        });\n      }\n    }\n  }, {\n    key: \"play\",\n    value: function play(start) {\n      var self = this;\n\n      if (!self.player) {\n        return;\n      }\n\n      if ('youtube' === self.type && self.player.playVideo) {\n        if ('undefined' !== typeof start) {\n          self.player.seekTo(start || 0);\n        }\n\n        if (global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.PlayerState.PLAYING !== self.player.getPlayerState()) {\n          self.player.playVideo();\n        }\n      }\n\n      if ('vimeo' === self.type) {\n        if ('undefined' !== typeof start) {\n          self.player.setCurrentTime(start);\n        }\n\n        self.player.getPaused().then(function (paused) {\n          if (paused) {\n            self.player.play();\n          }\n        });\n      }\n\n      if ('local' === self.type) {\n        if ('undefined' !== typeof start) {\n          self.player.currentTime = start;\n        }\n\n        if (self.player.paused) {\n          self.player.play();\n        }\n      }\n    }\n  }, {\n    key: \"pause\",\n    value: function pause() {\n      var self = this;\n\n      if (!self.player) {\n        return;\n      }\n\n      if ('youtube' === self.type && self.player.pauseVideo) {\n        if (global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.PlayerState.PLAYING === self.player.getPlayerState()) {\n          self.player.pauseVideo();\n        }\n      }\n\n      if ('vimeo' === self.type) {\n        self.player.getPaused().then(function (paused) {\n          if (!paused) {\n            self.player.pause();\n          }\n        });\n      }\n\n      if ('local' === self.type) {\n        if (!self.player.paused) {\n          self.player.pause();\n        }\n      }\n    }\n  }, {\n    key: \"mute\",\n    value: function mute() {\n      var self = this;\n\n      if (!self.player) {\n        return;\n      }\n\n      if ('youtube' === self.type && self.player.mute) {\n        self.player.mute();\n      }\n\n      if ('vimeo' === self.type && self.player.setVolume) {\n        self.player.setVolume(0);\n      }\n\n      if ('local' === self.type) {\n        self.$video.muted = true;\n      }\n    }\n  }, {\n    key: \"unmute\",\n    value: function unmute() {\n      var self = this;\n\n      if (!self.player) {\n        return;\n      }\n\n      if ('youtube' === self.type && self.player.mute) {\n        self.player.unMute();\n      }\n\n      if ('vimeo' === self.type && self.player.setVolume) {\n        self.player.setVolume(self.options.volume);\n      }\n\n      if ('local' === self.type) {\n        self.$video.muted = false;\n      }\n    }\n  }, {\n    key: \"setVolume\",\n    value: function setVolume() {\n      var volume = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      var self = this;\n\n      if (!self.player || !volume) {\n        return;\n      }\n\n      if ('youtube' === self.type && self.player.setVolume) {\n        self.player.setVolume(volume);\n      }\n\n      if ('vimeo' === self.type && self.player.setVolume) {\n        self.player.setVolume(volume);\n      }\n\n      if ('local' === self.type) {\n        self.$video.volume = volume / 100;\n      }\n    }\n  }, {\n    key: \"getVolume\",\n    value: function getVolume(callback) {\n      var self = this;\n\n      if (!self.player) {\n        callback(false);\n        return;\n      }\n\n      if ('youtube' === self.type && self.player.getVolume) {\n        callback(self.player.getVolume());\n      }\n\n      if ('vimeo' === self.type && self.player.getVolume) {\n        self.player.getVolume().then(function (volume) {\n          callback(volume);\n        });\n      }\n\n      if ('local' === self.type) {\n        callback(self.$video.volume * 100);\n      }\n    }\n  }, {\n    key: \"getMuted\",\n    value: function getMuted(callback) {\n      var self = this;\n\n      if (!self.player) {\n        callback(null);\n        return;\n      }\n\n      if ('youtube' === self.type && self.player.isMuted) {\n        callback(self.player.isMuted());\n      }\n\n      if ('vimeo' === self.type && self.player.getVolume) {\n        self.player.getVolume().then(function (volume) {\n          callback(!!volume);\n        });\n      }\n\n      if ('local' === self.type) {\n        callback(self.$video.muted);\n      }\n    }\n  }, {\n    key: \"getImageURL\",\n    value: function getImageURL(callback) {\n      var self = this;\n\n      if (self.videoImage) {\n        callback(self.videoImage);\n        return;\n      }\n\n      if ('youtube' === self.type) {\n        var availableSizes = ['maxresdefault', 'sddefault', 'hqdefault', '0'];\n        var step = 0;\n        var tempImg = new Image();\n\n        tempImg.onload = function () {\n          // if no thumbnail, youtube add their own image with width = 120px\n          if (120 !== (this.naturalWidth || this.width) || step === availableSizes.length - 1) {\n            // ok\n            self.videoImage = \"https://img.youtube.com/vi/\".concat(self.videoID, \"/\").concat(availableSizes[step], \".jpg\");\n            callback(self.videoImage);\n          } else {\n            // try another size\n            step += 1;\n            this.src = \"https://img.youtube.com/vi/\".concat(self.videoID, \"/\").concat(availableSizes[step], \".jpg\");\n          }\n        };\n\n        tempImg.src = \"https://img.youtube.com/vi/\".concat(self.videoID, \"/\").concat(availableSizes[step], \".jpg\");\n      }\n\n      if ('vimeo' === self.type) {\n        var request = new XMLHttpRequest();\n        request.open('GET', \"https://vimeo.com/api/v2/video/\".concat(self.videoID, \".json\"), true);\n\n        request.onreadystatechange = function () {\n          if (4 === this.readyState) {\n            if (200 <= this.status && 400 > this.status) {\n              // Success!\n              var response = JSON.parse(this.responseText);\n              self.videoImage = response[0].thumbnail_large;\n              callback(self.videoImage);\n            } else {// Error :(\n            }\n          }\n        };\n\n        request.send();\n        request = null;\n      }\n    } // fallback to the old version.\n\n  }, {\n    key: \"getIframe\",\n    value: function getIframe(callback) {\n      this.getVideo(callback);\n    }\n  }, {\n    key: \"getVideo\",\n    value: function getVideo(callback) {\n      var self = this; // return generated video block\n\n      if (self.$video) {\n        callback(self.$video);\n        return;\n      } // generate new video block\n\n\n      self.onAPIready(function () {\n        var hiddenDiv;\n\n        if (!self.$video) {\n          hiddenDiv = document.createElement('div');\n          hiddenDiv.style.display = 'none';\n        } // Youtube\n\n\n        if ('youtube' === self.type) {\n          self.playerOptions = {\n            // GDPR Compliance.\n            host: 'https://www.youtube-nocookie.com',\n            videoId: self.videoID,\n            playerVars: {\n              autohide: 1,\n              rel: 0,\n              autoplay: 0,\n              // autoplay enable on mobile devices\n              playsinline: 1\n            }\n          }; // hide controls\n\n          if (!self.options.showContols) {\n            self.playerOptions.playerVars.iv_load_policy = 3;\n            self.playerOptions.playerVars.modestbranding = 1;\n            self.playerOptions.playerVars.controls = 0;\n            self.playerOptions.playerVars.showinfo = 0;\n            self.playerOptions.playerVars.disablekb = 1;\n          } // events\n\n\n          var ytStarted;\n          var ytProgressInterval;\n          self.playerOptions.events = {\n            onReady: function onReady(e) {\n              // mute\n              if (self.options.mute) {\n                e.target.mute();\n              } else if (self.options.volume) {\n                e.target.setVolume(self.options.volume);\n              } // autoplay\n\n\n              if (self.options.autoplay) {\n                self.play(self.options.startTime);\n              }\n\n              self.fire('ready', e); // For seamless loops, set the endTime to 0.1 seconds less than the video's duration\n              // https://github.com/nk-o/video-worker/issues/2\n\n              if (self.options.loop && !self.options.endTime) {\n                var secondsOffset = 0.1;\n                self.options.endTime = self.player.getDuration() - secondsOffset;\n              } // volumechange\n\n\n              setInterval(function () {\n                self.getVolume(function (volume) {\n                  if (self.options.volume !== volume) {\n                    self.options.volume = volume;\n                    self.fire('volumechange', e);\n                  }\n                });\n              }, 150);\n            },\n            onStateChange: function onStateChange(e) {\n              // loop\n              if (self.options.loop && e.data === global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.PlayerState.ENDED) {\n                self.play(self.options.startTime);\n              }\n\n              if (!ytStarted && e.data === global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.PlayerState.PLAYING) {\n                ytStarted = 1;\n                self.fire('started', e);\n              }\n\n              if (e.data === global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.PlayerState.PLAYING) {\n                self.fire('play', e);\n              }\n\n              if (e.data === global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.PlayerState.PAUSED) {\n                self.fire('pause', e);\n              }\n\n              if (e.data === global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.PlayerState.ENDED) {\n                self.fire('ended', e);\n              } // progress check\n\n\n              if (e.data === global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.PlayerState.PLAYING) {\n                ytProgressInterval = setInterval(function () {\n                  self.fire('timeupdate', e); // check for end of video and play again or stop\n\n                  if (self.options.endTime && self.player.getCurrentTime() >= self.options.endTime) {\n                    if (self.options.loop) {\n                      self.play(self.options.startTime);\n                    } else {\n                      self.pause();\n                    }\n                  }\n                }, 150);\n              } else {\n                clearInterval(ytProgressInterval);\n              }\n            },\n            onError: function onError(e) {\n              self.fire('error', e);\n            }\n          };\n          var firstInit = !self.$video;\n\n          if (firstInit) {\n            var div = document.createElement('div');\n            div.setAttribute('id', self.playerID);\n            hiddenDiv.appendChild(div);\n            document.body.appendChild(hiddenDiv);\n          }\n\n          self.player = self.player || new global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.Player(self.playerID, self.playerOptions);\n\n          if (firstInit) {\n            self.$video = document.getElementById(self.playerID); // get video width and height\n\n            self.videoWidth = parseInt(self.$video.getAttribute('width'), 10) || 1280;\n            self.videoHeight = parseInt(self.$video.getAttribute('height'), 10) || 720;\n          }\n        } // Vimeo\n\n\n        if ('vimeo' === self.type) {\n          self.playerOptions = {\n            // GDPR Compliance.\n            dnt: 1,\n            id: self.videoID,\n            autopause: 0,\n            transparent: 0,\n            autoplay: self.options.autoplay ? 1 : 0,\n            loop: self.options.loop ? 1 : 0,\n            muted: self.options.mute ? 1 : 0\n          };\n\n          if (self.options.volume) {\n            self.playerOptions.volume = self.options.volume;\n          } // hide controls\n\n\n          if (!self.options.showContols) {\n            self.playerOptions.badge = 0;\n            self.playerOptions.byline = 0;\n            self.playerOptions.portrait = 0;\n            self.playerOptions.title = 0;\n            self.playerOptions.background = 1;\n          }\n\n          if (!self.$video) {\n            var playerOptionsString = '';\n            Object.keys(self.playerOptions).forEach(function (key) {\n              if ('' !== playerOptionsString) {\n                playerOptionsString += '&';\n              }\n\n              playerOptionsString += \"\".concat(key, \"=\").concat(encodeURIComponent(self.playerOptions[key]));\n            }); // we need to create iframe manually because when we create it using API\n            // js events won't triggers after iframe moved to another place\n\n            self.$video = document.createElement('iframe');\n            self.$video.setAttribute('id', self.playerID);\n            self.$video.setAttribute('src', \"https://player.vimeo.com/video/\".concat(self.videoID, \"?\").concat(playerOptionsString));\n            self.$video.setAttribute('frameborder', '0');\n            self.$video.setAttribute('mozallowfullscreen', '');\n            self.$video.setAttribute('allowfullscreen', '');\n            hiddenDiv.appendChild(self.$video);\n            document.body.appendChild(hiddenDiv);\n          }\n\n          self.player = self.player || new global__WEBPACK_IMPORTED_MODULE_0___default.a.Vimeo.Player(self.$video, self.playerOptions); // set current time for autoplay\n\n          if (self.options.startTime && self.options.autoplay) {\n            self.player.setCurrentTime(self.options.startTime);\n          } // get video width and height\n\n\n          self.player.getVideoWidth().then(function (width) {\n            self.videoWidth = width || 1280;\n          });\n          self.player.getVideoHeight().then(function (height) {\n            self.videoHeight = height || 720;\n          }); // events\n\n          var vmStarted;\n          self.player.on('timeupdate', function (e) {\n            if (!vmStarted) {\n              self.fire('started', e);\n              vmStarted = 1;\n            }\n\n            self.fire('timeupdate', e); // check for end of video and play again or stop\n\n            if (self.options.endTime) {\n              if (self.options.endTime && e.seconds >= self.options.endTime) {\n                if (self.options.loop) {\n                  self.play(self.options.startTime);\n                } else {\n                  self.pause();\n                }\n              }\n            }\n          });\n          self.player.on('play', function (e) {\n            self.fire('play', e); // check for the start time and start with it\n\n            if (self.options.startTime && 0 === e.seconds) {\n              self.play(self.options.startTime);\n            }\n          });\n          self.player.on('pause', function (e) {\n            self.fire('pause', e);\n          });\n          self.player.on('ended', function (e) {\n            self.fire('ended', e);\n          });\n          self.player.on('loaded', function (e) {\n            self.fire('ready', e);\n          });\n          self.player.on('volumechange', function (e) {\n            self.fire('volumechange', e);\n          });\n          self.player.on('error', function (e) {\n            self.fire('error', e);\n          });\n        } // Local\n\n\n        function addSourceToLocal(element, src, type) {\n          var source = document.createElement('source');\n          source.src = src;\n          source.type = type;\n          element.appendChild(source);\n        }\n\n        if ('local' === self.type) {\n          if (!self.$video) {\n            self.$video = document.createElement('video'); // show controls\n\n            if (self.options.showContols) {\n              self.$video.controls = true;\n            } // mute\n\n\n            if (self.options.mute) {\n              self.$video.muted = true;\n            } else if (self.$video.volume) {\n              self.$video.volume = self.options.volume / 100;\n            } // loop\n\n\n            if (self.options.loop) {\n              self.$video.loop = true;\n            } // autoplay enable on mobile devices\n\n\n            self.$video.setAttribute('playsinline', '');\n            self.$video.setAttribute('webkit-playsinline', '');\n            self.$video.setAttribute('id', self.playerID);\n            hiddenDiv.appendChild(self.$video);\n            document.body.appendChild(hiddenDiv);\n            Object.keys(self.videoID).forEach(function (key) {\n              addSourceToLocal(self.$video, self.videoID[key], \"video/\".concat(key));\n            });\n          }\n\n          self.player = self.player || self.$video;\n          var locStarted;\n          self.player.addEventListener('playing', function (e) {\n            if (!locStarted) {\n              self.fire('started', e);\n            }\n\n            locStarted = 1;\n          });\n          self.player.addEventListener('timeupdate', function (e) {\n            self.fire('timeupdate', e); // check for end of video and play again or stop\n\n            if (self.options.endTime) {\n              if (self.options.endTime && this.currentTime >= self.options.endTime) {\n                if (self.options.loop) {\n                  self.play(self.options.startTime);\n                } else {\n                  self.pause();\n                }\n              }\n            }\n          });\n          self.player.addEventListener('play', function (e) {\n            self.fire('play', e);\n          });\n          self.player.addEventListener('pause', function (e) {\n            self.fire('pause', e);\n          });\n          self.player.addEventListener('ended', function (e) {\n            self.fire('ended', e);\n          });\n          self.player.addEventListener('loadedmetadata', function () {\n            // get video width and height\n            self.videoWidth = this.videoWidth || 1280;\n            self.videoHeight = this.videoHeight || 720;\n            self.fire('ready'); // autoplay\n\n            if (self.options.autoplay) {\n              self.play(self.options.startTime);\n            }\n          });\n          self.player.addEventListener('volumechange', function (e) {\n            self.getVolume(function (volume) {\n              self.options.volume = volume;\n            });\n            self.fire('volumechange', e);\n          });\n          self.player.addEventListener('error', function (e) {\n            self.fire('error', e);\n          });\n        }\n\n        callback(self.$video);\n      });\n    }\n  }, {\n    key: \"init\",\n    value: function init() {\n      var self = this;\n      self.playerID = \"VideoWorker-\".concat(self.ID);\n    }\n  }, {\n    key: \"loadAPI\",\n    value: function loadAPI() {\n      var self = this;\n\n      if (YoutubeAPIadded && VimeoAPIadded) {\n        return;\n      }\n\n      var src = ''; // load Youtube API\n\n      if ('youtube' === self.type && !YoutubeAPIadded) {\n        YoutubeAPIadded = 1;\n        src = 'https://www.youtube.com/iframe_api';\n      } // load Vimeo API\n\n\n      if ('vimeo' === self.type && !VimeoAPIadded) {\n        VimeoAPIadded = 1; // Useful when Vimeo API added using RequireJS https://github.com/nk-o/video-worker/pull/7\n\n        if ('undefined' !== typeof global__WEBPACK_IMPORTED_MODULE_0___default.a.Vimeo) {\n          return;\n        }\n\n        src = 'https://player.vimeo.com/api/player.js';\n      }\n\n      if (!src) {\n        return;\n      } // add script in head section\n\n\n      var tag = document.createElement('script');\n      var head = document.getElementsByTagName('head')[0];\n      tag.src = src;\n      head.appendChild(tag);\n      head = null;\n      tag = null;\n    }\n  }, {\n    key: \"onAPIready\",\n    value: function onAPIready(callback) {\n      var self = this; // Youtube\n\n      if ('youtube' === self.type) {\n        // Listen for global YT player callback\n        if (('undefined' === typeof global__WEBPACK_IMPORTED_MODULE_0___default.a.YT || 0 === global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.loaded) && !loadingYoutubePlayer) {\n          // Prevents Ready event from being called twice\n          loadingYoutubePlayer = 1; // Creates deferred so, other players know when to wait.\n\n          window.onYouTubeIframeAPIReady = function () {\n            window.onYouTubeIframeAPIReady = null;\n            loadingYoutubeDefer.resolve('done');\n            callback();\n          };\n        } else if ('object' === _typeof(global__WEBPACK_IMPORTED_MODULE_0___default.a.YT) && 1 === global__WEBPACK_IMPORTED_MODULE_0___default.a.YT.loaded) {\n          callback();\n        } else {\n          loadingYoutubeDefer.done(function () {\n            callback();\n          });\n        }\n      } // Vimeo\n\n\n      if ('vimeo' === self.type) {\n        if ('undefined' === typeof global__WEBPACK_IMPORTED_MODULE_0___default.a.Vimeo && !loadingVimeoPlayer) {\n          loadingVimeoPlayer = 1;\n          var vimeoInterval = setInterval(function () {\n            if ('undefined' !== typeof global__WEBPACK_IMPORTED_MODULE_0___default.a.Vimeo) {\n              clearInterval(vimeoInterval);\n              loadingVimeoDefer.resolve('done');\n              callback();\n            }\n          }, 20);\n        } else if ('undefined' !== typeof global__WEBPACK_IMPORTED_MODULE_0___default.a.Vimeo) {\n          callback();\n        } else {\n          loadingVimeoDefer.done(function () {\n            callback();\n          });\n        }\n      } // Local\n\n\n      if ('local' === self.type) {\n        callback();\n      }\n    }\n  }]);\n\n  return VideoWorker;\n}();\n\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return jarallaxVideo; });\n/* harmony import */ var video_worker__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8);\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3);\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(global__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction jarallaxVideo() {\n  var jarallax = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : global__WEBPACK_IMPORTED_MODULE_1___default.a.jarallax;\n\n  if ('undefined' === typeof jarallax) {\n    return;\n  }\n\n  var Jarallax = jarallax.constructor; // append video after when block will be visible.\n\n  var defOnScroll = Jarallax.prototype.onScroll;\n\n  Jarallax.prototype.onScroll = function () {\n    var self = this;\n    defOnScroll.apply(self);\n    var isReady = !self.isVideoInserted && self.video && (!self.options.videoLazyLoading || self.isElementInViewport) && !self.options.disableVideo();\n\n    if (isReady) {\n      self.isVideoInserted = true;\n      self.video.getVideo(function (video) {\n        var $parent = video.parentNode;\n        self.css(video, {\n          position: self.image.position,\n          top: '0px',\n          left: '0px',\n          right: '0px',\n          bottom: '0px',\n          width: '100%',\n          height: '100%',\n          maxWidth: 'none',\n          maxHeight: 'none',\n          pointerEvents: 'none',\n          transformStyle: 'preserve-3d',\n          backfaceVisibility: 'hidden',\n          willChange: 'transform,opacity',\n          margin: 0,\n          zIndex: -1\n        });\n        self.$video = video; // add Poster attribute to self-hosted video\n\n        if ('local' === self.video.type) {\n          if (self.image.src) {\n            self.$video.setAttribute('poster', self.image.src);\n          } else if (self.image.$item && 'IMG' === self.image.$item.tagName && self.image.$item.src) {\n            self.$video.setAttribute('poster', self.image.$item.src);\n          }\n        } // insert video tag\n\n\n        self.image.$container.appendChild(video); // remove parent video element (created by VideoWorker)\n\n        $parent.parentNode.removeChild($parent);\n      });\n    }\n  }; // cover video\n\n\n  var defCoverImage = Jarallax.prototype.coverImage;\n\n  Jarallax.prototype.coverImage = function () {\n    var self = this;\n    var imageData = defCoverImage.apply(self);\n    var node = self.image.$item ? self.image.$item.nodeName : false;\n\n    if (imageData && self.video && node && ('IFRAME' === node || 'VIDEO' === node)) {\n      var h = imageData.image.height;\n      var w = h * self.image.width / self.image.height;\n      var ml = (imageData.container.width - w) / 2;\n      var mt = imageData.image.marginTop;\n\n      if (imageData.container.width > w) {\n        w = imageData.container.width;\n        h = w * self.image.height / self.image.width;\n        ml = 0;\n        mt += (imageData.image.height - h) / 2;\n      } // add video height over than need to hide controls\n\n\n      if ('IFRAME' === node) {\n        h += 400;\n        mt -= 200;\n      }\n\n      self.css(self.$video, {\n        width: \"\".concat(w, \"px\"),\n        marginLeft: \"\".concat(ml, \"px\"),\n        height: \"\".concat(h, \"px\"),\n        marginTop: \"\".concat(mt, \"px\")\n      });\n    }\n\n    return imageData;\n  }; // init video\n\n\n  var defInitImg = Jarallax.prototype.initImg;\n\n  Jarallax.prototype.initImg = function () {\n    var self = this;\n    var defaultResult = defInitImg.apply(self);\n\n    if (!self.options.videoSrc) {\n      self.options.videoSrc = self.$item.getAttribute('data-jarallax-video') || null;\n    }\n\n    if (self.options.videoSrc) {\n      self.defaultInitImgResult = defaultResult;\n      return true;\n    }\n\n    return defaultResult;\n  };\n\n  var defCanInitParallax = Jarallax.prototype.canInitParallax;\n\n  Jarallax.prototype.canInitParallax = function () {\n    var self = this;\n    var defaultResult = defCanInitParallax.apply(self);\n\n    if (!self.options.videoSrc) {\n      return defaultResult;\n    } // Init video api\n\n\n    var video = new video_worker__WEBPACK_IMPORTED_MODULE_0__[\"default\"](self.options.videoSrc, {\n      autoplay: true,\n      loop: self.options.videoLoop,\n      showContols: false,\n      startTime: self.options.videoStartTime || 0,\n      endTime: self.options.videoEndTime || 0,\n      mute: self.options.videoVolume ? 0 : 1,\n      volume: self.options.videoVolume || 0\n    });\n\n    function resetDefaultImage() {\n      if (self.image.$default_item) {\n        self.image.$item = self.image.$default_item;\n        self.image.$item.style.display = 'block'; // set image width and height\n\n        self.coverImage();\n        self.clipContainer();\n        self.onScroll();\n      }\n    }\n\n    if (video.isValid()) {\n      // Force enable parallax.\n      // When the parallax disabled on mobile devices, we still need to display videos.\n      // https://github.com/nk-o/jarallax/issues/159\n      if (this.options.disableParallax()) {\n        defaultResult = true;\n        self.image.position = 'absolute';\n        self.options.type = 'scroll';\n        self.options.speed = 1;\n      } // if parallax will not be inited, we can add thumbnail on background.\n\n\n      if (!defaultResult) {\n        if (!self.defaultInitImgResult) {\n          video.getImageURL(function (url) {\n            // save default user styles\n            var curStyle = self.$item.getAttribute('style');\n\n            if (curStyle) {\n              self.$item.setAttribute('data-jarallax-original-styles', curStyle);\n            } // set new background\n\n\n            self.css(self.$item, {\n              'background-image': \"url(\\\"\".concat(url, \"\\\")\"),\n              'background-position': 'center',\n              'background-size': 'cover'\n            });\n          });\n        } // init video\n\n      } else {\n        video.on('ready', function () {\n          if (self.options.videoPlayOnlyVisible) {\n            var oldOnScroll = self.onScroll;\n\n            self.onScroll = function () {\n              oldOnScroll.apply(self);\n\n              if (!self.videoError && (self.options.videoLoop || !self.options.videoLoop && !self.videoEnded)) {\n                if (self.isVisible()) {\n                  video.play();\n                } else {\n                  video.pause();\n                }\n              }\n            };\n          } else {\n            video.play();\n          }\n        });\n        video.on('started', function () {\n          self.image.$default_item = self.image.$item;\n          self.image.$item = self.$video; // set video width and height\n\n          self.image.width = self.video.videoWidth || 1280;\n          self.image.height = self.video.videoHeight || 720;\n          self.coverImage();\n          self.clipContainer();\n          self.onScroll(); // hide image\n\n          if (self.image.$default_item) {\n            self.image.$default_item.style.display = 'none';\n          }\n        });\n        video.on('ended', function () {\n          self.videoEnded = true;\n\n          if (!self.options.videoLoop) {\n            // show default image if Loop disabled.\n            resetDefaultImage();\n          }\n        });\n        video.on('error', function () {\n          self.videoError = true; // show default image if video loading error.\n\n          resetDefaultImage();\n        });\n        self.video = video; // set image if not exists\n\n        if (!self.defaultInitImgResult) {\n          // set empty image on self-hosted video if not defined\n          self.image.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';\n\n          if ('local' !== video.type) {\n            video.getImageURL(function (url) {\n              self.image.bgImage = \"url(\\\"\".concat(url, \"\\\")\");\n              self.init();\n            });\n            return false;\n          }\n        }\n      }\n    }\n\n    return defaultResult;\n  }; // Destroy video parallax\n\n\n  var defDestroy = Jarallax.prototype.destroy;\n\n  Jarallax.prototype.destroy = function () {\n    var self = this;\n\n    if (self.image.$default_item) {\n      self.image.$item = self.image.$default_item;\n      delete self.image.$default_item;\n    }\n\n    defDestroy.apply(self);\n  };\n}\n\n/***/ })\n/******/ ]);"]}