{"version": 3, "sources": ["jarallax.js"], "names": ["modules", "installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "callback", "document", "readyState", "attachEvent", "addEventListener", "global", "win", "window", "self", "this", "_typeof", "obj", "iterator", "constructor", "g", "Function", "e", "__webpack_exports__", "lite_ready__WEBPACK_IMPORTED_MODULE_0__", "lite_ready__WEBPACK_IMPORTED_MODULE_0___default", "global__WEBPACK_IMPORTED_MODULE_1__", "_jarallax_esm__WEBPACK_IMPORTED_MODULE_2__", "j<PERSON><PERSON>y<PERSON><PERSON>in", "oldJqPlugin", "oldPlugin", "jarall<PERSON>", "noConflict", "_len", "arguments", "length", "args", "Array", "_key", "unshift", "res", "apply", "fn", "querySelectorAll", "_slicedToArray", "arr", "isArray", "_arrayWithHoles", "_arr", "_n", "_d", "_e", "undefined", "_s", "_i", "next", "done", "push", "err", "_iterableToArrayLimit", "minLen", "_arrayLikeToArray", "toString", "slice", "from", "test", "_unsupportedIterableToArray", "TypeError", "_nonIterableRest", "len", "arr2", "_defineProperties", "target", "props", "descriptor", "configurable", "writable", "$deviceHelper", "wndH", "navigator", "isIE", "userAgent", "indexOf", "isMobile", "supportTransform", "prefixes", "split", "div", "createElement", "style", "updateWndVars", "body", "cssText", "append<PERSON><PERSON><PERSON>", "clientHeight", "innerHeight", "documentElement", "jarallaxList", "updateParallax", "for<PERSON>ach", "data", "k", "instance", "oldData", "clientRect", "$item", "getBoundingClientRect", "newData", "width", "height", "top", "bottom", "wndW", "innerWidth", "isResized", "isScrolled", "onResize", "onScroll", "requestAnimationFrame", "plugin", "items", "options", "HTMLElement", "nodeType", "nodeName", "ret", "_len2", "_key2", "Jarallax", "instanceID", "item", "userOptions", "<PERSON><PERSON><PERSON><PERSON>", "_classCallCheck", "defaults", "type", "speed", "imgSrc", "imgElement", "imgSize", "imgPosition", "imgRepeat", "keepImg", "elementInViewport", "zIndex", "disableParallax", "disable<PERSON><PERSON><PERSON>", "videoSrc", "videoStartTime", "videoEndTime", "videoVolume", "videoLoop", "videoPlayOnlyVisible", "videoLazyLoading", "onInit", "onDestroy", "onCoverImage", "disableParallaxRegexp", "disableVideoRegexp", "dataOptions", "dataset", "pureDataOptions", "keys", "loweCaseOption", "substr", "toLowerCase", "extend", "pureOptions", "Math", "min", "max", "parseFloat", "RegExp", "elementInVP", "Element", "image", "src", "$container", "useImgTag", "position", "initImg", "canInitParallax", "init", "protoProps", "staticProps", "el", "styles", "getComputedStyle", "getPropertyValue", "transform", "out", "clientWidth", "y", "scrollTop", "$imgElement", "querySelector", "Image", "cloneNode", "$itemParent", "parentNode", "bgImage", "css", "curStyle", "curImgStyle", "$parents", "containerStyles", "left", "overflow", "imageStyles", "pointerEvents", "transformStyle", "backfaceVisibility", "<PERSON><PERSON><PERSON><PERSON>", "getAttribute", "setAttribute", "z-index", "opacity", "concat", "object-fit", "object-position", "font-family", "max-width", "background-position", "background-size", "background-repeat", "background-image", "elem", "parents", "parentElement", "getParents", "filter", "parentTransform", "addToParallaxList", "splice", "removeFromParallaxList", "originalStylesImgTag", "originalStylesTag", "removeAttribute", "$clipStyles", "<PERSON><PERSON><PERSON><PERSON>", "rect", "head", "getElementsByTagName", "styleSheet", "innerHTML", "contH", "isScroll", "scrollDist", "resultH", "resultMT", "abs", "parallaxScrollDistance", "marginTop", "container", "isElementInViewport", "force", "beforeTop", "beforeTopEnd", "afterTop", "beforeBottom", "beforeBottomEnd", "afterBottom", "fromViewportCenter", "visiblePercent", "scale", "positionY", "contT", "viewportRect", "right", "section", "coverImage", "clipContainer"], "mappings": ";;;;;IAAA,SAAAA,GAEA,IAAAC,EAAA,GAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAC,QAGA,IAAAC,EAAAJ,EAAAE,GAAA,CACAG,EAAAH,EACAI,GAAA,EACAH,QAAA,IAUA,OANAJ,EAAAG,GAAAK,KAAAH,EAAAD,QAAAC,EAAAA,EAAAD,QAAAF,GAGAG,EAAAE,GAAA,EAGAF,EAAAD,QAKAF,EAAAO,EAAAT,EAGAE,EAAAQ,EAAAT,EAGAC,EAAAS,EAAA,SAAAP,EAAAQ,EAAAC,GACAX,EAAAY,EAAAV,EAAAQ,IACAG,OAAAC,eAAAZ,EAAAQ,EAAA,CAAAK,YAAA,EAAAC,IAAAL,KAKAX,EAAAiB,EAAA,SAAAf,GACA,oBAAAgB,QAAAA,OAAAC,aACAN,OAAAC,eAAAZ,EAAAgB,OAAAC,YAAA,CAAAC,MAAA,WAEAP,OAAAC,eAAAZ,EAAA,aAAA,CAAAkB,OAAA,KAQApB,EAAAqB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAApB,EAAAoB,IACA,EAAAE,EAAA,OAAAF,EACA,GAAA,EAAAE,GAAA,iBAAAF,GAAAA,GAAAA,EAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAzB,EAAAiB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,UAAA,CAAAT,YAAA,EAAAK,MAAAA,IACA,EAAAE,GAAA,iBAAAF,EAAA,IAAA,IAAAM,KAAAN,EAAApB,EAAAS,EAAAe,EAAAE,EAAA,SAAAA,GAAA,OAAAN,EAAAM,IAAAC,KAAA,KAAAD,IACA,OAAAF,GAIAxB,EAAA4B,EAAA,SAAAzB,GACA,IAAAQ,EAAAR,GAAAA,EAAAoB,WACA,WAAA,OAAApB,EAAA,SACA,WAAA,OAAAA,GAEA,OADAH,EAAAS,EAAAE,EAAA,IAAAA,GACAA,GAIAX,EAAAY,EAAA,SAAAiB,EAAAC,GAAA,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGA9B,EAAAiC,EAAA,GAIAjC,EAAAA,EAAAkC,EAAA,IAnFA,CAsFA,CACA,CACA,CAEA,SAAA/B,EAAAD,GAEAC,EAAAD,QAAA,SAAAiC,GACA,aAAAC,SAAAC,YAAA,gBAAAD,SAAAC,WAEAF,EAAA7B,OACA8B,SAAAE,YAEAF,SAAAE,YAAA,qBAAA,WACA,gBAAAF,SAAAC,YAAAF,EAAA7B,SAEA8B,SAAAG,kBAEAH,SAAAG,iBAAA,mBAAAJ,KAMA,SAAAhC,EAAAD,EAAAF,IAEA,SAAAwC,GAAA,IAGAC,EADA,oBAAAC,OACAA,YACA,IAAAF,EACAA,EACA,oBAAAG,KACAA,KAEA,GAGAxC,EAAAD,QAAAuC,IACAnC,KAAAsC,KAAA5C,EAAA,KAIA,SAAAG,EAAAD,GAEA,SAAA2C,EAAAC,GAAA,OAAAD,EAAA,mBAAA3B,QAAA,iBAAAA,OAAA6B,SAAA,SAAAD,GAAA,cAAAA,GAAA,SAAAA,GAAA,OAAAA,GAAA,mBAAA5B,QAAA4B,EAAAE,cAAA9B,QAAA4B,IAAA5B,OAAAa,UAAA,gBAAAe,IAAAA,GAEA,IAEAG,EAAA,WACA,OAAAL,KADA,GAIA,IAEAK,EAAAA,GAAA,IAAAC,SAAA,cAAA,GACA,MAAAC,GAEA,YAAA,oBAAAT,OAAA,YAAAG,EAAAH,WAAAO,EAAAP,QAMAvC,EAAAD,QAAA+C,GAGA,CACA,CACA,CACA,CACA,CAEA,SAAA9C,EAAAD,EAAAF,GAEAG,EAAAD,QAAAF,EAAA,KAKA,SAAAG,EAAAiD,EAAApD,gBAGAA,EAAAiB,EAAAmC,GACA,IAAAC,EAAArD,EAAA,GACAsD,EAAAtD,EAAA4B,EAAAyB,GACAE,EAAAvD,EAAA,GAEAwD,EAAAxD,EAAA,IACA,SAAA6C,EAAAC,GAAA,OAAAD,EAAA,mBAAA3B,QAAA,iBAAAA,OAAA6B,SAAA,SAAAD,GAAA,cAAAA,GAAA,SAAAA,GAAA,OAAAA,GAAA,mBAAA5B,QAAA4B,EAAAE,cAAA9B,QAAA4B,IAAA5B,OAAAa,UAAA,gBAAAe,IAAAA,GAMA,IAUAW,EAYAC,EAtBAC,EAAAJ,EAAA,OAAAK,SACAL,EAAA,OAAAK,SAAAJ,EAAA,QAEAD,EAAA,OAAAK,SAAAC,WAAA,WAEA,OADAN,EAAA,OAAAK,SAAAD,EACAf,WAIA,IAAAW,EAAA,UACAE,EAAA,WACA,IAAA,IAAAK,EAAAC,UAAAC,OAAAC,EAAA,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IACAF,EAAAE,GAAAJ,UAAAI,GAGAD,MAAAnC,UAAAqC,QAAA9D,KAAA2D,EAAArB,MACA,IAAAyB,EAAAb,EAAA,QAAAc,MAAAf,EAAA,OAAAU,GACA,MAAA,WAAApB,EAAAwB,GAAAA,EAAAzB,OAGAI,YAAAQ,EAAA,QAAAR,YAEAU,EAAAH,EAAA,OAAAgB,GAAAX,SACAL,EAAA,OAAAgB,GAAAX,SAAAH,EAEAF,EAAA,OAAAgB,GAAAX,SAAAC,WAAA,WAEA,OADAN,EAAA,OAAAgB,GAAAX,SAAAF,EACAd,OAKAU,GAAAA,CAAA,WACAzC,OAAA2C,EAAA,QAAA3C,CAAAuB,SAAAoC,iBAAA,uBAKA,SAAArE,EAAAiD,EAAApD,gBAGAA,EAAAiB,EAAAmC,GACA,IAAAC,EAAArD,EAAA,GACAsD,EAAAtD,EAAA4B,EAAAyB,GACAE,EAAAvD,EAAA,GAEA,SAAAyE,EAAAC,EAAAtE,GAAA,OAUA,SAAAsE,GAAA,GAAAR,MAAAS,QAAAD,GAAA,OAAAA,EAVAE,CAAAF,IAQA,SAAAA,EAAAtE,GAAA,GAAA,oBAAAc,UAAAA,OAAA6B,YAAAlC,OAAA6D,IAAA,OAAA,IAAAG,EAAA,GAAAC,GAAA,EAAAC,GAAA,EAAAC,OAAAC,EAAA,IAAA,IAAA,IAAAC,EAAAC,EAAAT,EAAAxD,OAAA6B,cAAA+B,GAAAI,EAAAC,EAAAC,QAAAC,QAAAR,EAAAS,KAAAJ,EAAA9D,QAAAhB,GAAAyE,EAAAb,SAAA5D,GAAA0E,GAAA,IAAA,MAAAS,GAAAR,GAAA,EAAAC,EAAAO,EAAA,QAAA,IAAAT,GAAA,MAAAK,EAAA,QAAAA,EAAA,SAAA,QAAA,GAAAJ,EAAA,MAAAC,GAAA,OAAAH,EARAW,CAAAd,EAAAtE,IAIA,SAAAQ,EAAA6E,GAAA,IAAA7E,EAAA,OAAA,GAAA,iBAAAA,EAAA,OAAA8E,EAAA9E,EAAA6E,GAAA,IAAA7D,EAAAf,OAAAkB,UAAA4D,SAAArF,KAAAM,GAAAgF,MAAA,GAAA,GAAA,WAAAhE,GAAAhB,EAAAoC,cAAApB,EAAAhB,EAAAoC,YAAAtC,MAAA,GAAA,QAAAkB,GAAA,QAAAA,EAAA,OAAAsC,MAAA2B,KAAAjF,GAAA,GAAA,cAAAgB,GAAA,2CAAAkE,KAAAlE,GAAA,OAAA8D,EAAA9E,EAAA6E,GAJAM,CAAArB,EAAAtE,IAEA,WAAA,MAAA,IAAA4F,UAAA,6IAFAC,GAMA,SAAAP,EAAAhB,EAAAwB,IAAA,MAAAA,GAAAA,EAAAxB,EAAAV,UAAAkC,EAAAxB,EAAAV,QAAA,IAAA,IAAA5D,EAAA,EAAA+F,EAAA,IAAAjC,MAAAgC,GAAA9F,EAAA8F,EAAA9F,IAAA+F,EAAA/F,GAAAsE,EAAAtE,GAAA,OAAA+F,EAMA,SAAAtD,EAAAC,GAAA,OAAAD,EAAA,mBAAA3B,QAAA,iBAAAA,OAAA6B,SAAA,SAAAD,GAAA,cAAAA,GAAA,SAAAA,GAAA,OAAAA,GAAA,mBAAA5B,QAAA4B,EAAAE,cAAA9B,QAAA4B,IAAA5B,OAAAa,UAAA,gBAAAe,IAAAA,GAIA,SAAAsD,EAAAC,EAAAC,GAAA,IAAA,IAAAlG,EAAA,EAAAA,EAAAkG,EAAAtC,OAAA5D,IAAA,CAAA,IAAAmG,EAAAD,EAAAlG,GAAAmG,EAAAxF,WAAAwF,EAAAxF,aAAA,EAAAwF,EAAAC,cAAA,EAAA,UAAAD,IAAAA,EAAAE,UAAA,GAAA5F,OAAAC,eAAAuF,EAAAE,EAAA7E,IAAA6E,IAMA,IAiBAG,EAiBAC,EAlCAC,EAAArD,EAAA,OAAAqD,UACAC,GAAA,EAAAD,EAAAE,UAAAC,QAAA,WAAA,EAAAH,EAAAE,UAAAC,QAAA,cAAA,EAAAH,EAAAE,UAAAC,QAAA,SACAC,EAAA,iEAAAlB,KAAAc,EAAAE,WAEAG,EAAA,WAIA,IAHA,IAAAC,EAAA,yCAAAC,MAAA,KACAC,EAAAhF,SAAAiF,cAAA,OAEAjH,EAAA,EAAAA,EAAA8G,EAAAlD,OAAA5D,GAAA,EACA,GAAAgH,QAAAnC,IAAAmC,EAAAE,MAAAJ,EAAA9G,IACA,OAAA8G,EAAA9G,GAIA,OAAA,EAVA,GAgCA,SAAAmH,IAEAZ,EADAK,IAbAN,GAAAtE,SAAAoF,QACAd,EAAAtE,SAAAiF,cAAA,QACAC,MAAAG,QAAA,mEACArF,SAAAoF,KAAAE,YAAAhB,KAGAA,EAAAA,EAAAiB,aAAA,IAAApE,EAAA,OAAAqE,aAAAxF,SAAAyF,gBAAAF,cAUApE,EAAA,OAAAqE,aAAAxF,SAAAyF,gBAAAF,aAIAJ,IACAhE,EAAA,OAAAhB,iBAAA,SAAAgF,GACAhE,EAAA,OAAAhB,iBAAA,oBAAAgF,GACAhE,EAAA,OAAAhB,iBAAA,OAAAgF,GACAjE,GAAAA,CAAA,WACAiE,MAMA,IAAAO,EAAA,GAgBA,SAAAC,IACAD,EAAA9D,SAIA8D,EAAAE,QAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAF,EAAAE,SACAC,EAAAH,EAAAG,QACAC,EAAAF,EAAAG,MAAAC,wBACAC,EAAA,CACAC,MAAAJ,EAAAI,MACAC,OAAAL,EAAAK,OACAC,IAAAN,EAAAM,IACAC,OAAAP,EAAAO,OACAC,KAAAtF,EAAA,OAAAuF,WACAnC,KAAAA,GAEAoC,GAAAX,GAAAA,EAAAS,OAAAL,EAAAK,MAAAT,EAAAzB,OAAA6B,EAAA7B,MAAAyB,EAAAK,QAAAD,EAAAC,OAAAL,EAAAM,SAAAF,EAAAE,OACAM,EAAAD,IAAAX,GAAAA,EAAAO,MAAAH,EAAAG,KAAAP,EAAAQ,SAAAJ,EAAAI,OACAd,EAAAI,GAAAE,QAAAI,EAEAO,GACAZ,EAAAc,WAGAD,GACAb,EAAAe,aAGA3F,EAAA,OAAA4F,sBAAApB,IAioBA,SAAAqB,EAAAC,EAAAC,IAGA,YAAA,oBAAAC,YAAA,YAAA1G,EAAA0G,cAAAF,aAAAE,YAAAF,GAAA,WAAAxG,EAAAwG,IAAA,OAAAA,GAAA,IAAAA,EAAAG,UAAA,iBAAAH,EAAAI,YACAJ,EAAA,CAAAA,IAOA,IAJA,IAEAK,EAFAxD,EAAAmD,EAAArF,OACAkE,EAAA,EAGAyB,EAAA5F,UAAAC,OAAAC,EAAA,IAAAC,MAAA,EAAAyF,EAAAA,EAAA,EAAA,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IACA3F,EAAA2F,EAAA,GAAA7F,UAAA6F,GAGA,KAAA1B,EAAAhC,EAAAgC,GAAA,EAUA,GATA,WAAArF,EAAAyG,SAAA,IAAAA,EACAD,EAAAnB,GAAAtE,WACAyF,EAAAnB,GAAAtE,SAAA,IAAAiG,EAAAR,EAAAnB,GAAAoB,IAEAD,EAAAnB,GAAAtE,WAEA8F,EAAAL,EAAAnB,GAAAtE,SAAA0F,GAAAhF,MAAA+E,EAAAnB,GAAAtE,SAAAK,SAGA,IAAAyF,EACA,OAAAA,EAIA,OAAAL,EA5pBA,IAAAS,EAAA,EAEAD,EAAA,WACA,SAAAA,EAAAE,EAAAC,IAlHA,SAAA7B,EAAA8B,GAAA,KAAA9B,aAAA8B,GAAA,MAAA,IAAAjE,UAAA,qCAmHAkE,CAAAtH,KAAAiH,GAEA,IAAAlH,EAAAC,KACAD,EAAAmH,WAAAA,EACAA,GAAA,EACAnH,EAAA2F,MAAAyB,EACApH,EAAAwH,SAAA,CACAC,KAAA,SAEAC,MAAA,GAEAC,OAAA,KACAC,WAAA,gBACAC,QAAA,QACAC,YAAA,UACAC,UAAA,YAEAC,SAAA,EAEAC,kBAAA,KACAC,QAAA,IACAC,iBAAA,EACAC,cAAA,EAEAC,SAAA,KACAC,eAAA,EACAC,aAAA,EACAC,YAAA,EACAC,WAAA,EACAC,sBAAA,EACAC,kBAAA,EAEApC,SAAA,KAEAqC,OAAA,KAEAC,UAAA,KAEAC,aAAA,MAIA,IA2BAC,EAmBAC,EA9CAC,EAAAjJ,EAAA2F,MAAAuD,SAAA,GACAC,EAAA,GACAjL,OAAAkL,KAAAH,GAAA5D,QAAA,SAAAtG,GACA,IAAAsK,EAAAtK,EAAAuK,OAAA,EAAA,GAAAC,cAAAxK,EAAAuK,OAAA,GAEAD,QAAA,IAAArJ,EAAAwH,SAAA6B,KACAF,EAAAE,GAAAJ,EAAAlK,MAGAiB,EAAA2G,QAAA3G,EAAAwJ,OAAA,GAAAxJ,EAAAwH,SAAA2B,EAAA9B,GACArH,EAAAyJ,YAAAzJ,EAAAwJ,OAAA,GAAAxJ,EAAA2G,SAEAzI,OAAAkL,KAAApJ,EAAA2G,SAAAtB,QAAA,SAAAtG,GACA,SAAAiB,EAAA2G,QAAA5H,GACAiB,EAAA2G,QAAA5H,IAAA,EACA,UAAAiB,EAAA2G,QAAA5H,KACAiB,EAAA2G,QAAA5H,IAAA,KAIAiB,EAAA2G,QAAAe,MAAAgC,KAAAC,IAAA,EAAAD,KAAAE,KAAA,EAAAC,WAAA7J,EAAA2G,QAAAe,SAEA,iBAAA1H,EAAA2G,QAAAwB,kBACAnI,EAAA2G,QAAAwB,gBAAA,IAAA2B,OAAA9J,EAAA2G,QAAAwB,kBAGAnI,EAAA2G,QAAAwB,2BAAA2B,SACAf,EAAA/I,EAAA2G,QAAAwB,gBAEAnI,EAAA2G,QAAAwB,gBAAA,WACA,OAAAY,EAAA5F,KAAAc,EAAAE,aAIA,mBAAAnE,EAAA2G,QAAAwB,kBACAnI,EAAA2G,QAAAwB,gBAAA,WACA,OAAA,IAKA,iBAAAnI,EAAA2G,QAAAyB,eACApI,EAAA2G,QAAAyB,aAAA,IAAA0B,OAAA9J,EAAA2G,QAAAyB,eAGApI,EAAA2G,QAAAyB,wBAAA0B,SACAd,EAAAhJ,EAAA2G,QAAAyB,aAEApI,EAAA2G,QAAAyB,aAAA,WACA,OAAAY,EAAA7F,KAAAc,EAAAE,aAIA,mBAAAnE,EAAA2G,QAAAyB,eACApI,EAAA2G,QAAAyB,aAAA,WACA,OAAA,IAKA,IAAA2B,EAAA/J,EAAA2G,QAAAsB,kBAEA8B,GAAA,WAAA7J,EAAA6J,SAAA,IAAAA,EAAA1I,SAKA0I,EAFAjI,EAFAiI,EAEA,GAEA,IAIAA,aAAAC,UACAD,EAAA,MAGA/J,EAAA2G,QAAAsB,kBAAA8B,EACA/J,EAAAiK,MAAA,CACAC,IAAAlK,EAAA2G,QAAAgB,QAAA,KACAwC,WAAA,KACAC,WAAA,EAIAC,SAAA,2BAAAlH,KAAAc,EAAAE,WAAA,WAAA,SAGAnE,EAAAsK,WAAAtK,EAAAuK,mBACAvK,EAAAwK,OAhPA,IAAAlD,EAAAmD,EAAAC,EAquBA,OAruBApD,EAsPAJ,GAtPAuD,EAsPA,CAAA,CACA1L,IAAA,MACAN,MAAA,SAAAkM,EAAAC,GACA,MAAA,iBAAAA,EACAhK,EAAA,OAAAiK,iBAAAF,GAAAG,iBAAAF,IAIAA,EAAAG,WAAAzG,IACAsG,EAAAtG,GAAAsG,EAAAG,WAGA7M,OAAAkL,KAAAwB,GAAAvF,QAAA,SAAAtG,GACA4L,EAAAhG,MAAA5F,GAAA6L,EAAA7L,KAEA4L,KAIA,CACA5L,IAAA,SACAN,MAAA,SAAAuM,GACA,IAAA,IAAA7J,EAAAC,UAAAC,OAAAC,EAAA,IAAAC,MAAA,EAAAJ,EAAAA,EAAA,EAAA,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IACAF,EAAAE,EAAA,GAAAJ,UAAAI,GAaA,OAVAwJ,EAAAA,GAAA,GACA9M,OAAAkL,KAAA9H,GAAA+D,QAAA,SAAA5H,GACA6D,EAAA7D,IAIAS,OAAAkL,KAAA9H,EAAA7D,IAAA4H,QAAA,SAAAtG,GACAiM,EAAAjM,GAAAuC,EAAA7D,GAAAsB,OAGAiM,IAIA,CACAjM,IAAA,gBACAN,MAAA,WACA,MAAA,CACAqH,MAAAlF,EAAA,OAAAuF,YAAA1G,SAAAyF,gBAAA+F,YACAlF,OAAA/B,EACAkH,EAAAzL,SAAAyF,gBAAAiG,aAIA,CACApM,IAAA,UACAN,MAAA,WACA,IAAAuB,EAAAC,KAEAmL,EAAApL,EAAA2G,QAAAiB,WA4BA,OA1BAwD,GAAA,iBAAAA,IACAA,EAAApL,EAAA2F,MAAA0F,cAAAD,IAIAA,aAAApB,UACAhK,EAAA2G,QAAAgB,QACAyD,EAAA,IAAAE,OACApB,IAAAlK,EAAA2G,QAAAgB,OAEAyD,EAAA,MAIAA,IACApL,EAAA2G,QAAAqB,QACAhI,EAAAiK,MAAAtE,MAAAyF,EAAAG,WAAA,IAEAvL,EAAAiK,MAAAtE,MAAAyF,EACApL,EAAAiK,MAAAuB,YAAAJ,EAAAK,YAGAzL,EAAAiK,MAAAG,WAAA,KAIApK,EAAAiK,MAAAtE,QAKA,OAAA3F,EAAAiK,MAAAC,MACAlK,EAAAiK,MAAAC,IAAA,iFACAlK,EAAAiK,MAAAyB,QAAA1L,EAAA2L,IAAA3L,EAAA2F,MAAA,wBAGA3F,EAAAiK,MAAAyB,SAAA,SAAA1L,EAAAiK,MAAAyB,YAEA,CACA3M,IAAA,kBACAN,MAAA,WACA,OAAA6F,IAAArE,KAAA0G,QAAAwB,oBAEA,CACApJ,IAAA,OACAN,MAAA,WACA,IAkBAmN,EAOAC,EAkEAC,EA3FA9L,EAAAC,KACA8L,EAAA,CACA1B,SAAA,WACArE,IAAA,EACAgG,KAAA,EACAlG,MAAA,OACAC,OAAA,OACAkG,SAAA,UAEAC,EAAA,CACAC,cAAA,OACAC,eAAA,cACAC,mBAAA,SACAC,WAAA,qBAGAtM,EAAA2G,QAAAqB,WAEA4D,EAAA5L,EAAA2F,MAAA4G,aAAA,WAGAvM,EAAA2F,MAAA6G,aAAA,gCAAAZ,IAGA5L,EAAAiK,MAAAG,YACAyB,EAAA7L,EAAAiK,MAAAtE,MAAA4G,aAAA,WAGAvM,EAAAiK,MAAAtE,MAAA6G,aAAA,gCAAAX,IAMA,WAAA7L,EAAA2L,IAAA3L,EAAA2F,MAAA,aACA3F,EAAA2L,IAAA3L,EAAA2F,MAAA,CACA0E,SAAA,aAIA,SAAArK,EAAA2L,IAAA3L,EAAA2F,MAAA,YACA3F,EAAA2L,IAAA3L,EAAA2F,MAAA,CACAuC,OAAA,IAKAlI,EAAAiK,MAAAE,WAAA1K,SAAAiF,cAAA,OACA1E,EAAA2L,IAAA3L,EAAAiK,MAAAE,WAAA4B,GACA/L,EAAA2L,IAAA3L,EAAAiK,MAAAE,WAAA,CACAsC,UAAAzM,EAAA2G,QAAAuB,SAGAhE,GACAlE,EAAA2L,IAAA3L,EAAAiK,MAAAE,WAAA,CACAuC,QAAA,QAIA1M,EAAAiK,MAAAE,WAAAqC,aAAA,KAAA,sBAAAG,OAAA3M,EAAAmH,aACAnH,EAAA2F,MAAAZ,YAAA/E,EAAAiK,MAAAE,YAEAnK,EAAAiK,MAAAG,UACA8B,EAAAlM,EAAAwJ,OAAA,CACAoD,aAAA5M,EAAA2G,QAAAkB,QACAgF,kBAAA7M,EAAA2G,QAAAmB,YAEAgF,cAAA,eAAAH,OAAA3M,EAAA2G,QAAAkB,QAAA,uBAAA8E,OAAA3M,EAAA2G,QAAAmB,YAAA,KACAiF,YAAA,QACAhB,EAAAG,IAEAlM,EAAAiK,MAAAtE,MAAAlG,SAAAiF,cAAA,OAEA1E,EAAAiK,MAAAC,MACAgC,EAAAlM,EAAAwJ,OAAA,CACAwD,sBAAAhN,EAAA2G,QAAAmB,YACAmF,kBAAAjN,EAAA2G,QAAAkB,QACAqF,oBAAAlN,EAAA2G,QAAAoB,UACAoF,mBAAAnN,EAAAiK,MAAAyB,SAAA,QAAAiB,OAAA3M,EAAAiK,MAAAC,IAAA,OACA6B,EAAAG,KAIA,YAAAlM,EAAA2G,QAAAc,MAAA,UAAAzH,EAAA2G,QAAAc,MAAA,kBAAAzH,EAAA2G,QAAAc,MAAA,IAAAzH,EAAA2G,QAAAe,QACA1H,EAAAiK,MAAAI,SAAA,YAMA,UAAArK,EAAAiK,MAAAI,WACAyB,EA3XA,SAAAsB,GAGA,IAFA,IAAAC,EAAA,GAEA,OAAAD,EAAAE,eAGA,KAFAF,EAAAA,EAAAE,eAEAzG,UACAwG,EAAA1K,KAAAyK,GAIA,OAAAC,EAgXAE,CAAAvN,EAAA2F,OAAA6H,OAAA,SAAA7C,GACA,IAAAC,EAAAhK,EAAA,OAAAiK,iBAAAF,GACA8C,EAAA7C,EAAA,sBAAAA,EAAA,mBAAAA,EAAAG,UAEA,OAAA0C,GAAA,SAAAA,GADA,gBACAtK,KAAAyH,EAAAqB,SAAArB,EAAA,cAAAA,EAAA,iBAEA5K,EAAAiK,MAAAI,SAAAyB,EAAAzK,OAAA,WAAA,SAIA6K,EAAA7B,SAAArK,EAAAiK,MAAAI,SAEArK,EAAA2L,IAAA3L,EAAAiK,MAAAtE,MAAAuG,GACAlM,EAAAiK,MAAAE,WAAApF,YAAA/E,EAAAiK,MAAAtE,OAEA3F,EAAAsG,WACAtG,EAAAuG,UAAA,GAEAvG,EAAA2G,QAAAiC,QACA5I,EAAA2G,QAAAiC,OAAAjL,KAAAqC,GAIA,SAAAA,EAAA2L,IAAA3L,EAAA2F,MAAA,qBACA3F,EAAA2L,IAAA3L,EAAA2F,MAAA,CACAwH,mBAAA,SAIAnN,EAAA0N,sBAGA,CACA3O,IAAA,oBACAN,MAAA,WACA0G,EAAAxC,KAAA,CACA6C,SAAAvF,OAGA,IAAAkF,EAAA9D,QACAT,EAAA,OAAA4F,sBAAApB,KAIA,CACArG,IAAA,yBACAN,MAAA,WACA,IAAAuB,EAAAC,KACAkF,EAAAE,QAAA,SAAAC,EAAAvG,GACAuG,EAAAE,SAAA2B,aAAAnH,EAAAmH,YACAhC,EAAAwI,OAAA5O,EAAA,OAIA,CACAA,IAAA,UACAN,MAAA,WACA,IAAAuB,EAAAC,KACAD,EAAA4N,yBAEA,IAWAC,EAXAC,EAAA9N,EAAA2F,MAAA4G,aAAA,iCACAvM,EAAA2F,MAAAoI,gBAAA,iCAEAD,EAGA9N,EAAA2F,MAAA6G,aAAA,QAAAsB,GAFA9N,EAAA2F,MAAAoI,gBAAA,SAKA/N,EAAAiK,MAAAG,YAEAyD,EAAA7N,EAAAiK,MAAAtE,MAAA4G,aAAA,iCACAvM,EAAAiK,MAAAtE,MAAAoI,gBAAA,iCAEAF,EAGA7N,EAAAiK,MAAAtE,MAAA6G,aAAA,QAAAsB,GAFA9N,EAAAiK,MAAAtE,MAAAoI,gBAAA,SAMA/N,EAAAiK,MAAAuB,aACAxL,EAAAiK,MAAAuB,YAAAzG,YAAA/E,EAAAiK,MAAAtE,QAKA3F,EAAAgO,aACAhO,EAAAgO,YAAAvC,WAAAwC,YAAAjO,EAAAgO,aAGAhO,EAAAiK,MAAAE,YACAnK,EAAAiK,MAAAE,WAAAsB,WAAAwC,YAAAjO,EAAAiK,MAAAE,YAIAnK,EAAA2G,QAAAkC,WACA7I,EAAA2G,QAAAkC,UAAAlL,KAAAqC,UAIAA,EAAA2F,MAAA1E,WAIA,CACAlC,IAAA,gBACAN,MAAA,WAEA,IAIAuB,EACAkO,EACApI,EACAC,EAYA6E,EAnBA,UAAA3K,KAAAgK,MAAAI,WAMAvE,GADAoI,GADAlO,EAAAC,MACAgK,MAAAE,WAAAvE,yBACAE,MACAC,EAAAmI,EAAAnI,OAEA/F,EAAAgO,cACAhO,EAAAgO,YAAAvO,SAAAiF,cAAA,SACA1E,EAAAgO,YAAAxB,aAAA,OAAA,YACAxM,EAAAgO,YAAAxB,aAAA,KAAA,iBAAAG,OAAA3M,EAAAmH,cACA1H,SAAA0O,MAAA1O,SAAA2O,qBAAA,QAAA,IACArJ,YAAA/E,EAAAgO,cAKApD,EAAA,uBAAA+B,OAAA3M,EAAAmH,WAAA,iCAAAwF,OAAA7G,EAAA,OAAA6G,OAAA5G,EAAA,sCAAA4G,OAAA7G,EAAA,QAAA6G,OAAA5G,EAAA,gKAEA/F,EAAAgO,YAAAK,WACArO,EAAAgO,YAAAK,WAAAvJ,QAAA8F,EAEA5K,EAAAgO,YAAAM,UAAA1D,KAGA,CACA7L,IAAA,aACAN,MAAA,WACA,IAAAuB,EAAAC,KACAiO,EAAAlO,EAAAiK,MAAAE,WAAAvE,wBACA2I,EAAAL,EAAAnI,OACA2B,EAAA1H,EAAA2G,QAAAe,MACA8G,EAAA,WAAAxO,EAAA2G,QAAAc,MAAA,mBAAAzH,EAAA2G,QAAAc,KACAgH,EAAA,EACAC,EAAAH,EACAI,EAAA,EAgDA,OA9CAH,IAEA9G,EAAA,GACA+G,EAAA/G,EAAAgC,KAAAE,IAAA2E,EAAAvK,GAEAA,EAAAuK,IACAE,GAAA/G,GAAA6G,EAAAvK,KAGAyK,EAAA/G,GAAA6G,EAAAvK,GAIA,EAAA0D,EACAgH,EAAAhF,KAAAkF,IAAAH,EAAAzK,GACA0D,EAAA,EACAgH,EAAAD,EAAA/G,EAAAgC,KAAAkF,IAAAH,GAEAC,IAAA1K,EAAAuK,IAAA,EAAA7G,GAGA+G,GAAA,GAIAzO,EAAA6O,uBAAAJ,EAGAE,EADAH,GACAxK,EAAA0K,GAAA,GAEAH,EAAAG,GAAA,EAIA1O,EAAA2L,IAAA3L,EAAAiK,MAAAtE,MAAA,CACAI,OAAA,GAAA4G,OAAA+B,EAAA,MACAI,UAAA,GAAAnC,OAAAgC,EAAA,MACA3C,KAAA,UAAAhM,EAAAiK,MAAAI,SAAA,GAAAsC,OAAAuB,EAAAlC,KAAA,MAAA,IACAlG,MAAA,GAAA6G,OAAAuB,EAAApI,MAAA,QAGA9F,EAAA2G,QAAAmC,cACA9I,EAAA2G,QAAAmC,aAAAnL,KAAAqC,GAIA,CACAiK,MAAA,CACAlE,OAAA2I,EACAI,UAAAH,GAEAI,UAAAb,KAGA,CACAnP,IAAA,YACAN,MAAA,WACA,OAAAwB,KAAA+O,sBAAA,IAEA,CACAjQ,IAAA,WACAN,MAAA,SAAAwQ,GACA,IAmBAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEAC,EAkBAC,EAaAC,EA1DA3P,EAAAC,KACAiO,EAAAlO,EAAA2F,MAAAC,wBACAgK,EAAA1B,EAAAlI,IACAuI,EAAAL,EAAAnI,OACA6E,EAAA,GAEAiF,EAAA3B,EAEAlO,EAAA2G,QAAAsB,oBACA4H,EAAA7P,EAAA2G,QAAAsB,kBAAArC,yBAGA5F,EAAAgP,oBAAA,GAAAa,EAAA5J,QAAA,GAAA4J,EAAAC,OAAAD,EAAA7J,KAAAhC,GAAA6L,EAAA7D,MAAApL,EAAA,OAAAuF,YAEA8I,GAAAjP,EAAAgP,uBAKAE,EAAAxF,KAAAE,IAAA,EAAAgG,GACAT,EAAAzF,KAAAE,IAAA,EAAA2E,EAAAqB,GACAR,EAAA1F,KAAAE,IAAA,GAAAgG,GACAP,EAAA3F,KAAAE,IAAA,EAAAgG,EAAArB,EAAAvK,GACAsL,EAAA5F,KAAAE,IAAA,EAAA2E,GAAAqB,EAAArB,EAAAvK,IACAuL,EAAA7F,KAAAE,IAAA,GAAAgG,EAAA5L,EAAAuK,GACAiB,EAAA,GAAAxL,EAAA4L,IAAA5L,EAAAuK,GAAA,EAEAkB,EAAA,EAEAlB,EAAAvK,EACAyL,EAAA,GAAAL,GAAAC,GAAAd,EACAY,GAAAnL,EACAyL,EAAAN,EAAAnL,EACAsL,GAAAtL,IACAyL,EAAAH,EAAAtL,GAIA,YAAAhE,EAAA2G,QAAAc,MAAA,kBAAAzH,EAAA2G,QAAAc,MAAA,mBAAAzH,EAAA2G,QAAAc,OACAmD,EAAAG,UAAA,qBACAH,EAAA8B,QAAA+C,GAIA,UAAAzP,EAAA2G,QAAAc,MAAA,kBAAAzH,EAAA2G,QAAAc,OACAiI,EAAA,EAEA1P,EAAA2G,QAAAe,MAAA,EACAgI,GAAA1P,EAAA2G,QAAAe,MAAA+H,EAEAC,GAAA1P,EAAA2G,QAAAe,OAAA,EAAA+H,GAGA7E,EAAAG,UAAA,SAAA4B,OAAA+C,EAAA,yBAIA,WAAA1P,EAAA2G,QAAAc,MAAA,mBAAAzH,EAAA2G,QAAAc,OACAkI,EAAA3P,EAAA6O,uBAAAW,EAEA,aAAAxP,EAAAiK,MAAAI,WACAsF,GAAAC,GAGAhF,EAAAG,UAAA,iBAAA4B,OAAAgD,EAAA,UAGA3P,EAAA2L,IAAA3L,EAAAiK,MAAAtE,MAAAiF,GAEA5K,EAAA2G,QAAAJ,UACAvG,EAAA2G,QAAAJ,SAAA5I,KAAAqC,EAAA,CACA+P,QAAA7B,EACAgB,UAAAA,EACAC,aAAAA,EACAC,SAAAA,EACAC,aAAAA,EACAC,gBAAAA,EACAC,YAAAA,EACAE,eAAAA,EACAD,mBAAAA,OAIA,CACAzQ,IAAA,WACAN,MAAA,WACAwB,KAAA+P,aACA/P,KAAAgQ,qBAjuBAxM,EAAA6D,EAAAlI,UAAAqL,GAAAC,GAAAjH,EAAA6D,EAAAoD,GAquBAxD,EAxnBA,GA6pBAT,EAAApG,YAAA6G,EACAzG,EAAA,QAAA", "file": "jarallax.min.js", "sourcesContent": ["/*!\n * Name    : Just Another Parallax [Jarallax]\n * Version : 1.12.5\n * Author  : nK <https://nkdev.info>\n * GitHub  : https://github.com/nk-o/jarallax\n */\n/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 10);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */,\n/* 1 */,\n/* 2 */\n/***/ (function(module, exports) {\n\nmodule.exports = function (callback) {\n  if (document.readyState === 'complete' || document.readyState === 'interactive') {\n    // Already ready or interactive, execute callback\n    callback.call();\n  } else if (document.attachEvent) {\n    // Old browsers\n    document.attachEvent('onreadystatechange', function () {\n      if (document.readyState === 'interactive') callback.call();\n    });\n  } else if (document.addEventListener) {\n    // Modern browsers\n    document.addEventListener('DOMContentLoaded', callback);\n  }\n};\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/* WEBPACK VAR INJECTION */(function(global) {var win;\n\nif (typeof window !== \"undefined\") {\n  win = window;\n} else if (typeof global !== \"undefined\") {\n  win = global;\n} else if (typeof self !== \"undefined\") {\n  win = self;\n} else {\n  win = {};\n}\n\nmodule.exports = win;\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(4)))\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports) {\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nvar g; // This works in non-strict mode\n\ng = function () {\n  return this;\n}();\n\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) === \"object\") g = window;\n} // g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\n\nmodule.exports = g;\n\n/***/ }),\n/* 5 */,\n/* 6 */,\n/* 7 */,\n/* 8 */,\n/* 9 */,\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\nmodule.exports = __webpack_require__(11);\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var lite_ready__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(2);\n/* harmony import */ var lite_ready__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lite_ready__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3);\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(global__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _jarallax_esm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(12);\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n\n\n // no conflict\n\nvar oldPlugin = global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].jarallax;\nglobal__WEBPACK_IMPORTED_MODULE_1__[\"window\"].jarallax = _jarallax_esm__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n\nglobal__WEBPACK_IMPORTED_MODULE_1__[\"window\"].jarallax.noConflict = function () {\n  global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].jarallax = oldPlugin;\n  return this;\n}; // jQuery support\n\n\nif ('undefined' !== typeof global__WEBPACK_IMPORTED_MODULE_1__[\"jQuery\"]) {\n  var jQueryPlugin = function jQueryPlugin() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    Array.prototype.unshift.call(args, this);\n    var res = _jarallax_esm__WEBPACK_IMPORTED_MODULE_2__[\"default\"].apply(global__WEBPACK_IMPORTED_MODULE_1__[\"window\"], args);\n    return 'object' !== _typeof(res) ? res : this;\n  };\n\n  jQueryPlugin.constructor = _jarallax_esm__WEBPACK_IMPORTED_MODULE_2__[\"default\"].constructor; // no conflict\n\n  var oldJqPlugin = global__WEBPACK_IMPORTED_MODULE_1__[\"jQuery\"].fn.jarallax;\n  global__WEBPACK_IMPORTED_MODULE_1__[\"jQuery\"].fn.jarallax = jQueryPlugin;\n\n  global__WEBPACK_IMPORTED_MODULE_1__[\"jQuery\"].fn.jarallax.noConflict = function () {\n    global__WEBPACK_IMPORTED_MODULE_1__[\"jQuery\"].fn.jarallax = oldJqPlugin;\n    return this;\n  };\n} // data-jarallax initialization\n\n\nlite_ready__WEBPACK_IMPORTED_MODULE_0___default()(function () {\n  Object(_jarallax_esm__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(document.querySelectorAll('[data-jarallax]'));\n});\n\n/***/ }),\n/* 12 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var lite_ready__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(2);\n/* harmony import */ var lite_ready__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lite_ready__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3);\n/* harmony import */ var global__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(global__WEBPACK_IMPORTED_MODULE_1__);\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return; var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n\n\nvar navigator = global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].navigator;\nvar isIE = -1 < navigator.userAgent.indexOf('MSIE ') || -1 < navigator.userAgent.indexOf('Trident/') || -1 < navigator.userAgent.indexOf('Edge/');\nvar isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n\nvar supportTransform = function () {\n  var prefixes = 'transform WebkitTransform MozTransform'.split(' ');\n  var div = document.createElement('div');\n\n  for (var i = 0; i < prefixes.length; i += 1) {\n    if (div && div.style[prefixes[i]] !== undefined) {\n      return prefixes[i];\n    }\n  }\n\n  return false;\n}();\n\nvar $deviceHelper;\n/**\n * The most popular mobile browsers changes height after page scroll and this generates image jumping.\n * We can fix it using this workaround with vh units.\n */\n\nfunction getDeviceHeight() {\n  if (!$deviceHelper && document.body) {\n    $deviceHelper = document.createElement('div');\n    $deviceHelper.style.cssText = 'position: fixed; top: -9999px; left: 0; height: 100vh; width: 0;';\n    document.body.appendChild($deviceHelper);\n  }\n\n  return ($deviceHelper ? $deviceHelper.clientHeight : 0) || global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].innerHeight || document.documentElement.clientHeight;\n} // Window height data\n\n\nvar wndH;\n\nfunction updateWndVars() {\n  if (isMobile) {\n    wndH = getDeviceHeight();\n  } else {\n    wndH = global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].innerHeight || document.documentElement.clientHeight;\n  }\n}\n\nupdateWndVars();\nglobal__WEBPACK_IMPORTED_MODULE_1__[\"window\"].addEventListener('resize', updateWndVars);\nglobal__WEBPACK_IMPORTED_MODULE_1__[\"window\"].addEventListener('orientationchange', updateWndVars);\nglobal__WEBPACK_IMPORTED_MODULE_1__[\"window\"].addEventListener('load', updateWndVars);\nlite_ready__WEBPACK_IMPORTED_MODULE_0___default()(function () {\n  updateWndVars({\n    type: 'dom-loaded'\n  });\n}); // list with all jarallax instances\n// need to render all in one scroll/resize event\n\nvar jarallaxList = []; // get all parents of the element.\n\nfunction getParents(elem) {\n  var parents = [];\n\n  while (null !== elem.parentElement) {\n    elem = elem.parentElement;\n\n    if (1 === elem.nodeType) {\n      parents.push(elem);\n    }\n  }\n\n  return parents;\n}\n\nfunction updateParallax() {\n  if (!jarallaxList.length) {\n    return;\n  }\n\n  jarallaxList.forEach(function (data, k) {\n    var instance = data.instance,\n        oldData = data.oldData;\n    var clientRect = instance.$item.getBoundingClientRect();\n    var newData = {\n      width: clientRect.width,\n      height: clientRect.height,\n      top: clientRect.top,\n      bottom: clientRect.bottom,\n      wndW: global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].innerWidth,\n      wndH: wndH\n    };\n    var isResized = !oldData || oldData.wndW !== newData.wndW || oldData.wndH !== newData.wndH || oldData.width !== newData.width || oldData.height !== newData.height;\n    var isScrolled = isResized || !oldData || oldData.top !== newData.top || oldData.bottom !== newData.bottom;\n    jarallaxList[k].oldData = newData;\n\n    if (isResized) {\n      instance.onResize();\n    }\n\n    if (isScrolled) {\n      instance.onScroll();\n    }\n  });\n  global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].requestAnimationFrame(updateParallax);\n}\n\nvar instanceID = 0; // Jarallax class\n\nvar Jarallax = /*#__PURE__*/function () {\n  function Jarallax(item, userOptions) {\n    _classCallCheck(this, Jarallax);\n\n    var self = this;\n    self.instanceID = instanceID;\n    instanceID += 1;\n    self.$item = item;\n    self.defaults = {\n      type: 'scroll',\n      // type of parallax: scroll, scale, opacity, scale-opacity, scroll-opacity\n      speed: 0.5,\n      // supported value from -1 to 2\n      imgSrc: null,\n      imgElement: '.jarallax-img',\n      imgSize: 'cover',\n      imgPosition: '50% 50%',\n      imgRepeat: 'no-repeat',\n      // supported only for background, not for <img> tag\n      keepImg: false,\n      // keep <img> tag in it's default place\n      elementInViewport: null,\n      zIndex: -100,\n      disableParallax: false,\n      disableVideo: false,\n      // video\n      videoSrc: null,\n      videoStartTime: 0,\n      videoEndTime: 0,\n      videoVolume: 0,\n      videoLoop: true,\n      videoPlayOnlyVisible: true,\n      videoLazyLoading: true,\n      // events\n      onScroll: null,\n      // function(calculations) {}\n      onInit: null,\n      // function() {}\n      onDestroy: null,\n      // function() {}\n      onCoverImage: null // function() {}\n\n    }; // prepare data-options\n\n    var dataOptions = self.$item.dataset || {};\n    var pureDataOptions = {};\n    Object.keys(dataOptions).forEach(function (key) {\n      var loweCaseOption = key.substr(0, 1).toLowerCase() + key.substr(1);\n\n      if (loweCaseOption && 'undefined' !== typeof self.defaults[loweCaseOption]) {\n        pureDataOptions[loweCaseOption] = dataOptions[key];\n      }\n    });\n    self.options = self.extend({}, self.defaults, pureDataOptions, userOptions);\n    self.pureOptions = self.extend({}, self.options); // prepare 'true' and 'false' strings to boolean\n\n    Object.keys(self.options).forEach(function (key) {\n      if ('true' === self.options[key]) {\n        self.options[key] = true;\n      } else if ('false' === self.options[key]) {\n        self.options[key] = false;\n      }\n    }); // fix speed option [-1.0, 2.0]\n\n    self.options.speed = Math.min(2, Math.max(-1, parseFloat(self.options.speed))); // prepare disableParallax callback\n\n    if ('string' === typeof self.options.disableParallax) {\n      self.options.disableParallax = new RegExp(self.options.disableParallax);\n    }\n\n    if (self.options.disableParallax instanceof RegExp) {\n      var disableParallaxRegexp = self.options.disableParallax;\n\n      self.options.disableParallax = function () {\n        return disableParallaxRegexp.test(navigator.userAgent);\n      };\n    }\n\n    if ('function' !== typeof self.options.disableParallax) {\n      self.options.disableParallax = function () {\n        return false;\n      };\n    } // prepare disableVideo callback\n\n\n    if ('string' === typeof self.options.disableVideo) {\n      self.options.disableVideo = new RegExp(self.options.disableVideo);\n    }\n\n    if (self.options.disableVideo instanceof RegExp) {\n      var disableVideoRegexp = self.options.disableVideo;\n\n      self.options.disableVideo = function () {\n        return disableVideoRegexp.test(navigator.userAgent);\n      };\n    }\n\n    if ('function' !== typeof self.options.disableVideo) {\n      self.options.disableVideo = function () {\n        return false;\n      };\n    } // custom element to check if parallax in viewport\n\n\n    var elementInVP = self.options.elementInViewport; // get first item from array\n\n    if (elementInVP && 'object' === _typeof(elementInVP) && 'undefined' !== typeof elementInVP.length) {\n      var _elementInVP = elementInVP;\n\n      var _elementInVP2 = _slicedToArray(_elementInVP, 1);\n\n      elementInVP = _elementInVP2[0];\n    } // check if dom element\n\n\n    if (!(elementInVP instanceof Element)) {\n      elementInVP = null;\n    }\n\n    self.options.elementInViewport = elementInVP;\n    self.image = {\n      src: self.options.imgSrc || null,\n      $container: null,\n      useImgTag: false,\n      // position fixed is needed for the most of browsers because absolute position have glitches\n      // on MacOS with smooth scroll there is a huge lags with absolute position - https://github.com/nk-o/jarallax/issues/75\n      // on mobile devices better scrolled with absolute position\n      position: /iPad|iPhone|iPod|Android/.test(navigator.userAgent) ? 'absolute' : 'fixed'\n    };\n\n    if (self.initImg() && self.canInitParallax()) {\n      self.init();\n    }\n  } // add styles to element\n  // eslint-disable-next-line class-methods-use-this\n\n\n  _createClass(Jarallax, [{\n    key: \"css\",\n    value: function css(el, styles) {\n      if ('string' === typeof styles) {\n        return global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].getComputedStyle(el).getPropertyValue(styles);\n      } // add transform property with vendor prefix\n\n\n      if (styles.transform && supportTransform) {\n        styles[supportTransform] = styles.transform;\n      }\n\n      Object.keys(styles).forEach(function (key) {\n        el.style[key] = styles[key];\n      });\n      return el;\n    } // Extend like jQuery.extend\n    // eslint-disable-next-line class-methods-use-this\n\n  }, {\n    key: \"extend\",\n    value: function extend(out) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      out = out || {};\n      Object.keys(args).forEach(function (i) {\n        if (!args[i]) {\n          return;\n        }\n\n        Object.keys(args[i]).forEach(function (key) {\n          out[key] = args[i][key];\n        });\n      });\n      return out;\n    } // get window size and scroll position. Useful for extensions\n    // eslint-disable-next-line class-methods-use-this\n\n  }, {\n    key: \"getWindowData\",\n    value: function getWindowData() {\n      return {\n        width: global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].innerWidth || document.documentElement.clientWidth,\n        height: wndH,\n        y: document.documentElement.scrollTop\n      };\n    } // Jarallax functions\n\n  }, {\n    key: \"initImg\",\n    value: function initImg() {\n      var self = this; // find image element\n\n      var $imgElement = self.options.imgElement;\n\n      if ($imgElement && 'string' === typeof $imgElement) {\n        $imgElement = self.$item.querySelector($imgElement);\n      } // check if dom element\n\n\n      if (!($imgElement instanceof Element)) {\n        if (self.options.imgSrc) {\n          $imgElement = new Image();\n          $imgElement.src = self.options.imgSrc;\n        } else {\n          $imgElement = null;\n        }\n      }\n\n      if ($imgElement) {\n        if (self.options.keepImg) {\n          self.image.$item = $imgElement.cloneNode(true);\n        } else {\n          self.image.$item = $imgElement;\n          self.image.$itemParent = $imgElement.parentNode;\n        }\n\n        self.image.useImgTag = true;\n      } // true if there is img tag\n\n\n      if (self.image.$item) {\n        return true;\n      } // get image src\n\n\n      if (null === self.image.src) {\n        self.image.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';\n        self.image.bgImage = self.css(self.$item, 'background-image');\n      }\n\n      return !(!self.image.bgImage || 'none' === self.image.bgImage);\n    }\n  }, {\n    key: \"canInitParallax\",\n    value: function canInitParallax() {\n      return supportTransform && !this.options.disableParallax();\n    }\n  }, {\n    key: \"init\",\n    value: function init() {\n      var self = this;\n      var containerStyles = {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        overflow: 'hidden'\n      };\n      var imageStyles = {\n        pointerEvents: 'none',\n        transformStyle: 'preserve-3d',\n        backfaceVisibility: 'hidden',\n        willChange: 'transform,opacity'\n      };\n\n      if (!self.options.keepImg) {\n        // save default user styles\n        var curStyle = self.$item.getAttribute('style');\n\n        if (curStyle) {\n          self.$item.setAttribute('data-jarallax-original-styles', curStyle);\n        }\n\n        if (self.image.useImgTag) {\n          var curImgStyle = self.image.$item.getAttribute('style');\n\n          if (curImgStyle) {\n            self.image.$item.setAttribute('data-jarallax-original-styles', curImgStyle);\n          }\n        }\n      } // set relative position and z-index to the parent\n\n\n      if ('static' === self.css(self.$item, 'position')) {\n        self.css(self.$item, {\n          position: 'relative'\n        });\n      }\n\n      if ('auto' === self.css(self.$item, 'z-index')) {\n        self.css(self.$item, {\n          zIndex: 0\n        });\n      } // container for parallax image\n\n\n      self.image.$container = document.createElement('div');\n      self.css(self.image.$container, containerStyles);\n      self.css(self.image.$container, {\n        'z-index': self.options.zIndex\n      }); // fix for IE https://github.com/nk-o/jarallax/issues/110\n\n      if (isIE) {\n        self.css(self.image.$container, {\n          opacity: 0.9999\n        });\n      }\n\n      self.image.$container.setAttribute('id', \"jarallax-container-\".concat(self.instanceID));\n      self.$item.appendChild(self.image.$container); // use img tag\n\n      if (self.image.useImgTag) {\n        imageStyles = self.extend({\n          'object-fit': self.options.imgSize,\n          'object-position': self.options.imgPosition,\n          // support for plugin https://github.com/bfred-it/object-fit-images\n          'font-family': \"object-fit: \".concat(self.options.imgSize, \"; object-position: \").concat(self.options.imgPosition, \";\"),\n          'max-width': 'none'\n        }, containerStyles, imageStyles); // use div with background image\n      } else {\n        self.image.$item = document.createElement('div');\n\n        if (self.image.src) {\n          imageStyles = self.extend({\n            'background-position': self.options.imgPosition,\n            'background-size': self.options.imgSize,\n            'background-repeat': self.options.imgRepeat,\n            'background-image': self.image.bgImage || \"url(\\\"\".concat(self.image.src, \"\\\")\")\n          }, containerStyles, imageStyles);\n        }\n      }\n\n      if ('opacity' === self.options.type || 'scale' === self.options.type || 'scale-opacity' === self.options.type || 1 === self.options.speed) {\n        self.image.position = 'absolute';\n      } // 1. Check if one of parents have transform style (without this check, scroll transform will be inverted if used parallax with position fixed)\n      //    discussion - https://github.com/nk-o/jarallax/issues/9\n      // 2. Check if parents have overflow scroll\n\n\n      if ('fixed' === self.image.position) {\n        var $parents = getParents(self.$item).filter(function (el) {\n          var styles = global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].getComputedStyle(el);\n          var parentTransform = styles['-webkit-transform'] || styles['-moz-transform'] || styles.transform;\n          var overflowRegex = /(auto|scroll)/;\n          return parentTransform && 'none' !== parentTransform || overflowRegex.test(styles.overflow + styles['overflow-y'] + styles['overflow-x']);\n        });\n        self.image.position = $parents.length ? 'absolute' : 'fixed';\n      } // add position to parallax block\n\n\n      imageStyles.position = self.image.position; // insert parallax image\n\n      self.css(self.image.$item, imageStyles);\n      self.image.$container.appendChild(self.image.$item); // set initial position and size\n\n      self.onResize();\n      self.onScroll(true); // call onInit event\n\n      if (self.options.onInit) {\n        self.options.onInit.call(self);\n      } // remove default user background\n\n\n      if ('none' !== self.css(self.$item, 'background-image')) {\n        self.css(self.$item, {\n          'background-image': 'none'\n        });\n      }\n\n      self.addToParallaxList();\n    } // add to parallax instances list\n\n  }, {\n    key: \"addToParallaxList\",\n    value: function addToParallaxList() {\n      jarallaxList.push({\n        instance: this\n      });\n\n      if (1 === jarallaxList.length) {\n        global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].requestAnimationFrame(updateParallax);\n      }\n    } // remove from parallax instances list\n\n  }, {\n    key: \"removeFromParallaxList\",\n    value: function removeFromParallaxList() {\n      var self = this;\n      jarallaxList.forEach(function (data, key) {\n        if (data.instance.instanceID === self.instanceID) {\n          jarallaxList.splice(key, 1);\n        }\n      });\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      var self = this;\n      self.removeFromParallaxList(); // return styles on container as before jarallax init\n\n      var originalStylesTag = self.$item.getAttribute('data-jarallax-original-styles');\n      self.$item.removeAttribute('data-jarallax-original-styles'); // null occurs if there is no style tag before jarallax init\n\n      if (!originalStylesTag) {\n        self.$item.removeAttribute('style');\n      } else {\n        self.$item.setAttribute('style', originalStylesTag);\n      }\n\n      if (self.image.useImgTag) {\n        // return styles on img tag as before jarallax init\n        var originalStylesImgTag = self.image.$item.getAttribute('data-jarallax-original-styles');\n        self.image.$item.removeAttribute('data-jarallax-original-styles'); // null occurs if there is no style tag before jarallax init\n\n        if (!originalStylesImgTag) {\n          self.image.$item.removeAttribute('style');\n        } else {\n          self.image.$item.setAttribute('style', originalStylesTag);\n        } // move img tag to its default position\n\n\n        if (self.image.$itemParent) {\n          self.image.$itemParent.appendChild(self.image.$item);\n        }\n      } // remove additional dom elements\n\n\n      if (self.$clipStyles) {\n        self.$clipStyles.parentNode.removeChild(self.$clipStyles);\n      }\n\n      if (self.image.$container) {\n        self.image.$container.parentNode.removeChild(self.image.$container);\n      } // call onDestroy event\n\n\n      if (self.options.onDestroy) {\n        self.options.onDestroy.call(self);\n      } // delete jarallax from item\n\n\n      delete self.$item.jarallax;\n    } // it will remove some image overlapping\n    // overlapping occur due to an image position fixed inside absolute position element\n\n  }, {\n    key: \"clipContainer\",\n    value: function clipContainer() {\n      // needed only when background in fixed position\n      if ('fixed' !== this.image.position) {\n        return;\n      }\n\n      var self = this;\n      var rect = self.image.$container.getBoundingClientRect();\n      var width = rect.width,\n          height = rect.height;\n\n      if (!self.$clipStyles) {\n        self.$clipStyles = document.createElement('style');\n        self.$clipStyles.setAttribute('type', 'text/css');\n        self.$clipStyles.setAttribute('id', \"jarallax-clip-\".concat(self.instanceID));\n        var head = document.head || document.getElementsByTagName('head')[0];\n        head.appendChild(self.$clipStyles);\n      } // clip is used for old browsers.\n      // clip-path for modern browsers (also fixes Safari v14 bug https://github.com/nk-o/jarallax/issues/181 ).\n\n\n      var styles = \"#jarallax-container-\".concat(self.instanceID, \" {\\n            clip: rect(0 \").concat(width, \"px \").concat(height, \"px 0);\\n            clip: rect(0, \").concat(width, \"px, \").concat(height, \"px, 0);\\n            -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);\\n            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);\\n        }\"); // add clip styles inline (this method need for support IE8 and less browsers)\n\n      if (self.$clipStyles.styleSheet) {\n        self.$clipStyles.styleSheet.cssText = styles;\n      } else {\n        self.$clipStyles.innerHTML = styles;\n      }\n    }\n  }, {\n    key: \"coverImage\",\n    value: function coverImage() {\n      var self = this;\n      var rect = self.image.$container.getBoundingClientRect();\n      var contH = rect.height;\n      var speed = self.options.speed;\n      var isScroll = 'scroll' === self.options.type || 'scroll-opacity' === self.options.type;\n      var scrollDist = 0;\n      var resultH = contH;\n      var resultMT = 0; // scroll parallax\n\n      if (isScroll) {\n        // scroll distance and height for image\n        if (0 > speed) {\n          scrollDist = speed * Math.max(contH, wndH);\n\n          if (wndH < contH) {\n            scrollDist -= speed * (contH - wndH);\n          }\n        } else {\n          scrollDist = speed * (contH + wndH);\n        } // size for scroll parallax\n\n\n        if (1 < speed) {\n          resultH = Math.abs(scrollDist - wndH);\n        } else if (0 > speed) {\n          resultH = scrollDist / speed + Math.abs(scrollDist);\n        } else {\n          resultH += (wndH - contH) * (1 - speed);\n        }\n\n        scrollDist /= 2;\n      } // store scroll distance\n\n\n      self.parallaxScrollDistance = scrollDist; // vertical center\n\n      if (isScroll) {\n        resultMT = (wndH - resultH) / 2;\n      } else {\n        resultMT = (contH - resultH) / 2;\n      } // apply result to item\n\n\n      self.css(self.image.$item, {\n        height: \"\".concat(resultH, \"px\"),\n        marginTop: \"\".concat(resultMT, \"px\"),\n        left: 'fixed' === self.image.position ? \"\".concat(rect.left, \"px\") : '0',\n        width: \"\".concat(rect.width, \"px\")\n      }); // call onCoverImage event\n\n      if (self.options.onCoverImage) {\n        self.options.onCoverImage.call(self);\n      } // return some useful data. Used in the video cover function\n\n\n      return {\n        image: {\n          height: resultH,\n          marginTop: resultMT\n        },\n        container: rect\n      };\n    }\n  }, {\n    key: \"isVisible\",\n    value: function isVisible() {\n      return this.isElementInViewport || false;\n    }\n  }, {\n    key: \"onScroll\",\n    value: function onScroll(force) {\n      var self = this;\n      var rect = self.$item.getBoundingClientRect();\n      var contT = rect.top;\n      var contH = rect.height;\n      var styles = {}; // check if in viewport\n\n      var viewportRect = rect;\n\n      if (self.options.elementInViewport) {\n        viewportRect = self.options.elementInViewport.getBoundingClientRect();\n      }\n\n      self.isElementInViewport = 0 <= viewportRect.bottom && 0 <= viewportRect.right && viewportRect.top <= wndH && viewportRect.left <= global__WEBPACK_IMPORTED_MODULE_1__[\"window\"].innerWidth; // stop calculations if item is not in viewport\n\n      if (force ? false : !self.isElementInViewport) {\n        return;\n      } // calculate parallax helping variables\n\n\n      var beforeTop = Math.max(0, contT);\n      var beforeTopEnd = Math.max(0, contH + contT);\n      var afterTop = Math.max(0, -contT);\n      var beforeBottom = Math.max(0, contT + contH - wndH);\n      var beforeBottomEnd = Math.max(0, contH - (contT + contH - wndH));\n      var afterBottom = Math.max(0, -contT + wndH - contH);\n      var fromViewportCenter = 1 - 2 * ((wndH - contT) / (wndH + contH)); // calculate on how percent of section is visible\n\n      var visiblePercent = 1;\n\n      if (contH < wndH) {\n        visiblePercent = 1 - (afterTop || beforeBottom) / contH;\n      } else if (beforeTopEnd <= wndH) {\n        visiblePercent = beforeTopEnd / wndH;\n      } else if (beforeBottomEnd <= wndH) {\n        visiblePercent = beforeBottomEnd / wndH;\n      } // opacity\n\n\n      if ('opacity' === self.options.type || 'scale-opacity' === self.options.type || 'scroll-opacity' === self.options.type) {\n        styles.transform = 'translate3d(0,0,0)';\n        styles.opacity = visiblePercent;\n      } // scale\n\n\n      if ('scale' === self.options.type || 'scale-opacity' === self.options.type) {\n        var scale = 1;\n\n        if (0 > self.options.speed) {\n          scale -= self.options.speed * visiblePercent;\n        } else {\n          scale += self.options.speed * (1 - visiblePercent);\n        }\n\n        styles.transform = \"scale(\".concat(scale, \") translate3d(0,0,0)\");\n      } // scroll\n\n\n      if ('scroll' === self.options.type || 'scroll-opacity' === self.options.type) {\n        var positionY = self.parallaxScrollDistance * fromViewportCenter; // fix if parallax block in absolute position\n\n        if ('absolute' === self.image.position) {\n          positionY -= contT;\n        }\n\n        styles.transform = \"translate3d(0,\".concat(positionY, \"px,0)\");\n      }\n\n      self.css(self.image.$item, styles); // call onScroll event\n\n      if (self.options.onScroll) {\n        self.options.onScroll.call(self, {\n          section: rect,\n          beforeTop: beforeTop,\n          beforeTopEnd: beforeTopEnd,\n          afterTop: afterTop,\n          beforeBottom: beforeBottom,\n          beforeBottomEnd: beforeBottomEnd,\n          afterBottom: afterBottom,\n          visiblePercent: visiblePercent,\n          fromViewportCenter: fromViewportCenter\n        });\n      }\n    }\n  }, {\n    key: \"onResize\",\n    value: function onResize() {\n      this.coverImage();\n      this.clipContainer();\n    }\n  }]);\n\n  return Jarallax;\n}(); // global definition\n\n\nvar plugin = function plugin(items, options) {\n  // check for dom element\n  // thanks: http://stackoverflow.com/questions/384286/javascript-isdom-how-do-you-check-if-a-javascript-object-is-a-dom-object\n  if ('object' === (typeof HTMLElement === \"undefined\" ? \"undefined\" : _typeof(HTMLElement)) ? items instanceof HTMLElement : items && 'object' === _typeof(items) && null !== items && 1 === items.nodeType && 'string' === typeof items.nodeName) {\n    items = [items];\n  }\n\n  var len = items.length;\n  var k = 0;\n  var ret;\n\n  for (var _len2 = arguments.length, args = new Array(_len2 > 2 ? _len2 - 2 : 0), _key2 = 2; _key2 < _len2; _key2++) {\n    args[_key2 - 2] = arguments[_key2];\n  }\n\n  for (k; k < len; k += 1) {\n    if ('object' === _typeof(options) || 'undefined' === typeof options) {\n      if (!items[k].jarallax) {\n        items[k].jarallax = new Jarallax(items[k], options);\n      }\n    } else if (items[k].jarallax) {\n      // eslint-disable-next-line prefer-spread\n      ret = items[k].jarallax[options].apply(items[k].jarallax, args);\n    }\n\n    if ('undefined' !== typeof ret) {\n      return ret;\n    }\n  }\n\n  return items;\n};\n\nplugin.constructor = Jarallax;\n/* harmony default export */ __webpack_exports__[\"default\"] = (plugin);\n\n/***/ })\n/******/ ]);"]}