<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Framework\Escaper;
use Magento\Framework\Pricing\Render;
use Magento\ProductAlert\Block\Email\Stock;

/** @var $escaper Escaper */
/** @var $block Stock */

?>
<?php if ($_products = $block->getProducts()): ?>
    <p><?= $escaper->escapeHtml(__('In stock alert! We wanted you to know that these products are now available:')) ?></p>
    <table>
    <?php foreach ($_products as $_product): ?>
        <?php
            $urlImage = "media/catalog/product".$_product->getImage();
            $productImage = $block->getImageResize($urlImage);
        ?>
        <tr>
            <td class="col photo">
                <a href="<?= $escaper->escapeUrl($_product->getProductUrl()) ?>" title="<?= $escaper->escapeHtml($_product->getName()) ?>" class="product photo">
                    <?= $productImage ?>
                </a>
            </td>
            <td class="col item">
                <p>
                    <strong class="product name">
                        <a href="<?= $escaper->escapeUrl($_product->getProductUrl()) ?>"><?= $escaper->escapeHtml($_product->getName()) ?></a>
                    </strong>
                </p>
                <?php if ($shortDescription = $block->getFilteredContent($_product->getShortDescription())): ?>
                    <p><small><?= /* @noEscape */  $shortDescription ?></small></p>
                <?php endif; ?>
                <?=
                $block->getProductPriceHtml(
                    $_product,
                    FinalPrice::PRICE_CODE,
                    Render::ZONE_EMAIL,
                    [
                        'display_label' => __('Price:')
                    ]
                );
                ?>
                <p><small><a href="<?= $escaper->escapeUrl($block->getProductUnsubscribeUrl($_product->getId())) ?>"><?= $escaper->escapeHtml(__('Click here to stop alerts for this product.')) ?></a></small></p>
            </td>
        </tr>
    <?php endforeach; ?>
    </table>
    <p><a href="<?= $escaper->escapeUrl($block->getUnsubscribeUrl()) ?>"><?= $escaper->escapeHtml(__('Unsubscribe from all stock alerts')) ?></a></p>
<?php endif; ?>
