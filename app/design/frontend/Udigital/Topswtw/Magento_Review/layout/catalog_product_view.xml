<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="product.detail.page">
            <block class="Magento\Review\Block\Product\View" name="review_list"
                   template="Magento_Review::product/view/list.phtml"
                   ifconfig="catalog/review/active">
                <block class="Magento\Theme\Block\Html\Pager" name="review_list.toolbar" ifconfig="catalog/review/active">
                    <arguments>
                        <argument name="pagination_url_anchor" xsi:type="string">customer-review-list</argument>
                    </arguments>
                </block>
            </block>
            <block class="Magento\Review\Block\Form"
                   name="product.review.form"
                   as="review_form"
                   template="Magento_Review::form.phtml"
                   ifconfig="catalog/review/active">
                <container name="product.review.form.fields.before"
                           as="form_fields_before"
                           label="Review Form Fields Before"
                />
            </block>
        </referenceBlock>
    </body>
</page>
