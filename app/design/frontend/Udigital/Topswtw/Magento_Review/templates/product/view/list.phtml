<?php

/**
 * Copyright Elgentos. All rights reserved.
 * https://elgentos.nl/
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\View\Helper\SecureHtmlRenderer;
use Magento\Framework\Escaper;
use Magento\Review\Block\Product\View as ProductReview;
use Magento\Theme\Block\Html\Pager;

/** @var ProductReview $block */
/** @var SecureHtmlRenderer $secureRenderer */
/** @var Escaper $escaper */
/** @var Pager $toolbar */
/** @var ViewModelRegistry $viewModels */

$heroicons = $viewModels->require(HeroiconsOutline::class);
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
$reviewCollection = $block->getReviewsCollection();

if ($toolbar = $block->getChildBlock('review_list.toolbar')) {
    $toolbar->setCollection($reviewCollection);
}

$reviewCollection->load()->addRateVotes();
$items = $reviewCollection->getItems();
$format = $block->getDateFormat() ?: IntlDateFormatter::MEDIUM;
$product = $block->getProduct();
?>

<?php if (count($items)): ?>
    <div class="items-center grow" id="customer-review-list">
        <div class="px-6 flex md:flex-row justify-between">
            <?php if (!$block->getHideTitle()): ?>
                <h3 class="text-gray-900 font-bold text-left basis-3/4 text-[18px] text-[#002C4B]">
                    <?= $escaper->escapeHtml(__('Laatste reviews')) ?>
                </h3>
                <a href="#" class="basis-1/4 sm:basis-1/2 flex justify-end items-center text-[#2EB6ED]" id="all-bekijk-alle-reviews">
                    <span><?= $escaper->escapeHtml(__('Bekijk alle reviews')); ?></span>
                    <?= $heroiconsSolid->chevronRightHtml('w-5 h-5'); ?>
                </a>
            <?php endif ?>
        </div>

        <div class="flex flex-wrap w-full py-3 justify-between text-[#002C4B] customer-review-list-container">
            <?php
            $count = count($items);
            $visibleItems = array_slice($items, max(0, $count - 2), 2);

            foreach ($visibleItems as $review): ?>
                <div class="card border-b p-6 pb-4 border-container rounded-xl review-list">

                    <div class="flex flex-wrap">
                        <div class="left-latest-review">
                            <?php if (count($review->getRatingVotes())): ?>
                                <div class="table">
                                    <?php foreach ($review->getRatingVotes() as $vote):
                                        $rating = $vote->getPercent();
                                        $ratingSteps = 5;
                                        $starsFilled = is_numeric($rating) ? floor($rating / 100 * $ratingSteps) : 0;
                                        $starsEmpty = $ratingSteps - $starsFilled;
                                        ?>
                                        <div class="table-row">
                                            <?= $escaper->escapeHtml($vote->getRatingCode()) ?>
                                            <div class="flex items-center">
                                                <?php for ($i = 0; $i < $starsFilled; $i++): ?>
                                                    <?= $heroiconsSolid->starHtml('text-yellow-400 w-[17px] h-[16px]'); ?>
                                                <?php endfor; ?>
                                                <?php for ($i = 0; $i < $starsEmpty; $i++): ?>
                                                    <?= $heroiconsSolid->starHtml('text-gray-400 w-[17px] h-[16px]'); ?>
                                                <?php endfor; ?>
                                                <div class="ml-2 md:text-[16px] text-[14px]">
                                                    <?= $escaper->escapeHtml("{$starsFilled} / {$ratingSteps}") ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="right-latest-review flex-grow flex justify-end flex-wrap text-right text-base text-[#A2AFBD]">
                            <span class="w-full"><?= $escaper->escapeHtml($review->getNickname()) ?></span>
                            <time datetime="<?= $escaper->escapeHtmlAttr($block->formatDate($review->getCreatedAt(), $format)) ?>">
                                <?= $escaper->escapeHtml($block->formatDate($review->getCreatedAt(), $format)) ?>
                            </time>
                        </div>
                    </div>

                    <div class="font-bold text-base my-4">
                        <?= $escaper->escapeHtml($review->getTitle()) ?>
                    </div>

                    <div class="mt-4 md:text-[16px] text-[13px]">
                        <?= /* @noEscape */ nl2br($escaper->escapeHtml($review->getDetail())) ?>
                    </div>

                    <div class="mt-4 reviewer-name-mobile md:text-[16px] text-[13px]">
                        <span>
                            <?= $escaper->escapeHtml($review->getNickname()) ?> |
                            <time datetime="<?= $escaper->escapeHtmlAttr($block->formatDate($review->getCreatedAt(), $format)) ?>">
                                <?= $escaper->escapeHtml($block->formatDate($review->getCreatedAt(), $format)) ?>
                            </time>
                        </span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif; ?>
