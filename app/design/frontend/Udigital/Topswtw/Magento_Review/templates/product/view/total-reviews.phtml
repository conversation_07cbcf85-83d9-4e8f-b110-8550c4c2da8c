<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\View\Helper\SecureHtmlRenderer;
use Magento\Framework\Escaper;
use Magento\Review\Block\Product\View as ProductReview;
use Magento\Theme\Block\Html\Pager;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var ProductReview $block */
/** @var SecureHtmlRenderer $secureRenderer */
/** @var Escaper $escaper */
/** @var Pager $toolbar */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$reviewCollection = $block->getReviewsCollection();

// Note: Setting the collection on the toolbar block applies pagination, so it has to happen before collection loading.
if ($toolbar = $block->getChildBlock('review_list.toolbar')) {
    $toolbar->setCollection($reviewCollection);
}
$reviewCollection->load()->addRateVotes();

$items = $reviewCollection->getItems();
$format = $block->getDateFormat() ?: \IntlDateFormatter::MEDIUM;
// $arr_review[0][0] , [0] first for star ( 1, 2, 3, 4, 5), ( [0][0] = star 1, value star )
$arr_review = [0,0,0,0,0];
$total_review = 0;
?>
<?php if (count($items)): ?>
    <?php foreach ($items as $review): ?>
        <?php if (count($review->getRatingVotes())): ?>
            <?php foreach ($review->getRatingVotes() as $vote): ?>
                <?php
                $rating = $vote->getPercent();
                $ratingSteps = 5;
                $starsFilled = is_numeric($rating) ? floor($rating / 100 * $ratingSteps) : 0;
                $starsEmpty = floor($ratingSteps - $starsFilled);
                if ($starsFilled == 1) {
                    $arr_review[0] +=  1;
                } elseif ($starsFilled == 2) {
                    $arr_review[1] +=  1;
                } elseif ($starsFilled == 3) {
                    $arr_review[2] +=  1;
                } elseif ($starsFilled == 4) {
                    $arr_review[3] +=  1;
                } elseif ($starsFilled == 5) {
                    $arr_review[4] +=  1;
                }
                 endforeach;
                 $total_review +=1;
            ?>
        <?php endif; ?>
    <?php endforeach; ?>
    <div class="card w-full border-b p-5 pb-4 mt-4 border-container last:border-0 last:mb-0 total-reviews rounded-xl" x-show="statusReview" >
        <div class="flex flex-wrap">
            <div class="left-latest-review">
                <div class="table">
                    <?php for ($atas = 1; $atas < 2; $atas++): ?>
                        <?php
                        $total_all_review = ($arr_review[4] * 5 +
                                            $arr_review[3] * 4 +
                                            $arr_review[2] * 3 +
                                            $arr_review[1] * 2 +
                                            $arr_review[0] * 1) / $total_review;
                        // $rating = 90;
                        $ratingSteps = 5;
                        $starsFilled = is_numeric($rating) ?  floor($total_all_review) : 0;// floor($rating / 100 * $ratingSteps) : 0;
                        $starsEmpty = floor($ratingSteps - $starsFilled);
                        ?>
                        <div class="table-row" i>
                            <span class="hidden"><?=
                                                                        /** @noEscape */
                                                                        $starsFilled; ?></span>
                            <div class="flex flex-row place-items-center">
                                <?php $i = 0; ?>
                                <?php while ($i < $starsFilled): ?>
                                    <?= $heroiconsSolid->starHtml('!text-yellow-400 w-[17px] h-[16px]'); ?>
                                    <?php $i++; ?>
                                <?php endwhile; ?>
                                <?php $i = 0; ?>
                                <?php while ($i < $starsEmpty): ?>
                                    <?= $heroiconsSolid->starHtml('!text-gray-400 w-[17px] h-[16px]'); ?>
                                    <?php $i++; ?>
                                <?php endwhile; ?>
                                <div>
                                    <span>
                                        <?=  $escaper->escapeHtml($total_all_review ." / ". $ratingSteps) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
            <div class="right-latest-review flex-grow flex justify-end flex-wrap">
                <span class="review-totals text-base w-full text-left ml-[3px] !text-[#002C4B]">
                    <?= $escaper->escapeHtml("(".$total_review." ".__("reviews").")") ?>
                </span>
            </div>
        </div>

        <div class="text-base my-4 grid md:grid-cols-[20%_48%_24%] grid-cols-[24%_46%_25%] gap-1 place-content-between review-container">
            <ol class="ol-stars-text">
                <li class="pl-1 flex flex-wrap mb-3" style="color:#002C4B;"><span>5 sterren</span></li>
                <li class="pl-1 flex flex-wrap mb-3" style="color:#002C4B;"><span>4 sterren</span></li>
                <li class="pl-1 flex flex-wrap mb-3" style="color:#002C4B;"><span>3 sterren</span></li>
                <li class="pl-1 flex flex-wrap mb-3" style="color:#002C4B;"><span>2 sterren</span></li>
                <li class="pl-1 flex flex-wrap mb-3" style="color:#002C4B;"><span>1 ster</span></li>
            </ol>
            <ol class="star-progress-review">
                <li class="pl-1 mb-3 h-6 flex items-center">
                    <div class="h-1 w-full rounded-full" style="background:#FFFF; color:#002C4B;">
                        <div class="h-1 rounded-full  transition-all on-progress-bar h-[3px] bg-[#002C4B]" style="width: <?= ($arr_review[4] / $total_review * 100); ?>%;">
                        </div>
                    </div>
                </li>
                <li class="pl-1 mb-3 h-6 flex items-center">
                    <div class="h-1 w-full rounded-full" style="background:#FFFF; color:#002C4B;">
                        <div class="h-1 rounded-full  transition-all on-progress-bar h-[3px] bg-[#002C4B]" style="width: <?= ($arr_review[3] / $total_review * 100); ?>%;">
                        </div>
                    </div>
                </li>
                <li class="pl-1 mb-3 h-6 flex items-center">
                    <div class="h-1 w-full rounded-full" style="background:#FFFF; color:#002C4B;">
                        <div class="h-1 rounded-full  transition-all on-progress-bar h-[3px] bg-[#002C4B]" style="width: <?= ($arr_review[2] / $total_review * 100); ?>%;">
                        </div>
                    </div>
                </li>
                <li class="pl-1 mb-3 h-6 flex items-center">
                    <div class="h-1 w-full rounded-full" style="background:#FFFF; color:#002C4B;">
                        <div class="h-1 rounded-full  transition-all on-progress-bar h-[3px] bg-[#002C4B]" style="width: <?= ($arr_review[1] / $total_review * 100); ?>%;">
                        </div>
                    </div>
                </li>
                <li class="pl-1 mb-3 h-6 flex items-center">
                    <div class="h-1 w-full rounded-full" style="background:#FFFF; color:#002C4B;">
                        <div class="h-1 rounded-full  transition-all on-progress-bar h-[3px] bg-[#002C4B]" style="width: <?= ($arr_review[0] / $total_review * 100); ?>%;">
                        </div>
                    </div>
                </li>
            </ol>
            <ol class="ol-stars-reviews">
                <li class="pl-1 flex flex-wrap place-content-end mb-3" style="color:#002C4B;">
                    <span>
                        <?= $escaper->escapeHtml($arr_review[4]." ".__("reviews")) ?>
                    </span>
                </li>
                <li class="pl-1 flex flex-wrap place-content-end mb-3" style="color:#002C4B;">
                    <span>
                        <?= $escaper->escapeHtml($arr_review[3]." ".__("reviews")) ?>
                    </span>
                </li>
                <li class="pl-1 flex flex-wrap place-content-end mb-3" style="color:#002C4B;">
                    <span>
                        <?= $escaper->escapeHtml($arr_review[2]." ".__("reviews")) ?>
                    </span>
                </li>
                <li class="pl-1 flex flex-wrap place-content-end mb-3" style="color:#002C4B;">
                    <span>
                        <?= $escaper->escapeHtml($arr_review[1]." ".__("reviews")) ?>
                    </span>
                </li>
                <li class="pl-1 flex flex-wrap place-content-end mb-3" style="color:#002C4B;">
                    <span>
                        <?= $escaper->escapeHtml($arr_review[0]." ".__("reviews")) ?>
                    </span>
                </li>
            </ol>
        </div>
    </div>
<?php endif; ?>
