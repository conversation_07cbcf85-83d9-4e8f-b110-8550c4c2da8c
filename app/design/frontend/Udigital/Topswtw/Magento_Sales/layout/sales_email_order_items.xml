<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" label="Email Order Items List" design_abstraction="custom">
    <update handle="sales_email_order_renderers"/>
    <update handle="sales_email_item_price"/>
    <body>
        <block class="Magento\Sales\Block\Order\Email\Items" name="items" template="Magento_Sales::email/items.phtml" cacheable="false">
            <arguments>
                <argument name="order_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Order</argument>
            </arguments>
            <block class="Magento\Framework\View\Element\RendererList" name="sales.email.order.renderers" as="renderer.list"/>
            <block class="Magento\Sales\Block\Order\Totals" name="order_totals" template="Magento_Sales::order/totals.phtml">
                <arguments>
                    <argument name="label_properties" xsi:type="string">colspan="4"</argument>
                </arguments>
                <block class="Udigital\CategoryAttribute\Block\Sales\Totals" name="mollie_payment_fee" as="mollie_payment_fee" />
                <block class="Magento\Tax\Block\Sales\Order\Tax" name="tax" template="Magento_Tax::order/tax.phtml">
                    <action method="setIsPlaneMode">
                        <argument name="value" xsi:type="string">1</argument>
                    </action>
                </block>
            </block>
        </block>
        <block class="Magento\Framework\View\Element\Template" name="additional.product.info" template="Magento_Theme::template.phtml"/>
    </body>
</page>
