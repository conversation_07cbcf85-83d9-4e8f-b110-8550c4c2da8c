<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <referenceBlock name="sales.order.view">
        <arguments>
            <argument name="order_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Order</argument>
        </arguments>
        <referenceBlock name="sales.order.info">
            <arguments>
                <argument name="order_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Order</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="order_items">
            <arguments>
                <argument name="order_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Order</argument>
            </arguments>
        </referenceBlock>
    </referenceBlock>
    <move element="sales.order.info" destination="content" after="sales.order.view"/>
    <body>
        <referenceBlock name="order.date">
            <block class="Magento\Shipping\Block\Tracking\Link" name="tracking-info-link" template="Magento_Shipping::tracking/link.phtml">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Track your order</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="sales.order.info">
                <block class="Magento\Sales\Block\Order\Totals" name="order_totals" template="Magento_Sales::order/totals.phtml">
                    <arguments>
                        <argument name="label_properties" xsi:type="string">colspan="4" class="mark md:text-xl text-left pl-4 py-1"</argument>
                        <argument name="value_properties" xsi:type="string">class="amount md:text-3xl py-1"</argument>
                    </arguments>
                    <block class="Magento\Tax\Block\Sales\Order\Tax" name="tax" template="Magento_Tax::order/tax.phtml"/>
                </block>
        </referenceBlock>
    </body>
</page>
