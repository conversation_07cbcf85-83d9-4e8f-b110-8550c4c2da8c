<?php

/**
 * Copyright © Magento, Inc. All rights reserved.
*/

declare(strict_types=1);

use Magento\Sales\Block\Order\Email\Creditmemo\Items;
use Magento\Framework\Escaper;

/**
 * @var Items $block
 * @var Escaper $escaper
 */

$_creditmemo = $block->getCreditmemo();
$_order      = $block->getOrder();

?>
<?php if ($_creditmemo && $_order): ?>
    <table class="email-items">
        <thead>
        <tr>
            <th class="item-info">
                <?= $escaper->escapeHtml(__('Items')) ?>
            </th>
            <th class="item-qty">
                <?= $escaper->escapeHtml(__('Qty')) ?>
            </th>
            <th class="item-subtotal">
                <?= $escaper->escapeHtml(__('Subtotal')) ?>
            </th>
        </tr>
        </thead>
        <?php foreach ($_creditmemo->getAllItems() as $_item): ?>
            <?php if (!$_item->getOrderItem()->getParentItem()): ?>
                <tbody>
                <?= $block->getItemHtml($_item) ?>
                </tbody>
            <?php endif; ?>
        <?php endforeach; ?>
        <tfoot class="order-totals">
        <?= $block->getChildHtml('creditmemo_totals') ?>
        </tfoot>
    </table>
<?php endif; ?>
