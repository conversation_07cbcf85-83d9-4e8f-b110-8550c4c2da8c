<?php

use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Email\Items;

/** @var Items $block */
/** @var Escaper $escaper */

$_order = $block->getOrder();
?>

<?php if ($_order): ?>
    <?php $_items = $_order->getAllItems(); ?>
    <div class="wrapper-email-items">
        <table class="email-items">
            <thead>
                <tr>
                    <th class="item-info">
                        <?= $escaper->escapeHtml(__('Image')) ?>
                    </th>
                    <th class="item-info item-product">
                        <?= $escaper->escapeHtml(__('Product')) ?>
                    </th>
                    <th class="item-price">
                        <?= $escaper->escapeHtml(__('Price per piece')) ?>
                    </th>
                    <th class="item-qty">
                        <?= $escaper->escapeHtml(__('Total amount')) ?>
                    </th>
                    <th class="item-totals">
                        <?= $escaper->escapeHtml(__('Subtotal')) ?>
                    </th>
                </tr>
            </thead>
            <?php foreach ($_items as $_item): ?>
                <?php if (!$_item->getParentItem()): ?>
                    <tbody>
                        <?= $block->getItemHtml($_item) ?>
                    </tbody>
                <?php endif; ?>
            <?php endforeach; ?>
            <tfoot class="order-totals">
                <?= $block->getChildHtml('order_totals') ?>
            </tfoot>
        </table>
        <?php if ($block->getOrderAction()->isMessagesAllowed('order', $_order, $_order->getStore())
            && $_order->getGiftMessageId()
        ): ?>
            <?php $_giftMessage = $block->getOrderAction()->getGiftMessage($_order->getGiftMessageId()); ?>
            <?php if ($_giftMessage): ?>
                <br />
                <table class="message-gift">
                    <tr>
                        <td>
                            <h3><?= $escaper->escapeHtml(__('Gift Message for this Order')) ?></h3>
                            <strong><?= $escaper->escapeHtml(__('From:')) ?></strong> <?= $escaper->escapeHtml($_giftMessage->getSender()) ?>
                            <br /><strong><?= $escaper->escapeHtml(__('To:')) ?></strong> <?= $escaper->escapeHtml($_giftMessage->getRecipient()) ?>
                            <br /><strong><?= $escaper->escapeHtml(__('Message:')) ?></strong>
                            <br /><?= $escaper->escapeHtml($_giftMessage->getMessage()) ?>
                        </td>
                    </tr>
                </table>
            <?php endif; ?>
        <?php endif; ?>
    </div>
<?php endif; ?>
