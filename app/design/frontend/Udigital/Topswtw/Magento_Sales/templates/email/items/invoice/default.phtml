<?php

use Magento\Framework\View\Element\Template;
use Magento\Sales\Model\Order\Item;
use Magento\Sales\Model\Order;
use Magento\Framework\Escaper;

/**
 * @var $block Template
 * @var $escaper Escaper
 * @var $_item Item
 * @var $_order Order
 */

$_item = $block->getItem();
$_order = $block->getItem()->getOrder();

?>
<tr>
    <td class="item-info<?= ($block->getItemOptions() ? ' has-extra' : '') ?>">
        <p class="product-name"><?= $escaper->escapeHtml($_item->getName()) ?></p>
        <p class="sku"><?= $escaper->escapeHtml(__('SKU')) ?>: <?= $escaper->escapeHtml($block->getSku($_item)) ?></p>
        <?php if ($block->getItemOptions()): ?>
            <?php foreach ($block->getItemOptions() as $option): ?>
                <p class="item-options"><?= $escaper->escapeHtml($option['label']) ?>: <?= /* @noEscape */  nl2br($escaper->escapeHtml($option['value'])) ?></p>
            <?php endforeach; ?>
        <?php endif; ?>
        <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
        <?php if ($addInfoBlock): ?>
            <?= $addInfoBlock->setItem($_item->getOrderItem())->toHtml() ?>
        <?php endif; ?>
        <?= $escaper->escapeHtml($_item->getDescription()) ?>
    </td>
    <td class="item-qty"><?= (float) $_item->getQty() ?></td>
    <td class="item-price">
        <?= /* @noEscape */ $block->getItemPrice($_item->getOrderItem()) ?>
    </td>
</tr>
