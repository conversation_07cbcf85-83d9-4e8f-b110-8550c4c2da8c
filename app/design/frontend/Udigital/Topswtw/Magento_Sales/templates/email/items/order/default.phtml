<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\Escaper;
use Magento\Framework\UrlInterface;
use Magento\Sales\Block\Order\Email\Items\DefaultItems;
use Magento\Sales\Model\Order\Item;

/** @var $block DefaultItems  */
/** @var $_item Item */
/** @var $escaper Escaper */

$_item  = $block->getItem();
$_order = $_item->getOrder();
$_store = $_order->getStore();

$_baseImageUrl = $_store->getBaseUrl(UrlInterface::URL_TYPE_MEDIA).'catalog/product';
$productForThumbnail = $_item->getProduct();

//product url for glasklem
$produrl = $_item->getProduct()->getProductUrl();

if ($urlCartBundle = $block->getOrderAction()->checkIfCartContainBunlde($_item->getSku())) {
    $produrl = $_store->getBaseUrl().$block->getOrderAction()->getRubberParentUrl($urlCartBundle);
}

?>
<tr>
    <td class="item-info">
    <?php if ($_item->getProductType() == 'configurable'): ?>
        <?php
        foreach ($block->getOrder()->getAllItems() as $orderItem) {
            if ($orderItem->getParentItemId() == $_item->getId()) {
                $productForThumbnail = $orderItem->getProduct();
                break;
            }
        }
            $urlImage = "media/catalog/product".$productForThumbnail->getImage();
            $productImage = $block->getOrderAction()->getImageResize($urlImage, 100, 100, "product");
        ?>
        <p class="m-0">
            <span>
                <img src="<?= $productImage ?>" alt="<?= $productForThumbnail->getName(); ?>">
            </span>
        </p>
        <?php else: ?>
            <?php
                $urlImage = "media/catalog/product".$productForThumbnail->getImage();
                $productImage = $block->getOrderAction()->getImageResize($urlImage, 100, 100, "product");
            ?>
            <p class="m-0">
                <span>
                    <img src="<?= $productImage;?>" alt="<?= $productForThumbnail->getName(); ?>">
                </span>
            </p>
        <?php endif; ?>
    </td>
    <td class="item-product item-info<?= ($block->getItemOptions() ? ' has-extra' : '') ?>">
        <p class="product-name"><a href="<?= $escaper->escapeHtml($produrl) ?>"><?= $escaper->escapeHtml($_item->getName()) ?></a></p>
        <p class="sku item-options"><?= $escaper->escapeHtml(__('SKU')) ?>:  <?= $escaper->escapeHtml($_item->getProduct()->getData('manufacturer_product_code')) ?></p>
        <?php if ($block->getItemOptions()): ?>
            <?php foreach ($block->getItemOptions() as $option): ?>
                <p class="item-options"><?= $escaper->escapeHtml($option['label']) ?>: <?= /* @noEscape */  nl2br($escaper->escapeHtml($option['value'])) ?></p>
            <?php endforeach; ?>
        <?php endif; ?>
        <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
        <?php if ($addInfoBlock):?>
            <?= $addInfoBlock->setItem($_item)->toHtml() ?>
        <?php endif; ?>
        <?php
            //$block->escapeHtml($_item->getDescription())
            $tdesc = $_item->getDescription();
        if ($tdesc) {
            if ($tdesc != strip_tags($tdesc)) {
                $tdesc = str_replace('<dt>', '<p class="item-options">', $tdesc);
                $tdesc = str_replace('</dt><dd>', ': ', $tdesc);
                $tdesc = str_replace('</dd>', '</p>', $tdesc);
            } else {
                if ($tdesc == 'Maak order*') {
                    $tdesc = '<p class="item-options d-none">'.$tdesc.'</p>';
                } else {
                    $tdesc = '<p class="item-options">'.$tdesc.'</p>';
                }
            }
            echo $tdesc;
        }
        ?>
    </td>
    <td class="item-price">
        <?= $block->getOrderAction()->currency($_item->getPriceInclTax(), true, false) ?>
    </td>
    <td class="item-qty">
        <?php
            $qtyOrder = $_item->getQtyOrdered();
            $description = $_item->getDescription();

        if (!empty($description)) {
            if (strpos($description, 'Lengte') !== false) {
                preg_match_all('!\d+!', $description, $matches);
                if (is_array($matches) && sizeof($matches[0]) > 0) {
                    $length = $matches[0][0];
                    $qty = $matches[0][1];

                    $qtyItem = $qty * floatval($length/1000);

                    if (floatval($qtyItem) != floatval($qtyOrder)) {
                        echo number_format((float) $qtyItem, 2, ',', '');
                    } else {
                        echo number_format((float) $qtyOrder, 2, ',', '');
                    }
                } else {
                    echo (float) $qtyOrder;
                }
            } else {
                echo (float) $qtyOrder;
            }
        } else {
            echo (float) $qtyOrder;
        }
        ?>
    </td>
    <td class="item-totals">
        <?php $totalPrice = (float)$qtyOrder * (float)$_item->getPriceInclTax() ?>
        <?= $block->getOrderAction()->currency($totalPrice, true, false) ?>
    </td>
</tr>
<?php if ($_item->getGiftMessageId()
    && $_giftMessage = $block->getOrderAction()->getGiftMessage($_item->getGiftMessageId())
): ?>
    <tr>
    <td colspan="3" class="item-extra">
        <table class="message-gift">
            <tr>
                <td>
                    <h3><?= $escaper->escapeHtml(__('Gift Message')) ?></h3>
                    <strong><?= $escaper->escapeHtml(__('From:')) ?></strong> <?= $escaper->escapeHtml($_giftMessage->getSender()) ?>
                    <br /><strong><?= $escaper->escapeHtml(__('To:')) ?></strong> <?= $escaper->escapeHtml($_giftMessage->getRecipient()) ?>
                    <br /><strong><?= $escaper->escapeHtml(__('Message:')) ?></strong>
                    <br /><?= $escaper->escapeHtml($_giftMessage->getMessage()) ?>
                </td>
            </tr>
        </table>
    </td>
</tr>
<?php endif; ?>
