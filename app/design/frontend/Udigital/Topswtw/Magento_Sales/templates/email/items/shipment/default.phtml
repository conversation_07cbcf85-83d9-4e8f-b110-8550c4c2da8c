<?php

use Magento\Framework\View\Element\Template;
use Magento\Sales\Model\Order\Item;
use Magento\Framework\Escaper;

/**
 * @var Template $block
 * @var Escaper $escaper
 * @var Item $_item
 * @var mixed $option
 */

$_item = $block->getItem();

?>
<tr>
    <td class="item-info<?= ($block->getItemOptions() ? ' has-extra' : '') ?>">
        <p class="product-name"><?= $escaper->escapeHtml($_item->getName()) ?></p>
        <p class="sku"><?= $escaper->escapeHtml(__('SKU')) ?>: <?= $escaper->escapeHtml($block->getSku($_item)) ?></p>
        <?php if ($block->getItemOptions()): ?>
            <p class="item-options"><?= $escaper->escapeHtml($option['label']) ?>: <?= /* @noEscape */  nl2br($escaper->escapeHtml($option['value'])) ?></p>
        <?php endif; ?>
        <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
        <?php if ($addInfoBlock): ?>
            <?= $addInfoBlock->setItem($_item->getOrderItem())->toHtml() ?>
        <?php endif; ?>
        <?= $escaper->escapeHtml($_item->getDescription()) ?>
    </td>
    <td class="item-qty"><?= (float) $_item->getQty() ?></td>
</tr>
