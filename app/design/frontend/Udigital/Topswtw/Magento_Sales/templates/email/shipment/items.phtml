<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Email\Shipment\Items;

/** @var $block Items */
/** @var $escaper Escaper */

$_shipment = $block->getShipment() ;
$_order    = $block->getOrder();

?>
<?php if ($_shipment && $_order): ?>
    <table class="email-items">
        <thead>
        <tr>
            <th class="item-info">
                <?= $escaper->escapeHtml(__('Items')) ?>
            </th>
            <th class="item-qty">
                <?= $escaper->escapeHtml(__('Qty')) ?>
            </th>
        </tr>
        </thead>
        <?php foreach ($_shipment->getAllItems() as $_item): ?>
            <?php if (!$_item->getOrderItem()->getParentItem()): ?>
                <tbody>
                <?= $block->getItemHtml($_item) ?>
                </tbody>
            <?php endif; ?>
        <?php endforeach; ?>
    </table>
<?php endif; ?>
