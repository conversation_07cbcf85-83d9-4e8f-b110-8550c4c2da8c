<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Sales\Model\Order;

/** @var $block Template */
/** @var $escaper  Escaper */

$_shipment = $block->getShipment();

/** @var Order $_order */
$_order = $block->getOrder()

?>
<?php if ($_shipment && $_order): ?>
    <?php $trackCollection = $_order->getTracksCollection($_shipment->getId()) ?>
    <?php if ($trackCollection): ?>
        <br />
        <table class="shipment-track">
            <thead>
            <tr>
                <th><?= $escaper->escapeHtml(__('Shipped By')) ?></th>
                <th><?= $escaper->escapeHtml(__('Tracking Number')) ?></th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($trackCollection as $_item): ?>
                <tr>
                    <td><?= $escaper->escapeHtml($_item->getTitle()) ?>:</td>
                    <td>
                        <a href="<?= $escaper->escapeUrl($block->getTrackingUrl()->getUrl($_item)) ?>" target="_blank">
                            <?= $escaper->escapeHtml($_item->getNumber()) ?>
                        </a>
                    </td>
                </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    <?php endif; ?>
<?php endif; ?>
