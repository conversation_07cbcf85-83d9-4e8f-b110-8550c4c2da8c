<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\History;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var History $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<?php $orders = $block->getOrders(); ?>
<div class="text-2xl mb-6"><?= $escaper->escapeHtml(__('Orders')) ?></div>
<?= $block->getChildHtml('info') ?>
<?php if ($orders && count($orders)): ?>
    <?php foreach ($orders as $order): ?>
        <?php $show_date = date('j F Y', strtotime($order->getCreatedAt())); ?>
        <div class="bg-white rounded-lg border border-white-azureish grid grid-cols-1 lg:grid-cols-6 mb-4 p-4">
            <div class="col-span-3 text-left">
                <p class="text-lg font-bold"><?= $show_date ?> | #<?= $escaper->escapeHtml($order->getRealOrderId()) ?></p>
                <p class="text-sm text-gray-500">
                    <span><?= $escaper->escapeHtml($order->getStatusLabel()) ?></span><br />
                    <?php $extra = $block->getChildBlock('extra.container'); ?>
                    <?php if ($extra): ?>
                        <?php $extra->setOrder($order); ?>
                        <?= $extra->getChildHtml() ?>
                    <?php endif; ?>
                </p>   
            </div>
            <div class="lg:col-span-1 text-center lg:text-left text-2xl lg:text-xl xl:text-2xl font-bold text-blue-picton flex items-end md:items-center">
                <?= /* @noEscape */$order->formatPrice($order->getGrandTotal()) ?>
            </div>
            <div class="lg:col-span-2 flex flex-col lg:flex-row justify-center items-center space-y-2 lg:space-y-0 lg:space-x-2 mt-4 lg:mt-0">
                <a href="<?= $escaper->escapeUrl($block->getViewUrl($order)) ?>" class="bg-blue-picton text-blue-prussian px-4 py-2 lg:max-2xl:px-2 text-base lg:max-2xl:text-sm min-w-36 md:min-w-auto text-center rounded-lg hover:bg-white transition duration-300" title="<?= $escaper->escapeHtmlAttr(__('View Order')) ?>">
                    <span><?= $escaper->escapeHtml(__('Details')) ?></span>
                </a>
                <?php if ($block->getOrderAction()->canReorder($order->getEntityId())): ?>
                    <?php $formData = json_decode(
                        $block->getOrderAction()->getPostData(
                            $block->getReorderUrl($order)
                        ),
                        true
                    ) ?>
                    <form action="<?= /* @noEscape */ $formData['action'] ?>" method="post"
                          class="inline-flex items-center min-w-36 md:min-w-auto">
                        <?= $block->getBlockHtml('formkey'); ?>
                        <input type="hidden" name="data"
                               value="<?= $escaper->escapeHtmlAttr(json_encode($formData['data'], JSON_UNESCAPED_UNICODE)) ?>"
                        />
                        <button title="<?= $escaper->escapeHtmlAttr(__('Order again')) ?>"
                                type="submit"
                                class="border border-blue-picton text-blue-prussian px-4 py-2 lg:max-2xl:px-2 rounded-lg hover:bg-blue-picton text-base lg:max-2xl:text-sm transition duration-300">
                            <span><?= $escaper->escapeHtml(__('Order again')) ?></span>
                        </button>
                    </form>
                <?php endif ?>
            </div>
        </div>
    <?php endforeach; ?>
    <?php if ($block->getPagerHtml()): ?>
        <div class="mt-6 hidden"><?= $block->getPagerHtml() ?></div>
    <?php endif ?>
<?php else: ?>
    <div class="bg-blue-100 text-blue-900 p-4 rounded-lg"><?= $escaper->escapeHtml(__('There are no historical orders yet.')) ?></div>
<?php endif ?>