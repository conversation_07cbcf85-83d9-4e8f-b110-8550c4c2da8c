<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Info;

/**
 * @var Info $block
 * @var Escaper $escaper
 */

$order = $block->getOrder()

?>
<div class="mb-4 pb-4 border-b border-container mobile_shopup">
    <div class="block-title-custom text-2xl text-cadet-space font-bold mb-1" style="border-bottom: 0px;">
        <strong><?= $escaper->escapeHtml(__('Order Information')) ?></strong>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
        <div class="w-full py-4 address_view">
            <div class="address p-4 border border-white-azureish rounded-xl">
                <p class="font-semibold mb-2 title"><?= $escaper->escapeHtml(__('Billing Address')) ?></p>
                <div class="box-content">
                    <address class="not-italic"><?= /* @noEscape */ $block->getFormattedAddress($order->getBillingAddress()) ?></address>
                </div>
            </div>
        </div>
        <?php if (!$order->getIsVirtual()): ?>
            <div class="w-full py-4">
                <div class="address p-4 border border-white-azureish rounded-xl">
                    <p class="font-semibold mb-2 title"><?= $escaper->escapeHtml(__('Shipping Address')) ?></p>
                    <div class="box-content">
                        <address class="not-italic"><?= /* @noEscape */ $block->getFormattedAddress($order->getShippingAddress()) ?></address>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        <div class="w-full py-4 mobile_view">
            <table class="data table table-order-items totals w-full">
                <tbody>
                <?= $block->getChildHtml('order_totals') ?>
                <?php if ($block->getOrderAction()->canReorder($order->getEntityId())): ?>
                    <?php $formData = json_decode(
                        $block->getOrderAction()->getPostData($block->getUrl('sales/order/reorder', ['order_id' => $order->getId()])),
                        true
                    ) ?>
                <tr class="reorder">
                    <td colspan="5">
                        <div class="action-reorder border border-blue-picton rounded-lg text-center py-3 w-full md:w-48 md:ml-auto mt-8">
                            <!-- <a href="#" data-post='<?=
                            /* @noEscape */ $block->getOrderAction()
                                ->getPostData($block->getUrl('sales/order/reorder', ['order_id' => $order->getId()]))
                            ?>' class="action order">
                                <span><?= $escaper->escapeHtml(__('Reorder')) ?></span>
                            </a> -->

                            <form action="<?= /* @noEscape */ $formData['action'] ?>" method="post"
                                  class="inline-flex items-center">
                                <?= $block->getBlockHtml('formkey'); ?>
                                <input type="hidden" name="data"
                                       value="<?= $escaper->escapeHtmlAttr(json_encode($formData['data'], JSON_UNESCAPED_UNICODE)) ?>"
                                />
                                <button title="<?= $escaper->escapeHtmlAttr(__('Order again')) ?>"
                                        type="submit"
                                        class="action order">
                                    <span><?= $escaper->escapeHtml(__('Order again')) ?></span>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endif ?>
                </tbody>
            </table>
        </div>

    </div>
</div>
