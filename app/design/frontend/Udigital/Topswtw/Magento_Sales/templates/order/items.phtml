<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Items;

/** @var Items $block */
/** @var Escaper $escaper*/

?>
<div class="table-wrapper order-items" style="border-bottom: 0px;">
    <table class="data table table-order-items w-full" id="my-orders-table" summary="<?= $escaper->escapeHtml(__('Items Ordered')) ?>">
        <!-- remove thead items -->
        <?php $items = $block->getItems(); ?>
        <?php $giftMessage = ''?>
        <?php foreach ($items as $item):
            if ($item->getParentItem()):
                continue;
            endif;
            ?>
            <tbody>
                <?= $block->getItemHtml($item) ?>
                <?php if ($block->getOrderAction()->isMessagesAllowed('order_item', $item) && $item->getGiftMessageId()): ?>
                    <?php $giftMessage = $block->getOrderAction()->getGiftMessageForEntity($item); ?>
                    <tr>
                        <td class="col options" colspan="5">
                            <a href="#"
                               id="order-item-gift-message-link-<?= (int) $item->getId() ?>"
                               class="action show"
                               aria-controls="order-item-gift-message-<?= (int) $item->getId() ?>"
                               data-item-id="<?= (int) $item->getId() ?>">
                                <?= $escaper->escapeHtml(__('Gift Message')) ?>
                            </a>
                            <?php $giftMessage = $block->getOrderAction()->getGiftMessageForEntity($item); ?>
                            <div class="order-gift-message" id="order-item-gift-message-<?= (int) $item->getId() ?>" role="region" aria-expanded="false" tabindex="-1">
                                <a href="#"
                                   title="<?= $escaper->escapeHtml(__('Close')) ?>"
                                   aria-controls="order-item-gift-message-<?= (int) $item->getId() ?>"
                                   data-item-id="<?= (int) $item->getId() ?>"
                                   class="action close">
                                    <?= $escaper->escapeHtml(__('Close')) ?>
                                </a>
                                <dl class="item-options">
                                    <dt class="item-sender"><strong class="label"><?= $escaper->escapeHtml(__('From')) ?></strong><?= $escaper->escapeHtml($giftMessage->getSender()) ?></dt>
                                    <dt class="item-recipient"><strong class="label"><?= $escaper->escapeHtml(__('To')) ?></strong><?= $escaper->escapeHtml($giftMessage->getRecipient()) ?></dt>
                                    <dd class="item-message"><?= /* @noEscape */ $block->getOrderAction()->getEscapedGiftMessage($item) ?></dd>
                                </dl>
                            </div>
                        </td>
                    </tr>
                <?php endif ?>
            </tbody>
        <?php endforeach; ?>
        <tfoot>
            <?php if ($block->isPagerDisplayed()): ?>
                <tr>
                    <td colspan="5" data-block="order-items-pager-bottom" class="order-pager-wrapper order-pager-wrapper-bottom">
                        <?= $block->getPagerHtml() ?>
                    </td>
                </tr>
            <?php endif ?>
            <?= $block->getChildHtml('order_totals') ?>
        </tfoot>
    </table>
</div>
<?php if ($giftMessage): ?>
<script type="text/x-magento-init">
    {
        "a.action.show, a.action.close": {
            "giftMessage": {}
        }
    }
</script>
<?php endif; ?>
