<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\Escaper;
use Magento\Framework\Locale\LocaleFormatter;
use Magento\Sales\Block\Order\Item\Renderer\DefaultRenderer;

/**
 * @var DefaultRenderer $block
 * @var LocaleFormatter $localeFormatter
 * @var Escaper $escaper
 */

$_item = $block->getItem();
?>
<tr id="order-item-row-<?= (int) $_item->getId() ?>" class="grid grid-cols-3 md:grid-cols-7 justify-items-center items-center content-center">
    <?php
        $imageUrl = $block->getOrderAction()->getImageUrl($_item->getProduct(), 'product_thumbnail_image');
        $imageLargeUrl = $block->getOrderAction()->getImageUrl($_item->getProduct(), 'product_base_image');
    ?>
    <td class="col item image 3 row-span-2 md:row-auto" data-th="<?= $escaper->escapeHtmlAttr(__('Image')) ?>">
        <img class="image-item sm:w-10 md:w-64" src="<?= $imageUrl; ?>" srcset="<?= $imageUrl ?> 300w, <?= $imageLargeUrl ?> 1000w">
    </td>
    <td class="col item name col-span-2 md:col-span-4" colspan="2" data-th="<?= $escaper->escapeHtml(__('Product Name')) ?>">
        <strong class="product name product-item-name text-base md:text-2xl text-cadet-space font-bold"><?= $escaper->escapeHtml($_item->getName()) ?></strong>
        <div class="additional-info-product-name text-base text-blue-cadet">
            <?php if ($_options = $block->getItemOptions()): ?>
                <dl class="item-options">
                <?php foreach ($_options as $_option): ?>
                    <dt><?= $escaper->escapeHtml($_option['label']) ?></dt>
                    <?php if (!$block->getPrintStatus()): ?>
                        <?php $_formatedOptionValue = $block->getFormatedOptionValue($_option) ?>
                        <dd<?= (isset($_formatedOptionValue['full_view']) ? ' class="tooltip wrapper"' : '') ?>>
                            <?= $escaper->escapeHtml($_formatedOptionValue['value'], ['a']) ?>
                            <?php if (isset($_formatedOptionValue['full_view'])): ?>
                                <div class="tooltip content">
                                    <dl class="item options">
                                        <dt><?= $escaper->escapeHtml($_option['label']) ?></dt>
                                        <dd><?= $escaper->escapeHtml($_formatedOptionValue['full_view']) ?></dd>
                                    </dl>
                                </div>
                            <?php endif; ?>
                        </dd>
                    <?php else: ?>
                        <?php $optionValue = isset($_option['print_value']) ? $_option['print_value'] : $_option['value'] ?>
                        <dd><?= $escaper->escapeHtml($optionValue) ?></dd>
                    <?php endif; ?>
                <?php endforeach; ?>
                </dl>
            <?php endif; ?>
            <?php $addtInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
            <?php if ($addtInfoBlock): ?>
                <?= $addtInfoBlock->setItem($_item)->toHtml() ?>
            <?php endif; ?>
            <?= $escaper->escapeHtml($_item->getDescription()) ?>
            <?= /* @noEscape */ 'SKU :'.$block->prepareSku($block->getSku()) ?>
        </div>
    </td>
    <td class="col item qty col-start-2 md:col-start-auto" data-th="<?= $escaper->escapeHtml(__('Qty')) ?>">
        <ul class="items-qty">
        <?php if ($block->getItem()->getQtyOrdered() > 0): ?>
            <li class="item">
                <!-- <span class="title"><?= $escaper->escapeHtml(__('Ordered')) ?></span> -->
                <span class="content text-base md:text-2xl text-blue-prussian font-normal">
                    <?= $escaper->escapeHtml($localeFormatter->formatNumber((float) $block->getItem()->getQtyOrdered()))?>x
                </span>
            </li>
        <?php endif; ?>
        <?php if ($block->getItem()->getQtyShipped() > 0): ?>
            <li class="item">
                <span class="title"><?= $escaper->escapeHtml(__('Shipped')) ?></span>
                <span class="content">
                    <?= $escaper->escapeHtml($localeFormatter->formatNumber((float) $block->getItem()->getQtyShipped()))?>
                </span>
            </li>
        <?php endif; ?>
        <?php if ($block->getItem()->getQtyCanceled() > 0): ?>
            <li class="item">
                <span class="title"><?= $escaper->escapeHtml(__('Canceled')) ?></span>
                <span class="content">
                    <?= $escaper->escapeHtml(
                        $localeFormatter->formatNumber((float) $block->getItem()->getQtyCanceled())
                    )?>
                </span>
            </li>
        <?php endif; ?>
        <?php if ($block->getItem()->getQtyRefunded() > 0): ?>
            <li class="item">
                <span class="title"><?= $escaper->escapeHtml(__('Refunded')) ?></span>
                <span class="content">
                    <?= $escaper->escapeHtml(
                        $localeFormatter->formatNumber((float) $block->getItem()->getQtyRefunded())
                    )?>
                </span>
            </li>
        <?php endif; ?>
        </ul>
    </td>
    <td class="col item subtotal text-right col-start-3 md:col-start-auto" data-th="<?= $escaper->escapeHtml(__('Subtotal')) ?>">
        <div class="item-row-total text-lg md:text-3xl text-blue-picton"><?= $block->getItemRowTotalHtml() ?></div>
        <div class="item-price text-xxs md:text-sm text-cadet-space flex justify-end gap-x-1">
            <span><?= $block->getItemPriceHtml() ?></span>
            <span class="price-text"><?= $escaper->escapeHtml(__('per set')) ?></span>
        </div>
    </td>
</tr>
