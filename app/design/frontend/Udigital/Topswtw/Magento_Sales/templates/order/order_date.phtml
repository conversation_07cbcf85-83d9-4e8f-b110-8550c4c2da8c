<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
?>
<div class="custom-page-title text-2xl text-cadet-space font-bold mb-1">
    <?= $escaper->escapeHtml(__('Details van je bestelling')) ?>
</div>
<div class="info-order-bottom flex relative justify-between w-full">
    <div class="info-order info-order-date text-sm">
        <?= $escaper->escapeHtml(
            __(
                '<span class="info-label"> Besteld op:</span> %1',
                '<span class="info-value">' . $block->formatDate(
                    $block->getOrder()->getCreatedAt(),
                    \IntlDateFormatter::LONG
                ) . '</span>'
            ),
            ['span']
        ) ?>
    </div>
    <div class="info-order order-track text-base">
        <?php if (!empty($block->getOrder()->getTracksCollection()->getItems())): ?>
            <span class="info-label text-blue-cadet">
                <?= $escaper->escapeHtml(__('Je bestelling is onderweg.')) ?>
            </span>
            <span class="info-value-track text-blue-picton">
                <?= $block->getChildHtml('tracking-info-link') ?>
            </span>
        <?php endif; ?>
    </div>
    <div class="info-order order-number text-base">
        <span class="info-label text-blue-cadet">
            <?= $escaper->escapeHtml(__('Ordernummer:')) ?>
        </span>
        <span class="info-value text-blue-prussian font-bold">
            <?= $escaper->escapeHtml(__('#' . $block->getOrder()->getIncrementId())) ?>
        </span>
    </div>
</div>
<div class="info-order-bottom tracking hidden">
    <?php if (!empty($block->getOrder()->getTracksCollection()->getItems())): ?>
        <span class="info-label">
            <?= $escaper->escapeHtml(__('Je bestelling is onderweg.')) ?>
        </span>
        <span class="info-value-track text-blue-picton">
            <?= $block->getChildHtml('tracking-info-link') ?>
        </span>
    <?php endif; ?>
</div>
