<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Totals;

/**
 * @var Totals $block
 * @var Escaper $escaper
 */

$temp = '';
?>
<?php foreach ($block->getTotals() as $_code => $_total): ?>
    <?php if ($_total->getBlockName()): ?>
        <?php  $temp = $block->getChildHtml($_total->getBlockName(), false); ?>
    <?php else:?>
    <tr class="<?= $escaper->escapeHtmlAttr($_code) ?>">
        <th <?= /* @noEscape */ $block->getLabelProperties() ?> class="test" scope="row">
            <?php if ($_total->getStrong()): ?>
                <strong><?= $escaper->escapeHtml($_total->getLabel()) ?></strong>
            <?php else: ?>
                <?= $escaper->escapeHtml($_total->getLabel()) ?>
            <?php endif ?>
        </th>
        <td <?= /* @noEscape */ $block->getValueProperties() ?> data-th="<?= $escaper->escapeHtmlAttr($_total->getLabel()) ?>">
            <?php if ($_total->getStrong()): ?>
                <strong><?= /* @noEscape */ $block->formatValue($_total) ?></strong>
            <?php else: ?>
                <?= /* @noEscape */ $block->formatValue($_total) ?>
            <?php endif?>
        </td>
    </tr>
    <?php endif; ?>
<?php endforeach?>
<?= $temp; ?>
