<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\View;

/** @var View $block */
/** @var Escaper $escaper */

$order = $block->getOrder();
?>
<div class="md:flex items-center mt-8 mb-3 block">
    <div class="lg:flex items-center text-center md:text-left w-full">
        <div class="lg:inline-block w-full">
            <div class="text-2xl hidden"><?= $escaper->escapeHtml(__('Order # %1', $order->getRealOrderId())) ?></div>
            <?= $block->getChildHtml('order.date') ?>
        </div>
    </div>
</div>
<div class="p-4 rounded-xl border border-white-azureish mb-8">
    <div class="-mx-4 hidden">
        <?= $block->getChildHtml('order_top_items') ?>
    </div>
    <div>
        <div class="mb-4">
            <?= $block->getChildHtml('sales.order.info') ?>
        </div>

        <?= $block->getChildHtml('order.comments') ?>

        <?php if (!$block->getData('is_context_shipment')): ?>
            <div class="font-bold text-lg mb-2">
                <?php if (!empty($order->getTracksCollection()->getItems())): ?>
                    <?= $block->getChildHtml('tracking-info-link') ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?= $block->getChildHtml('order_items') ?>
        <?php if ($block->getOrderAction()->isMessagesAllowed('order', $order) && $order->getGiftMessageId()): ?>
            <div class="mt-6">
                <div class="font-bold text-lg mb-2"><?= $escaper->escapeHtml(__('Gift Message for This Order')) ?></div>
                <?php
                $giftMessage = $block->getOrderAction()->getGiftMessageForEntity($order);
                ?>
                <div class="bg-white p-4 rounded-lg">
                    <dl class="space-y-2">
                        <div>
                            <dt class="font-semibold"><?= $escaper->escapeHtml(__('From')) ?></dt>
                            <dd><?= $escaper->escapeHtml($giftMessage->getSender()) ?></dd>
                        </div>
                        <div>
                            <dt class="font-semibold"><?= $escaper->escapeHtml(__('To')) ?></dt>
                            <dd><?= $escaper->escapeHtml($giftMessage->getRecipient()) ?></dd>
                        </div>
                        <div>
                            <dt class="font-semibold"><?= $escaper->escapeHtml(__('Message')) ?></dt>
                            <dd><?= /* @noEscape */ $block->getOrderAction()->getEscapedGiftMessage($order) ?></dd>
                        </div>
                    </dl>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>