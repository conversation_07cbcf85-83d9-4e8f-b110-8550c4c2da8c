<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Search\ViewModel\AdditionalSearchFormData;
use Magento\Search\ViewModel\ConfigProvider;

?>
<?php
/** @var Template $block */
/** @var Escaper $escaper */
/** @var ConfigProvider $configProvider*/
$configProvider = $block->getData('configProvider');
/** @var AdditionalSearchFormData $versionManager*/
$additionalSearchFormData = $block->getData('additionalSearchFormData');
$helper = $configProvider->getSearchHelperData();
$allowedSuggestion = $configProvider->isSuggestionsAllowed();
$quickSearchUrl = $allowedSuggestion ? $escaper->escapeUrl($helper->getSuggestUrl()) : '';
?>
<div class="block block-search">
    <div class="block block-title"><strong><?= $escaper->escapeHtml(__('Search')) ?></strong></div>
    <div class="block block-content">
        <form class="form minisearch active" id="search_mini_form"
              action="<?= $escaper->escapeUrl($helper->getResultUrl()) ?>" method="get">
            <?php  if (!empty($queryParams = $additionalSearchFormData->getFormData())): ?>
                <?php foreach ($queryParams as $param): ?>
                    <input type="hidden" name="<?= $escaper->escapeHtmlAttr($param['name']) ?>"
                           value="<?= $escaper->escapeHtmlAttr($param['value']) ?>"/>
                <?php endforeach; ?>
            <?php endif; ?>
            <div class="field search">
                <label class="label active" for="search" data-role="minisearch-label">
                    <span><?= $escaper->escapeHtml(__('Search')) ?></span>
                </label>
                <div class="control">
                    <input id="search"
                           data-mage-init='{
                            "quickSearch": {
                                "formSelector": "#search_mini_form",
                                "url": "<?= /* @noEscape */ $quickSearchUrl ?>",
                                "destinationSelector": "#search_autocomplete",
                                "minSearchLength": "<?= $escaper->escapeHtml($helper->getMinQueryLength()) ?>"
                            }
                        }'
                           type="text"
                           name="<?= $escaper->escapeHtmlAttr($helper->getQueryParamName()) ?>"
                           value="<?= /* @noEscape */ $helper->getEscapedQueryText() ?>"
                           placeholder="<?= $escaper->escapeHtmlAttr(__('What are you looking for?')) ?>"
                           class="input-text"
                           maxlength="<?= $escaper->escapeHtmlAttr($helper->getMaxQueryLength()) ?>"
                           role="combobox"
                           aria-haspopup="false"
                           aria-autocomplete="both"
                           autocomplete="off"
                           aria-expanded="false"/>
                    <div id="search_autocomplete" class="search-autocomplete"></div>
                    <?= $block->getChildHtml() ?>
                </div>
            </div>
            <div class="actions">
                <button type="submit"
                        title="<?= $escaper->escapeHtml(__('Search')) ?>"
                        class="action search"
                        aria-label="Search"
                >
                    <span><?= $escaper->escapeHtml(__('Search')) ?></span>
                </button>
            </div>
        </form>
    </div>
</div>
