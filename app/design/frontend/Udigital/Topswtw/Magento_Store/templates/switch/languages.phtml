<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Store\Block\Switcher;
use Siteation\HyvaIconsFlags\ViewModel\FlagsIcons;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var Switcher $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */


$count = count($block->getStores());
$code  = strtolower($block->getStoreCode());
$name  = $block->getStoreName();

$flagsIcons = $viewModels->require(FlagsIcons::class);

/** @var SvgIcons $hyvaicons */
$hyvaicons = $viewModels->require(SvgIcons::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$languageFlag = $block->getUrlAction()->getLanguageFlag();
$otherStores  = $languageFlag['other_store'];
$localeCode   = $languageFlag['locale_code'];
$localeName   = $languageFlag['locale_name'];
$localUpper   = $languageFlag['local_upper'];

$id = $block->getIdModifier() ? '-' . $block->getIdModifier() : '';
?>
<div class="switcher language switcher-language"
    data-ui-id="language-switcher"
    id="switcher-language<?= $escaper->escapeHtmlAttr($id) ?>">
    <span class="header-tops-filters mr">
        <span>
            <a href="<?= $block->getUrl($block->getUrlAction()->getUrlAboutUs()) ?>"
            class="text-blue-picton text-decoration-none">
                <?= $escaper->escapeHtml(__("About us")) ?>
            </a>
        </span>
        <span>
            <a href="<?= $block->getUrl($block->getUrlAction()->getRouteBlog()) ?>"
            class="text-blue-picton text-decoration-none">
                <?= $escaper->escapeHtml(__("Blogs")) ?>
            </a>
        </span>
        <span>
            <a href="<?= $block->getUrl($block->getUrlAction()->getUrlAdvantages()) ?>"
            class="text-blue-picton text-decoration-none">
                <?= $escaper->escapeHtml(__("Tops advantages")) ?>
            </a>
        </span>
        <span>
            <a href="<?= $block->getUrl($block->getUrlAction()->getUrlCustomerService()) ?>"
            class="text-blue-picton text-decoration-none">
                <?= $escaper->escapeHtml(__("Customer service")) ?>
            </a>
        </span>
    </span>
    <span class="label switcher-label">
        <?= $escaper->escapeHtml(__('Language')) ?>
    </span>
    <div class="actions dropdown options switcher-options" x-data="{ show: false }">
        <div class="action switcher-trigger cursor-pointer flex justify-between px-2" @click="show = ! show"
                id="switcher-language-trigger<?= $escaper->escapeHtmlAttr($id) ?>">
            <span class="view-<?= $escaper->escapeHtml($block->getCurrentStoreCode()) ?>">
                <span class="label-store-language text-blue-picton hidden screen-333.75:inline-block"><?= $escaper->escapeHtml($localUpper) ?></span>
                <span class="flag-icon" data-flag="<?= $escaper->escapeHtml($localeCode) ?>">
                    <?= $flagsIcons->renderHtml($localeCode, 'inline-block ml-2 -mt-1', 21, 21, ["aria-label" => $localeName]) ?>
                </span>
            </span>
            <?php if ($count > 1): ?>
                <div :class="{'hidden': show ,'block': !show }">
                    <?= $heroiconsSolid->renderHtml('chevron-down', 'text-blue-picton ml-1', 20, 20, ['aria-hidden' => 'true']); ?>
                </div>
                <div class="hidden" :class="{'!block': show }">
                    <?= $heroiconsSolid->renderHtml('chevron-up', 'text-blue-picton ml-1', 20, 20, ['aria-hidden' => 'true']); ?>
                </div>
            <?php endif; ?>
        </div>
        <?php if ($count > 1): ?>
            <ul class="z-40 absolute bg-container rounded-md p-2 hidden" :class="{'!block': show }" x-show.transition.in="show">
                <?php foreach ($otherStores as $store):
                    $countryFlag = $block->getUrlAction()->getCountryFlag($store->getLocaleCode());
                    $localeCode  = $countryFlag['code'];
                    $localeName  = $countryFlag['name'];
                    $localUpper  = $countryFlag['upper'];
                    ?>
                    <li class="view-<?= $escaper->escapeHtml($store->getCode()) ?> switcher-option">
                        <a href="<?= $escaper->escapeUrl($block->getViewModel()->getTargetStoreRedirectUrl($store)) ?>">
                            <span class="label-store-language text-blue-picton"><?= $escaper->escapeHtml($localUpper) ?></span>
                            <span class="flag-icon" data-flag="<?= $escaper->escapeHtml($localeCode) ?>">
                                <?= $flagsIcons->renderHtml($localeCode, 'inline-block ml-2 -mt-1', 21, 21, ["aria-label" => $localeName]) ?>
                            </span>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    </div>
</div>

