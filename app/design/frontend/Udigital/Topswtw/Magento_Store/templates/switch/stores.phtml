<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\Escaper;
use Magento\Store\Block\Switcher;

/**
 * @var Switcher $block
 * @var Escaper $escaper
 */

$groups = $block->getGroups();
$count  = count($groups);

if ($count > 1):
?>
<div class="switcher store switcher-store" id="switcher-store">
    <strong class="label switcher-label"><span><?= $escaper->escapeHtml(__('Select Store')) ?></span></strong>
    <div class="actions dropdown options switcher-options">
        <?php foreach ($groups as $_group): ?>
            <?php if ($_group->getId() == $block->getCurrentGroupId()): ?>
                <div class="action toggle switcher-trigger"
                        role="button"
                        tabindex="0"
                        data-mage-init='{"dropdown":{}}'
                        data-toggle="dropdown"
                        data-trigger-keypress-button="true"
                        id="switcher-store-trigger">
                    <strong>
                        <span><?= $escaper->escapeHtml($_group->getName()) ?></span>
                    </strong>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
        <ul class="dropdown switcher-dropdown" data-target="dropdown">
            <?php foreach ($groups as $_group): ?>
                <?php if (!($_group->getId() == $block->getCurrentGroupId())): ?>
                    <li class="switcher-option">
                        <a href="#" data-post='<?= /* @noEscape */ $block->getTargetStorePostData($_group->getDefaultStore()) ?>'>
                            <?= $escaper->escapeHtml($_group->getName()) ?>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </div>
</div>
<?php endif; ?>
