<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */

?>
<script>
    function initSwatchOptions(swatchConfig) {
        return {
            swatchConfig,
            getAttributeSwatchData(attributeId) {
                const swatchConfig = Object.assign({}, this.swatchConfig[attributeId]);
                swatchConfig['details'] = JSON.parse(swatchConfig['additional_data']);

                return swatchConfig;
            },
            getAllAttributeOptions(attributeId) {
                return (
                    this.optionConfig.attributes[attributeId] &&
                    this.optionConfig.attributes[attributeId].options
                ) || []
            },
            optionIsActive(attributeId, optionId) {
                // return true if a product with this option is in stock
                return !!this.getAllowedAttributeOptions(attributeId).find(
                    option => option.id === optionId
                )
            },
            optionIsEnabled(attributeId, optionId) {
                // return true if a product with this option is enabled
                for (const productId in this.optionConfig.index) {
                    if (this.optionConfig.index[productId][attributeId] === optionId) {
                        return true;
                    }
                }
                return false;
            },
            mapSwatchTypeNumberToTypeCode(typeNumber) {
                switch ("" + typeNumber) {
                    case "1":
                        return "color"
                    case "2":
                        return "image"
                    case "3":
                        return "empty"
                    case "0":
                    default:
                        return "text"
                }
            },
            getTypeOfFirstOption(attributeId) {
                for (const optionId in this.swatchConfig[attributeId]) {
                    const option = this.swatchConfig[attributeId][optionId];
                    if (typeof option.type !== 'undefined') {
                        return this.mapSwatchTypeNumberToTypeCode(option.type);
                    }
                }
            },
            getVisualSwatchType(attributeId, targetOptionId) {
                // If a type configuration is present for the given option id, use it
                const config = this.swatchConfig[attributeId];
                if (config[targetOptionId] && typeof config[targetOptionId].type !== 'undefined') {
                    return this.mapSwatchTypeNumberToTypeCode(config[targetOptionId].type);
                }

                // Otherwise - if no config is present for the target option - use the type of the first option
                // with a type property from the attribute, thus assuming its the same type as the target option.
                // (This edge case condition can occur on single swatch products if some options are not salable)
                return this.getTypeOfFirstOption(attributeId);
            },
            getSwatchType(attributeId, optionId) {
                // Deserialize the attribute details the first time they are used
                if (this.swatchConfig[attributeId] && ! this.swatchConfig[attributeId].details) {
                    this.swatchConfig[attributeId] = this.getAttributeSwatchData(attributeId);
                }
                const type =  this.swatchConfig[attributeId] &&
                    this.swatchConfig[attributeId].details &&
                    this.swatchConfig[attributeId].details.swatch_input_type ||
                    "empty";
                return type === 'visual' ? this.getVisualSwatchType(attributeId, optionId) : type;
            },
            isTextSwatch(attributeId, optionId) {
                return this.getSwatchType(attributeId, optionId) === 'text';
            },
            isVisualSwatch(attributeId, optionId) {
                const type = this.getSwatchType(attributeId, optionId);

                return ['image', 'color'].includes(type);
            },
            getSwatchBackgroundStyle(attributeId, optionId) {
                const config = this.getSwatchConfig(attributeId, optionId);
                const type = this.getSwatchType(attributeId, optionId);

                if (type === "color") {
                        return 'background-color:' + config.value;
                } else if (type === "image") {
                        return "background: #ffffff url('" + config.value + "') no-repeat center";
                } else {
                    return '';
                }
            },
            getSwatchText(attributeId, optionId) {
                const config = this.getSwatchConfig(attributeId, optionId);
                return config.label || config.value || this.getOptionLabelFromOptionConfig(attributeId, optionId);
            },
            getOptionLabelFromOptionConfig(attributeId, optionId) {
                // Fallback if no value is present in swatchConfig data
                // Reference issue https://gitlab.hyva.io/hyva-themes/magento2-default-theme/-/issues/190
                const option = this.getAllAttributeOptions(attributeId).filter(option => option.id === optionId);
                return option && option[0] && option[0].label ||'';
            },
            getSwatchConfig(attributeId, optionId) {
                return this.swatchConfig[attributeId] && this.swatchConfig[attributeId][optionId]
                    ? this.swatchConfig[attributeId][optionId]
                    : false;
            },
            activeTooltipItem: false,
            tooltipPositionElement: false,
            isTooltipVisible() {
                return this.activeTooltipItem &&
                    this.getSwatchConfig(
                        this.activeTooltipItem.attribute,
                        this.activeTooltipItem.item
                    );
            },
            getTooltipImageStyle(attributeId, optionId) {
                const config = this.getSwatchConfig(attributeId, optionId);
                const type = this.getSwatchType(attributeId, optionId);

                if (type === "color") {
                    return 'background-color:' + config.value + '; width: 40px; height: 40px;';
                } else if (type === "image") {
                    return "background: #ffffff url('" + config.thumb +
                        "') center center no-repeat; width: 40px; height: 40px;";
                } else {
                    return 'display:none';
                }
            },
            getTooltipPosition() {
                let widthTooltip = 
                    document.getElementById(`attr-product-${this.activeTooltipItem.item}`).offsetWidth;
                return this.tooltipPositionElement ?
                    `top: ${this.tooltipPositionElement.offsetTop + 70 }px;` +
                    `left: ${
                        this.tooltipPositionElement.offsetLeft - (
                            this.tooltipPositionElement.closest('.snap') &&
                            this.tooltipPositionElement.closest('.snap').scrollLeft ||
                            0
                        ) + (widthTooltip - ((widthTooltip / 2) + 4))
                    }px;` : ''
            },
            getTooltipLabel() {
                return this.getSwatchConfig(this.activeTooltipItem.attribute, this.activeTooltipItem.item).label
            },
            focusedLabel: false,
            focusLabel(optionId) {
                this.focusedLabel = optionId;
            },
            blurLabel() {
                this.focusedLabel = false;
            },
            showSwatches: false,
            initShowSwatchesIntersect() {
                if ('IntersectionObserver' in window && !window.scrollY) {
                    let io = new IntersectionObserver(
                        entries => {
                            entries.map(entry => {
                                if (entry.isIntersecting) {
                                    this.showSwatches = true;
                                    io.unobserve(this.$root);
                                }
                            })
                        }
                    );
                    io.observe(this.$root);
                } else {
                    this.showSwatches = true
                }
            }
        }
    }
</script>
