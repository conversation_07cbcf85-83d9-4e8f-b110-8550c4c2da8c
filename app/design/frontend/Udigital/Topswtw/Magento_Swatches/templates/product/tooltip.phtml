<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

?>
<template x-if="isTooltipVisible()">
    <div class="absolute" :style="getTooltipPosition()">
        <div class="shadow-lg">
            <div class="flex absolute top-0 left-0 z-30 min-w-20 px-2 py-1 -mt-6 text-sm leading-tight text-white
                transform -translate-x-1/2 rounded-lg shadow-lg text-center justify-between items-center 
                bg-blue-prussian">
                <template x-if="isVisualSwatch(activeTooltipItem.attribute, activeTooltipItem.item)">
                    <div class="inline-block shadow-sm rounded-md my-1" 
                    :style="getTooltipImageStyle(activeTooltipItem.attribute, activeTooltipItem.item)"></div>
                </template>
                <span class="mx-2 text-sm font-semibold whitespace-nowrap subtitle" x-html="getTooltipLabel()"></span>
                <?php /** @var Hyva\Theme\ViewModel\HeroiconsOutline $heroicons */
                $heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
                ?>
                <i class="text-xs mr-1">
                    <?= $heroicons->informationCircleHtml('w-3 h-3 text-blue-picton') ?>
                </i>
            </div>
            <svg class="absolute z-10 w-8 h-8 text-blue-prussian transform -translate-x-1/2
                    -translate-y-8 fill-current stroke-current text-blue-prussian" width="12" height="12">
                <rect x="12" y="-12" width="12" height="12" transform="rotate(45)" class="shadow-xl" />
            </svg>
        </div>
    </div>
</template>