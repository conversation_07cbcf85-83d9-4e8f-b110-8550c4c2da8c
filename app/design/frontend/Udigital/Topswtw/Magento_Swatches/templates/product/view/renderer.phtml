<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SwatchRenderer;
use Magento\Framework\Escaper;
use Magento\Swatches\Block\Product\Renderer\Configurable;
use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Configurable $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var \Hyva\Theme\ViewModel\Modal $modalViewModel */
$modalViewModel = $viewModels->require(\Hyva\Theme\ViewModel\Modal::class);

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

/** @var SwatchRenderer $swatchRendererViewModel */
$swatchRendererViewModel = $viewModels->require(SwatchRenderer::class);

$product = $block->getProduct();
$productId = $product->getId();

$attributes = $block->decorateArray($block->getAllowAttributes());

$layout = $block->getLayout();
$swatchItemBlock = $layout->getBlock('product.swatch.item');
$swatchItemBlock->setData('product_id', $productId);

$tooltipBlockHtml = $block->getBlockHtml('product.swatch.tooltip');
?>
<?php if ($product->isSaleable() && count($attributes)): ?>
    <script>
        function initConfigurableSwatchOptions_<?= (int) $productId ?>() {
            const configurableOptionsComponent = initConfigurableOptions(
                '<?= (int) $productId ?>',
                <?= /* @noEscape */ $block->getJsonConfig() ?>
            );
            const swatchOptionsComponent = initSwatchOptions(
                <?= /* @noEscape */ $block->getJsonSwatchConfig() ?>
            );

            return Object.assign(
                configurableOptionsComponent,
                swatchOptionsComponent
            );
        }
    </script>


    <div x-data="initConfigurableSwatchOptions_<?= (int) $productId ?>()" x-init="init(); initShowSwatchesIntersect();" @private-content-loaded.window="onGetCartData($event.detail.data)" class="relative mb-1">
        <!-- <h2 class="mb-4 text-xl font-medium text-gray-900 title-font">
            <?php // $escaper->escapeHtml(__('Product Options disable:'))
            ?>
        </h2> -->
        <div class="flex flex-col-reverse">
            <?php foreach ($attributes as $attribute): ?>
                <?php $attributeId = $attribute->getAttributeId(); ?>
                <?php $productAttribute = $attribute->getProductAttribute();  ?>
                <?php if ($swatchRendererViewModel->isSwatchAttribute($productAttribute)): ?>
                    <div class="swatch-attribute last:border-b border-container min-h-14 
                            <?= $escaper->escapeHtmlAttr($productAttribute->getAttributeCode()) ?>">
                        <template x-if="showSwatches">
                            <div class="flex flex-col items-start py-4 sm:py-1 w-full border-gray-300"
                                x-data="hyva.modal()">
                                <label class="basis-full text-left text-gray-700 label product-option-label flex flex-wrap h-11" for="attribute<?= $escaper->escapeHtmlAttr($productAttribute->getAttributeCode()) ?>">
                                    <span class="font-bold md:font-bold">
                                        <?= $escaper->escapeHtml($productAttribute->getStoreLabel()) ?>
                                    </span>
                                    <?php /** @var Hyva\Theme\ViewModel\HeroiconsOutline $heroicons */
                                    $heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
                                    ?>
                                    <div>
                                        <?php
                                            $contentModal = '';
                                            $classModal   = 'justify-center';
                                            $dialogClass  = 'modal-view-product';
                                        if ($productAttribute->getAttributeCode() == "merk") {
                                            $classModal   = 'justify-end';
                                            $dialogClass .= ' rounded-r-0';
                                            $contentModal = $block->getLayout()
                                                ->createBlock(\Magento\Cms\Block\Block::class)
                                                ->setBlockId('overlay_advies')
                                                ->toHtml();
                                        } elseif ($productAttribute->getAttributeCode() == "filterklasse") {
                                            $classModal   = 'justify-center';
                                            $contentModal = $block->getLayout()
                                                ->createBlock(\Magento\Cms\Block\Block::class)
                                                ->setBlockId('overlay_filterklasse_uitleg')
                                                ->toHtml();
                                        }
                                            $modal = $modalViewModel->createModal()->withContent('
                                            <div class="relative w-full h-full md:h-auto text-blue-prussian text-justify">
                                                <!-- Modal header -->
                                                <div class="flex items-start justify-end pb-4 lg:pl-24">
                                                    <button class="ml-6 lg:ml-24 -mt-4" @click="hide">
                                                        <span aria-hidden="true" class="text-2xl font-bold text-blue-picton">&times;</span><span class="sr-only">Close</span>
                                                    </button>
                                                </div>
                                                <!-- Modal body -->
                                                <div class="pr-6 py-4 lg:px-24 body-content-scroll content-modal-attr">
                                                    '.$contentModal.'
                                                </div>
                                            </div>')->withContainerClasses('fixed', 'flex', $classModal, 'items-center')
                                        ->withAriaLabelledby('the-label')
                                        ->addDialogClass($dialogClass); ?>
                                        <button type="button" class="text-xs text-blue-picton ml-2" @click="<?= $escaper->escapeHtmlAttr($modal->getShowJs()) ?>">
                                            <?= $heroicons->informationCircleHtml('w-3 h-3') ?>
                                        </button>
                                        <?= /** @noEscape */ $modal ?>
                                    </div>
                                </label>
                                <div class="basis-full w-3/4 sm:ml-2 sm:w-full text-left text-md text-gray-900 product-option-values">
                                    <div class="flex items-center -mx-4 space-x-4 swatch-attribute-options h-11" role="radiogroup">
                                        <template x-for="(item, index) in optionConfig.attributes[<?= (int) $attributeId ?>].options" :key="item.id">
                                            <?= /* @noEscape */ $swatchItemBlock->setData('attribute_id', $attributeId)->toHtml(); ?>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                <?php else: ?>
                    <div class="flex items-center py-2 w-full border-gray-300 last:border-b">
                        <label class="w-1/2 text-left text-gray-700 label" for="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>">
                            <span>
                                <?= $escaper->escapeHtml($attribute->getProductAttribute()->getStoreLabel()) ?>
                            </span>
                        </label>
                        <div class="ml-2 w-1/2 text-left text-gray-900">
                            <select name="super_attribute[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>]" id="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>" class="form-select super-attribute-select" x-on:change="changeOption(<?= (int) $attribute->getAttributeId() ?>, event.target.value)" required="required">
                                <option value="">
                                    <?= $escaper->escapeHtml(__('Choose an Option...')) ?>
                                </option>
                                <template x-for="(item, index) in getAllowedAttributeOptions(<?= (int) $attribute->getAttributeId() ?>)" :key="item.id">
                                    <option :value="item.id" x-html="item.label" :selected="selectedValues[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>] ===
                                item.id">
                                    </option>
                                </template>
                            </select>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>



            <?= /* @noEscape */ $tooltipBlockHtml ?>
        </div>
    </div>

<?php endif; ?>