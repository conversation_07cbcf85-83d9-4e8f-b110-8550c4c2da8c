<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

/**
 * The AlpineJS scope of this file is the method `initCartTotals()` in Magento_Checkout::php-cart/totals.phtml
 */
declare(strict_types=1);

use Magento\Checkout\Block\Cart;
use Magento\Framework\Escaper;

?>
<!-- display subtotal excluding tax -->
<template x-if="segment.code === 'grand_total' && checkoutConfig.includeTaxInGrandTotal">
    <div>
        <div class="flex py-2 pt-6 my-2 mx-auto text-xl md:grid md:grid-cols-2 md:w-full border-b border-container">
            <div class="w-7/12 font-semibold text-left md:w-auto" 
            x-html="`${segment.title} ${includingTaxMessage}`"></div>
            <div class="w-5/12 text-right md:w-auto" x-text="hyva.formatPrice(segment.value)"></div>
        </div>

        <div class="flex py-2 my-2 mx-auto mb-12 text-md md:grid md:grid-cols-2 md:w-full">
            <div class="w-7/12 font-semibold text-left md:w-auto" 
            x-html="`${segment.title} ${excludingTaxMessage}` "></div>
            <div class="w-5/12 text-right md:w-auto" x-text="hyva.formatPrice(totalsData.grand_total)"></div>
        </div>
    </div>
</template>

<!-- display subtotal including tax -->

<template x-if="segment.code === 'grand_total' && !checkoutConfig.includeTaxInGrandTotal">
    
    <div class="flex pb-2 pt-2 my-2 mx-auto text-xl md:grid md:grid-cols-2 md:w-full f-size-cart-m-15 line-cart">
    
        <div class="w-7/12 font-semibold text-left md:w-auto text-cadet-space">
            <?= $escaper->escapeHtml(__('Total')) ?>
        </div>

        <div class="w-5/12 font-semibold text-right md:w-auto text-cadet-space" 
        x-text="hyva.formatPrice(segment.value)"></div>
        
    </div>
    
</template>

    
