<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

/**
 * The AlpineJS scope of this file is the method `initCartTotals()` in Magento_Checkout::php-cart/totals.phtml
 */
?>
<!-- display shipping including and excluding tax -->
<template x-if="segment.code === 'shipping' && quoteData.is_virtual === 0 
    && !!shippingMethod && 'both' === checkoutConfig.reviewShippingDisplayMode &&
    shippingMethod.amount != undefined">
    <div>
        <div class="flex pb-2 my-2 border-b-color text-md lg:text-sm md:grid md:grid-cols-2 md:w-full ">
            <div class="w-7/12 text-left md:w-auto">
                <span class="text-blue-prussian" x-html="`${segment.title} ${excludingTaxMessage}`"></span>
            </div>
            <div class="w-5/12 text-right md:w-auto">
                <span class="text-blue-prussian" x-text="hyva.formatPrice(segment.value)"></span>
            </div>
        </div>

        <div class="flex pb-2 my-2 border-b-color text-md lg:text-sm md:grid md:grid-cols-2 md:w-full ">
            <div class="w-7/12 text-left md:w-auto">
                <span class="text-blue-prussian" x-html="`${segment.title} ${includingTaxMessage}`"></span>
            </div>
            <div class="w-5/12 text-right md:w-auto">
                <span class="text-blue-prussian" x-text="hyva.formatPrice(totalsData.shipping_incl_tax)"></span>
            </div>
        </div>
    </div>
</template>

<!-- display shipping including or excluding tax -->
<template x-if="segment.code === 'shipping' && quoteData.is_virtual === 0 
    && !!shippingMethod && ('including' === checkoutConfig.reviewShippingDisplayMode ||
    'excluding' === checkoutConfig.reviewShippingDisplayMode) &&
    shippingMethod.amount != undefined">
    <div class="flex flex-col pb-2 my-2 border-b-color text-md lg:text-sm md:grid md:grid-cols-2 md:w-full ">
        <div class="w-full flex justify-between col-span-2 f-size-cart-m-15 gap-2">
            <div class="text-left md:w-auto" >
                <template x-if="shippingMethod.carrier_title != undefined">
                    <span class="text-blue-prussian" x-html="`${shippingMethod.carrier_title}`"></span>
                </template>
                <template x-if="shippingMethod.carrier_title == undefined">
                    <span class="text-blue-prussian">
                        <?= $escaper->escapeHtml(__('Shipping costs')) ?>
                    </span>
                </template>
            </div>
            <div class="text-right md:w-auto font-size-subtotal">
                <template x-if="getShippingCost() > 0">
                    <span class="text-blue-prussian text-md lg:text-sm" 
                    x-text="getFormattedShippingCost()"></span>
                </template>
                <template x-if="getShippingCost() <= 0">
                    <span class="uppercase text-blue-prussian text-md lg:text-sm">
                        <?= $escaper->escapeHtml(__('Free')) ?>
                    </span>
                </template>
            </div>
        </div>
        <template x-if="parseFloat(valueCondition) > 0 && getShippingCost() > 0">
            <div class="text-blue-cadet text-3.25 w-full col-span-2 mt-3 
                text-title-shipping-bottom f-size-cart-m-13 mb-3">
                <span><?= $escaper->escapeHtml(__('Order before')) ?></span>
                <span x-html="getFeeRemaining()"></span>
                <span><?= $escaper->escapeHtml(__('for free shipping')) ?> </span>
            </div>
        </template>
    </div>
</template>
