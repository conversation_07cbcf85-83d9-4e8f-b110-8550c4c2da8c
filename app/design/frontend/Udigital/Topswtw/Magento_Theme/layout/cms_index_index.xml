<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="back-botton" remove="true"/>
        <referenceContainer name="page.wrapper" htmlClass="page-wrapper is-nav"/>
        <referenceContainer name="main.content" htmlClass="page-main-full-width" />
        <referenceBlock name="hero" remove="true"/>
        <referenceBlock name="content-1" remove="true"/>
        <referenceBlock name="slider-1" remove="true"/>
        <referenceBlock name="slider-2" remove="true"/>
        <referenceBlock name="catalog.topnav" remove="true"/>
        <referenceContainer name="header.container">
            <block class="Magento\Framework\View\Element\Template" name="js_content" template="Magento_Theme::html/js.phtml">
            </block>
        </referenceContainer>
        <referenceContainer name="page.top">
            <block class="Magento\Framework\View\Element\Template" name="store.menu" group="navigation-sections" template="Magento_Theme::html/container.phtml">
                <arguments>
                    <argument name="title" translate="true" xsi:type="string">Menu</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
