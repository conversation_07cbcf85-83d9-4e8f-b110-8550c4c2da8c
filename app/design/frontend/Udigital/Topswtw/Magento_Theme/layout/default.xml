<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="hyva_modal"/>
    <head>
        <title>Create New Customer Account</title>
    </head>
    <body>
        <referenceContainer name="footer-container" htmlClass="page-footer content-auto"/>

        <referenceContainer name="header-wrapper">
            <referenceBlock name="top.kiyohmicrodata" remove="true"/>
            <!-- <block class="Interactivated\Customerreview\Block\Customerreview" name="top.kiyohmicrodata" as="top.kiyohmicrodata" template="Interactivated_Customerreview::microdata.phtml" /> -->
        </referenceContainer>
        <referenceContainer name="header.container">
            <referenceContainer name="header.panel.wrapper">
                <referenceContainer name="header.panel">
                    <block class="Magento\Framework\View\Element\Template" name="skip_to_content" template="Magento_Theme::html/reviews.phtml">
                        <arguments>
                            <argument name="additional" translate="true" xsi:type="string">reviews</argument>
                            <argument name="login" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Customer</argument>
                        </arguments>
                        <block class="Interactivated\Customerreview\Block\Customerreview" name="review.kiyoh" template="Magento_Theme::html/reviews_kiyoh.phtml">
                            <arguments>
                                <argument name="cache_lifetime" xsi:type="number">3600</argument>
                            </arguments>
                        </block>
                    </block>
                    <block class="Magento\Store\Block\Switcher" name="store_language" as="store_language" template="Magento_Store::switch/languages.phtml">
                        <arguments>
                            <argument name="view_model" xsi:type="object">Magento\Store\ViewModel\SwitcherUrlProvider</argument>
                            <argument name="login" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Customer</argument>
                            <argument name="url_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Url</argument>
                        </arguments>
                    </block>
                </referenceContainer>
            </referenceContainer>

            <referenceContainer name="header-content">
                <referenceBlock name="logo">
                    <arguments>
                        <argument name="logo_file" xsi:type="string">images/logo-image.png</argument>
                        <argument name="logo_width" xsi:type="number">142.595</argument>
                        <argument name="logo_height" xsi:type="number">38.526</argument>
                        <argument name="logoPathResolver" xsi:type="object">Magento\Theme\ViewModel\Block\Html\Header\LogoPathResolver</argument>
                        <argument name="logo_size_resolver" xsi:type="object">Magento\Theme\ViewModel\Block\Html\Header\LogoSizeResolver</argument>
                    </arguments>
                </referenceBlock>
                <referenceBlock name="header-search">
                    <arguments>
                        <argument name="home_action" xsi:type="object">Udigital\Homepage\ViewModel\Home</argument>
                        <argument name="search_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Search</argument>
                    </arguments>
                </referenceBlock>
                <block name="header-search-mobile" class="Smile\ElasticsuiteCore\Block\Search\Form\Autocomplete" >
                    <arguments>
                        <argument name="rendererList" xsi:type="array">
                            <item name="term" xsi:type="array">
                                <item name="title" xsi:type="string" translate="true">Search terms</item>
                                <item name="template" xsi:type="string">Hyva_SmileElasticsuite::core/autocomplete/term.phtml</item>
                            </item>
                            <item name="product" xsi:type="array">
                                <item name="title" xsi:type="string" translate="true">Products</item>
                                <item name="template" xsi:type="string">Hyva_SmileElasticsuite::catalog/autocomplete/product.phtml</item>
                            </item>
                            <item name="category" xsi:type="array">
                                <item name="title" xsi:type="string" translate="true">Categories</item>
                                <item name="template" xsi:type="string">Hyva_SmileElasticsuite::catalog/autocomplete/category.phtml</item>
                            </item>
                            <item name="product_attribute" xsi:type="array">
                                <item name="title" xsi:type="string" translate="true">Attributes</item>
                                <item name="template" xsi:type="string">Hyva_SmileElasticsuite::catalog/autocomplete/product_attribute.phtml</item>
                                <item name="titleRenderer" xsi:type="string">renderEsAutocompleteTitleAttribute</item>
                            </item>
                            <item name="cms_page" xsi:type="array">
                                <item name="title" xsi:type="string" translate="true">Cms page</item>
                                <item name="template" xsi:type="string">Hyva_SmileElasticsuite::cms-search/autocomplete/cms.phtml</item>
                            </item>
                        </argument>
                    </arguments>
                    <action method="setTemplate">
                        <argument name="template" xsi:type="string">Hyva_SmileElasticsuite::core/search/form.mini-mobile.phtml</argument>
                    </action>
                    <arguments>
                        <argument name="home_action" xsi:type="object">Udigital\Homepage\ViewModel\Home</argument>
                        <argument name="search_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Search</argument>
                    </arguments>
                </block>
                <referenceBlock name="cart-drawer" template="Magento_Theme::html/cart/cart-drawer.phtml" >
                    <arguments>
                        <argument name="cart_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Cart</argument>
                    </arguments>
                    <block name="cart-drawer-loading" as="loading" template="Hyva_Theme::ui/loading.phtml"/>
                    <block class="Magento\Checkout\Block\QuoteShortcutButtons" name="extra_actions"/>
                    <container name="cart-drawer.top"/>
                    <container name="cart-drawer.items.before"/>
                    <container name="cart-drawer.totals.before"/>
                    <container name="cart-drawer.bottom"/>
                </referenceBlock>
                <referenceBlock name="topmenu_generic">
                    <arguments>
                        <argument name="cache_lifetime" xsi:type="null" />
                    </arguments>
                    <referenceBlock name="topmenu_mobile">
                        <arguments>
                            <argument name="item" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Product</argument>
                            <argument name="category" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
                            <argument name="url_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Url</argument>
                        </arguments>
                    </referenceBlock>
                    <block class="Magento\Theme\Block\Html\Topmenu" name="topmenu_desktop" as="topmenu.desktop" template="Magento_Theme::html/header/menu/desktop.phtml" >
                        <arguments>
                            <argument name="theme_action" xsi:type="object">Udigital\StoreInformation\ViewModel\Theme</argument>
                            <argument name="menu_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Theme</argument>
                        </arguments>
                    </block>
                </referenceBlock>
            </referenceContainer>
        </referenceContainer>

        <block class="Magento\Theme\Block\Html\Footer"
            name="footer_other"
            template="Magento_Theme::html/footer_other.phtml">
            <arguments>
                <argument name="login" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Customer</argument>
            </arguments>
        </block>

        <referenceContainer name="footer-container">
            <referenceContainer name="footer">
                <block class="Magento\Theme\Block\Html\Footer" name="footer_before" template="Magento_Theme::html/footer_before.phtml" before="-">
                    <arguments>
                        <argument name="additional" translate="true" xsi:type="string">reviews</argument>
                        <argument name="login" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Customer</argument>
                    </arguments>
                </block>
                <referenceBlock name="footer-content">
                    <arguments>
                        <argument name="theme_action" xsi:type="object">Udigital\StoreInformation\ViewModel\Theme</argument>
                        <argument name="menu_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Theme</argument>
                        <argument name="login" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Customer</argument>
                        <argument name="category" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Category</argument>
                        <argument name="url_action" xsi:type="object">Udigital\CategoryAttribute\ViewModel\Url</argument>
                    </arguments>
                </referenceBlock>
                <referenceBlock name="store_switcher" remove="true" />
                <referenceBlock name="footer_links" remove="true"/>
                <referenceBlock name="form.subscribe" remove="true"/>
                <referenceBlock name="report.bugs" remove="true"/>
            </referenceContainer>
        </referenceContainer>
        <referenceContainer name="columns">
            <referenceContainer name="div.sidebar.additional" htmlClass="sidebar sidebar-additional block account-nav"/>
        </referenceContainer>
        <move element="footer_other" destination="page.wrapper" before="page.bottom.container"/>
        <move element="footer_before" destination="page.bottom.container"/>
        <move element="div.sidebar.additional" destination="div.sidebar.main" after="-"/>
    </body>
</page>
