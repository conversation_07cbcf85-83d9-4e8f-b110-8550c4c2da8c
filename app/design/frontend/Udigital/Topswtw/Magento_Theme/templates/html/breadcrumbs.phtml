<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Theme\Block\Html\Breadcrumbs;
use Magento\Framework\Escaper;

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Breadcrumbs $block */
/** @var array[] $crumbs */

/** @var \Tops\Breadcrumb\ViewModel\ParentCategory $viewModel */
$viewModel = $block->getViewModel();
$parentCategoryUrl = $viewModel->getParentCategoryUrl();

$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

?>
<?php if ($crumbs && is_array($crumbs)): ?>
<nav class="breadcrumbs bg-container-lighter" aria-label="Breadcrumb">
    <div class="container">
        <div class="flex flex-nowrap pt-4 items-center">
            <a href="<?= $block->escapeUrl($parentCategoryUrl) ?>" onclick="history.back()" class="back-home py-1 flex
                flex-nowrap text-sm text-blue-picton">
                <?=
                    $heroiconsSolid->renderHtml(
                        'chevron-left',
                        'mr-2 fill-current text-blue-picton max-md:hidden',
                        19,
                        19
                    );
                ?>
                <?= $escaper->escapeHtml(__("Back")) ?>
            </a>
            <span class="py-1 mx-4"> | </span>
            <ol class="items list-reset py-1 rounded flex flex-nowrap text-blue-picton text-sm whitespace-nowrap my-auto h-full overflow-x-auto no-scrollbar">
                <?php foreach ($crumbs as $crumbName => $crumbInfo): ?>
                    <li class="item flex <?= $escaper->escapeHtmlAttr($crumbName) ?>">
                    <?php if (!$crumbInfo['first']): ?>
                        <?=
                            $heroiconsSolid->renderHtml(
                                'chevron-right',
                                'fill-current text-blue-picton separator text-sm mx-2',
                                19,
                                19
                            );
                        ?>
                    <?php endif; ?>
                    <?php if ($crumbInfo['link']): ?>
                        <a href="<?= $escaper->escapeUrl($crumbInfo['link']) ?>"
                        class="no-underline text-blue-picton"
                        title="<?= $escaper->escapeHtmlAttr($crumbInfo['title']) ?>"
                        ><?= $escaper->escapeHtml($crumbInfo['label']) ?></a>
                    <?php elseif ($crumbInfo['last']): ?>
                        <span
                        class="text-blue-prussian"
                        aria-current="page"
                        ><?= $escaper->escapeHtml($crumbInfo['label']) ?></span>
                    <?php else: ?>
                        <?= $escaper->escapeHtml($crumbInfo['label']) ?>
                    <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ol>
        </div>
    </div>
</nav>
<?php endif; ?>
