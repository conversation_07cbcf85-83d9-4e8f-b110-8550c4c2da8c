<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

$storeConfig = $viewModels->require(StoreConfig::class);
$maxItemsToDisplay = $storeConfig->getStoreConfig('checkout/sidebar/max_items_display_count');

/** @var HeroiconsOutline $heroicons */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<script>
    function initCartDrawer() {
        return {
            open: false,
            isLoading: false,
            dispatch: null,
            cart: {},
            checkoutConfig: null,
            shippingMethod: null,
            valueCondition: null,
            taxData: null,
            totalsData: null,
            maxItemsToDisplay: <?= (int) $maxItemsToDisplay ?>,
            itemsCount: 0,
            totalCartAmount: 0,
            cartItems: [],
            getData(data) {
                if (!data) return;
                if (data.cart) {
                    this.cart = data.cart;
                    this.itemsCount = Array.isArray(data.cart.items) ? data.cart.items.length : 0;
                    this.totalCartAmount = this.cart.summary_count ?? 0;
                    this.setCartItems();
                }

                this.shippingMethod = data.selectedshippingmethod?.data ?? data.selectedShippingMethod ?? null;
                this.valueCondition = data.valuecondition?.data ?? data.valueCondition ?? null;
                this.totalsData = data.totalsdata?.data ?? data.totalsData ?? null;
                this.taxData = data.taxdata ?? data.taxData ?? null;

                this.isLoading = false;
            },
            initMinicart($dispatch){
                this.dispatch = $dispatch;
            },
            updateShippingMethod(shippingMethod) {
                this.shippingMethod = shippingMethod;
            },
            eventListenersMinicart: {
                ['@private-content-loaded.window']($event) {
                    this.getData($event.detail.data)
                },
                ['@update-checkout-data.window']($event) {
                    this.getData($event.detail.data)
                },
                ['@toggle-cart.window']() {
                    this.open = true
                },
                ['@keydown.window.escape']() {
                    this.open = false;
                },
                ['@update-shipping-method.window']($event) {
                    this.updateShippingMethod($event.detail.method)
                },
                ['@update-shipping-method-minicart.window']($event) {
                    this.updateShippingMethod($event.detail.method)
                }
            },
            getItemCountTitle() {
                return hyva.strf('(%0 <?= $escaper->escapeJs(__('of')) ?> %1)', this.maxItemsToDisplay, this.itemsCount)
            },
            getFormattedPrice(price) {
                return hyva.formatPrice(price)
            },
            getFormattedShippingCost() {
                return this.getFormattedPrice(this.getShippingCost());
            },
            getShippingCost() {
                let cost = 0;
                if (this.taxData) {
                    const modeTax = this.taxData.reviewShippingDisplayMode;
                    if (modeTax === 'including') {
                        if (this.totalsData.shipping_incl_tax != undefined &&
                            this.totalsData.shipping_incl_tax != 0) {
                            cost = parseFloat(this.totalsData.shipping_incl_tax)
                        }
                        if (this.shippingMethod && cost == 0) {
                            cost = this.shippingMethod.price_incl_tax;
                        }
                    } else if (modeTax === 'excluding') {
                        if (this.totalsData.shipping_amount != undefined &&
                            this.totalsData.shipping_amount != 0) {
                            cost = parseFloat(this.totalsData.shipping_amount)
                        }
                        if (this.shippingMethod && cost == 0) {
                            cost = this.shippingMethod.price_excl_tax;
                        }
                    }
                }

                if (this.shippingMethod &&
                    this.shippingMethod.amount != undefined &&
                    this.shippingMethod.amount != 0 &&
                    cost == 0
                ) {
                    cost = this.shippingMethod.amount;
                }
                return cost;
            },
            isNormalPrice(price, final_price) {
                const fix_price = parseFloat(price).toFixed(2);
                const fix_final_price = parseFloat(final_price).toFixed(2);
                return fix_final_price > fix_price ? true : false;
            },
            getFeeRemaining() {
                const remaining = parseFloat(this.valueCondition) - parseFloat(this.cart.subtotalAmount);

                if (remaining <= 0) {
                    return null;
                }

                return hyva.formatPrice(remaining)
            },
            getFormattedToPay() {
                const subtotal = this.cart.subtotalAmount;
                const shippingCost = this.getShippingCost();

                return hyva.formatPrice(parseFloat(subtotal) + this.getShippingCost());
            },
            setCartItems() {
                this.cartItems = this.cart.items && this.cart.items.sort((a, b) => b.item_id - a.item_id) || [];

                if (this.maxItemsToDisplay > 0) {
                    this.cartItems = this.cartItems.slice(0, parseInt(this.maxItemsToDisplay, 10));
                }
            },
            deleteItemFromCart(itemId) {
                this.isLoading = true;

                const formKey = hyva.getFormKey();
                const postUrl = BASE_URL + 'checkout/sidebar/removeItem/';

                fetch(postUrl, {
                    "headers": {
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                    "body": "form_key=" + formKey + "&item_id=" + itemId,
                    "method": "POST",
                    "mode": "cors",
                    "credentials": "include"
                }).then(response => {
                    if (response.redirected) {
                        window.location.href = response.url;
                    } else if (response.ok) {
                        return response.json();
                    } else {
                        window.dispatchMessages && window.dispatchMessages([{
                            type: 'warning',
                            text: '<?= $escaper->escapeJs(__('Could not remove item from quote.')) ?>'
                        }]);
                        this.isLoading = false;
                    }
                }).then(result => {
                    let checkoutConfigData
                    if (result.success) {
                        checkoutConfigData = result.checkout;
                        if (checkoutConfigData) {
                            this.checkoutConfig = checkoutConfigData;
                            this.dispatch('update-checkout-data', {data: checkoutConfigData});
                            if (checkoutConfigData.totalsData) {
                                this.dispatch('update-totals', {data: checkoutConfigData.totalsData});
                            }
                            if (checkoutConfigData.selectedShippingMethod) {
                                this.dispatch('update-shipping-method', {
                                    method: checkoutConfigData.selectedShippingMethod
                                });
                                this.dispatch('update-shipping-method-minicart', {
                                    method: checkoutConfigData.selectedShippingMethod
                                });
                            }
                        }
                    }
                    window.dispatchMessages && window.dispatchMessages([{
                        type: result.success ? 'success' : 'error',
                        text: result.success
                            ? '<?= $escaper->escapeJs(__('You removed the item.')) ?>'
                            : result.error_message
                    }], result.success ? 5000 : 0)

                    window.dispatchEvent(new CustomEvent('reload-customer-section-data'));

                    this.isLoading = false;
                });
            },
            updateItemFromCart(qty, itemId, type) {
                if (qty > 0) {
                    this.isLoading = true;

                    const formKey = hyva.getFormKey();
                    const postUrl = BASE_URL + 'checkout/sidebar/updateItemQty/';

                    fetch(postUrl, {
                        "headers": {
                            "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                        },
                        "body": "form_key=" + formKey + "&item_id=" + itemId+"&item_qty="+qty,
                        "method": "POST",
                        "mode": "cors",
                        "credentials": "include"
                    }).then(response => {
                        if (response.redirected) {
                            window.location.href = response.url;
                        } else if (response.ok) {
                            return response.json();
                        } else {
                            window.dispatchMessages && window.dispatchMessages([{
                                type: 'warning',
                                text: '<?= $escaper->escapeJs(__('Could not update item from quote.')) ?>'
                            }]);
                            this.isLoading = false;
                        }
                    }).then(result => {
                        this.isLoading = false;
                        if (result.success) {
                            const checkoutConfigData = result.checkout;
                            if (checkoutConfigData) {
                                this.checkoutConfig = checkoutConfigData;
                                this.dispatch('update-checkout-data', {
                                    data: checkoutConfigData
                                });
                                if (checkoutConfigData.totalsData) {
                                    this.dispatch('update-totals', {
                                        data: checkoutConfigData.totalsData
                                    });
                                }
                                if (checkoutConfigData.selectedShippingMethod) {
                                    this.dispatch('update-shipping-method-minicart', {
                                        method: checkoutConfigData.selectedShippingMethod
                                    });
                                }
                            }
                        }
                        window.dispatchMessages && window.dispatchMessages([{
                            type: result.success ? 'success' : 'error',
                            text: result.success
                                ? '<?= $escaper->escapeJs(__('You updated the item.')) ?>'
                                : result.error_message
                        }], result.success ? 5000 : 0)
                    });
                }
            }
        }
    }
</script>
<section id="cart-drawer"
         x-data="initCartDrawer()"
         x-init="initMinicart($dispatch)"
         x-bind="eventListenersMinicart"
>
    <template x-if="cart && cart.summary_count">
        <div role="dialog"
             aria-labelledby="cart-drawer-title"
             aria-modal="true"
             @click.outside="open=false"
             class="fixed inset-y-0 right-0 z-30 flex max-w-full">
            <div class="backdrop"
                 x-show="open"
                 x-transition:enter="ease-in-out duration-500"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="ease-in-out duration-500"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 @click="open=false"
                 aria-label="Close panel"></div>
            <div class="relative w-screen max-w-md shadow-2xl"
                 x-show="open"
                 x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
                 x-transition:enter-start="translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="translate-x-full"
            >
                <div
                    x-show="open"
                    x-transition:enter="ease-in-out duration-500"
                    x-transition:enter-start="opacity-0"
                    x-transition:enter-end="opacity-100"
                    x-transition:leave="ease-in-out duration-500"
                    x-transition:leave-start="opacity-100"
                    x-transition:leave-end="opacity-0" class="absolute top-0 right-0 flex p-2 mt-2">
                    <button @click="open=false" aria-label="Close panel"
                            class="px-3 pt-6 pb-2 text-blue-cadet
                            transition duration-150 ease-in-out hover:text-black">
                        <?= $heroiconsSolid->renderHtml('x', 'text-blue-cadet mr-2 stroke-current', 24, 24); ?>
                    </button>
                </div>
                <div class="flex flex-col h-full pt-6 bg-white shadow-xl">
                    <?= $block->getChildHtml('cart-drawer.top'); ?>

                    <header class="px-4 sm:px-6 py-4">
                        <h2 id="cart-drawer-title" class="text-2xl font-bold leading-7 text-blue-prussian">
                            <?= $escaper->escapeHtml(__('Shopping cart')) ?>
                            <span class="items-total text-xs"
                                  x-show="maxItemsToDisplay && maxItemsToDisplay < itemsCount"
                                  x-text="getItemCountTitle()">
                            </span>
                        </h2>
                    </header>

                    <?= $block->getChildHtml('cart-drawer.items.before'); ?>

                    <div class="flex flex-col justify-between h-full">
                        <div class="relative grid gap-2 px-4 overflow-y-auto bg-white
                            sm:px-6 border-container max-h-screen-52">
                            <hr class="border-white-azureish mt-4">
                            <template x-for="item in cartItems">
                                <div class="flex items-start px-3 pt-3 pb-6 space-x-4 transition duration-150
                                    ease-in-out border-b border-white-azureish">
                                    <a :href="item.product_url" class="w-1/4">
                                        <img
                                            :src="item.product_image.src"
                                            :width="item.product_image.width"
                                            :height="item.product_image.height"
                                            loading="lazy"
                                        />
                                    </a>
                                    <div class="w-3/4 space-y-2" x-data="{ qty_default: item.qty}"
                                         x-init="$watch('item.qty', (value) => item.qty = value <= 0 ? 1 : value)">
                                        <div class="grid grid-cols-5 gap-4">
                                            <div class="col-span-4">
                                                <div>
                                                    <p class="text-sm-1 font-bold text-blue-prussian">
                                                        <span x-html="item.product_name"></span>
                                                    </p>
                                                    <p class="text-sm-1 mt-2 mb-1 text-blue-cadet">
                                                        <span x-html="item.trade_item_code"></span>
                                                    </p>
                                                </div>
                                                <template x-for="option in item.options">
                                                    <div class="pt-2">
                                                        <p class="font-semibold text-sm
                                                        text-blue-prussian" x-text="option.label + ':'"></p>
                                                        <p class="text-blue-cadet" x-html="option.value"></p>
                                                    </div>
                                                </template>
                                            </div>
                                            <div class="col-span-1 text-right">
                                                <button
                                                    class="inline-flex px-2 pb-2"
                                                    @click="deleteItemFromCart(item.item_id)">
                                                    <?=
                                                    $heroiconsSolid->renderHtml(
                                                        'trash-alt',
                                                        'text-blue-picton',
                                                        20,
                                                        20
                                                    );
                                                    ?>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-4 gap-4">
                                            <div class="col-span-2 flex items-center">
                                                <div class="minicart-item-quantity">
                                                    <input type="number" name="qty" required readonly
                                                           x-model="item.qty"
                                                           x-on:change.debounce="updateItemFromCart(
                                                        item.qty, item.item_id)"
                                                           title="Qty" class="w-20 mr-4">
                                                    <div class="item-quantity-nav">
                                                        <div class="item-quantity-button item-quantity-up"
                                                             @click="(item.qty < (item.skwirrel_status == 138 ? 5 :
                                                            (item.skwirrel_status == 152 ? 1 : 9999))
                                                            ? item.qty++ : ''),
                                                            updateItemFromCart(item.qty, item.item_id)"
                                                        >
                                                            <?=
                                                            $heroiconsSolid->renderHtml(
                                                                'chevron-up',
                                                                'fill-current text-blue-prussian mr-1.5 mt-1.5',
                                                                18,
                                                                18,
                                                                ['aria-hidden' => 'true']
                                                            );
                                                            ?>
                                                        </div>
                                                        <div class="item-quantity-button item-quantity-down"
                                                             @click="item.qty--, (item.qty > 0 ?
                                                            updateItemFromCart(item.qty, item.item_id) : '')"
                                                        >
                                                            <?=
                                                            $heroiconsSolid->renderHtml(
                                                                'chevron-down',
                                                                'fill-current text-blue-prussian mr-1.5 mb-1.5',
                                                                18,
                                                                18,
                                                                ['aria-hidden' => 'true']
                                                            );
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-span-2 text-right">
                                                <p class="mr-3 font-bold text-blue-prussian">
                                                    <span x-html="getFormattedPrice(item.qty *
                                                    item.product_price_value)"></span>
                                                </p>
                                                <span class="text-blue-prussian text-sm-1"
                                                      x-html="getFormattedPrice(item.product_price_value)"></span>
                                                <span class="mr-3 text-blue-prussian text-sm-1"
                                                      x-html="item.per_set"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div class="relative gap-6 px-4 pt-6 mb-6 bg-white sm:gap-8 sm:px-6">
                            <?= $block->getChildHtml('cart-drawer.totals.before'); ?>
                            <div class="w-full py-3 transition duration-150 ease-in-out pr-0">
                                <div class="grid grid-cols-2 gap-4 text-sm+1">
                                    <div class="col-span-1">
                                        <span class="text-blue-prussian">
                                            <?= $escaper->escapeHtml(__('Subtotal')) ?>
                                        </span>
                                    </div>
                                    <div class="col-span-1 text-right">
                                        <span class="text-blue-prussian"
                                              x-html="getFormattedPrice(cart.subtotalAmount)"></span>
                                    </div>
                                </div>
                                <template x-if="shippingMethod && shippingMethod.amount != undefined">
                                    <div class="grid grid-cols-2 gap-4 text-sm+1 mt-4">
                                        <div class="col-span-1">
                                            <span class="text-blue-prussian">
                                                <?= $escaper->escapeHtml(__('Shipping costs')) ?>
                                            </span>
                                        </div>
                                        <div class="col-span-1 text-right">
                                            <template x-if="getShippingCost() > 0">
                                                <span class="text-blue-prussian uppercase"
                                                      x-text="getFormattedShippingCost()"></span>
                                            </template>
                                            <template x-if="getShippingCost() <= 0">
                                                <span class="text-blue-prussian">
                                                    <?= $escaper->escapeHtml(__('Free')) ?>
                                                </span>
                                            </template>
                                        </div>
                                        <template x-if="getFeeRemaining()">
                                            <div class="col-span-2 -mt-4 pt-1">
                                                <span class="text-blue-cadet text-sm-1">
                                                    <span>
                                                        <?= $escaper->escapeHtml(__('Order before')) ?>
                                                    </span>
                                                    <span x-html="getFeeRemaining()"></span>
                                                    <span>
                                                        <?= $escaper->escapeHtml(__('for free shipping')) ?>
                                                    </span>
                                                </span>
                                            </div>
                                        </template>
                                    </div>
                                </template>
                            </div>

                            <hr class="border-blue-prussian my-4">

                            <div class="w-full py-3 transition duration-150 ease-in-out
                                rounded-lg pr-0">
                                <div class="grid grid-cols-2 gap-4 pb-6 text-sm+1">
                                    <div class="col-span-1">
                                        <span class="text-blue-prussian font-bold">
                                            <?= $escaper->escapeHtml(__('Total')) ?>
                                        </span>
                                    </div>
                                    <div class="col-span-1 text-right">
                                        <span class="text-blue-prussian font-bold"
                                              x-html="getFormattedToPay()">
                                        </span>
                                    </div>
                                </div>

                                <div class="mt-2">
                                    <a href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart')) ?>"
                                       class="inline-flex justify-center btn btn-green-islamic w-full h-11.5">
                                        <?= $escaper->escapeHtml(__('Proceed to order')) ?>
                                    </a>
                                </div>
                            </div>
                            <?= $block->getChildHtml('extra_actions') ?>
                            <?= $block->getChildHtml('cart-drawer.bottom'); ?>
                        </div>
                    </div>
                </div>
            </div>
            <?= $block->getChildHtml('loading') ?>
        </div>
    </template>
</section>
