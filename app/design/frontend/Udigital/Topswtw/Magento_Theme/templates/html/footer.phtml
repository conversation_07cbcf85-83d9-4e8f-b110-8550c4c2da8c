<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$categories       = $block->getCategory()->getFooterCategories(9,"ASC");
$webLink          = $block->getCategory()->getInfoMail();
$titleTopsfilters = $block->getCategory()->getTitleTopsFilter();
$isCategories     = !empty($categories->toArray());
$customerService  = $block->getThemeAction()->getCustomerService();
$allLanguages     = $block->getMenuAction()->getLanguageAll();

$websiteCode = $block->getCategory()->getWebsite()->getCode();
$exCode   = explode("_", $websiteCode);
$lastWeb  = end($exCode);
?>
<div class="grid sm:grid-cols-4 lg:grid-cols-5 gap-8 lg:gap-0 footer-wrapper md:ml-8 xl:ml-0">
    <div class="sm:col-span-4 md:col-span-1 lg:col-span-2 xl:col-span-1
    xl:flex items-end justify-end lg:justify-center xl:justify-end">
        <!-- desktop -->
        <div class="hidden xl:grid grid-cols-3 md:grid-cols-1 gap-2">
            <div class="md:col-span-1">
                <img width="294" height="416"
                src="<?= $escaper->escapeUrl(
                    $block->getCategory()->resizeImageTheme(
                        "images/sven_home/afbeelding.png",
                        294,
                        416
                    )
                ); ?>"
                alt="sven_home" class="topswtw-img-sven-home-desktop" />
            </div>
        </div>
        <!-- mobile -->
        <div class="flex flex-row md:hidden w-full">
            <div class="w-11/20 pl-4">
                <div class="mb-3 md:mt-4 md:mb-4">
                    <h3 class="text-blue-picton md:text-white-azureish text-3.75
                    md:text-xl font-bold">
                        <?= $escaper->escapeHtml(__('Do you have a question?')) ?>
                    </h3>
                </div>
                <div class="flex items-start justify-between mb-3 md:mb-5">
                    <div class="mr-3 md:mr-4 h-[35px] md:h-11.25 flex items-center justify-center">
                        <?= $heroiconsSolid->renderHtml('phone-alt', 'text-white', 20, 20); ?>
                    </div>
                    <div class="w-full">
                        <div class="text-3.25 text-white md:text-white-azureish h-8.75 md:h-11.25 flex items-center">
                            <a href="tel://<?= $escaper->escapeUrl('00'.str_replace(
                                " ",
                                "",
                                substr((string)(
                                    $block->getThemeAction()->getStoreInformation("phone")
                                ), 1)
                            )); ?>">
                                <?= $escaper->escapeHtml($block->getThemeAction()->getStoreInformation("phone")); ?>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="flex items-start justify-between mb-3 md:mb-5">
                    <div class="mr-3 md:mr-4 h-[35px] md:h-11.25 flex items-center justify-center">
                        <?= $heroiconsSolid->renderHtml('mail', 'text-white', 24, 24); ?>
                    </div>
                    <div class="w-full">
                        <div class="text-3.25 text-white md:text-white-azureish h-8.75 md:h-11.25 flex items-center">
                            <a target="_blank"
                               href="mailto:<?= $escaper->escapeHtml(
                                   $block->getThemeAction()->getStoreInformation("email")
                               ); ?>"
                               target="__blank">
                                <?= // $block->escapeHtml( __('Mail ons'))
                                $escaper->escapeHtml($block->getThemeAction()->getStoreInformation("email")); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-9/20 flex justify-center">
                <img class="mr-2" width="175" height="248"
                    src="<?= $escaper->escapeUrl($block->getCategory()->resizeImageTheme(
                        "images/sven_home/afbeelding.png",
                        175,
                        248
                    )); ?>"
                    alt="sven_home_nl_n-mobile" />
            </div>
        </div>
    </div>
    <div class="hidden md:block sm:col-span-4 md:col-span-4 lg:col-span-5 xl:col-span-4 ml-0 lg:ml-8">
        <div class="grid sm:grid-cols-1 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-4 mt-2 gap-footer-content">
            <div class="col-span-1 footer-content-data">
                <div class="mb-5 lg:mb-10">
                    <h3 class="text-blue-picton md:text-white-azureish text-3.75 md:text-xl font-bold">
                        <?= $escaper->escapeHtml(__('Do you have a question?')) ?>
                    </h3>
                </div>
                <div class="flex items-start justify-between mb-8">
                    <div class="mr-4 h-11.25 flex items-center justify-center">
                        <?= $heroiconsSolid->renderHtml('phone-alt', 'text-white', 24, 24); ?>
                    </div>
                    <div class="w-full mt-2">
                        <a href="tel://<?= $escaper->escapeUrl('00'.str_replace(
                            " ",
                            "",
                            substr((string)(
                                    $block->getThemeAction()->getStoreInformation("phone")
                                ), 1)
                        )); ?>" class="text-base text-white md:text-white-azureish">
                            <?= // $block->escapeHtml( __('Call'))
                            $escaper->escapeHtml($block->getThemeAction()->getStoreInformation("phone")); ?>
                        </a>
                    </div>
                </div>
                <div class="flex items-start justify-between mb-8">
                    <a href="mailto:<?= $escaper->escapeHtml($block->getThemeAction()->getStoreInformation("email")); ?>"
                    target="__blank"
                    class="mr-4 h-11.25 flex items-center justify-center">
                        <?= $heroiconsSolid->renderHtml('mail', 'text-white', 24, 24); ?>
                    </a>
                    <div class="w-full">
                        <div class="text-base text-white md:text-white-azureish
                        h-8.75 md:h-11.25 flex items-center">
                            <a target="_blank"
                            href="mailto:<?= $escaper->escapeHtml(
                                $block->getThemeAction()->getStoreInformation("email")
                            ); ?>"
                            target="__blank">
                                <?= // $block->escapeHtml( __('Mail ons'))
                                $escaper->escapeHtml($block->getThemeAction()->getStoreInformation("email")); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-1 footer-content-data">
                <div class="mb-5">
                    <h3 class="text-3.75 md:text-xl font-bold">
                        <?= $escaper->escapeHtml(__('Customer Service')) ?>
                    </h3>
                </div>
                <?php if (!empty($customerService)): ?>
                    <?php foreach ($customerService as $cs): ?>
                        <div class="mb-3">
                            <a class="text-sm" href="<?= $escaper->escapeUrl($block->getUrl($cs['link'])) ?>">
                                <?= $escaper->escapeHtml(__($cs['title'])) ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="mb-3">
                        <a class="text-sm" href="#">
                            <?= $escaper->escapeHtml(__("Empty")) ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            <div class="md:col-span-1 footer-content-data hidden md:block">
                <div class="mb-5">
                    <h3 class="text-3.75 md:text-xl font-bold">
                        <?= $escaper->escapeHtml(__('Categories')) ?>
                    </h3>
                </div>
                <?php if ($isCategories): ?>
                    <?php foreach ($categories as $category): ?>
                        <div class="mb-3">
                            <a class="text-sm" href="<?= $escaper->escapeUrl($category->getUrl()); ?>">
                                <?= $escaper->escapeHtml(__($category->getName())) ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="mb-3"><?= $escaper->escapeHtml(__('Empty')) ?></div>
                <?php endif; ?>
            </div>
            <div class="col-span-1 footer-content-data">
                <div class="mb-5">
                    <h3 class="text-3.75 md:text-xl font-bold"
                    allLanguages="<?= $escaper->escapeHtml($allLanguages); ?>">
                        <?= $escaper->escapeHtml($titleTopsfilters) ?>
                    </h3>
                </div>
                <div class="mb-3">
                    <a class="text-sm"
                    href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlAboutUs())) ?>">
                        <?= $escaper->escapeHtml(__('About us')) ?>
                    </a>
                </div>
                <div class="mb-3">
                    <a class="text-sm"
                    href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getRouteBlog())) ?>">
                        <?= $escaper->escapeHtml(__('Blogs')) ?>
                    </a>
                </div>
                <div class="mb-3">
                    <a class="text-sm"
                    href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlAdvantages())) ?>">
                        <?= $escaper->escapeHtml(__('Tops advantages')) ?>
                    </a>
                </div>
                <div class="mb-3">
                    <a class="text-sm"
                    href="<?= $escaper->escapeUrl($block->getUrl('contact')) ?>">
                        <?= $escaper->escapeHtml(__('Contact')) ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<div x-data="{ open_customer_service: false, open_our_assortment: false,
    open_about_tops_filters: false, open_my_account: false  }"
    class="grid grid-cols-1 gap-4 border-t border-blue-picton md:hidden">
    <div class="col-span-1 border-b border-blue-picton">
        <div class="my-4 mx-4">
            <div class="cursor-pointer flex justify-between"
                id="footer-customer-service"
                @click.prevent="open_customer_service = true"
                :aria-expanded="open_customer_service ? 'true' : 'false'"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Customer Service')) ?>"
            >
                <h3 class="font-semibold text-3.25 flex items-center">
                    <?= $escaper->escapeHtml(__('Customer Service')) ?>
                </h3>
                <div :class="{'hidden': open_customer_service ,'block': !open_customer_service }">
                    <?= $heroiconsSolid->renderHtml('chevron-down', 'text-white-azureish', 24, 24); ?>
                </div>
                <div :class="{'hidden': !open_customer_service ,'block': !open_customer_service }">
                    <?= $heroiconsSolid->renderHtml('chevron-up', 'text-white-azureish', 24, 24); ?>
                </div>
            </div>
            <div class="mt-4"
                x-cloak x-show="open_customer_service"
                @click.outside="open_customer_service = false"
                aria-labelledby="footer-customer-service"
            >
                <?php if (!empty($customerService)): ?>
                    <?php foreach ($customerService as $cs): ?>
                        <div class="mb-3 text-3.25">
                            <a href="<?= $escaper->escapeUrl($block->getUrl($cs['link'])) ?>">
                                <?= $escaper->escapeHtml(__($cs['title'])) ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="mb-3 text-3.25">
                        <a href="#">
                            <?= $escaper->escapeHtml(__("Empty")) ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="col-span-1 border-b border-blue-picton">
        <div class="mb-4 mx-4">
            <div class="cursor-pointer flex justify-between"
                id="footer-our-assortment"
                @click.prevent="open_our_assortment = true"
                :aria-expanded="open_our_assortment ? 'true' : 'false'"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Our Assortment')) ?>"
            >
                <h3 class="font-semibold text-3.25 flex items-center">
                    <?= $escaper->escapeHtml(__('Our Assortment')) ?>
                </h3>
                <div :class="{'hidden': open_our_assortment ,'block': !open_our_assortment }">
                    <?= $heroiconsSolid->renderHtml('chevron-down', 'text-white-azureish', 24, 24); ?>
                </div>
                <div :class="{'hidden': !open_our_assortment ,'block': !open_our_assortment }">
                    <?= $heroiconsSolid->renderHtml('chevron-up', 'text-white-azureish', 24, 24); ?>
                </div>
            </div>

            <div class="mt-4"
                x-cloak x-show="open_our_assortment"
                @click.outside="open_our_assortment = false"
                aria-labelledby="footer-our-assortment"
            >
                <?php if (!empty($categories)): ?>
                    <?php foreach ($categories as $category): ?>
                        <div class="mb-3 text-3.25">
                            <a href="<?= $escaper->escapeUrl($category->getUrl()); ?>">
                                <?= $escaper->escapeHtml(__($category->getName())) ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="mb-3 text-3.25"><?= $escaper->escapeHtml(__('Empty')) ?></div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="col-span-1 border-b border-blue-picton">
        <div class="mb-4 mx-4">
            <div class="cursor-pointer flex justify-between"
                id="footer-about-tops-filters"
                @click.prevent="open_about_tops_filters = true"
                :aria-expanded="open_about_tops_filters ? 'true' : 'false'"
                aria-label="<?= $escaper->escapeHtmlAttr(__('About Tops Filters')) ?>"
            >
                <h3 class="font-semibold text-3.25 flex items-center">
                    <?= $escaper->escapeHtml(__('About Tops Filters')) ?>
                </h3>
                <div :class="{'hidden': open_about_tops_filters ,'block': !open_about_tops_filters }">
                    <?= $heroiconsSolid->renderHtml('chevron-down', 'text-white-azureish', 24, 24); ?>
                </div>
                <div :class="{'hidden': !open_about_tops_filters ,'block': !open_about_tops_filters }">
                    <?= $heroiconsSolid->renderHtml('chevron-up', 'text-white-azureish', 24, 24); ?>
                </div>
            </div>
            <div class="mt-4"
                x-cloak x-show="open_about_tops_filters"
                @click.outside="open_about_tops_filters = false"
                aria-labelledby="footer-about-tops-filters"
            >
                <div class="mb-3 text-3.25">
                    <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlAboutUs())) ?>">
                        <?= $escaper->escapeHtml(__('About us')) ?>
                    </a>
                </div>
                <div class="mb-3 text-3.25">
                    <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getRouteBlog())) ?>">
                        <?= $escaper->escapeHtml(__('Blog')) ?>
                    </a>
                </div>
                <div class="mb-3 text-3.25">
                    <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlAdvantages())) ?>">
                        <?= $escaper->escapeHtml(__('Tops advantages')) ?>
                    </a>
                </div>
                <div class="mb-3 text-3.25">
                    <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlContact())) ?>">
                        <?= $escaper->escapeHtml(__('Contact')) ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-1 border-b border-blue-picton">
        <div class="mb-4 mx-4">
            <div class="cursor-pointer flex justify-between"
                id="footer-my-account"
                @click.prevent="open_my_account = true"
                :aria-expanded="open_my_account ? 'true' : 'false'"
                aria-label="<?= $escaper->escapeHtmlAttr(__('My Account')) ?>"
            >
                <h3 class="font-semibold text-3.25 flex items-center">
                    <?= $escaper->escapeHtml(__('My Account')) ?>
                </h3>
                <div :class="{'hidden': open_my_account ,'block': !open_my_account }">
                    <?= $heroiconsSolid->renderHtml('chevron-down', 'text-white-azureish', 24, 24); ?>
                </div>
                <div :class="{'hidden': !open_my_account ,'block': !open_my_account }">
                    <?= $heroiconsSolid->renderHtml('chevron-up', 'text-white-azureish', 24, 24); ?>
                </div>
            </div>
            <div class="mt-4"
                x-cloak x-show="open_my_account"
                @click.outside="open_my_account = false"
                aria-labelledby="footer-my-account"
            >
                <?php if ($block->getLogin()->isLoggedIn()): ?>
                    <div class="mb-3 text-3.25">
                        <a href="<?= $escaper->escapeUrl($block->getUrl("sales/order/history")) ?>">
                            <?= $escaper->escapeHtml(__('Orders')) ?>
                        </a>
                    </div>
                    <div class="mb-3 text-3.25">
                        <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlReturn())) ?>">
                            <?= $escaper->escapeHtml(__('Return')) ?>
                        </a>
                    </div>
                    <div class="mb-3 text-3.25">
                        <a href="<?= $escaper->escapeUrl($block->getUrl("customer/account")) ?>">
                            <?= $escaper->escapeHtml(__('Information')) ?>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="mb-3 text-3.25">
                        <a href="<?= $escaper->escapeUrl($block->getUrl("customer/account/index")) ?>">
                            <?= $escaper->escapeHtml(__('Sign In')) ?>
                        </a>
                    </div>
                    <div class="mb-3 text-3.25">
                        <a href="<?= $escaper->escapeUrl($block->getUrl("customer/account/create")) ?>">
                            <?= $escaper->escapeHtml(__('Create an Account')) ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php if ($block->getLogin()->isLoggedIn()): ?>
        <div class="col-span-1 border-b border-blue-picton">
            <div class="mb-4 mx-4">
                <a href="<?= $escaper->escapeUrl($block->getUrl('customer/account/logout')) ?>"
                class="flex justify-between">
                    <h3 class="font-semibold text-3.25 flex items-center">
                        <?= $escaper->escapeHtml(__('Log out')) ?>
                    </h3>
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>
<div class="footer-bottom text-sm">
    <div class="hidden md:grid grid-cols-5 h-full">
        <div class="col-span-1 flex items-center justify-center">
            <?php
                $arrEmota = ["de", "at"];
            if (in_array($lastWeb, $arrEmota)): ?>
                <?= $heroiconsSolid->renderHtml('emota', '', 125, 70); ?>
            <?php else: ?>
                <a href="https://www.keurmerk.info/" target="_blank">
                    <?= $heroiconsSolid->renderHtml('webshop-keurmerk', '', 125, 70); ?>
                </a>
            <?php endif; ?>
        </div>
        <div class="w-full col-span-4 xl:col-span-2 flex items-center justify-start gap-6 pl-8 pr-6">
            <?php if ($lastWeb == "at"): ?>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('eps', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-pink-cherry-blossom">
                    <?= $heroiconsSolid->renderHtml('klarna', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('paypal', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('mc', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('maestro', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('visa', '', 30, 30); ?>
                </div>
            <?php elseif ($lastWeb == "de"): ?>
                <div class="card-footer-payment-method bg-pink-cherry-blossom">
                    <?= $heroiconsSolid->renderHtml('klarna', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('paypal', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('sofort', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('giropay', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('mc', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('maestro', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('visa', '', 30, 30); ?>
                </div>
            <?php elseif ($lastWeb == "be"): ?>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('bancontact', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('kbc', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('belfius', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('ing', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('payconiq', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('visa', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('mc', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('maestro', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('paypal', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('i-deal', '', 20, 20); ?>
                </div>
            <?php else: ?>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('i-deal', '', 20, 20); ?>
                </div>
                <div class="card-footer-payment-method bg-pink-cherry-blossom">
                    <?= $heroiconsSolid->renderHtml('klarna', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('visa', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('mc', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('maestro', '', 30, 30); ?>
                </div>
                <div class="card-footer-payment-method bg-white">
                    <?= $heroiconsSolid->renderHtml('paypal', '', 30, 30); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="col-span-5 xl:col-span-2 flex items-center xl:justify-center justify-center gap-[52px]
            xl:gap-8 mb-2 xl:mb-0 lg:mr-10"
            @mouseover.away="footer_bottom_right = ''"
            x-data="{ footer_bottom_right: ''}">
            <div class="text-center">
                <span class="text-blue-prussian text-sm">
                    <?= $block->getChildHtml('copyright'); ?>
                </span>
            </div>
            <div class="text-center" @mouseover = "footer_bottom_right = 'privacy'">
                <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlPrivacy())) ?>"
                class="text-blue-prussian text-sm" :class="{'underline': footer_bottom_right != 'privacy'}">
                    <?= $escaper->escapeHtml(__('Privacy')) ?>
                </a>
            </div>
            <div class="text-center" @mouseover = "footer_bottom_right = 'terms'">
                <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlTerms())) ?>"
                class="text-blue-prussian text-sm" :class="{'underline': footer_bottom_right != 'terms'}">
                    <?= $escaper->escapeHtml(__('Terms and Conditions')) ?>
                </a>
            </div>
            <div class="text-center" @mouseover = "footer_bottom_right = 'disclaimer'">
                <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlDisclaimer())) ?>"
                   class="text-blue-prussian text-sm" :class="{'underline': footer_bottom_right != 'disclaimer'}">
                    <?= $escaper->escapeHtml(__('Disclaimer')) ?>
                </a>
            </div>
            <div class="text-center" @mouseover = "footer_bottom_right = 'cookie'">
                <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlCookie())) ?>"
                   class="text-blue-prussian text-sm" :class="{'underline': footer_bottom_right != 'cookie'}">
                    <?= $escaper->escapeHtml(__('Cookie')) ?>
                </a>
            </div>
        </div>
    </div>
    <div class="flex flex-col md:hidden h-full">
        <ul class="flex justify-between px-6 pt-4"
        @mouseover.away="footer_bottom = ''" x-data="{ footer_bottom: ''}">
            <li class="inline-block" @mouseover = "footer_bottom = 'privacy'">
                <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlPrivacy())) ?>"
                class="text-blue-prussian text-3.25" :class="{'underline': footer_bottom != 'privacy'}">
                    <?= $escaper->escapeHtml(__('Privacy')) ?>
                </a>
            </li>
            <li class="inline-block" @mouseover = "footer_bottom = 'terms'">
                <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlTerms())) ?>"
                class="text-blue-prussian text-3.25" :class="{'underline': footer_bottom != 'terms'}">
                    <?= $escaper->escapeHtml(__('Terms and Conditions')) ?>
                </a>
            </li>
            <li class="inline-block" @mouseover = "footer_bottom = 'disclaimer'">
                <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlDisclaimer())) ?>"
                   class="text-blue-prussian text-3.25" :class="{'underline': footer_bottom != 'disclaimer'}">
                    <?= $escaper->escapeHtml(__('Disclaimer')) ?>
                </a>
            </li>
            <li class="inline-block" @mouseover = "footer_bottom = 'cookie'">
                <a href="<?= $escaper->escapeUrl($block->getUrl($block->getUrlAction()->getUrlCookie())) ?>"
                   class="text-blue-prussian text-3.25" :class="{'underline': footer_bottom != 'cookie'}">
                    <?= $escaper->escapeHtml(__('Cookie')) ?>
                </a>
            </li>
        </ul>
        <div class="text-blue-prussian text-3.25 w-full text-center px-4 h-full flex items-center justify-center">
            <?= $block->getChildHtml('copyright'); ?>
        </div>
    </div>
</div>
