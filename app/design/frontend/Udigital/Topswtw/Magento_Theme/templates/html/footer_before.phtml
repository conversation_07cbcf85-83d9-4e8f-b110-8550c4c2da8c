<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var ViewModelRegistry $viewModels */
/** @var Template $block */
/** @var Escaper $escaper */

$description  = $block->getLogin()->getDesignFooterDescription();

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

?>
<div class="footer-top md:mb-10 rating-summary-rate md:flex xl:ml-3 custom-botton">
    <?=  $block->getLayout()
            ->createBlock(\Interactivated\Customerreview\Block\Customerreview::class)
            ->setTemplate('Magento_Theme::html/reviews_kiyoh.phtml')
            ->toHtml();
    ?>
    <span class="hidden xl:flex flex-wrap ml-42">
        <?php if (!empty($description)):
            $descriptions = explode("|", $description); ?>
            <div class="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 footer-description">
                <?php foreach ($descriptions as $inc => $des): ?>
                    <div class="col-span-1 flex">
                        <span class="description flex">
                            <?= $heroiconsSolid->renderHtml(
                                'check',
                                'fill-current text-green-islamic mr-2',
                                18,
                                18,
                                ['aria-hidden' => 'true']
                            );
                            ?>
                            <?= $escaper->escapeHtml(__($des)) ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </span>
</div>
