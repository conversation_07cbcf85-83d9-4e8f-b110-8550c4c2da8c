<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\Store;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$getGaConfig  = $block->getLogin()->getValueConfig('googleanalytics4/gtm/account');
$storeViewModel     = $viewModels->require(Store::class);
?>
<div class="footer-other container mb-12 mt-6 top-content-footer">
    <div class="flex flex-col space-y-4">
        <h3 class="text-blue-prussian font-bold text-4.25 leading-6">
            <?= $escaper->escapeHtml(__('Our advantages')) ?>
        </h3>
        <div class="flex justify-between">
            <div class="h-full flex items-start justify-start mr-4">
                <?= /* @noEscape */ $heroicons->checkHtml('text-green-islamic') ?>
            </div>
            <div class="w-full leading-snug">
                <h4 class="text-blue-prussian font-semibold">
                    <?= $escaper->escapeHtml(__('Free delivery from €%1', (int) $storeViewModel->getWebsiteId() === 4 ? 100 : 50)) ?>
                </h4>
                <span class="text-3.25 text-blue-prussian">
                    <?= $escaper->escapeHtml(__('Enjoy free shipping on all orders over €%1.', (int) $storeViewModel->getWebsiteId() === 4 ? 100 : 50)) ?>
                </span>
            </div>
        </div>
        <div class="flex justify-between">
            <div class="h-full flex items-start justify-start mr-4">
                <?= /* @noEscape */ $heroicons->checkHtml('text-green-islamic') ?>
            </div>
            <div class="w-full leading-snug">
                <h4 class="text-blue-prussian font-semibold">
                    <?= $escaper->escapeHtml(__('Hassle‑free returns')) ?>
                </h4>
                <span class="text-3.25 text-blue-prussian">
                    <?= $escaper->escapeHtml(__('Not satisfied? Return your filter for free, no questions asked.')) ?>
                </span>
            </div>
        </div>
        <div class="flex justify-between">
            <div class="h-full flex items-start justify-start mr-4">
                <?= /* @noEscape */ $heroicons->checkHtml('text-green-islamic') ?>
            </div>
            <div class="w-full leading-snug">
                <h4 class="text-blue-prussian font-semibold">
                    <?= $escaper->escapeHtml(__('Extensive filter range')) ?>
                </h4>
                <span class="text-3.25 text-blue-prussian">
                    <?= $escaper->escapeHtml(__('Discover the largest selection of home air filters.')) ?>
                </span>
            </div>
        </div>
        <div class="flex justify-between">
            <div class="h-full flex items-start justify-start mr-4">
                <?= /* @noEscape */ $heroicons->checkHtml('text-green-islamic') ?>
            </div>
            <div class="w-full leading-snug">
                <h4 class="text-blue-prussian font-semibold">
                    <?= $escaper->escapeHtml(__('Over 45 years of expertise')) ?>
                </h4>
                <span class="text-3.25 text-blue-prussian">
                    <?= $escaper->escapeHtml(__('Four decades as the go‑to specialist in air filtration.')) ?>
                </span>
            </div>
        </div>
        <div class="flex justify-between">
            <div class="h-full flex items-start justify-start mr-4">
                <?= /* @noEscape */ $heroicons->checkHtml('text-green-islamic') ?>
            </div>
            <div class="w-full leading-snug">
                <h4 class="text-blue-prussian font-semibold">
                    <?= $escaper->escapeHtml(__('Personalized advice & support')) ?>
                </h4>
                <span class="text-3.25 text-blue-prussian">
                    <?= $escaper->escapeHtml(__('Our filter experts are ready with tailored guidance.')) ?>
                </span>
            </div>
        </div>
    </div>
</div>
