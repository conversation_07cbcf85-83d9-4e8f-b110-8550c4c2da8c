<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var Hyva\Theme\ViewModel\StoreConfig $storeConfig */
$storeConfig = $viewModels->require(Hyva\Theme\ViewModel\StoreConfig::class);
$showMiniCart = $storeConfig->getStoreConfig(\Magento\Checkout\Block\Cart\Sidebar::XML_PATH_CHECKOUT_SIDEBAR_DISPLAY);
?>
<script>
    function initHeader () {
        return {
            searchOpen: true,
            cart: {},
            getData(data) {
                if (data.cart) { this.cart = data.cart }
            }
        }
    }
</script>
<div id="header"
    class="header-main relative z-30 w-full shadow border-container-lighter bg-blue-prussian"
    x-data="initHeader()"
    @keydown.window.escape="searchOpen = false;"
    @private-content-loaded.window="getData(event.detail.data)"
    @update-checkout-data.window="getData(event.detail.data)"
>
    <div class="container flex flex-wrap items-center justify-between w-full px-6 md:py-2 lg:py-4 mx-auto mt-0 max-md:h-11.25 lg:h-18.75" id="sub-header">
        <!--Logo-->
        <?= $block->getChildHtml('logo'); ?>

        <!--Main Navigation-->
        <?= $block->getChildHtml('topmenu') ?>

        <!--Search Desktop -->
        <?= $block->getChildHtml('header-search') ?>

        <!--Top Menu-->
        <?= $block->getChildHtml('category-topmenu') ?>

        <div class="max-md:absolute max-md:right-7 flex items-center order-3">

            <!--Search Icon-->
            <button id="menu-search-icon"
                    class="hidden ml-1 no-underline sm:ml-3 text-white hover:text-blue-picton"
                    @click.prevent="
                        searchOpen = !searchOpen;
                        $nextTick(() => {
                            const searchInput = document.querySelector('#search');
                            searchInput.focus();
                            searchInput.select();
                    });
                    "
            >
                <span class="sr-only label">
                    <?= $escaper->escapeHtml(__('Search')) ?>
                </span>

                <?= $heroicons->searchHtml(
                    "w-8 h-8 md:h-6 md:w-6 text-white hover:text-blue-picton",
                    25,
                    25
                ) ?>
            </button>

            <!--Customer Icon & Dropdown-->
            <?= $block->getChildHtml('customer') ?>

            <!--Cart Icon-->
            <a id="menu-cart-icon"
                <?php if ($showMiniCart): ?>@click.prevent.stop="$dispatch('toggle-cart',{});"<?php endif ?>
                class="relative flex items-center justify-center ml-0 no-underline
                sm:ml-3 text-white hover:text-blue-picton lg:border-[1px] border-blue-cadet
                rounded-lg pl-5 md:pr-5 md:pt-2 pb-0 md:pb-2 h-10.25"
                href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/index')) ?>"
            >
                <span class="sr-only label">
                    <?= $escaper->escapeHtml(__('Cart')) ?>
                </span>

                <span :class="{'mr-2 md:mr-0': cart.summary_count }">
                    <?= $heroicons->renderHtml('shopping-cart-alt', 'text-white hover:text-blue-picton', 23, 23); ?>
                </span>

                <span x-text="(cart.summary_count > 99 ? '99+' : (cart.summary_count > 0 ? cart.summary_count : 0))"
                    class="relative right-0 h-6 md:h-5 -mt-2 md:-mt-5 badge-minicart font-semibold
                        leading-none text-center text-white uppercase transform -translate-x-1
                        translate-y-1/2 rounded-full -ml-6 md:ml-auto md:border-0 border-blue-prussian hidden md:block"
                    :class="{
                        'bg-saffron border-2': cart.summary_count }"
                >0</span>

                <span x-text="(cart.summary_count > 99 ? '99+' : (cart.summary_count > 0 ? cart.summary_count : ''))"
                    class="relative right-0 h-6 md:h-5 -mt-2 md:-mt-5 badge-minicart font-semibold
                        leading-none text-center text-white uppercase transform -translate-x-1
                        translate-y-1/2 rounded-full -ml-6 md:ml-auto md:border-0 border-blue-prussian md:hidden"
                    :class="{
                        'hidden': !cart.summary_count,
                        'block bg-saffron border-2': cart.summary_count }"
                ></span>
            </a>

        </div>
    </div>
    <!--Search Mobile -->
    <div class="container px-4 py-2 wrapper-searching absolute z-10 w-full shadow-sm bg-container-lighter border-container-lighter"
        id="search-content"
        :class="{ 'block': searchOpen}" x-cloak x-show="searchOpen"
        @click.outside="searchOpen = true"
    >
        <?= $block->getChildHtml('header-search-mobile'); ?>
    </div>

    <!--Cart Drawer-->
    <?= $block->getChildHtml('cart-drawer'); ?>

    <!--Authentication Pop-Up-->
    <?= $block->getChildHtml('authentication-popup'); ?>
</div>
