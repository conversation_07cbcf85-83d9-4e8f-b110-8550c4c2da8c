<?php
/**
 * HyvÃ¤ Themes - https://hyva.io
 * Copyright Â© HyvÃ¤ Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Navigation $viewModelNavigation */
/** @var HeroiconsSolid $heroiconsSolid */
/** @var HeroiconsOutline $heroicons */


$viewModelNavigation = $viewModels->require(Navigation::class);
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
$heroicons = $viewModels->require(HeroiconsOutline::class);
$uniqueId = '_' . uniqid();

// Order is important here: 1. build the menu data, 2. then set the cache tags from the view model identities
$menuItems = $viewModelNavigation->getNavigation(4);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

$limitChild = 5;
$customerService = $block->getThemeAction()->getCustomerService($limitChild);

/** show categories enable as 'Accessories' */
$collectionAccessories = $block->getMenuAction()->getShowColumnMenu2ndCategory();
?>
<div x-data="{ open_menu: false}"
     class="z-20 order-2 sm:order-2 lg:order-2 lg:flex ml-10 pl-3 topmenu-desktop hidden"
>
    <div x-data="initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?>()">
        <a href="javascript:void(0)"
        class="menu-header"
            @click.prevent="open_menu = true"
            :aria-expanded="open_menu ? 'true' : 'false'"
            <?php if (!empty($menuItems)): ?>
                :class="{ 'active' : open_menu }"
            <?php endif; ?>
            aria-label="<?= $escaper->escapeHtmlAttr(__('Categories')) ?>"
        >
            <?= $heroicons->renderHtml('menu', 'mr-3', 24, 24, ['aria-hidden' => 'true']); ?>
            <span class="text-sm title-categories-menu-header">
                <?= __('View all <strong>categories</strong>') // @codingStandardsIgnoreLine?>
            </span>
            <div class="ml-3 mr-1 py-1">
                <?php if (!empty($menuItems)): ?>
                    <?= $heroicons->renderHtml(
                        'chevron-down',
                        '',
                        16,
                        16,
                        ['aria-hidden' => 'true']
                    );
                    ?>
                <?php endif; ?>
            </div>
        </a>
        <!-- desktop -->
        <?php if (!empty($menuItems)): ?>
            <nav x-ref="nav-desktop"
                    x-cloak x-show="open_menu"
                    @click.outside="open_menu = false"
                    @load.window="setActiveMenu($root)"
                    aria-labelledby="categories-menu"
                    class="absolute z-20 pb-2 pt-5 origin-top-right
                        shadow-lg w-menu-top-nav bg-container-lighter">
                <nav
                    class="w-full duration-150 ease-in-out transform block justify-start
                    relative min-h-0 transition-display"
                >
                    <?php foreach ($menuItems as $index => $menuItem): ?>
                        <div class="relative level-0"
                                @mouseenter="hoverPanelActiveId
                                = '<?= /* @noEscape */ (string) $index ?>'"
                                @mouseleave="hoverPanelActiveId = 0"
                        >
                        <?php $isChild = (!empty($menuItem['childData'])) ? true : false; ?>
                            <?php
                                $menuItemId = str_replace("category-node-", "", $menuItem['id']);
                                $titleTypeMenu = $block->getMenuAction()->checkCategorizeCategoryMenu(
                                    $menuItemId,
                                    false,
                                    0
                                );
                                $detailTypeMenu = $block->getMenuAction()->checkCategorizeCategoryMenu(
                                    $menuItemId,
                                    true,
                                    2
                                );
                                $titleMenu = $escaper->escapeHtml(
                                    !empty($titleTypeMenu) ? __("by ". $titleTypeMenu) : ""
                                );
                            ?>
                            <span class="block px-4 py-2 lg:px-5 lg:py-2 hover:font-semibold hover:bg-gray-100">
                                <a class="w-full flex justify-between text-sm text-blue-prussian level-0 items-center"
                                    href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                    title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                                >
                                    <span><?= $escaper->escapeHtml(__($menuItem['name'])) ?></span>
                                    <?php if ($isChild): ?>
                                        <?= $heroicons->renderHtml(
                                            'chevron-right',
                                            '-mr-2 py-1',
                                            24,
                                            24,
                                            ['aria-hidden' => 'true']
                                        );
                                        ?>
                                    <?php endif; ?>
                                </a>
                            </span>
                            <?php if ($isChild): ?>
                                <div
                                    class="absolute z-10 hidden px-6 py-4 -ml-6 show-menu-levels shadow-lg
                                    bg-container-lighter/95 bg-white -mt-3"
                                    :class="{
                                        'hidden' : hoverPanelActiveId
                                        !== '<?= /* @noEscape */ (string) $index ?>',
                                        'block' : hoverPanelActiveId
                                        === '<?= /* @noEscape */ (string) $index ?>'
                                    }">

                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="flex flex-col justify-between">
                                            <div class="py-1">
                                                <h5 class="font-bold mt-1 mb-2 px-3 text-base
                                                text-blue-prussian">
                                                    <?= $escaper->escapeHtml($menuItem['name']." ".$titleMenu) ?>
                                                </h5>
                                                <ul>
                                                    <?php foreach (array_slice(
                                                        $menuItem['childData'],
                                                        0,
                                                        $limitChild
                                                    ) as $subMenuItem): ?>
                                                        <li class="py-2 hover:font-semibold hover:bg-gray-100 px-3">
                                                            <a href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                                            title="<?= $escaper->escapeHtmlAttr($subMenuItem['name'])?>"
                                                            class="text-sm text-blue-prussian block"
                                                            data-type="level-2">
                                                                <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                                            </a>
                                                        </li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>
                                            <div class="py-1 hover:font-semibold hover:bg-gray-100 px-3 mt-6">
                                                <a class="text-sm text-blue-picton link-detail-menu"
                                                href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                                data-id="<?= $escaper->escapeHtml($menuItem['id']) ?>">
                                                    <?= $escaper->escapeHtml(
                                                        __(!empty($detailTypeMenu) ?
                                                            "View all $detailTypeMenu" :
                                                            "View all brands")
                                                    )
                                                    ?>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="flex flex-col justify-between">
                                            <div class="py-1">
                                                <h5 class="font-bold mt-1 mb-2 px-3 text-base text-blue-prussian">
                                                    <?= $escaper->escapeHtml(__('Need help?')) ?>
                                                </h5>
                                                <ul>
                                                    <?php if (!empty($customerService)): ?>
                                                        <?php foreach ($customerService as $cs): ?>
                                                            <li class="py-2 hover:font-semibold hover:bg-gray-100 px-3">
                                                                <a href="<?= $escaper->escapeUrl($block->getUrl(
                                                                    $cs['link']
                                                                )) ?>"
                                                                class="text-sm text-blue-prussian block"
                                                                    data-type="help">
                                                                    <?= $escaper->escapeHtml(__($cs['title'])) ?>
                                                                </a>
                                                            </li>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <li class="py-2 hover:font-semibold hover:bg-gray-100 px-3">
                                                            <a href="#" class="text-sm text-blue-prussian block">
                                                                <?= $escaper->escapeHtml(__("Empty")) ?>
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                            <div class="py-1 hover:font-semibold hover:bg-gray-100 px-3 mt-6">
                                                <a class="text-sm text-blue-picton"
                                                href="<?= $escaper->escapeUrl($block->getUrl(
                                                    $block->getMenuAction()->getUrlCustomerService()
                                                ))?>">
                                                    <?= $escaper->escapeHtml(__('Read more of our advice')) ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </nav>
            </nav>
        <?php endif; ?>
    </div>
</div>
<script>
    'use strict';

    const initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            hoverPanelActiveId: null,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('font-semibold');
                    const type = item.getAttribute('data-type');
                    if (type != undefined && type == 'level-2') {
                        item.closest('div.level-0') &&
                        item.closest('div.level-0').querySelector('a.level-0').classList.add('font-semibold');
                    }
                });
            }
        }
    }
</script>
