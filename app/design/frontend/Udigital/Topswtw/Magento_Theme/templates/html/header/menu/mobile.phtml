<?php
/**
 * HyvÃ¤ Themes - https://hyva.io
 * Copyright Â© HyvÃ¤ Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Siteation\HyvaIconsFlags\ViewModel\FlagsIcons;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

$flagsIcons = $viewModels->require(FlagsIcons::class);
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var Navigation $viewModelNavigation */
$viewModelNavigation = $viewModels->require(Navigation::class);

$languageFlag = $block->getUrlAction()->getLanguageFlag();
$otherStores  = $languageFlag['other_store'];
$localeCode   = $languageFlag['locale_code'];
$localeName   = $languageFlag['locale_name'];
$countStore   = $languageFlag['count_store'];

$uniqueId = '_' . uniqid();
$limitChild = 9;

// Order is important here: 1. build the menu data, 2. then set the cache tags from the view model identities
$menuItems = array_slice($viewModelNavigation->getNavigation(5), 0, 8);

$block->setData('cache_tags', $viewModelNavigation->getIdentities());

?>
<div x-data="initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?>()"
     x-defer="interact"
     class="z-20 order-1 lg:order-2 lg:hidden" :class="{'fixed inset-0 bg-black bg-opacity-50' : open}">
    <!-- mobile -->
    <div @load.window="setActiveMenu($root)"
        class="bg-container-lighter sm:mr-6"
        :class="{'h-screen overflow-x-hidden overflow-y-auto fixed top-0 left-0 screen-94:w-17/20' : open}"
        @toggle-mobile-menu.window="open = !open"
        @keydown.window.escape="open=false">
        <div class="flex items-baseline justify-between menu-icon bg-blue-prussian"
        :class="{ 'h-18 ': open }">
            <span class="text-white w-full text-center m-auto text-4.25 leading-6
            md:text-2xl font-bold relative -translate-x-1/2 left-1/2 hidden"
                :class="{ '!block': open, 'hidden': !open }">
                <?= $escaper->escapeHtml(__("Menu")) ?>
            </span>
            <div class="flex justify-end w-full h-full">
                <a @click="$dispatch('toggle-mobile-menu')"
                    class="flex items-center justify-center cursor-pointer bg-blue-prussian mr-2 text-white"
                    :class="{ 'ml-auto': open }">
                    <?=
                        $heroiconsSolid->xHtml(
                            'hidden bg-blue-prussian text-white p-4 stroke-current',
                            50,
                            50,
                            [":class" => "{ 'hidden' : !open, 'block': open }"]
                        );
                        ?>
                    <?= $heroicons->menuHtml('', 28, 28, [":class" => "{ 'hidden' : open, 'block': !open }"]); ?>
                </a>
            </div>
        </div>

        <nav
            class="hidden w-full duration-150 ease-in-out transform border-t
            transition-display border-container"
            :class="{ 'hidden' : !open }">
        <?php foreach ($menuItems as $index => $menuItem):
            $category = $block->getCategory()->getCategoryById($index);
            $img = $category->getHomeImageCategory() ?? ($category->getImageUrl() ?? null);
            if (!empty($img)) { $img = substr($img, 1, strlen($img));
            }
            $isEmptyImg = empty($img) ? true : false;
            $imgUrl = $block->getCategory()->getImageResize($img); ?>

            <?php if (!empty($category->getIncludeInMenu())): ?>
            <div class="level-0">
                <span
                    class="flex items-center transition-transform duration-150
                    ease-in-out transform"
                    :class="{
                        '-translate-x-full' : mobilePanelActiveId,
                        'translate-x-0' : !mobilePanelActiveId
                    }">
                    <a class="flex items-center w-full pr-8 py-3 border-b cursor-pointer text-3.25
                        bg-container-lighter border-container level-0 text-blue-picton"
                        href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                        title="<?= $escaper->escapeHtmlAttr($category->getName()) ?>">
                        <div class="ml-6 mr-2 w-1/4 flex items-center justify-center px-2">
                            <img width="45" ignore-lazy-load
                                class="<?= $isEmptyImg ? '' : 'mix-blend-multiply' ?>"
                                src="<?= $escaper->escapeUrl($imgUrl) ?>"
                                alt="<?= $escaper->escapeHtml($category->getName()) ?>">
                        </div>
                        <?= $escaper->escapeHtml($category->getName()) ?>
                    </a>
                    <?php if (!empty($menuItem['childData'])): ?>
                        <a class="absolute right-0 flex w-8 h-8 mr-3 cursor-pointer
                            bg-container-lighter border-container"
                            @click="mobilePanelActiveId =
                            mobilePanelActiveId === '<?= /* @noEscape */ (string) $index ?>' ?
                            0 : '<?= /* @noEscape */ (string) $index ?>'">
                            <?=
                                $heroiconsSolid->chevronRightHtml(
                                    'w-full h-full p-1 text-blue-picton',
                                    20,
                                    20
                                );
                            ?>
                        </a>
                    <?php endif; ?>
                </span>
                <?php if (!empty($menuItem['childData'])): ?>
                    <div
                        class="absolute top-0 right-0 z-10 w-full h-full transition-transform
                        duration-200 ease-in-out
                            translate-x-full transform bg-container-lighter"
                        :class="{
                            'translate-x-full' : mobilePanelActiveId !== '<?= /* @noEscape */ (string) $index ?>',
                            'translate-x-0' : mobilePanelActiveId === '<?= /* @noEscape */ (string) $index ?>',
                        }">
                        <a class="flex items-center p-3 border-b cursor-pointer bg-container border-container"
                            @click="mobilePanelActiveId = 0">
                            <?= $heroiconsSolid->chevronLeftHtml('text-blue-picton', 20, 20); ?>
                            <span class="ml-4">
                                <span class="text-blue-picton text-3.25">
                                    <?= $escaper->escapeHtml(__('All Categories')) ?>
                                </span>
                            </span>
                        </a>
                        <a href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                            title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                            class="flex items-center w-full pl-4 pr-6 pt-5 pb-2 cursor-pointer
                            bg-container-lighter border-container">
                            <span class="font-bold ml-2 text-blue-prussian text-3.25">
                                <?= $escaper->escapeHtml($menuItem['name']) ?>
                            </span>
                        </a>
                        <?php
                            $childDataAll  = $menuItem['childData'];
                            $childData     = array_slice($childDataAll, 0, $limitChild);
                            $countChild    = count($childData);
                            $countChildAll = count($childDataAll);
                            $incChild      = 0;
                        foreach ($childData as $incSub => $subMenuItem):
                            $incChild++;
                            ?>
                                <span class="flex items-center transition-transform duration-150
                                ease-in-out transform
                                    cursor-pointer bg-container-lighter"
                                    :class="{
                                        '-translate-x-full' : mobilePanelActiveId
                                        === '<?= /* @noEscape */ (string) $index ?>',
                                        'translate-x-0' : !mobileSubPanelActiveId
                                    }">
                                    <a href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                        title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                        class="flex items-center w-full pr-6 justify-between
                                    <?= ($incChild == 1 ? 'mt-3 pb-2' : 'py-2') ?>
                                    <?= ($incChild == $countChild ? 'border-b pb-6' : '') ?>
                                        cursor-pointer
                                        bg-container-lighter border-container">
                                        <span class="ml-6 text-blue-picton lg:ml-0 text-3.25">
                                        <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                        </span>
                                    </a>
                                <?php if (!empty($subMenuItem['childData'])): ?>
                                        <a class="absolute right-0 flex w-8 h-8 mr-3 cursor-pointer text-3.25
                                            bg-container-lighter border-container
                                            <?= ($incChild == $countChild ? 'mb-4' : '') ?>"
                                            @click="mobileSubPanelActiveId =
                                            mobileSubPanelActiveId === '<?= /* @noEscape */ (string) $incSub ?>' ?
                                            0 : '<?= /* @noEscape */ (string) $incSub ?>'">
                                            <?=
                                                $heroiconsSolid->chevronRightHtml(
                                                    'w-full h-full p-1 text-blue-picton',
                                                    20,
                                                    20
                                                );
                                            ?>
                                        </a>
                                    <?php endif; ?>
                                </span>
                            <?php if (!empty($subMenuItem['childData'])): ?>
                                <div
                                    class="absolute top-0 right-0 z-10 w-full h-full
                                    transition-transform duration-200 ease-in-out
                                        translate-x-full transform bg-container-lighter"
                                    :class="{
                                        'translate-x-full' : mobileSubPanelActiveId
                                        !== '<?= /* @noEscape */ (string) $incSub ?>',
                                        'translate-x-0' : mobileSubPanelActiveId
                                        === '<?= /* @noEscape */ (string) $incSub ?>',
                                    }">
                                    <a class="flex items-center p-3 border-b cursor-pointer
                                    bg-container border-container"
                                        @click="mobileSubPanelActiveId = 0">
                                        <?= $heroiconsSolid->chevronLeftHtml('text-blue-picton', 20, 20); ?>
                                        <span class="ml-4">
                                            <span class="text-blue-picton">
                                                <?= $escaper->escapeHtml(__('Back')) ?>
                                            </span>
                                        </span>
                                    </a>
                                    <a href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                        title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                        class="flex items-center w-full pl-4 pr-6 pt-5 pb-2 cursor-pointer
                                        bg-container-lighter border-container">
                                        <span class="font-bold ml-2 text-blue-prussian text-3.25">
                                            <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                        </span>
                                    </a>
                                    <?php
                                        $childSubDataAll  = $subMenuItem['childData'];
                                        $childSubData     = array_slice($childSubDataAll, 0, $limitChild);
                                        $countSubChild    = count($childSubData);
                                        $countSubChildAll = count($childSubDataAll);
                                        $incChildSub      = 0;
                                    foreach ($childSubData as $incSub2 => $sub2MenuItem):
                                        $incChildSub++;
                                        ?>
                                        <span class="flex items-center transition-transform
                                        duration-150 ease-in-out transform
                                            cursor-pointer bg-container-lighter"
                                            :class="{
                                                '-translate-x-full' : mobileSubPanelActiveId
                                                === '<?= /* @noEscape */ (string) $incSub ?>',
                                                'translate-x-0' : !mobileSub2PanelActiveId
                                            }">
                                            <a href="<?= $escaper->escapeUrl($sub2MenuItem['url']) ?>"
                                                title="<?= $escaper->escapeHtmlAttr($sub2MenuItem['name']) ?>"
                                                class="flex items-center w-full pr-6 justify-between
                                                <?= ($incChildSub == 1 ? 'mt-3 pb-2' : 'py-2') ?>
                                                <?= ($incChildSub == $countSubChild ? 'border-b pb-6' : '') ?>
                                                cursor-pointer
                                                bg-container-lighter border-container">
                                                <span class="ml-6 text-blue-picton lg:ml-0 text-3.25">
                                                    <?= $escaper->escapeHtml($sub2MenuItem['name']) ?>
                                                </span>
                                            </a>
                                            <?php if (!empty($sub2MenuItem['childData'])): ?>
                                                <a class="absolute right-0 flex w-8 h-8 mr-3 cursor-pointer text-3.25
                                                    bg-container-lighter border-container
                                                    <?= ($incChildSub == $countSubChild ? 'mb-4' : '') ?>"
                                                    @click="mobileSub2PanelActiveId =
                                                    mobileSub2PanelActiveId
                                                    === '<?= /* @noEscape */ (string) $incSub2 ?>' ?
                                                    0 : '<?= /* @noEscape */ (string) $incSub2 ?>'">
                                                    <?=
                                                        $heroiconsSolid->chevronRightHtml(
                                                            'w-full h-full p-1 text-blue-picton',
                                                            20,
                                                            20
                                                        );
                                                    ?>
                                                </a>
                                            <?php endif; ?>
                                        </span>
                                        <?php if (!empty($sub2MenuItem['childData'])): ?>
                                            <div
                                                class="absolute top-0 right-0 z-10 w-full h-full
                                                transition-transform duration-200 ease-in-out
                                                    translate-x-full transform bg-container-lighter"
                                                :class="{
                                                    'translate-x-full' : mobileSub2PanelActiveId
                                                    !== '<?= /* @noEscape */ (string) $incSub2 ?>',
                                                    'translate-x-0' : mobileSub2PanelActiveId
                                                    === '<?= /* @noEscape */ (string) $incSub2 ?>',
                                                }">
                                                <a class="flex items-center p-3 border-b cursor-pointer
                                                bg-container border-container"
                                                    @click="mobileSub2PanelActiveId = 0">
                                                    <?= $heroiconsSolid->chevronLeftHtml('text-blue-picton', 20, 20); ?>
                                                    <span class="ml-4">
                                                        <span class="text-blue-picton">
                                                            <?= $escaper->escapeHtml(__('Back')) ?>
                                                        </span>
                                                    </span>
                                                </a>
                                                <a href="<?= $escaper->escapeUrl($sub2MenuItem['url']) ?>"
                                                    title="<?= $escaper->escapeHtmlAttr($sub2MenuItem['name']) ?>"
                                                    class="flex items-center w-full pl-4 pr-5 pt-6 pb-2 cursor-pointer
                                                    bg-container-lighter border-container">
                                                    <span class="font-bold ml-2 text-blue-prussian text-3.25">
                                                        <?= $escaper->escapeHtml($sub2MenuItem['name']) ?>
                                                    </span>
                                                </a>
                                                <?php
                                                    $childSub2DataAll  = $sub2MenuItem['childData'];
                                                    $childSub2Data     = array_slice($childSub2DataAll, 0, $limitChild);
                                                    $countSub2Child    = count($childSub2Data);
                                                    $countSub2ChildAll = count($childSub2DataAll);
                                                    $incChildSub2      = 0;
                                                foreach ($childSub2Data as $incSubLast => $subLastMenuItem):
                                                    $incChildSub2++;
                                                    ?>
                                                    <span class="flex items-center transition-transform duration-150
                                                    ease-in-out transform cursor-pointer bg-container-lighter"
                                                        :class="{
                                                            '-translate-x-full' : mobileSub2PanelActiveId
                                                            === '<?= /* @noEscape */ (string) $incSub2 ?>',
                                                            'translate-x-0' : !mobileSub3PanelActiveId
                                                        }">
                                                        <a href="<?= $escaper->escapeUrl($subLastMenuItem['url']) ?>"
                                                        title="<?= $escaper->escapeHtmlAttr($subLastMenuItem['name'])?>"
                                                            class="flex items-center w-full pr-6 justify-between
                                                            <?= ($incChildSub2 == 1 ? 'mt-3 pb-2' : 'py-2') ?>
                                                            <?= ($incChildSub2 == $countSub2Child
                                                            ? 'border-b pb-6' : '') ?>
                                                            cursor-pointer
                                                            bg-container-lighter border-container">
                                                            <span class="ml-6 text-blue-picton lg:ml-0 text-3.25">
                                                                <?= $escaper->escapeHtml($subLastMenuItem['name']) ?>
                                                            </span>
                                                        </a>
                                                    </span>
                                                <?php endforeach; ?>
                                                <?php if ($countSub2ChildAll > $countSub2Child): ?>
                                                    <span class="flex items-center transition-transform
                                                    duration-150 ease-in-out transform bg-container-lighter"
                                                        :class="{
                                                            '-translate-x-full' : mobileSub2PanelActiveId
                                                            === '<?= /* @noEscape */ (string) $incSub2 ?>',
                                                            'translate-x-0' : !mobileSub3PanelActiveId
                                                        }">
                                                        <a href="<?= $escaper->escapeUrl($sub2MenuItem['url']) ?>"
                                                        title="<?= $escaper->escapeHtmlAttr($sub2MenuItem['name']) ?>"
                                                            class="flex items-center w-full pl-4 pr-6 pt-5
                                                            pb-2 cursor-pointer
                                                            bg-container-lighter border-container">
                                                            <span class="pl-2 text-3.25 text-blue-picton">
                                                                <?= $escaper->escapeHtml(__("View All")) ?>
                                                            </span>
                                                        </a>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                    <?php if ($countSubChildAll > $countSubChild): ?>
                                        <span class="flex items-center transition-transform duration-150
                                        ease-in-out transform bg-container-lighter"
                                            :class="{
                                                '-translate-x-full' : mobileSubPanelActiveId
                                                === '<?= /* @noEscape */ (string) $incSub ?>',
                                                'translate-x-0' : !mobileSub2PanelActiveId
                                            }">
                                            <a href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                                title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                                class="flex items-center w-full pl-4 pr-6 pt-5 pb-2 cursor-pointer
                                                bg-container-lighter border-container">
                                                <span class="pl-2 text-3.25 text-blue-picton">
                                                    <?= $escaper->escapeHtml(__("View All")) ?>
                                                </span>
                                            </a>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                        <?php if ($countChildAll > $countChild): ?>
                            <span class="flex items-center transition-transform duration-150
                            ease-in-out transform bg-container-lighter"
                            :class="{
                                '-translate-x-full' : mobilePanelActiveId
                                === '<?= /* @noEscape */ (string) $index ?>',
                                'translate-x-0' : !mobileSubPanelActiveId
                            }">
                                <a href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                    title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                                    class="flex items-center w-full pl-4 pr-6 pt-5 pb-2 cursor-pointer
                                    bg-container-lighter border-container">
                                    <span class="pl-2 text-3.25 text-blue-picton">
                                        <?= $escaper->escapeHtml(__("View All")) ?>
                                    </span>
                                </a>
                            </span>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        <?php endforeach; ?>
        </nav>
        <div class="bg-container-lighter p-6 bottom-0 hidden" :class="{ '!block': open, 'hidden': !open }">
            <ul>
                <li class="my-2">
                    <a href="<?= $escaper->escapeUrl(
                        $block->getUrl($block->getUrlAction()->getUrlAboutUs())
                    ) ?>"
                    class="text-3.25 text-blue-picton text-decoration-none">
                        <?= $escaper->escapeHtml(__("About us")) ?>
                    </a>
                </li>
                <li class="my-2">
                    <a href="<?= $escaper->escapeUrl(
                        $block->getUrl($block->getUrlAction()->getRouteBlog())
                    ) ?>"
                    class="text-3.25 text-blue-picton text-decoration-none">
                        <?= $escaper->escapeHtml(__("Blog")) ?>
                    </a>
                </li>
                <li class="my-2">
                    <a href="<?= $escaper->escapeUrl(
                        $block->getUrl($block->getUrlAction()->getUrlAdvantages())
                    ) ?>"
                    class="text-3.25 text-blue-picton text-decoration-none">
                        <?= $escaper->escapeHtml(__('Tops advantages')) ?>
                    </a>
                </li>
                <li class="my-2">
                    <a href="<?= $escaper->escapeUrl(
                        $block->getUrl($block->getUrlAction()->getUrlCustomerService())
                    ) ?>"
                    class="text-3.25 text-blue-picton text-decoration-none">
                        <?= $escaper->escapeHtml(__("Customer service")) ?>
                    </a>
                </li>
                <li class="my-2">
                    <a href="<?= $escaper->escapeUrl($block->getUrl('customer/account')) ?>"
                    class="text-3.25 text-blue-picton text-decoration-none">
                        <?= $escaper->escapeHtml(__("My Account")) ?>
                    </a>
                </li>
                <li class="my-2 flex" x-data="{ show: false }">
                    <span  class="text-3.25 text-blue-picton mr-2">
                        <?= $escaper->escapeHtml(__("Language")) ?>:
                    </span>
                    <div class="flex flex-col justify-between">
                        <div class="flex justify-between" @click="show = ! show">
                            <a href="javascript:void(0)" class="text-3.25 text-blue-picton
                            text-decoration-none flex justify-between"
                            :class="{'justify-between w-full': show}">
                                <span><?= $escaper->escapeHtml(__($localeName)) ?></span>
                                <span class="flag-icon">
                                    <?=
                                        $flagsIcons->renderHtml(
                                            $localeCode,
                                            'px-1 pb-1 inline-block ml-2',
                                            24,
                                            24,
                                            ["aria-label" => $localeName]
                                        )
                                        ?>
                                </span>
                            </a>
                            <?php if ($countStore > 1): ?>
                                <div :class="{'hidden': show ,'block': !show }">
                                    <?=
                                        $heroiconsSolid->renderHtml(
                                            'chevron-down',
                                            'text-blue-picton ml-1 absolute',
                                            18,
                                            18,
                                            ['aria-hidden' => 'true']
                                        );
                                    ?>
                                </div>
                                <div :class="{'hidden': !show ,'block': show }">
                                    <?=
                                        $heroiconsSolid->renderHtml(
                                            'chevron-up',
                                            'text-blue-picton ml-1 absolute',
                                            18,
                                            18,
                                            ['aria-hidden' => 'true']
                                        );
                                    ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if ($countStore > 1): ?>
                            <ul x-show.transition.in="show">
                                <?php foreach ($otherStores as $store):
                                    $countryFlag = $block->getUrlAction()->getCountryFlag($store->getLocaleCode());
                                    $localeCode  = $countryFlag['code'];
                                    $localeName  = $countryFlag['name'];
                                    ?>
                                    <li class="view-<?= $escaper->escapeHtml($store->getCode()) ?>
                                    switcher-option text-blue-picton">
                                        <a class="text-3.25 text-blue-picton text-decoration-none
                                        flex justify-between"
                                        href="<?= $escaper->escapeUrl($block->getItem()->getTargetStoreRedirectUrl(
                                            $store
                                        )) ?>">
                                            <span class="label-store-language">
                                                <?= $escaper->escapeHtml(__($localeName)) ?>
                                            </span>
                                            <span class="flag-icon">
                                                <?=
                                                    $flagsIcons->renderHtml(
                                                        $localeCode,
                                                        'px-1 pb-1 inline-block ml-2',
                                                        24,
                                                        24,
                                                        ["aria-label" => $localeName]
                                                    )
                                                ?>
                                            </span>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>
<script>
    'use strict';

    const initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            mobilePanelActiveId: null,
            mobileSubPanelActiveId: null,
            mobileSub2PanelActiveId: null,
            mobileSub3PanelActiveId: null,
            open: false,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('font-bold');
                    item.closest('div.level-0') &&
                    item.closest('div.level-0').querySelector('a.level-0').classList.add('font-bold');
                });
            }
        }
    }
</script>
