<script>
    function initExternalAlpineComponent(){
        const close_notif = document.getElementsByClassName("notif-tops")[0];
        close_notif.setAttribute("style", "display: none;");
    }
(() => {
    
function myEventCallback() {
    const close_notif = document.getElementsByClassName("close-banner")[0];
    let html = '<a href="#" onclick="initExternalAlpineComponent()">X</a>';
    // close_notif.insertAdjacentHTML("afterend", html);
    if (close_notif) {
        close_notif.innerHTML = html;
    }
    const svg_arrow = '<svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" style="margin-left:5px;"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"></path></svg>';
    // const arrow_right = document.querySelector(".suggested-prod .suggested-prod-title p a max-[1024px]:mt-2.5");
    // const arrow_right2 = document.querySelector(".blog-container .blog-title p a");
    // arrow_right.innerHTML = arrow_right.innerHTML + svg_arrow;
    // arrow_right2.innerHTML = arrow_right2.innerHTML + svg_arrow;
}
window.addEventListener('DOMContentLoaded', myEventCallback);
// const liveChatButton = document.querySelector('[title="close-notif"]');
//     liveChatButton.addEventListener('click', () => {
//         alert('liveChatButton');
//         // Implement live chat 
//         // This may be a script include or embed code, depending on the vendor

//         // Programmatically trigger 'click' of actual live chat button to open panel/window (but ensure live chat library has loaded first)
//         // liveChatButton.click();
//     }, {once: true});

})();


</script>
