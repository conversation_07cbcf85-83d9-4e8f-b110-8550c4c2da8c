<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\App\ResourceConnection;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $description */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

$description  = $block->getLogin()->getDesignHeaderDescription();
$descriptions = explode("|", $description  ?? '');
$descriptions = array_filter($descriptions, function ($val) {
    return ($val !== null && $val !== false && $val !== "");
});

$reviews = $block->getAdditional();

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>

<?= $block->getChildHtml('review.kiyoh'); ?>

<span class="header-welcome rating-summary-rate md:flex space-x-4">
    <span class="welcome hidden 2xl:block">
        <?php if (!empty($description) && count($descriptions) > 0): ?>
            <?php foreach ($descriptions as $desc): ?>
                <div class="flex ml-2 md:ml-3">
                    <div id="icon-description-header" class="icon-check">
                        <?=
                            $heroiconsSolid->renderHtml(
                                'check',
                                'fill-current text-green-islamic',
                                18,
                                18,
                                ['aria-hidden' => 'true']
                            );
                        ?>
                    </div>
                    <span id="description-header" class="2xl:ml-3 ml-1">
                        <?= $escaper->escapeHtml(__($desc)) ?>
                    </span>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </span>
</span>
