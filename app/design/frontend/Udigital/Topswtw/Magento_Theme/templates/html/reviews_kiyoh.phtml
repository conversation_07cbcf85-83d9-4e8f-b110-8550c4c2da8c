<?php

/**
 * Copyright © 2023 Udigital. All rights reserved.
 * See COPYING.txt for license details.
 */

use Hyva\Theme\ViewModel\HeroiconsSolid;

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
// @codingStandardsIgnoreFile

/** @var \Interactivated\Customerreview\Block\Customerreview $block */
if ($block->getCorrectData()) :
    $rating_percentage = $block->getRatingPercentage();
    $maxrating = $block->getMaxrating();
    $url = $block->getMicrodataUrl();
    $rating = $block->getRating();
    $reviews = $block->getReviews();
    $display = 'display:none;';
    if ($block->getShowRating()) {
        $display = 'display:block;';
    }
?>

    <div class="kiyoh-shop-snippets" style="<?php echo $display ?>">
        <div class="rating-box">
            <div class="rating" style="width:<?php echo $rating_percentage; ?>%"></div>
        </div>
        <div class="kiyoh-schema" >
            <meta />
            <a href="<?php echo $block->getBaseUrl() ?>" aria-label="kiyoh reviews topswtw" style="display: none"><?php echo $block->getBaseUrl() ?></a>
            <div >
                <p>
                    <a href="<?php echo $url; ?>" target="_blank" class="kiyoh-link flex flex-row" aria-label="kiyoh reviews topswtw">
                        <?php
                        $ratingSteps = 5;
                        $starsFilled = is_numeric(floatval($rating)) ? floor($rating / 10 * $ratingSteps) : 0;
                        $starsEmpty = floor($ratingSteps - $starsFilled);
                        ?>
                        <div class="flex flex-row items-center">
                            <?php $i = 0; ?>
                            <?php while ($i < $starsFilled) : ?>
                                <?= $heroiconsSolid->starHtml('text-saffron h-4 w-4 md:h-5 md:w-5'); ?>
                                <?php $i++; ?>
                            <?php endwhile; ?>
                            <?php $i = 0; ?>
                            <?php while ($i < $starsEmpty) : ?>
                                <?= $heroiconsSolid->starHtml('text-gray-200 h-4 w-4 md:h-5 md:w-5'); ?>
                                <?php $i++; ?>
                            <?php endwhile; ?>
                            <span class="text-xs-lh-none md:text-3.25 text-blue-prussian"><?= ' &nbsp; ' . (!empty($rating) ? $rating : 0) ?></span>
                            <span class="text-xs-lh-none md:text-3.25 text-blue-prussian inline-block" >
                                <?= ' &nbsp;(' . (!empty($reviews) ? number_format((float)$reviews ,0 ,null, ".") : 0) . __(' reviews)'); ?>
                            </span>
                        </div>
                    </a>
                </p>
            </div>
        </div>
    </div>
<?php endif; ?>
