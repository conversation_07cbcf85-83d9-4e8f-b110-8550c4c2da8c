<?php

/**
 * Copyright Elgentos. All rights reserved.
 * https://elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HyvaCsp;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var HyvaCsp $hyvaCsp */
/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsOutline $heroicons */
/** @var StoreConfig $storeConfig */
$heroicons = $viewModels->require(HeroiconsOutline::class);
$storeConfig = $viewModels->require(StoreConfig::class);

$defaultSuccessMessageTimeout = $storeConfig->getStoreConfig('hyva_theme_general/messages/success_message_timeout');

?>
<script>
    <?php if ($defaultSuccessMessageTimeout): ?>
    window.defaultSuccessMessageTimeout = <?= (int) $defaultSuccessMessageTimeout ?>;
    <?php endif; ?>
    function initMessages() {
        "use strict";
        return {
            messages: window.mageMessages || [],
            isEmpty() {
                return this.messages.reduce(
                    function (isEmpty, message) {
                        return isEmpty && message === undefined
                    }, true
                )
            },
            hasMessages() {
                return !this.isEmpty();
            },
            hasMessage() {
                return !!this.message;
            },
            removeMessage() {
                this.messages[this.index] = undefined;
            },
            addMessages(messages, hideAfter) {
                messages.map((message) => {
                    this.messages = this.messages.concat(message);
                    if (hideAfter === undefined && message.type === 'success' && window.defaultSuccessMessageTimeout) {
                        hideAfter = window.defaultSuccessMessageTimeout;
                    }

                    if (hideAfter) {
                        this.setHideTimeOut(this.messages.length -1, hideAfter);
                    }

                    // If we add something to the cart, we want to toggle it.
                    if (message.type === 'success' && message.text.includes('checkout/cart')) {
                        window.dispatchEvent(new CustomEvent('toggle-cart', {
                            detail: { isOpen: true }
                        }));
                    }
                });
            },
            setHideTimeOut(messageIndex, hideAfter) {
                setTimeout((messageIndex) => {
                    this.removeMessage(messageIndex);
                }, hideAfter, messageIndex);
            },
            eventListeners: {
                ['@messages-loaded.window'](event) {
                    this.addMessages(event.detail.messages, event.detail.hideAfter)
                },
                ['@private-content-loaded.window'](event) {
                    const data = event.detail.data;
                    if (
                        data.messages &&
                        data.messages.messages &&
                        data.messages.messages.length
                    ) {
                        this.addMessages(data.messages.messages);
                    }
                },
                ['@clear-messages.window']() {
                    this.messages = [];
                }
            },
            getMessageUiId() {
                return 'message-' + this.message.type;
            }
        }
    }

    window.addEventListener('alpine:init', () => Alpine.data('initMessages', initMessages), {once: true})
</script>
<?php $hyvaCsp->registerInlineScript() ?>
<section id="messages"
         x-data="initMessages"
         x-bind="eventListeners"
         aria-live="assertive"
         role="alert"
>
    <template x-if="hasMessages">
        <div class="w-full">
            <div class="messages container mx-auto py-3">
                <template x-for="(message, index) in messages" :key="index">
                    <div>
                        <template x-if="hasMessage">
                            <div class="message"
                                 :class="message.type"
                                 :ui-id="getMessageUiId"
                            >
                                <span x-html="message.text"></span>
                                <button
                                    type="button"
                                    class="text-gray-600 hover:text-black"
                                    aria-label="<?= $escaper->escapeHtml(__('Close message')) ?>"
                                    @click.prevent="removeMessage"
                                >
                                    <?= $heroicons->xHtml('stroke-current', 18, 18, ['aria-hidden' => 'true']); ?>
                                </button>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
        </div>
    </template>
</section>
