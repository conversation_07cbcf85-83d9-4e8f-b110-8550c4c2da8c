<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Checkout\Block\Onepage\Success;
use Udigital\ContactThankyou\ViewModel\Contact;
use Hyva\Theme\Model\ViewModelRegistry;

/** @var Success $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Contact $heroiconsSolid */

$contactData = $viewModels->require(Contact::class);
$Customer = $contactData->getContact();
?>
<style>
    .message.success{
        display:none;
    }

    #maincontent > div.container {
        display: none;
    }

    .right-sidebar-klantenservice {
        min-height: 500px;
    }
</style>

    <div class="grid grid-cols-1 lg:grid-cols-4 -mt-6 position-content">
            <div class="gap-0 content-padding-text top-content col-span-3">
                <!-- Bedankt voor je bestelling, < Voornaam >! -->
                <p class="content-title-text content-desktop-thankyoupage" >
                    <strong>
                        <?= $escaper->escapeHtml(
                            ($contactData->getDynamicContactUsPageThanksTitle() ??
                            __("Thanks for your message")).", ". $Customer['name']
                        ); ?>
                    </strong>
                </p>
                <p class="content-title-text content-mobile-thankyoupage" >
                    <strong>
                        <?= $escaper->escapeHtml(
                            ($contactData->getDynamicContactUsPageThanksTitle() ??
                            __("Thanks for your message")).", ". $Customer['name']
                        ); ?>
                    </strong>
                </p>
                <p class="mt-10 text-[#002C4B] title-text-thankyoupage-contact title-text-thankyoupage-mobile">
                    <?php
                    $descEmptyContact = "Every question deserves a top(s) answer";
                    $descEmptyContact .= " We will therefore do our utmost to answer your message";
                    $descEmptyContact .= "as quickly and completely as possible.";
                    $descEmptyContact .= " Have you not received a response after 24 hours and";
                    $descEmptyContact .= " are you unable or unwilling to wait any longer? Give us a call.";
                    $descEmptyContact .= " That also gives breathing space… and that is exactly what";
                    $descEmptyContact .= " Tops is all about. Want to know more about an optimal indoor";
                    $descEmptyContact .= " climate? Then be sure to 'sniff' further on our website";
                    $descEmptyContact .= " (possibly with all that clean air. <br><br>";
                    $descEmptyContact .= "Thank you for your interest in Tops filters and see you soon!";
                    ?>
                    <?= $escaper->escapeHtml(
                        $contactData->getDynamicContactUsPageThanksContent() ??
                        __($descEmptyContact)
                    ); ?>
                </p>
            </div>
            <div class="pagebuilder-column-group" style="padding-left: 2vw;">
                <div class="pagebuilder-column-line">
                    <div class="pagebuilder-column right_nav" style="width: 100%;padding-right: 3.3vw;">
                        <div class="rounded right-sidebar-klantenservice gap-0 content-contact-tablet">
                            <div class="widget block block-static-block">
                                <?= $block->getLayout()
                                    ->createBlock(\Magento\Cms\Block\Block::class)
                                    ->setBlockId('right-sidebar-klantenservice')
                                    ->toHtml();
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
<script>
    function showpassword({action}){
       
        if (action == "hide") {
            
            document.getElementById("showpassword").style.display = "block";
            document.getElementById("hidepassword").style.display = "none";
            document.getElementById("password-thankyou").type = "password";

        }

        if (action == "show") {

            document.getElementById("showpassword").style.display = "none";
            document.getElementById("hidepassword").style.display = "block";
            document.getElementById("password-thankyou").type = "text";
        }


        if (action == "hide") {
                        
            document.getElementById("showpassword-mobile").style.display = "block";
            document.getElementById("hidepassword-mobile").style.display = "none";
            document.getElementById("password-thankyou-mobile").type = "password";

        }

        if (action == "show") {
        
            document.getElementById("showpassword-mobile").style.display = "none";
            document.getElementById("hidepassword-mobile").style.display = "block";
            document.getElementById("password-thankyou-mobile").type = "text";
        }
    }
</script>


<!-- <div class="checkout-success container py-4 px-6">
    <?php if ($block->getOrderId()): ?>
        <?php if ($block->getCanViewOrder()): ?>
            <p>
                <?= $escaper->escapeHtml(
                    __(
                        'Your order number is: %1.',
                        sprintf(
                            '<a href="%s" class="order-number"><strong>%s</strong></a>',
                            $escaper->escapeUrl($block->getViewOrderUrl()),
                            $block->getOrderId()
                        )
                    ),
                    ['a', 'strong']
                ) ?>
                ccc ccc
            </p>
        <?php  else: ?>
            <p><?= $escaper->escapeHtml(__('Your order # is: <span>%1</span>.', $block->getOrderId()), ['span']) ?></p>
        <?php endif;?>
        <p>
            <?= $escaper->escapeHtml(
                __('We\'ll email you an order confirmation with details and tracking info.')
            ) ?>
        </p>
    <?php endif; ?>

    <?= $block->getAdditionalInfoHtml() ?>

    <div class="my-4">
        <a class="btn btn-primary inline-flex" href="<?= $escaper->escapeUrl($block->getContinueUrl()) ?>">
            <span><?= $escaper->escapeHtml(__('Continue Shopping')) ?></span>
        </a>
    </div>
</div> -->
