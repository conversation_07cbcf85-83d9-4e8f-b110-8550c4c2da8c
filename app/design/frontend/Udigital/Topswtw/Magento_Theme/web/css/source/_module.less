// @codingStandardsIgnoreFile
// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@import 'module/_collapsible_navigation.less';

//
//  Theme variables
//  _____________________________________________

//  Messages
@message-global-note__color: @text__color;
@message-global-note__background: @color-yellow-light2;
@message-global-note-link__color: @link__color;
@message-global-note-link__color-hover: @link__hover__color;
@message-global-note-link__color-active: @link__active__color;
@message-global-note__border-color: @color-yellow-light3;

@message-global-caution__color: @color-white;
@message-global-caution__background: @color-red9;
@message-global-caution-link__color: @link__color;
@message-global-caution-link__color-hover: @link__hover__color;
@message-global-caution-link__color-active: @link__active__color;
@message-global-caution__border-color: none;

@header__background-color: false;
@header-panel__background-color: @color-blue-prussian;
@header-panel__text-color: @color-white;
@header-panel__text-color-blue-picton: @color-blue-picton;
@header-icons-color: @color-blue-picton;
@header-icons-color-hover: @color-blue-picton;
@customer-welcome__z-index: @dropdown-list__z-index + 1;

@addto-color: @color-gray40;
@addto-hover-color: @primary__color;
@minicart-icons-color: @color-white;
@minicart-icons-color-hover: @color-white;

@price-color: @color-gray34;
@price-size: 22px;
@price-size-desktop: 36px;

@button__shadow: inset 0 1px 0 0 rgba(255, 255, 255, 1), inset 0 -1px 0 0 fade(@border-color__base, 30); // Used for secondary button and catalog toolbar controls
@button__shadow-active: inset 0 1px 0 0 fade(@border-color__base, 80), inset 0 -1px 0 0 fade(@border-color__base, 30); // Used for secondary button and catalog toolbar controls

@h1__margin-bottom__desktop: @indent__xl;

//  Footer
@footer__background-color: @color-cadet-space;
@footer-links-separator-border-color: @color-gray-light4;
@footer-links-color: @color-white-azureish;
@footer-links-color-hover: @color-white;
@footer-links-color-current: @color-white;

//  Layered navigation
@filter-title-background-color: @color-gray94;
@filter-link: @color-gray-darken4;
@filter-link-hover: darken(@filter-link, 30%);
@filter-quantity: @color-gray46;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    body {
        .lib-css(background-color, @page__background-color);
    }

    .page-wrapper {
        .lib-vendor-prefix-display(flex);
        .lib-vendor-prefix-flex-direction(column);
        min-height: 100vh; // Stretch content area for sticky footer
        top: 170px;
        position: relative;
    }

    main.page-main, main.page-main-full-width {
        .lib-vendor-prefix-flex-grow(1);

        .category-view{
            margin-bottom:100px;
        }
    }

    .page-main {
        > .page-title-wrapper {
            .page-title {
                hyphens: auto;
            }
        }
    }

    //
    //  Header
    //  ---------------------------------------------

    .page-header {
        top: 0;
        z-index: 10;
        width: 100%;
        position: fixed;
        .lib-css(background-color, @header__background-color);
        border-bottom: 1px solid @border-color__base;
        margin-bottom: 45px;
        margin-top: -37px;


        .panel.wrapper {
            .lib-css(background-color, @header-panel__background-color);
            .lib-css(color, @header-panel__text-color-blue-picton);
            position: relative;
            top: 146px;
            z-index: 1;
        }

        .header-welcome {
            font-size: 13px;
            .lib-css(color, @color-blue-prussian);
        }
    }

    .header-welcome .stars, .footer-top .stars{
        margin-right: 10px;
    }

    .header.panel {
        > .header.links {
            .lib-list-inline();
            float: right;
            font-size: 0;
            margin-left: auto;
            margin-right: @indent__base;

            > li {
                font-size: @font-size__base;
                margin: 0 0 0 15px;

                &.customer-welcome {
                    margin: 0 0 0 5px;
                }

                > a {
                    .lib-link(
                    @_link-color: @header-panel__text-color-blue-picton,
                    @_link-text-decoration: none,
                    @_link-color-visited: @header-panel__text-color-blue-picton,
                    @_link-text-decoration-visited: none,
                    @_link-color-hover: @header-panel__text-color-blue-picton,
                    @_link-text-decoration-hover: underline,
                    @_link-color-active: @header-panel__text-color-blue-picton,
                    @_link-text-decoration-active: underline
                    );
                }
            }
        }
    }

    .header {
        &.content:extend(.abs-add-clearfix all) {
            padding-top: @indent__s;
            position: relative;
            .lib-css(background-color, @color-blue-prussian);
        }
    }

    .nav-toggle{
        top: 10px!important;

        &:before {
            .lib-css(color, @color-white) !important;
        }
    }

    .logo {
        float: left;
        margin: 0 0 @indent__s @indent__xl;
        max-width: 100%;

        width: 52%!important;
        display: flex;
        align-content: stretch;
        flex-wrap: wrap;
        flex-direction: column;
        align-items: end;
        margin-bottom: 20px;
        margin-top: 8px;

        position: relative;
        z-index: 5;

        img {
            display: block;
            height: auto;
            width: 102.595px;
        }

        .page-print & {
            display: inline-block;
            float: none;
        }
    }

    .page-print {
        .nav-toggle {
            display: none;
        }
    }

    .page-main {
        > .page-title-wrapper {
            .page-title + .action {
                margin-top: @indent__l;
            }
        }
    }

    .action.skip {
        &:not(:focus) {
            &:extend(.abs-visually-hidden all);
        }

        &:focus {
            .lib-css(background, @color-gray94);
            .lib-css(padding, @indent__s);
            box-sizing: border-box;
            left: 0;
            position: absolute;
            text-align: center;
            top: 0;
            width: 100%;
            z-index: 15;
        }
    }

    .action-skip-wrapper {
        height: 0;
        position: relative;
    }

    //
    //  Global notice
    //  ---------------------------------------------

    .message.global {
        p {
            margin: 0;
        }

        &.noscript,
        &.cookie {
            .lib-message(@_message-type: global-note);
            margin: 0;
        }

        &.cookie {
            bottom: 0;
            left: 0;
            position: fixed;
            right: 0;
            z-index: 3;

            .actions {
                margin-top: @indent__s;
            }
        }

        &.demo {
            .lib-message(@_message-type: global-caution);
            margin-bottom: 0;
            text-align: center;
        }
    }

    //
    //  Footer
    //  ---------------------------------------------

    .page-footer {
        margin-top: @indent__s;
    }

    .footer {
        &.content {
            border-top: 1px solid @footer-links-separator-border-color;
            padding-top: @indent__base;

            .links {
                > li {
                    .lib-css(background, @footer__background-color);
                    .lib-css(color, @color-white-azureish);
                    .lib-font-size(16);
                    border-top: 1px solid @footer-links-separator-border-color;
                    margin: 0 -@layout__width-xs-indent;
                    padding: 0 @layout__width-xs-indent;
                }

                a,
                strong {
                    display: block;
                    padding: @indent__s 0;
                }

                strong {
                    .lib-css(color, @footer-links-color-current);
                    font-weight: normal;
                }

                a {
                    .lib-link(
                    @_link-color: @footer-links-color,
                    @_link-text-decoration: none,
                    @_link-color-visited: @footer-links-color,
                    @_link-text-decoration-visited: none,
                    @_link-color-hover: @footer-links-color-hover,
                    @_link-text-decoration-hover: underline,
                    @_link-color-active: @footer-links-color-hover,
                    @_link-text-decoration-active: underline
                    );
                }
            }
        }
    }

    .copyright {
        .lib-css(background-color, @copyright__background-color);
        .lib-css(color, @color-white);
        box-sizing: border-box;
        display: block;
        padding: @indent__s;
        text-align: center;
        height: 93px;
    }

    .page-header .panel.wrapper,
    .page-footer {
        .switcher {
            .options {
                .lib-dropdown(
                @_dropdown-actions-padding: 0,
                @_dropdown-list-item-padding: 0,
                @_dropdown-toggle-icon-content: @icon-down,
                @_dropdown-toggle-active-icon-content: @icon-up,
                @_icon-font-text-hide: true,
                @_icon-font-size: 10px,
                @_icon-font-line-height: 22px,
                @_dropdown-list-min-width: 160px
                );

                ul.dropdown {
                    a {
                        .lib-link(
                        @_link-color: @color-gray20,
                        @_link-text-decoration: none,
                        @_link-color-visited: @color-gray20,
                        @_link-text-decoration-visited: none,
                        @_link-color-hover: @color-gray20,
                        @_link-text-decoration-hover: none,
                        @_link-color-active: @color-gray20,
                        @_link-text-decoration-active: none
                        );
                        display: block;
                        padding: 8px;
                    }
                }
            }

            strong {
                font-weight: @font-weight__regular;
            }

            .label {
                &:extend(.abs-visually-hidden all);
            }
        }
    }
    .page-header .panel.wrapper {
        .switcher {
            .options {
                ul.dropdown {
                    right: 0;
                    &:before {
                        left: auto;
                        right: 10px;
                    }
                    &:after {
                        left: auto;
                        right: 9px;
                    }
                }
            }
        }
    }

    //
    //  Widgets
    //  ---------------------------------------------
    .sidebar {
        .widget.block:not(:last-child),
        .widget:not(:last-child) {
            margin-bottom: @indent__xl;
        }
    }

    .widget {
        clear: both;
    }

    .page-header,
    .page-footer {
        .widget.block {
            .lib-css(margin, @indent__base 0);
        }

        .links .widget.block {
            margin: 0;
        }
    }

    //
    //  Calendar
    //  ---------------------------------------------

    .ui-datepicker td {
        padding: 0;
    }

    .ui-tooltip {
        .lib-css(background, @tooltip__background);
        border: @tooltip__border-width solid @tooltip__border-color;
        padding: 10px;
    }

    .cookie-status-message {
        display: none;
    }
}

//
//  Navigation
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .customer-name,
    .customer-welcome + .authorization-link {
        display: none;
    }

    .nav-sections .switcher-trigger strong:after {
        .lib-font-size(16);
        right: -25px;
        top: -2px;
    }

    .navigation .parent .level-top:after {
        .lib-font-size(16);
        right: 10px;
        top: 7px;
    }

    .page-footer .footer.content {
        .switcher-store {
            .lib-css(background, @footer__background-color);
            .lib-css(color, @color-white-azureish);
            .lib-font-size(16);
            margin: 0 -@layout__width-xs-indent;

            .switcher-options {
                display: block;

                ul.dropdown {
                    background: none;
                    border: 0;
                    box-shadow: none;
                    margin: 0;
                    position: relative;

                    &:before,
                    &:after {
                        display: none;
                    }
                }

                .switcher-trigger,
                .switcher-option {
                    border-top: 1px solid @footer-links-separator-border-color;
                    display: block;
                    padding: @indent__s @layout-indent__width;
                }

                .switcher-trigger strong {
                    padding: @indent__s 0;
                }

                .switcher-option a {
                    padding: 0;
                }
            }
        }
    }
}


//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .cms-page-view .page-main {
        padding-top: 0;
        position: relative;
    }

    .page-main {
        [class^='cms-privacy-policy'] & {
            padding-top: 41px;
            position: relative;
        }
    }

    .cms-content .data-table {
        .lib-table-responsive();
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    html,
    body {
        height: 100%; // Stretch screen area for sticky footer
    }

    .navigation ul {
        padding: 0 8px;
    }

    .logo {
        display: block;
        width: 12%!important;

        img {
            width: 142.595px;
        }
    }

    .page-header {
        border: 0;
        margin-bottom: 0;
        margin-top: 0;
        .lib-css(background-color, @header-panel__background-color);

        .panel.wrapper {
            .lib-css(background-color, @header-panel__text-color);
            position: static;
        }

        .header.panel:extend(.abs-add-clearfix-desktop all) {
            padding-bottom: 5px;
            padding-top: 5px;
        }

        .switcher {
            float: right;
            margin-left: 15px;
            margin-right: -6px;
            .lib-vendor-prefix-order(1);
        }
    }

    .page-main {
        > .page-title-wrapper {
            .page-title {
                display: inline-block;
            }

            .page-title + .action {
                float: right;
                margin-top: @indent__base;
            }
        }
    }

    .customer-welcome {
        .lib-dropdown(
        @_toggle-selector: ~'.action.switch',
        @_options-selector: ~'ul',
        @_dropdown-actions-padding: 0,
        @_dropdown-list-item-padding: 0,
        @_dropdown-toggle-icon-content: @icon-down,
        @_dropdown-toggle-active-icon-content: @icon-up,
        @_icon-font-text-hide: true,
        @_icon-font-size: 10px,
        @_icon-font-line-height: 22px,
        @_dropdown-list-pointer-position: right,
        @_dropdown-list-position-right: 0,
        @_dropdown-list-z-index: @customer-welcome__z-index
        );

        li:extend(.switcher li all) {
            a {
                .lib-link(
                @_link-color: @color-gray20,
                @_link-text-decoration: none,
                @_link-color-visited: @color-gray20,
                @_link-text-decoration-visited: none,
                @_link-color-hover: @color-gray20,
                @_link-text-decoration-hover: none,
                @_link-color-active: @color-gray20,
                @_link-text-decoration-active: none
                );
                display: block;
                line-height: 1.4;
                padding: 8px;
            }
        }

        .customer-name {
            cursor: pointer;
        }

        .customer-menu {
            display: none;
        }

        .action.switch {
            .lib-button-reset();
            .lib-css(color, @color-white);
        }

        .header.links {
            min-width: 175px;
            z-index: 1000;
        }

        &.active {
            .action.switch {
                .lib-icon-font-symbol(
                @_icon-font-content: @icon-up,
                @_icon-font-position: after
                );
            }

            .customer-menu {
                display: block;
            }
        }

        .greet {
            display: none;
        }
    }

    .wrapper-nav {
        .lib-dropdown(
        @_toggle-selector: ~'.action.switch',
        @_options-selector: ~'ul',
        @_dropdown-actions-padding: 0,
        @_dropdown-list-item-padding: 0,
        @_dropdown-toggle-icon-content: @icon-down,
        @_dropdown-toggle-active-icon-content: @icon-up,
        @_icon-font-text-hide: true,
        @_icon-font-size: 10px,
        @_icon-font-line-height: 22px,
        @_dropdown-list-pointer-position: right,
        @_dropdown-list-position-right: 0,
        @_dropdown-list-z-index: @customer-welcome__z-index
        );

        ul.master-menu{
            padding-top: 20px;
            box-shadow: 0px 3px 6px #00000029;
            margin-top: 0;
            border: 0;
            background: #ffffff 0% 0% no-repeat padding-box;
            border-bottom-right-radius: 8px;
            border-bottom-left-radius: 8px;
            opacity: 1;
        }

        ul:before, ul:after{
            display: none!important;
        }

        li:extend(.switcher li all) {
            a {
                .lib-link(
                @_link-color: @color-gray20,
                @_link-text-decoration: none,
                @_link-color-visited: @color-gray20,
                @_link-text-decoration-visited: none,
                @_link-color-hover: @color-gray20,
                @_link-text-decoration-hover: none,
                @_link-color-active: @color-gray20,
                @_link-text-decoration-active: none
                );
                display: block;
                line-height: 1.4;
                padding: 8px;
            }
        }

        .customer-name {
            cursor: pointer;
        }

        .customer-menu {
            display: none;
        }

        .action.switch {
            .lib-button-reset();
            .lib-css(color, @color-white);
        }

        .header.links {
            min-width: 175px;
            z-index: 1000;
        }

        &.active {
            .action.switch {
                .lib-icon-font-symbol(
                @_icon-font-content: @icon-up,
                @_icon-font-position: after
                );
            }

            .customer-menu {
                display: block;
            }
        }

        .greet {
            display: none;
        }
    }

    .header {
        &.panel {
            > .header.links {
                .lib-list-inline();
                float: right;
                margin-left: auto;

                > li {
                    margin: 0 0 0 15px;

                    &.welcome,
                    > a {
                        display: inline-block;
                        line-height: 1.4;
                        font-size: 13px;
                    }

                    &.welcome {
                        a {
                            .lib-css(color, @color-white);
                            .lib-css(padding-left, @indent__xs);
                        }
                    }
                }

                > .authorization-link {
                    &:after {
                        content: attr(data-label);
                        display: inline-block;
                        margin: 0 -@indent__xs 0 @indent__xs;
                    }

                    &:last-child {
                        &:after {
                            display: none;
                        }
                    }
                }

                > .customer-welcome + .authorization-link {
                    display: none;
                }
            }
        }

        &.content:extend(.abs-add-clearfix-desktop all) {
            padding: @indent__m @indent__base 0;
        }
    }

    .logo {
        margin: -8px auto @indent__m 0;

        img {
            max-height: inherit;
        }
    }

    .page-wrapper {
        margin: 0;
        transition: margin .3s ease-out 0s;

        > .breadcrumbs,
        > .top-container,
        > .widget {
            box-sizing: border-box;
            width: 100%;
        }
    }

    .page-footer {
        .lib-css(background, @footer__background-color);
        .lib-css(color, @color-white-azureish);
        margin-top: auto;

        .switcher {
            .options {
                ul.dropdown {
                    bottom: -@indent__s;
                    left: 100%;
                    margin: 0 0 0 @indent__base;
                    top: auto;

                    &:before,
                    &:after {
                        bottom: @indent__base - 7px;
                        left: auto;
                        right: 100%;
                        top: auto;
                    }

                    &:before {
                        .lib-css(border-color, transparent @dropdown-list__background transparent transparent);
                    }

                    &:after {
                        .lib-css(border-color, transparent @dropdown-list-pointer__border transparent transparent);
                        margin: 0 0 -1px -1px;
                    }
                }
            }
        }
    }

    .footer {
        &.content {
            border-top: none;

            .block {
                float: right;
            }

            ul {
                padding-right: 50px;
            }

            .switcher.store {
                display: block;
                margin: 0;
            }

            .links {
                display: inline-block;
                padding-right: 50px;
                vertical-align: top;

                li {
                    background: transparent;
                    border: none;
                    font-size: 14px;
                    margin: 0 0 8px;
                    padding: 0;
                }

                a,
                strong {
                    display: inline;
                }
            }
        }
    }
}



//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .header-auth{
        display: inline-block;
        position: relative;
        float: right;
        margin-top: -5px;
        margin-left: 13px;
        margin-right: 0;
        .lib-css(border,1px solid @color-blue-cadet);
        border-radius: 8px;
        padding: 8px 15px;
        .lib-css(color, @color-white);

        span {
            display: inline-block;
        }
    }

    .wrapper-nav{
        display: inline-block;
        position: relative;
        margin-top: -5px;
        margin-left: 13px;
        .lib-css(border,1px solid @color-blue-cadet);
        border-radius: 8px;
        padding: 8px 15px;
        cursor: pointer;
        .lib-css(color, @color-white);
    }

    .topmenu-desktop {
        position: relative;
        display: inline-block;
        padding-left: 2%;
    }

    .topmenu-desktop .active{
        .lib-css(border,0 solid @color-white);
        .lib-css(background-color, @color-white);
        font-weight: bold;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }

    .topmenu-desktop .active .title-categoies{
        .lib-css(color,@color-blue-prussian);
    }

    .header-tops-filters {
        display:inline-block;
        margin-right: 20px;
    }

    .header-tops-filters span{
        padding: 0 15px;
    }

    .title-filter-nav {
        .lib-css(padding-left, @layout__width-xs-indent);
        .lib-css(padding-bottom, 0);
        .lib-css(padding-right, @layout-indent__width);
        .lib-css(color, @color-blue-prussian);
        .lib-css(font-size, @navigation-desktop__font-size);
        display: inline-block;
    }

    .panel.header .header.links{
        display: none!important;
    }

    .topmenu-desktop .active >.topswtw-icon-menu:before,
    .topmenu-desktop .active >.topswtw-icon-array-down:before{
        .lib-css(color, @color-blue-prussian);
        font-weight: bold;
    }

    .page-bottom{
        display: inline-block;
    }

    .footer-sven-home-desktop{
        display: flex;
        width: 276px!important;
    }

    .footer-sven-home-mobile{
        display: none;
    }

    .header-auth span a{
        .lib-css(color, @color-white);
        margin-right:10px;
        text-decoration: none;
    }

    .page-wrapper > .page-bottom{
        margin-top: 50px;
        margin-left: 21%!important;
        margin-right: 8%!important;
    }

    .page-wrapper > .page-bottom .welcome, .header-welcome .welcome{
        display: inline-block;
    }

    .panel.header{
        text-align:left;
    }

    .page-wrapper > .page-bottom .footer-top{
        text-align: left;
        float: left;
        width: 100%;
    }

    .copyright{
        display: block;
    }

    .page-footer .footer-categories{
        display: block;
    }

    .footer.content{
        padding-bottom: 0;
    }

    .page-footer .col-footer-logo{
        display: none;
    }

    .page-footer {
        margin-top: @indent__xs;
    }

    .header-welcome .reviews, .footer-top .reviews{
        margin-right: 7%;
    }
}

.copyright{
    display: none;
}

.title-filter-nav {
    .lib-css(padding-left, @layout__width-xs-indent);
    .lib-css(padding-bottom, @layout__width-xs-indent);
    .lib-css(padding-right, @layout-indent__width);
    .lib-css(color, @color-blue-prussian);
    .lib-css(font-size, 1.6rem);
    display: inline-block;
}

.page-wrapper > .page-bottom .footer-top{
    text-align: center;
    width: 100%;
}

.header-auth span{
    display: none;
}

.header-auth{
    position: relative;
    float: right;
    margin-top: 7px;
    margin-right: 30px;
}

.auth-person {
    height: 20px;
    width: 20px;
    line-height: 20px;
    margin-right: 8px;
}

.footer-sven-home-desktop{
    display: none;
}

.page-wrapper > .page-bottom .welcome, .header-welcome .welcome{
    display: none;
}

.panel.header{
    text-align:center;
    padding: 8px;
    .lib-css(background-color, @color-white);
}

.footer-sven-home-mobile{
    display: flex;
}

.wrapper-footer-contact{
    width: 100%;
}


.lib-main-navigation-desktop(
    @_nav-background-color: @navigation-desktop__background,
    @_nav-border: @navigation-desktop__border,
    @_nav-font-size: @navigation-desktop__font-size,
    @_nav-font-weight: @navigation-desktop__font-weight,

    @_nav-level0-item-line-height: @navigation-desktop-level0-item__line-height,
    @_nav-level0-item-margin: @navigation-desktop-level0-item__margin,
    @_nav-level0-item-padding: @navigation-desktop-level0-item__padding,

    @_nav-level0-item-background-color: @navigation-desktop-level0-item__background,
    @_nav-level0-item-border: @navigation-desktop-level0-item__border,
    @_nav-level0-item-color: @navigation-desktop-level0-item__color-menu,
    @_nav-level0-item-text-decoration: @navigation-desktop-level0-item__text-decoration,

    @_nav-level0-item-background-color-hover: @navigation-desktop-level0-item__hover__background,
    @_nav-level0-item-border-hover: @navigation-desktop-level0-item__hover__border,
    @_nav-level0-item-color-hover: @navigation-desktop-level0-item__hover__color-menu,
    @_nav-level0-item-text-decoration-hover: @navigation-desktop-level0-item__hover__text-decoration,

    @_nav-level0-item-background-color-active: @navigation-desktop-level0-item__active__background,
    @_nav-level0-item__active__border-color: @navigation-desktop-level0-item__active__border-color,
    @_nav-level0-item__active__border-style: @navigation-desktop-level0-item__active__border-style,
    @_nav-level0-item__active__border-width: @navigation-desktop-level0-item__active__border-width,
    @_nav-level0-item-color-active: @navigation-desktop-level0-item__active__color-menu,
    @_nav-level0-item-text-decoration-active: @navigation-desktop-level0-item__active__text-decoration,

    @_submenu-background-color: @submenu-desktop__background,
    @_submenu-border-width: @submenu-desktop__border-width,
    @_submenu-border-style: @submenu-desktop__border-style,
    @_submenu-border-color: @submenu-desktop__border-color,
    @_submenu-box-shadow: @submenu-desktop__box-shadow,
    @_submenu-font-size: @submenu-desktop__font-size,
    @_submenu-font-weight: @submenu-desktop__font-weight,
    @_submenu-min-width: @submenu-desktop__min-width,
    @_submenu-padding: @submenu-desktop__padding,

    @_submenu-arrow: @submenu-desktop-arrow,
    @_submenu-arrow-size: @submenu-desktop-arrow__size,
    @_submenu-arrow-left: @submenu-desktop-arrow__left,

    @_submenu-item-padding: @submenu-desktop-item__padding,
    @_submenu-item-background-color: @submenu-desktop-item__background,
    @_submenu-item-border: @submenu-desktop-item__border,
    @_submenu-item-color: @submenu-desktop-item__color-menu,
    @_submenu-item-text-decoration: @submenu-desktop-item__text-decoration,

    @_submenu-item__hover__background-color: @submenu-desktop-item__hover__background,
    @_submenu-item-border-hover: @submenu-desktop-item__hover__border,
    @_submenu-item-color-hover: @submenu-desktop-item__hover__color-menu,
    @_submenu-item-text-decoration-hover: @submenu-desktop-item__hover__text-decoration,

    @_submenu-item-background-color-active: @submenu-desktop-item__active__background,
    @_submenu-item__active__border-color: @submenu-desktop-item__active__border-color,
    @_submenu-item__active__border-style: @submenu-desktop-item__active__border-style,
    @_submenu-item__active__border-width: @submenu-desktop-item__active__border-width,
    @_submenu-item-color-active: @submenu-desktop-item__active__color-menu,
    @_submenu-item-text-decoration-active: @submenu-desktop-item__active__text-decoration
) {
    .navigation-menu {
        .lib-css(background, @_nav-background-color);
        .lib-css(border, @_nav-border);
        .lib-css(font-size, @_nav-font-size);
        .lib-css(font-weight, @_nav-font-weight);
        height: inherit;
        left: auto;
        overflow: inherit;
        padding: 0;
        top: 0;
        width: 100%;
        z-index: 3;

        &:empty {
            display: none;
        }

        ul {
            margin-top: 0;
            margin-bottom: 0;
            padding: 0;
            position: relative;
        }

        li.level0 {
            .lib-css(border-top, none);
            width: 100%;
        }

        li.level1 {
            position: relative;
        }

        .level0 {
            .lib-css(margin, @_nav-level0-item-margin);
            display: inline-block;
            position: relative;

            &:last-child {
                margin-right: 0;
                padding-right: 0;
            }

            .submenu:before, .submenu:after{
                display: none!important;
            }

            .wrapper-submenu{
                padding-left: 1.5rem !important;
                padding-bottom: 70px!important;
            }

            .submenu{
                left: 100%!important;
                background: #FFFFFF 0% 0% no-repeat padding-box!important;
                box-shadow: 0px 3px 6px #00000029!important;
                border-radius: 0px 8px 8px 0px;
                opacity: 1;
                border: 0!important;

                ol {
                    list-style:none !important;
                    padding: 0!important;
                }

                .title {
                    margin-right: 1.5rem !important;
                    margin-left: 1.5rem !important;
                    margin-top: 1rem !important;
                    margin-bottom: 1rem !important;
                    padding-left: 0.5rem !important;

                    span {
                        font-weight: bold !important;
                        .lib-css(color, @color-blue-prussian)!important;
                    }
                }

                .other.active > a{
                    border-color: transparent!important;
                    border-style: solid;
                    border-width: 0;
                }

                .other {
                    margin-bottom: 15px;
                    position:absolute;
                    bottom:0;

                    a {
                        font-weight: normal !important;
                        .lib-css(color, @color-blue-picton)!important;
                        text-decoration: none!important;
                    }
                }
            }

            &:hover {
                &:after {
                    content: '';
                    display: block;
                    position: absolute;
                    top: 0;
                    left: 100%;
                    width: 10px;
                    height: calc(~'100% + 3px');
                    z-index: 1;
                }
            }

            > .level-top {
                .lib-css(background, @_nav-level0-item-background-color);
                .lib-css(border, @_nav-level0-item-border);
                .lib-css(color, @_nav-level0-item-color);
                .lib-css(line-height, @_nav-level0-item-line-height);
                .lib-css(padding, @_nav-level0-item-padding);
                .lib-css(text-decoration, @_nav-level0-item-text-decoration);
                box-sizing: border-box;
                position: relative;
                display: inline-block;
                font-weight: normal;
                font-size: 14px;

                &:hover, &.ui-state-focus {
                    .lib-css(background, @_nav-level0-item-background-color-hover);
                    .lib-css(border, @_nav-level0-item-border-hover);
                    .lib-css(color, @_nav-level0-item-color-hover);
                    .lib-css(text-decoration, @_nav-level0-item-text-decoration-hover);
                }
            }

            &.active,
            &.has-active {
                > .level-top {
                    .lib-css(background, @color-white-azureish 0% 0% no-repeat padding-box);
                    .lib-css(color, @_nav-level0-item-color-active);
                    .lib-css(text-decoration, @_nav-level0-item-text-decoration-active);
                    display: inline-block;
                    width: 100%;
                }
            }

            &.parent:hover > .submenu {
                overflow: visible !important;
            }

            &.parent {
                > .level-top {
                    padding-right: 20px;
                    width: 100%;

                    > .ui-menu-icon {
                        position: absolute;
                        right: 0;
                        padding-right: 15px;

                        .lib-icon-font(
                            @icon-next,
                            @_icon-font-size: 12px,
                            @_icon-font-line-height: 20px,
                            @_icon-font-text-hide: true,
                            @_icon-font-position: after
                        );
                    }
                }
            }

            .submenu {
                .lib-css(background, @_submenu-background-color);
                .lib-css(border, @_submenu-border-width @_submenu-border-style @_submenu-border-color);
                .lib-css(box-shadow, @_submenu-box-shadow);
                .lib-css(font-size, @_submenu-font-size);
                .lib-css(font-weight, @_submenu-font-weight);
                .lib-css(min-width, 750px);
                .lib-css(padding, @_submenu-padding);
                display: none;
                left: 0;
                margin: 0 0 0 2px !important;
                position: absolute;
                z-index: 1;

                ._lib-submenu-arrow(
                    @_submenu-arrow,
                    @_bg: @_submenu-background-color,
                    @_border: @_submenu-border-color,
                    @_size: @_submenu-arrow-size,
                    @_left: @_submenu-arrow-left
                );

                &:before {
                    content: '';
                    display: block;
                    position: absolute;
                    width: 100%;
                    height: 4px;
                    left: 0;
                    top: -4px;
                    z-index: 1;
                }

                a {
                    display: block;
                    line-height: inherit;
                    .lib-css(background, @_submenu-item-background-color);
                    .lib-css(border, @_submenu-item-border);
                    .lib-css(color, @_submenu-item-color);

                    .lib-css(padding, @_submenu-item-padding);
                    .lib-css(text-decoration, @_submenu-item-text-decoration);

                    &:hover,
                    &.ui-state-focus {
                        .lib-css(background, @_submenu-item__hover__background-color);
                        .lib-css(border, @_submenu-item-border-hover);
                        .lib-css(color, @_submenu-item-color-hover);
                        .lib-css(text-decoration, @_submenu-item-text-decoration-hover);
                    }
                }

                .active > a {
                    .lib-css(background, @_submenu-item-background-color-active);
                    .lib-css(border-color, @color-blue-prussian);
                    .lib-css(border-style, @_submenu-item__active__border-style);
                    .lib-css(border-width, @_submenu-item__active__border-width);
                    .lib-css(color, @_submenu-item-color-active);
                    .lib-css(text-decoration, @_submenu-item-text-decoration-active);
                }

                .submenu {
                    top: -1px !important;
                    left: 100% !important;
                }

                .submenu-reverse{
                    left: auto !important;
                    right: 100%;
                }

                li {
                    margin: 0;
                    position: relative;

                    &.parent {
                        > a {
                            > .ui-menu-icon {
                                position: absolute;
                                right: 3px;

                                .lib-icon-font(
                                    @icon-next,
                                    @_icon-font-size: 12px,
                                    @_icon-font-line-height: 20px,
                                    @_icon-font-text-hide: true,
                                    @_icon-font-position: after
                                );
                            }
                        }
                    }
                }
            }

            &.more {
                position: relative;
                .lib-icon-font(
                    @icon-pointer-down,
                    @_icon-font-size: 26px,
                    @_icon-font-position: after
                );

                &:before {
                    display: none;
                }

                &:after {
                    cursor: pointer;
                    padding: 8px 12px;
                    position: relative;
                    z-index: 1;
                }

                &:hover > .submenu {
                    overflow: visible !important;
                }

                li {
                    display: block;
                }
            }
        }
    }
}

.wrapper-nav{
    display: none;
}

.header-tops-filters {
    display:none;
}

.page-footer{
    .lib-css(background-color, @color-cadet-space);
    .lib-css(color, @color-white-azureish);
}

.page-footer .footer-categories{
    display: none;
}

.page-footer a{
    .lib-css(color, @color-white-azureish);
    text-decoration: none !important;
}

.footer.content{
    padding-bottom: 50px;
}

.title-question-footer{
    font-weight: bold!important;
    margin-bottom: 0!important;
}

.page-footer .col-footer-logo{
    display: block;
}

.topswtw-img-sven-home-desktop{
    .lib-icon-image(
        @_icon-image: '@{baseDir}images/sven_home/sven_home_nl_n.png',
        @_icon-image-position-x: 0,
        @_icon-image-position-y: 0,
        @_icon-image-width: 276px,
        @_icon-image-height: 421px
    );
}


.topswtw-img-icon-footer{
    .lib-icon-image(
        @_icon-image: '@{baseDir}images/logo-footer.png',
        @_icon-image-position-x: 0,
        @_icon-image-position-y: 0,
        @_icon-image-width: 255px,
        @_icon-image-height: 68px
    );
}


.topswtw-img-sven-home-mobile{
    .lib-icon-image(
        @_icon-image: '@{baseDir}images/sven_home/sven_home_nl_n-mobile.png',
        @_icon-image-position-x: 0,
        @_icon-image-position-y: 0,
        @_icon-image-width: 220px,
        @_icon-image-height: 261px
    );
}
