define(['jquery','matchMedia'], function ($, mediaCheck) {
	$(document).ready(function () {
		$(".close-banner div a").on("click", function() {$(".pagebuilder-column.notif-tops").css("display","none");});
	});
    mediaCheck({
        media: '(min-width: 769px)',
        // Switch to Desktop Version
        entry: function () {
            $("#mpblog-search-box").attr('placeholder',"Zoek op apparaat, merk, type of artikelnummer");
            var input_search_conatiner_m = $(".header-tops .header-tops-one .input-search-container.mobile");
            var input_search_conatiner_d = $(".header-tops .header-tops-one .input-search-container.desktop");
            if(input_search_conatiner_m.html().length > 1){
                input_search_conatiner_d.html(input_search_conatiner_m.html());
                // alert("mobile length: " + input_search_conatiner_m.html().length);
            }
            // console.log("input search di desktop, data mobile  : "+input_search_conatiner_m.html());
        },
        // Switch to Mobile Version
        exit: function () {
            $("#mpblog-search-box").attr('placeholder',"Zoek op merk, type of artikelnummer");
            var input_search_conatiner_m = $(".header-tops .header-tops-one .input-search-container.mobile");
            var input_search_conatiner_d = $(".header-tops .header-tops-one .input-search-container.desktop");
            if(input_search_conatiner_d.html().length > 1){
                input_search_conatiner_m.html(input_search_conatiner_d.html());
                // alert("desktop length: " + input_search_conatiner_d.html().length);
            }
            // console.log("input search di mobile, data desktop  : "+input_search_conatiner_d.html());
        },
        // Mobile and Desktop Versions
        both: function () {
            
        }
    });
});