<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\View\Helper\SecureHtmlRenderer;
use Magento\Weee\Block\Item\Price\Renderer;
use Magento\Weee\Helper\Data;

/**
 * @var Renderer $block
 * @var SecureHtmlRenderer $secureRenderer
 */

/** @var Data $weeeHelper */



$weeeHelper = $block->getData('weeeHelper');

$item = $block->getItem();

// var_dump($block->getItem()->debug());
// die;
?>

<?php if (($block->displayPriceInclTax() || $block->displayBothPrices()) && !$item->getNoSubtotal()): ?>
    <span class="price-including-tax" data-label="<?= $block->escapeHtmlAttr(__('Incl. Tax')) ?>">
        <?php if ($block->displayPriceWithWeeeDetails()): ?>
            <span class="cart-tax-total"
                data-mage-init='{"taxToggle": {"itemTaxId" : "#subtotal-item-tax-details<?= (int) $item->getId() ?>"}}'>
        <?php else: ?>
            <span class="cart-price">
        <?php endif; ?>
            <?= /* @noEscape */ $block->formatPrice($block->getRowDisplayPriceInclTax()) ?>
            </span>

        <?php if ($weeeHelper->getApplied($item)): ?>
            <div class="cart-tax-info no-display" id="subtotal-item-tax-details<?= (int) $item->getId() ?>">
                <?php foreach ($weeeHelper->getApplied($item) as $tax): ?>
                    <span class="weee" data-label="<?= $block->escapeHtmlAttr($tax['title']) ?>">
                        <?= /* @noEscape */ $block->formatPrice($tax['row_amount_incl_tax'], true, true) ?>
                    </span>
                <?php endforeach; ?>
            </div>

            <?php if ($block->displayFinalPrice()): ?>
                <span class="cart-tax-total"
                    data-mage-init='{"taxToggle": {"itemTaxId" : "#subtotal-item-tax-details<?= (int) $item->getId()
                    ?>"}}'>
                    <span class="weee" data-label="<?= $block->escapeHtmlAttr(__('Total Incl. Tax')) ?>">
                        <?= /* @noEscape */ $block->formatPrice($block->getFinalRowDisplayPriceInclTax()) ?>
                    </span>
                </span>
            <?php endif; ?>
        <?php endif; ?>
    </span>
<?php endif; ?>

<?php if ($block->displayPriceExclTax() || $block->displayBothPrices()): ?>
    <span class="price-excluding-tax" data-label="<?= $block->escapeHtmlAttr(__('Excl. Tax')) ?>">
        <?php if ($block->displayPriceWithWeeeDetails()): ?>
            <span class="cart-tax-total"
                data-mage-init='{"taxToggle": {"itemTaxId" : "#esubtotal-item-tax-details<?= (int) $item->getId()?>"}}'>
        <?php else: ?>
            <span class="cart-price  price-<?= $block->escapeHtml($item->getId()); ?>"
            style="margin-right: 20px;color: #2EB6ED;font-size: 30px;margin-top: 100px;">
        <?php endif; ?>
                <?= /* @noEscape */ $block->formatPrice($block->getRowDisplayPriceExclTax()) ?>

            </span>
            <div class="content-perset" >
                <?= $block->escapeHtml(number_format($item->getPrice(), 2). ' '. __('per set')); ?>
            </div>
            <div class="content-trush" >
                <i class="gg-trash gg-trash-<?= $block->escapeHtml($item->getItemId()); ?>"
                data-id="<?= $block->escapeHtml($item->getItemId()) ?>"></i>
                <div class="trush-text"> Verwijderen </div>
            </div>


        <?php if ($weeeHelper->getApplied($item)): ?>
            <span class="cart-tax-info no-display" id="esubtotal-item-tax-details<?= (int) $item->getId() ?>">
                <?php foreach ($weeeHelper->getApplied($item) as $tax): ?>
                    <span class="weee" data-label="<?= $block->escapeHtmlAttr($tax['title']) ?>">
                        <?= /* @noEscape */ $block->formatPrice($tax['row_amount'], true, true) ?>
                    </span>
                <?php endforeach; ?>
            </span>

            <?php if ($block->displayFinalPrice()): ?>
                <span class="cart-tax-total"
                      data-mage-init='{"taxToggle": {"itemTaxId" : "#esubtotal-item-tax-details<?= (int)$item->getId()
                        ?>"}}'>
                    <span class="weee" data-label="<?= $block->escapeHtmlAttr(__('Total')) ?>">
                        <?= /* @noEscape */ $block->formatPrice($block->getFinalRowDisplayPriceExclTax()) ?>
                    </span>
                </span>
            <?php endif; ?>
        <?php endif; ?>
    </span>
<?php endif; ?>
<script>
      require([
    'jquery',
    'Magento_Checkout/js/action/get-totals',
    'Magento_Customer/js/customer-data'

], function ($, getTotalsAction, customerData) {
    'use strict';

    $(document).on('click', '.gg-trash-<?= $block->escapeHtml($item->getItemId()); ?>', function(e){

    var form = $('form#form-validate');


   $('#cart-'+$(this).data('id')+'-qty').val(0);

    $.ajax({
        url: form.attr('action'),
        data: form.serialize(),
        showLoader: false,
        success: function (res) {
                var parsedResponse = $.parseHTML(res);
                var result = $(parsedResponse).find("#form-validate");
                var sections = ['cart'];

                $("#form-validate").replaceWith(result);

                /* Minicart reloading */
                customerData.reload(sections, true);

                /* Totals summary reloading */
                var deferred = $.Deferred();
                getTotalsAction([], deferred);

                var valuesccc = $("input[name='productid[]']")
                .map(function(){return $(this).val();}).get();

                for (let i = 0; i < valuesccc.length; i++) {
                    var element = valuesccc[i];

                    // console.log(valuesccc[i]);


                // console.log(element);

               console.log($('#cart-'+element+'-qty').val());

                if ($('#cart-'+element+'-qty').val() >= 2) {
                    $('.active-5-'+element+'').addClass('active-persen');
                    $('.active-7-'+element+'').removeClass('active-persen');
                    $('.active-10-'+element+'').removeClass('active-persen');
                }

                if ($('#cart-'+element+'-qty').val() >= 6) {
                    $('.active-5-'+element+'').addClass('active-persen');
                    $('.active-7-'+element+'').addClass('active-persen');
                    $('.active-10-'+element+'').removeClass('active-persen');
                }

                if ($('#cart-'+element+'-qty').val() >= 11) {
                    $('.active-5-'+element+'').addClass('active-persen');
                    $('.active-7-'+element+'').addClass('active-persen');
                    $('.active-10-'+element+'').addClass('active-persen');
                }

                }

        },
        error: function (xhr, status, error) {
            var err = eval("(" + xhr.responseText + ")");
            console.log(err.Message);
        }
    });


    });



});

</script>
