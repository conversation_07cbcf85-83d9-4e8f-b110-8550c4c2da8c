<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Wishlist\Block\Customer\Wishlist\Item\Column\Cart;
use Magento\Wishlist\Model\Item;
use Magento\Wishlist\ViewModel\AllowedQuantity;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;

/** @var Cart $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var Item $item */
$item = $block->getItem();
$product = $item->getProduct();
/** @var AllowedQuantity $viewModel */
$viewModel = $viewModels->require(AllowedQuantity::class);
$allowedQty = $viewModel->setItem($item)->getMinMaxQty();
$uniqueId = '_' . uniqid();
?>
<?php foreach ($block->getChildNames() as $childName): ?>
    <?= /* @noEscape */ $block->getLayout()->renderElement($childName, false) ?>
<?php endforeach;?>
<div class="box-tocart"
    x-data="initWishlistQty_<?= /** @noEscape */ $uniqueId ?>()"
    x-init="$watch('inputQuantity', (value) => inputQuantity = value <= 0 ? 1 : value)">
    <fieldset class="fieldset">
    <?php if ($item->canHaveQty() && $product->isVisibleInSiteVisibility()): ?>
        <div class="field qty">
            <label class="label"
                    for="qty[<?= $escaper->escapeHtmlAttr($item->getId()) ?>]">
                <span>
                    <?= $escaper->escapeHtml(__('Qty')) ?>
                </span>
            </label>
            <div class="control">
                <div class="w-full input-quantity">
                    <input type="number"
                        x-ref="product-qty-<?= $escaper->escapeHtmlAttr($item->getId()) ?>"
                        data-role="qty"
                        id="qty[<?= $escaper->escapeHtmlAttr($item->getId()) ?>]"
                        class="w-20"
                        x-model="inputQuantity"
                        name="qty[<?= $escaper->escapeHtmlAttr($item->getId()) ?>]"
                        <?= $product->isSaleable() ? '' : 'disabled="disabled"' ?>
                        >
                    <div class="item-quantity-nav">
                        <div class="item-quantity-button item-quantity-up"
                            @click="inputQuantity++"
                        >
                            <?= $heroiconsSolid->renderHtml('chevron-up', 'fill-current text-blue-prussian mr-1.5 mt-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                        </div>
                        <div class="item-quantity-button item-quantity-down"
                            @click="inputQuantity--"
                        >
                            <?= $heroiconsSolid->renderHtml('chevron-down', 'fill-current text-blue-prussian mr-1.5 mb-1.5', 18, 18, ['aria-hidden' => 'true']); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?php if ($product->isSaleable()): ?>
    <div class="product-item-actions mt-4">
        <div class="actions-primary">
            <button type="button"
                    data-role="tocart"
                    @click.prevent='addToCart("<?= $escaper->escapeHtmlAttr($item->getId()) ?>",
                        <?= /* @noEscape */ $block->getItemAddToCartParams($item) ?>)'
                    title="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>"
                    class="action tocart primary btn btn-primary">
                <span><?= $escaper->escapeHtml(__('Add to Cart')) ?></span>
            </button>
        </div>
    </div>
    <?php else: ?>
        <?php if ($product->getIsSalable()): ?>
            <p class="available stock" title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                <span><?= $escaper->escapeHtml(__('In stock')) ?></span>
            </p>
        <?php else: ?>
            <p class="unavailable stock" title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                <span><?= $escaper->escapeHtml(__('Out of stock')) ?></span>
            </p>
        <?php endif; ?>
    <?php endif; ?>
    </fieldset>
</div>
<script>
    function initWishlistQty_<?= /** @noEscape */ $uniqueId ?>()
    {
        return {
            inputQuantity : <?= /* @noEscape */ $block->getAddToCartQty($item) * 1 ?>,
        }
    }
</script>