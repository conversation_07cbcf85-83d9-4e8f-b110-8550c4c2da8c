<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Framework\Escaper;
use Magento\Wishlist\Block\Customer\Wishlist\Items;

/** @var Escaper $escaper */
/** @var Items $block */
$columns = $block->getColumns();
?>

<div class="products-grid wishlist">
    <?php if (count($block->getItems())): ?>
    <ol class="product-items mx-auto grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        <?php foreach ($block->getItems() as $item): ?>
            <li data-row="product-item" class="product-item w-full flex"
                id="item_<?= $escaper->escapeHtmlAttr($item->getId()) ?>">
                <div class="product-item-info card card-interactive mr-2 mb-2 w-full card-product flex flex-col justify-between" data-container="product-grid">
                    <?php foreach ($columns as $column): ?>
                        <?= $column->setItem($item)->toHtml();?>
                    <?php endforeach; ?>
                </div>
            </li>
        <?php endforeach; ?>
    </ol>
    <?php else: ?>
        <div class="message info empty">
            <span><?= $escaper->escapeHtml(__('This Wish List has no Items')) ?></span>
        </div>
    <?php endif; ?>
</div>

<?php foreach ($columns as $column): ?>
    <?= $column->getAdditionalHtml() ?>
<?php endforeach; ?>
