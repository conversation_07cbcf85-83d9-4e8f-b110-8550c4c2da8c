<?php

/**
 * Copyright Elgentos BV. All rights reserved.
 * https://www.elgentos.nl/
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Mageplaza\Blog\Block\Widget\Posts;
use Udigital\Blog\ViewModel\Blog;

/** @var ViewModelRegistry $viewModels */
/** @var Posts $block */
/** @var Escaper $escaper */

$blogViewModel = $viewModels->require(Blog::class);

$posts = $block->getCollection();

if ($posts->getSize() === 0) {

}

?>
<div class="post-list-content" id="mpblog-list-container"
     x-data="{
         isPostIndexAbsent: document.getElementsByClassName('mpblog-post-index').length === 0
     }"
     x-init="
       if (isPostIndexAbsent) {
            const slug = window.location.pathname.split('/').pop();
            const target = document.getElementById(`filters-${slug}`);
            if (target) {
                target.classList.add('active-filter');
            }
        } else {
            const allCategoriesElement = document.getElementById('list-all-categories');
            if (allCategoriesElement) {
                allCategoriesElement.classList.add('active-filter');
            }
        }
     ">
    <?php if ($posts->getSize() === 0): ?>
        <?= $escaper->escapeHtml(__('There are no posts at this moment')) ?>
    <?php else: ?>
        <div class="post-list-body mt-4 blog-container flex flex-col md:grid md:grid-cols-3 gap-4">
            <?php foreach ($posts as $post): ?>
                <div>
                    <div class="flex flex-col border border-white-azureish rounded-lg">
                        <div class="post-image h-[267px] relative overflow-hidden w-full">
                            <?php if ($post->getImage()): ?>
                                <a href="<?= $escaper->escapeUrl($post->getUrl()) ?>">
                                    <img class="absolute top-0 left-0 w-full h-full object-cover rounded-t-lg"
                                         src="<?= $escaper->escapeUrl($block->resizeImage($post->getImage(), '550x')) ?>"
                                         alt="<?= $escaper->escapeHtml($post->getName()); ?>"/>

                                    <?php $badge = $block->getPostCategoryHtml($post); ?>
                                    <?php if (!empty($badge)): ?>
                                        <div class="cat-banner">
                                            <p><?= $block->getPostCategoryHtml($post) ?></p>
                                        </div>
                                    <?php endif; ?>
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="post-info-wraper">
                            <h2 class="mp-post-title text-xl">
                                <a class="post-link-title block overflow-hidden text-nowrap text-ellipsis "
                                   title="<?= $escaper->escapeHtmlAttr($post->getName()) ?>"
                                   href="<?= $escaper->escapeUrl($post->getUrl()) ?>">
                                    <?= $escaper->escapeHtml($post->getName()) ?>
                                </a>
                            </h2>
                            <div class="post-author">
                                <?php $author = $blogViewModel->getAuthorByPost($post); ?>
                                <?= $escaper->escapeHtml($author->getName()
                                    . "&nbsp;&nbsp;|&nbsp;&nbsp;".  __("Reading time") .": 6 min.");
                                ?>
                            </div>
                            <div class="mp-clearfix"></div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?= $block->getPagerHtml() ?>
    <?php endif; ?>
    <?= $block->getChildHtml('additional'); ?>
</div>
