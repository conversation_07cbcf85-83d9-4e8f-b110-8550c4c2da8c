<?php
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Blog
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */

use Mageplaza\Blog\Block\Widget\Posts;

/** @var Posts $block */
$posts = $block->getCollection();
$_helper = $block->getHelperData();
?>
<div class="post-list-container mpcss" id="mpblog-list-container">
    <div class="row">
        <?php foreach ($posts as $post): ?>
            <div class="post-list-item col-12 col-md-4 col-lg-4" style="margin-left: 5px">
                <div class="post-item-wraper">
                    <?php if ($post->getImage()): ?>
                        <div class="post-image">
                            <a href="<?= $escaper->escapeUrl($post->getUrl()) ?>">
                                <img class="img-responsive" style="height: 267px"
                                     src="<?= $escaper->escapeUrl($block->resizeImage($post->getImage(), '400x')) ?>"
                                     alt="<?= $escaper->escapeHtmlAttr($post->getName()); ?>"/>
                                     
                                <div class="cat-banner">
                                    <p><?= $block->getPostCategoryHtml($post) ?></p>
                                </div>
                            </a>
                        </div>
                    <?php endif; ?>
                    <div class="post-info-wraper">
                        <h2 class="mp-post-title" style="font-size: 22px">
                            <a class="post-link-title" title="<?= $escaper->escapeHtmlAttr($post->getName()) ?>"
                               href="<?= $escaper->escapeUrl($post->getUrl()) ?>">
                                <?= $escaper->escapeHtmlAttr($post->getName()) ?>
                            </a>
                        </h2>
                        <div class="post-author"> <?php
                                $author = $_helper->getAuthorByPost($post);
                                echo $author->getName() . " | Leestijd: 6 min.";
                        ?></div>
                        <!-- <div class="mp-post-info">
                            <?= /** @noEscape */ $block->getPostInfo($post) ?>
                        </div> -->
                        <!-- <div class="post-short-description">
                            <p><?= /** @noEscape */ $post->getShortDescription() ?></p>
                        </div> -->
                        <div class="mp-clearfix"></div>
                        <!-- <div class="mp-post-meta-info mp-alignright">
                            <div class="actions">
                                <a class="mp-read-more" href="<?= $escaper->escapeUrl($post->getUrl()) ?>"
                                   title="<?= $escaper->escapeHtmlAttr(__('Read More')) ?>">
                                    <?= $escaper->escapeHtmlAttr(__('Read more')) ?>
                                </a>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
