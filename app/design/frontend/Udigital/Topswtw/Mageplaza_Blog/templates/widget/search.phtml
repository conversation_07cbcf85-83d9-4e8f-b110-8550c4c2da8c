<?php
/**
 * Udigital
 */

use Magento\Framework\Escaper;
use Mageplaza\Blog\Block\Sidebar\Search;

/** @var Search $block */
/** @var Escaper $escaper */
?>
<style>
    .sidebar.sidebar-main:before {
        content: unset;
    }
</style>
<div class="mpblog-search">
    <div class="field search">
        <label class="label" for="mpblog-search-box" data-role="minisearch-label">
            <span><?= $escaper->escapeHtml(__('Search')) ?></span>
        </label>
        <div class="control">
            <input id="mpblog-search-box"
                   type="text"
                   name="query"
                   value="<?= $escaper->escapeHtmlAttr($block->getRequest()->getParam('query')) ?>"
                   placeholder="<?= $escaper->escapeHtmlAttr(__('Search blogs here...')) ?>"
                   class="input-text"
                   role="combobox"
                   aria-haspopup="false"
                   aria-autocomplete="both"
            />
        </div>
    </div>
    <div class="actions">
        <button type="submit" title="<?= $escaper->escapeHtmlAttr(__('Search')) ?>" class="action search">
            <i class="fa fa-search" aria-hidden="true" style="font-size: 16px;margin-top: 5px;"></i>
        </button>
    </div>
</div>
<script>
    require(['jquery', 'mpDevbridgeAutocomplete'], function ($) {
        var visibleImage = <?= /** @noEscape */ $block->getSidebarConfig('search/show_image') ?>;

        $('#mpblog-search-box').autocomplete({
            lookup: <?= /** @noEscape */ $block->getSearchBlogData() ?>,
            lookupLimit: <?= $block->getSidebarConfig('search/search_limit') ?: 10; ?>,
            maxHeight: 2000,
            minChars: <?= $block->getSidebarConfig('search/min_chars') ?: 1; ?>,
            autoSelectFirst: true,
            showNoSuggestionNotice: true,
            triggerSelectOnValidInput: false,
            onSelect: function (suggestion) {
                window.location.href = suggestion.url;
            },
            formatResult: function (suggestion, currentValue) {
                var additionClass = '',
                    html          = "<div class='mpblog-suggestion'>";

                if (visibleImage) {
                    html += "<div class='mpblog-suggestion-left'><img class='img-responsive' src='" + suggestion.image + "' /></div>";
                    additionClass = 'image-visible';
                }

                html += "<div class='mpblog-suggestion-right " + additionClass + "'>" +
                    "<div class='mpblog-product-line mpblog-product-name'>" + suggestion.value + "</div>" +
                    "<div class='mpblog-product-des'><p class='mpblog-short-des'>" + suggestion.desc + "</p></div></div></div>";

                return html;
            }
        });
    });
</script>
