<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HyvaCsp;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Vendic\GoogleAutocomplete\ViewModel\Settings;
use Vendic\HyvaCheckoutGoogleAddressAutocomplete\ViewModel\AutoCompleteSelectors;
use Vendic\HyvaCheckoutGoogleAddressAutocomplete\ViewModel\FieldMapping;

$googleAutocompleteSettings = $viewModels->require(Settings::class);
$apiKey = $escaper->escapeHtml($googleAutocompleteSettings->getApiKey());
$autocompleteSelectorsViewModel = $viewModels->require(AutoCompleteSelectors::class);
$autocompleteSelectors = $autocompleteSelectorsViewModel->get();
$fieldMapping = $viewModels->require(FieldMapping::class);

if (!$apiKey) {
    return;
}

?>
<script>
    (() => {
        'use strict';

        let googleAutoCompleteInputs = [];
        let googleApiLoaded = false;

        function setAutoCompleteInputs() {
            const querySelectors = <?= /* @noEscape */ json_encode($autocompleteSelectors) ?>;
            googleAutoCompleteInputs = [];
            querySelectors.forEach(selector => {
                const input = document.querySelector(selector);
                if (input && input.name !== 'company') {
                    googleAutoCompleteInputs.push(input);
                }
            });
            updatePlaceholderByCountry();
        }

        function updatePlaceholderByCountry() {
            const countryInput = document.querySelector('[name="country_id"]');
            if (!countryInput) return;

            const postcodeNLPresent =
                      document.querySelector('[wire\\:key="field-wrapper-shipping-address-aac_address"]') ||
                      document.querySelector('[wire\\:key="postcode_eu_auto_complete_shipping"]');

            googleAutoCompleteInputs.forEach(input => {
                if (postcodeNLPresent && countryInput.value === 'NL') {
                    input.placeholder = '';
                } else {
                    input.placeholder = '<?= $escaper->escapeHtml(__('Enter street and house number and select your address from the list.')); ?>';
                }
            });
        }

        function loadGoogleApi() {
            if (!googleApiLoaded) {
                const script = document.createElement('script');
                script.src = 'https://maps.googleapis.com/maps/api/js?key=<?= $escaper->escapeJs($apiKey) ?>&libraries=places&callback=googleReady&region=<?= $escaper->escapeJs($googleAutocompleteSettings->getDefaultCountryId())?>';
                script.type = 'text/javascript';
                document.head.appendChild(script);
                googleApiLoaded = true;
            }
        }

        function initAutocompleteInput(input) {
            if (!window.google?.maps || input.googleAutocompleteInstance) return;
            const autocomplete = new google.maps.places.Autocomplete(input);
            input.googleAutocompleteInstance = autocomplete;
            google.maps.event.addListener(
                autocomplete,
                'place_changed',
                () => handleResponse(autocomplete.getPlace(), input)
            );
        }

        function initAutocompleteIfReady() {
            googleAutoCompleteInputs.forEach(initAutocompleteInput);
        }

        function reInitAutocomplete() {
            setAutoCompleteInputs();
            initAutocompleteIfReady();
        }

        document.addEventListener('magewire:load', reInitAutocomplete);
        window.addEventListener('billing-as-shipping-toggled', reInitAutocomplete);
        window.addEventListener('re-init-google-autocomplete', reInitAutocomplete);
        document.addEventListener('google_maps_js_loaded', initAutocompleteIfReady);

        // Event listener voor landwijziging
        const countryInput = document.querySelector('[name="country_id"]');
        if (countryInput) {
            countryInput.addEventListener('change', updatePlaceholderByCountry);
        }

        function handleResponse(result, element) {
            if (!Array.isArray(result.address_components)) return;

            const eventDetail = {
                company: result.business_status === 'OPERATIONAL' ? result.name : '',
                addressComponents: result.address_components,
                addressType: getAddressType(element)
            };

            window.dispatchEvent(
                new CustomEvent('google-autocomplete-address-changed', { detail: eventDetail })
            );
        }

        function getAddressType(element) {
            if (!element.id) return null;
            const match = element.id.match(/^([^-]+)/);
            return match ? match[1] : null;
        }

        window.addEventListener('google-autocomplete-address-changed', (event) => {
            if (!event.detail.addressType) return;
            const address = {};
            event.detail.addressComponents.forEach(component => {
                component.types.forEach(type => {
                    address[type] = type === 'country' ? component.short_name : component.long_name;
                });
            });
            if (event.detail.company !== undefined) address['company'] = event.detail.company;
            updateCheckoutAddressFields(event.detail.addressType, address);
        });

        function updateCheckoutAddressFields(addressType, address) {
            const fieldMappings = <?= /* @noEscape */ json_encode($fieldMapping->get()) ?>;
            const form = ['address-form', 'address-list.form'];
            let component, formContainsComponent;
            form.forEach(f => {
                try {
                    const componentFormName = `checkout.${addressType}-details.` + f;
                    component = Magewire.find(componentFormName);
                    formContainsComponent = document.querySelector('[wire\\:id="' + componentFormName + '"]');
                } catch (e) {}
            });
            Object.keys(fieldMappings).forEach(key => {
                const value = address[key];
                const identifier = `address.${fieldMappings[key]}`;
                const element = formContainsComponent?.querySelector('[wire\\:model\\.defer="' + identifier + '"]');
                if (element) {
                    if (identifier == 'address.company') {
                        console.log('COMPANY: ' + element.value);
                        console.log(element);
                        if (element.value == '') {
                            element.value = value;
                        }
                    } else {
                        element.value = value;
                    }
                    element.dispatchEvent(new Event('change'));
                }
                component.set(identifier, value ?? "");
            });
            component.save();
        }

        function initAutoCompleteJS() {
            setAutoCompleteInputs();
            loadGoogleApi();
        }

        document.addEventListener('magewire:load', initAutoCompleteJS, { once: true });

    })();
    function googleReady() {
        document.dispatchEvent(new Event('google_maps_js_loaded'));
    }
</script>
<?php $hyvaCsp->registerInlineScript() ?>
