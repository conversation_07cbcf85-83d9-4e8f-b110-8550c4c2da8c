{"name": "udigital/theme-topswtw", "description": "Custom Theme Magento 2 Udigital Topswtw", "config": {"sort-packages": true}, "minimum-stability": "dev", "type": "magento2-theme", "version": "100.0.1", "license": ["OSL-3.0", "AFL-3.0"], "require": {"php": "~7.4|~8.0|~8.1|~8.2", "Udigital/Topswtw": "100.0.*", "magento/theme-frontend-blank": "100.4.*", "magento/framework": "103.0.*", "hyva-themes/magento2-reset-theme": "*", "hyva-themes/magento2-theme-module": ">=1.1.9", "hyva-themes/magento2-email-module": "*", "hyva-themes/magento2-graphql-tokens": "*", "hyva-themes/magento2-graphql-view-model": ">=1.0.1", "magento/module-customer-graph-ql": "*", "magento/module-directory-graph-ql": "*", "magento/module-graph-ql": "*", "magento/module-quote-graph-ql": "*"}, "authors": [{"name": "Hyvä Themes B.V.", "email": "<EMAIL>"}], "autoload": {"files": ["registration.php"]}, "repositories": {"magento": {"type": "composer", "url": "https://repo.magento.com/"}, "hyva-themes/magento2-theme-module": {"type": "git", "url": "******************:hyva-themes/magento2-theme-module.git"}, "hyva-themes/magento2-reset-theme": {"type": "git", "url": "******************:hyva-themes/magento2-reset-theme.git"}, "hyva-themes/magento2-email-module": {"type": "git", "url": "******************:hyva-themes/magento2-email-module.git"}}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "magento/magento-coding-standard": "dev-master", "phpro/grumphp": "dev-master"}}