.mpblog-post-view .page-title {
    @apply !leading-[normal];
    font: normal normal bold 24px / 28px Public Sans;
}
.mpblog-post-view .back-page-cat a {
    color: var(--blue-picton);
}
.mpblog-post-view #sharing-new .share-col-right h5 {
    @apply mb-[unset];
}
.mpblog-post-view .page-main {
    @apply mt-[16px];
}
.mpblog-post-view .columns {
    @apply mt-7 grid-cols-[unset] mt-0;
}
.mpblog-post-view .page-title-wrapper .page-title {
    @apply mx-1/10 my-5;
}
.mpblog-post-view .post-view-image .img-responsive {
    @apply w-full h-[450px] object-cover rounded-xl;
    /*if 394px image would be cropped*/
}
.mpblog-post-view .columns .column.main {
    @apply w-full px-1/10 py-0;
}
.mpblog-post-view .post-list-content .post-post_content p:first-of-type {
    @apply text-[#16365B] font-bold;
    font-family: "Public Sans", Bold;
}
.mpblog-post-view .mp-blog-short-desc .mpcss.short-desc {
    @apply text-[#16365B];
    font-family: "Public Sans", Bold;
}
.mpblog-post-view .post-list-content .post-post_content p {
    @apply text-base text-[#16365B] mt-5 py-0;
}
.mpblog-post-view .post-list-content .post-post_content p img {
    @apply max-w-[130%] ml-[-15%] w-[130%] h-[611px] object-cover rounded-xl;
}
.mpblog-post-view .blog-container .post-list-item {
    @apply basis-[32.8%];
}
.mpblog-post-view .share-col-right {
    @apply float-right w-3/10 flex flex-wrap items-center place-content-center mt-10 pr-10;
}
.mpblog-post-view .share-col-right h5 {
    @apply text-3.25 text-[#002C4B] mr-2.5;
    font-family: "Public Sans", Bold;
}
.mpblog-post-view .post-sharing-button .inline_share_toolbox {
    @apply w-full flex flex-wrap;
}
.mpblog-post-view .post-sharing-button .inline_share_toolbox a {
    @apply no-underline border rounded mr-[5px] p-[7px] border-solid border-[#002C4B];
}
.mpblog-post-view
    .post-sharing-button
    .inline_share_toolbox
    .button_facebook
    i {
    @apply bg-[url(../images/facebook-f.svg)] w-[15px] h-[15px] block bg-no-repeat bg-[bottom_center,50%,50%];
}
.mpblog-post-view .post-sharing-button .inline_share_toolbox .button_linkin i {
    @apply bg-[url(../images/linkedin-in.svg)] w-[15px] h-[15px] block bg-no-repeat bg-[bottom_center,50%,50%];
}
.mpblog-post-view
    .post-sharing-button
    .inline_share_toolbox
    .button_whatsapp
    i {
    @apply bg-[url(../images/whatsapp.svg)] w-[15px] h-[15px] block bg-no-repeat bg-[bottom_center,50%,50%];
}
.mpblog-post-view .post-sharing-button .inline_share_toolbox .button_copy i {
    @apply bg-[url(../images/link-solid.svg)] w-[15px] h-[15px] block bg-no-repeat bg-[bottom_center,50%,50%];
}
.mpblog-post-view .block-blog-related.topic-list.mpcss {
    @apply w-full ml-0 mt-[90px];
}
.mpblog-post-view .block-blog-related.topic-list.mpcss .blog-realeted-title {
    @apply flex flex-wrap w-full place-items-center;
}
.mpblog-post-view .block-blog-related.topic-list.mpcss .blog-realeted-title h2 {
    @apply basis-[82%] text-2xl font-bold text-[#16365B];
    font-family: "Public Sans, Bold";
}
.mpblog-post-view
    .block-blog-related.topic-list.mpcss
    .blog-realeted-title
    a.right {
    @apply basis-[18%] text-right text-sm text-[#2EB6ED] flex place-items-center transition-all duration-[0.5s] cursor-pointer;
    place-content: flex-end;
}
.mpblog-post-view
    .block-blog-related.topic-list.mpcss
    .blog-realeted-title
    a.right
    span {
    @apply cursor-pointer transition-[0.5s];
}
.mpblog-post-view
    .block-blog-related.topic-list.mpcss
    .blog-realeted-title
    a.right
    svg {
    @apply text-[#2EB6ED] place-items-center transition-all duration-[0.5s] cursor-pointer ml-2.5 mt-[5px];
}
.mpblog-post-view
    .block-blog-related.topic-list.mpcss
    .blog-realeted-title
    a.right:hover
    div {
    @apply transition-all duration-[0.5s] cursor-pointer pr-2.5;
}
.mpblog-post-view
    .block-blog-related.topic-list.mpcss
    .blog-realeted-title
    a.right:hover
    span {
    @apply transition-all duration-[0.5s] cursor-pointer pr-[7px];
}
.mpblog-post-view .blog-container {
    @apply place-content-between mt-3.5;
}
.mpblog-post-view .blog-container .post-item-wraper {
    @apply border min-h-[366px] h-auto px-[17px] py-2.5 p-0 rounded-xl border-solid border-[#D5EAF0];
    /* height: 378px; */
}
.mpblog-post-view .blog-container .post-item-wraper .post-image a img {
    @apply w-full object-top object-cover rounded-[12px_12px_0px_0px];
    position: inherit !important;
}
.mpblog-post-view .blog-container .post-list-item .cat-banner {
    @apply z-[1] absolute left-3 top-3;
}
.mpblog-post-view .blog-container .post-list-item .cat-banner p {
    @apply text-sm w-[110%] text-center px-3.5 py-1 rounded-[15px] text-blue-alice;
    background: #f9b734;
}
.mpblog-post-view .blog-container .post-list-item .cat-banner a.mp-info {
    @apply text-[#F2F9FB];
}
.mpblog-post-view .blog-container .mp-post-title a {
    @apply text-xl text-[#2EB6ED];
    font-family: "Public Sans, Bold";
}
.mpblog-post-view .blog-container .post-author {
    @apply text-4.25 text-[#002C4B];
}
.mpblog-post-view .blog-container .blog-title h2 {
    @apply text-[#16365B] text-2xl;
    font-family: "Public Sans, Bold";
}
.mpblog-post-view .blog-container .blog-title {
    @apply flex flex-wrap place-content-between place-items-baseline;
}
.mpblog-post-view .blog-container .blog-title p a {
    @apply text-[#2EB6ED] place-items-center flex text-sm after:text-sm after:leading-[33px] after:text-[#2EB6ED] after:content-['\e608'] after:align-top after:inline-block after:font-[normal] after:overflow-hidden after:text-center after:m-[5px];
}
.mpblog-post-view .blog-container .blog-title p a::after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: "luma-icons";
}
.mpblog-post-view .blog-container .post-image {
    @apply w-full;
}
.mpblog-post-view .blog-container .post-info-wraper h2.mp-post-title {
    @apply h-[65px] font-[22px];
}
.mpblog-post-view .blog-container .post-info-wraper h2.mp-post-title a {
    @apply text-[#2EB6ED] text-xl;
}

/* .related-content-container.blog-container{
    margin-top: 14px;
} */

@media (min-width: 922px) and (max-width: 992px) {
    .mpblog-post-view .post-list-content .post-post_content p {
        @apply px-[10%] py-0;
    }
}
@media (min-width: 992px) and (max-width: 1339px) {
    .mpblog-post-view .blog-container .post-item-wraper .post-image a img {
        @apply h-[200px];
    }
}
@media (min-width: 1200px) and (max-width: 1339px) {
    .mpblog-post-view .post-info-wraper h2.mp-post-title {
        @apply h-[55px];
    }
}
@media (min-width: 992px) and (max-width: 1199px) {
    .mpblog-post-view .post-info-wraper h2.mp-post-title {
        @apply h-auto;
    }
}
@media (min-width: 1441px) {
    .mpblog-post-view .mp-blog-view {
        @apply py-0;
    }
}
@media (min-width: 1025px) and (max-width: 1280px) {
    .mpblog-post-view .post-list-content .post-post_content p {
        @apply py-0;
    }
}
@media (min-width: 1025px) {
    .mpblog-post-view .post-link-title.desktop-1024 {
        @apply hidden;
    }
    .mpblog-post-view .post-info-wraper h2.mp-post-title {
        @apply h-[unset] leading-[23px];
    }
    .mpblog-post-view .blog-container .post-author {
        @apply mt-[5px];
    }
    .mpblog-post-view .post-view-image {
        @apply mt-10 py-0;
    }
    .mpblog-post-view .short-desc {
        @apply mt-10 py-0;
    }
    .mpblog-post-view .mp-blog-view {
        @apply w-full mt-10;
    }
    .mpblog-post-view .mp-blog-short-desc {
        @apply w-full mt-10 px-[7.5%] py-0;
    }
    .mpblog-post-view .post-info-wraper-for-mobile {
        @apply hidden;
    }
    .mpblog-post-view .blog-realeted-title .title-realeted-mobile {
        @apply hidden;
    }
    .mpblog-post-view .block-blog-related.topic-list.mpcss {
        @apply mb-[149px] w-[126%] ml-[-13%] mt-[140px];
    }
    .mpblog-post-view .page-layout-2columns-right .column.main {
        @apply w-full px-[10%] py-0;
    }
    .mpblog-post-view .post-list-content .post-post_content p img {
        @apply ml-[-9%] w-[118%] mt-[30px];
    }
    .mpblog-post-view .share-col-right {
        @apply mt-[60px];
    }
    .mpblog-post-view .post-link-title.mobile {
        @apply hidden;
    }
}

@media (min-width: 1536px) {
    .mpblog-post-view .post-list-content .post-post_content p {
        @apply py-0;
    }
}
@media (min-width: 1484px) {
    .mpblog-post-view .share-col-right {
        @apply w-[30%];
    }
}

.mpblog-post-view .title-countainer .back-page-cat {
    @apply max-screen-256:px-[2%] px-[10%];
}

@media (max-width: 1483px) {
    .mpblog-post-view .share-col-right {
        @apply w-[29%];
    }
}
@media (max-width: 1385px) {
    .mpblog-post-view .share-col-right {
        @apply w-[24%];
    }
}
@media (min-width: 1280px) {
    .mpblog-post-view .post-list-content .post-post_content p {
        @apply py-0;
    }
}
@media (min-width: 1025px) and (max-width: 1440px) {
    .mpblog-post-view .post-list-content .post-post_content p {
        @apply p-0;
    }
    .mpblog-post-view .mp-blog-view {
        @apply pl-0 pr-[7.5%] py-0;
    }
    .mpblog-post-view .post-list-content .post-post_content p {
        @apply p-0;
    }
}
@media (max-width: 1024px) {
    .mpblog-post-view #maincontent h1.page-title {
        @apply text-4.25 font-black;
    }
    .mpblog-post-view .post-list-content .post-post_content p strong span {
        @apply !text-4.25;
        font-family: "Open Sans";
    }
    .mpblog-post-view .columns .column.main {
        @apply w-full mb-[50px] px-[2%];
    }
    .mpblog-post-view .share-col-right {
        @apply float-left float-left w-2/5 w-3/5 mt-[30px] mb-[60px];
    }
    .mpblog-post-view .blog-container .post-item-wraper {
        @apply h-[420px] min-h-[381px] h-auto;
    }
    .mpblog-post-view .post-info-wraper h2.mp-post-title {
        @apply h-auto leading-[normal] h-auto text-4.25;
    }
    .mpblog-post-view .page-title-wrapper .page-title {
        @apply m-[unset];
    }
    .mpblog-post-view .page-title-wrapper .page-title .base {
        @apply text-6.25;
    }
    .mpblog-post-view .post-list-content .post-post_content p img {
        @apply max-w-[130%] max-w-full h-[257px] ml-0;
    }
    .mpblog-post-view .block-blog-related.topic-list.mpcss {
        @apply w-full ml-[unset] mt-[100px];
    }
    .mpblog-post-view .blog-realeted-title h2 {
        @apply basis-[70%] text-4.25;
    }
    .mpblog-post-view .blog-realeted-title a.right {
        @apply basis-[30%] text-3.25 text-[#00a6ff] font-medium;
    }
    .mpblog-post-view .block-blog-related .related-content-container {
        @apply w-full overflow-y-hidden overflow-x-auto;
        flex-wrap: initial !important;
    }
    .mpblog-post-view .post-list-item {
        @apply w-6/12 ml-[5px];
    }
    .mpblog-post-view .post-link-title.desktop {
        @apply hidden;
    }
    .mpblog-post-view .post-link-title.desktop-1024 {
        @apply hidden;
    }
    .mpblog-post-view .post-list-content .post-post_content p:first-of-type {
        @apply text-3.75 font-normal;
        font-family: inherit;
    }
    .mpblog-post-view .post-list-content .post-post_content p:first-of-type span {
        @apply !text-3.75;
    }
    .mpblog-post-view .column.main .container {
        @apply ml-0 pl-0;
    }
    .mpblog-post-view .post-info-wraper-for-mobile .post-author {
        @apply text-3.25 text-[#16365b] opacity-50;
    }
    .mpblog-post-view .post-list-content .post-post_content p {
        @apply text-3.75 p-0;
        font-family: "Open Sans";
    }
    .mpblog-post-view .page-main {
        @apply mt-0;
    }
    .mpblog-post-view .page-title.title-font .base {
        @apply text-4.25 text-[#16365b];
    }
    .mpblog-post-view .row.related-content-container.blog-container {
        @apply flex w-full overflow-y-hidden overflow-x-auto;
        flex-wrap: initial !important;
    }
    .mpblog-post-view .blog-container .post-list-item {
        @apply basis-[66%] flex-[0_0_auto] w-[33.33333333%] mr-[4%];
    }
    .mpblog-post-view .blog-realeted-title .title-realeted-desktop {
        @apply hidden;
    }
    .mpblog-post-view .blog-container .post-author {
        @apply text-3.25 text-[#002c4b] opacity-50 mt-[5px];
    }
    .mpblog-post-view .blog-container .post-list-item .cat-banner p {
        @apply text-2.75 font-semibold;
    }
    .mpblog-post-view
        .row.related-content-container.blog-container::-webkit-scrollbar {
        @apply w-0 hidden;
    }
    .mpblog-post-view .blog-container .post-item-wraper .post-image a img {
        @apply h-auto min-h-[317px];
    }
    .mpblog-post-view .page-layout-2columns-right .columns {
        background: #fff;
    }
    .mpblog-post-view .blog-container .post-info-wraper h2.mp-post-title a {
        @apply text-3.75 text-[#16365b];
    }
    .mpblog-post-view .share-col-right {
        place-content: flex-start;
    }
    .mpblog-post-view .content-container.blog-container {
        @apply mt-2;
    }
    .mpblog-post-view .post-info-wraper-for-mobile {
        @apply mb-[26px];
    }
    .mpblog-post-view .post-item-wraper .post-info-wraper {
        @apply p-2.5;
    }
    .mpblog-post-view #maincontent {
        @apply mb-[25px] pb-0;
    }
}
@media (min-width: 769px) and (max-width: 1024px) {
    .mpblog-post-view .mp-blog-view {
        @apply p-0;
    }
    /* .page-layout-2columns-right .column.main {
          margin-top: 35px;
      } */
}
@media (max-width: 576px) {
    .mpblog-post-view .blog-realeted-title h2 {
        @apply basis-13/20;
    }
    .mpblog-post-view .blog-realeted-title a.right {
        @apply basis-7/20;
    }
    .mpblog-post-view .post-info-wraper h2.mp-post-title {
        @apply h-auto text-4.25;
    }
    .mpblog-post-view .blog-container .post-item-wraper {
        @apply min-h-[auto];
    }
    .mpblog-post-view .blog-container .post-item-wraper .post-image a img {
        @apply min-h-[auto];
    }
    .mpblog-post-view .mpblog-post-view .page-title {
        @apply leading-5;
    }
}
@media (max-width: 480px) {
    .mpblog-post-view .column.main .container {
        @apply mt-8.75;
    }
    .mpblog-post-view .page-main {
        @apply mt-[25px];
    }
    .mpblog-post-view .blog-realeted-title h2 {
        @apply basis-13/20;
    }
    .mpblog-post-view .blog-realeted-title a.right {
        @apply basis-7/20;
    }
    .mpblog-post-view .post-info-wraper h2.mp-post-title {
        @apply h-auto text-4.25;
    }
    .mpblog-post-view .blog-container .post-item-wraper {
        @apply min-h-[auto];
    }
    .mpblog-post-view .post-view-image .img-responsive {
        height: 250px;
    }
}
@media (max-width: 440px) {
    .mpblog-post-view #maincontent {
        @apply mt-[15px];
    }
}
@media (max-width: 425px) {
    .mpblog-post-view .column.main {
        @apply w-99/100;
    }
    .mpblog-post-view .blog-realeted-title h2 {
        @apply basis-11/20;
    }
    .mpblog-post-view .blog-realeted-title a.right {
        @apply basis-9/20;
    }
    .mpblog-post-view .share-col-right {
        @apply w-4/5;
    }
}
@media (max-width: 390px) {
    .mpblog-post-view .column.main {
        @apply w-9/10;
    }
}
@media (max-width: 320px) {
    .mpblog-post-view .column.main {
        @apply w-13/20;
    }
}

.mpblog-post-view {
    .mp-blog-rss {
        @apply hidden;
    }
    .post-view-image {
        &.col-xs-12 {
            @apply hidden;
            &.is-banner {
                @apply block;
            }
        }
    }
    .title-account {
        @apply px-0;
    }
}