/* .text-gray-900.page-title.title-font.title-cart-checkout{
    font: normal normal bold 24px/28px Public Sans !important; 
    color: #16365B !important; 
} */

/* mpblog-category-view */
.mpblog-post-index,
.mpblog-category-view {
    .input-search-header {
        @apply h-[41px] px-3 py-0;
        font-family: unset !important;
    }
    .title-categories-menu-header b {
        @apply font-[bolder];
        font-family: "Public Sans, Bold";
    }
    .post-image {
        @apply min-h-[267px];
    }
    .toolbar-number-dcc {
        @apply mb-0;
    }
    .pager span.toolbar-number {
        @apply flex;
    }
    .page-main {
        @apply mt-0;
    }
    .back-page-cat {
        @apply mt-[15.5px] py-0;
    }
    .container .page-title .base {
        @apply text-2xl text-[#16365B] block;
        font-family: "Public Sans, Bold";
    }
    .container-filter-cat {
        @apply flex flex-wrap place-items-center mb-[60px];
    }
    .page-title-wrapper .page-title {
        @apply text-2xl mt-[52.5px];
    }
    .post-list-content .post-list-body,
    .post-list-content .post-list-body .post-item-wraper {
        @apply flex flex-wrap; 
        /* place-content-between removed, see card TOPS-441  */
    }
    .post-list-content .post-list-body .post-list-item {
        @apply basis-[32.3%] mr-[7px] mb-[41px] p-1;
    }
    .container-filter-cat .mp-sidebar.mpcss {
        @apply flex-[auto];
    }
    .mpblog-search {
        @apply w-full max-w-[25%] mb-0 relative;
        place-content: flex-end;
    }
    .mp-sidebar {
        @apply mb-0;
    }
    .mp-sidebar .row {
        @apply place-items-center;
    }
    .mpblog-search input {
        @apply h-10 border w-full opacity-100 text-base rounded-lg border-solid border-[#002C4B];
    }
    .mpblog-search button.action.search {
        @apply h-10 w-10 text-white rounded-[0px_8px_8px_0px] right-0 top-0;
        background: #002c4b !important;
    }
    
    .mpblog-search .actions {
        @apply absolute;
    }
    .mpblog-search .field.search {
        @apply place-items-center w-full mt-0;
    }
    .mpblog-search .field.search .label {
        @apply hidden;
        /* flex-basis: 15%; */
    }
    .mpblog-search .field.search .control {
        @apply w-full basis-full;
    }
    .mp-sidebar .block-title p {
        @apply text-gray-800 mb-0;
        /* margin-right: 16px; */
    }
    ul.menu-categories a.list-categories {
        @apply border text-base text-[#002C4B] font-semibold px-[19px] py-2.5 rounded-lg border-solid border-[#2EB6ED];
        list-style: none;
        font-family: "Public Sans, Regular";
    }
    ul.menu-categories a.list-categories.active-filter {
        @apply text-white;
        background: #2EB6ED;
    }
    .d-flex-n-wrap {
        @apply flex flex-wrap;
    }
    .container-filter-cat .mp-sidebar .row div {
        @apply w-[unset];
    }
    .blog-container .post-item-wraper {
        @apply border p-0 rounded-xl border-solid border-[#D5EAF0] h-[400px];
    }
    .blog-container .post-list-item .cat-banner {
        @apply z-[1] absolute left-3 top-3;
    }
    .blog-container .post-list-item .cat-banner p {
        @apply text-sm px-3 py-0.5 rounded-[15px] text-blue-alice;
        background: #f9b734;
    }
    .blog-container .post-list-item .cat-banner a.mp-info {
        @apply text-blue-alice;
    }
    .blog-container .mp-post-title a {
        @apply text-xl text-[#2EB6ED];
        font-family: "Public Sans, Bold";
    }
    .blog-container .post-author {
        @apply text-[17px] text-[#002C4B] font-[normal];
        font-family: "Open Sans";
    }
    .blog-container .post-item-wraper .post-image a img {
        @apply w-full object-top object-cover rounded-[12px_12px_0px_0px];
        position: inherit !important;
    }
    .blog-container .blog-title h2 {
        @apply text-[#16365B] text-2xl;
        font-family: "Public Sans, Bold";
    }
    .blog-container .blog-title {
        @apply flex flex-wrap place-content-between place-items-baseline;
    }
    .blog-container .blog-title p a {
        @apply text-[#2EB6ED] place-items-center flex text-sm after:text-sm after:leading-[33px] after:text-[#2EB6ED] after:content-['\e608'] after:align-top after:inline-block after:font-[normal] after:overflow-hidden after:text-center after:m-[5px];
    }
    .blog-container .blog-title p a::after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-family: "luma-icons";
    }
    .blog-container .post-image {
        @apply w-full;
        /* position: absolute; */
    }
    .blog-container .post-info-wraper h2.mp-post-title a {
        @apply !text-[#2EB6ED] text-xl font-bold tracking-[-1px] overflow-hidden;
        /* font-family: "Open Sans"; */
        word-spacing: 2px;
        font-family: "Public Sans, Bold";
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
    }
    .post-info-wraper {
        @apply grow;
    }
    .pages strong.page span {
        @apply px-[5px] py-0;
    }
    #mpblog-list-container .pager {
        @apply flex flex-wrap place-content-center text-[#002C4B] mb-[100px];
    }
    #mpblog-list-container .pager p span,
    #mpblog-list-container .pager li a span {
        @apply text-[#002C4B];
    }
    .post-info-wraper h2.mp-post-title {
        @apply h-auto;
    }
    .post-list-item .post-info-wraper {
        @apply px-5 py-[18px];
    }
    .post-list-item .post-item-wraper {
        @apply h-auto;
    }
    .block-content.menu-categories.category-level2 {
        @apply mt-1.5 px-[5px] py-0;
    }
    #maincontent {
        background: #ffff;
    }
    .page-wrapper {
        @apply top-[100px];
    }
    .mpblog-list {
        @apply absolute block w-full z-[999] box-border border shadow-[1px_4px_3px_rgba(50,50,50,0.64)] overflow-auto overflow-x-hidden max-h-[2000px] px-0 py-2.5 rounded-[3px] border-t-0 border-solid border-[#ddd];
        background: #fff;
    }
    .autocomplete-suggestion {
        @apply w-full;
    }
    .mpblog-product-line {
        @apply cursor-pointer;
    }
    .mpblog-suggestion {
        @apply flex border-b-[#eee] border-b border-solid last:border-b-[none];
    }
    .mpblog-suggestion-left {
        @apply max-w-[25%] float-left p-2.5;
    }
    .mpblog-short-des {
        @apply text-[#002C4B];
    }
    .mpblog-title {
        @apply text-[#2EB6ED];
    }
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .mpblog-post-index,
    .mpblog-category-view {
        ul.menu-categories a.list-categories {
            @apply px-2.5 py-3;
        }
    }
}
@media (min-width: 1120px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .post-link-title.desktop-1024 {
            @apply hidden;
        }
    }
}
@media (min-width: 1025px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .mp-sidebar.mpcss {
            @apply w-9/12;
        }

        .container-filter-cat .mp-sidebar .row div.block-title {
            @apply w-[12%] place-items-center;
            overflow-y: unset;
            overflow-x: unset;
        }
        .container-filter-cat .mp-sidebar .row div {
            @apply flex-nowrap w-[87%] overflow-y-hidden overflow-x-auto basis-auto flex h-[59px];
            place-content: flex-start;
        }
        .block-content.menu-categories.category-level2 {
            @apply flex-[0_0_auto] w-auto mr-px mt-1.5 px-[5px] py-2.5;
            /* padding: 0 5px !important;
              margin-top: 6px !important; */
            /* flex-basis: 18% !important; */
        }
    }
}
@media (min-width: 1025px) and (max-width: 1280px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .container-filter-cat .mp-sidebar.mpcss {
            @apply w-[70%];
        }
        .container-filter-cat .mp-sidebar .row div {
            @apply w-[83%];
        }
        .container .page-title {
            @apply ml-[22px] mb-0;
        }
    }
}
@media (min-width: 1281px) and (max-width: 1919px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .container-filter-cat .mp-sidebar .row div.block-title {
            @apply w-[12%];
        }
    }
}
@media (min-width: 1025px), print {
    .mpblog-post-index,
    .mpblog-category-view {
        .column.main {
            @apply w-full;
        }
        .columns {
            @apply grid-cols-[unset];
        }
        .container .page-title {
            @apply mb-5;
        }
        .back-page-cat {
            @apply mt-[30.5px] mb-[30px];
        }
        .post-link-title.mobile {
            @apply hidden;
        }
    }
}
@media (max-width: 1024px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .post-link-title.desktop {
            @apply hidden;
        }
        .post-link-title.desktop-1024 {
            @apply hidden;
        }
        .wrapper-searching .container {
            @apply px-[0.8rem];
        }
        .columns {
            @apply pl-[0.7em] pr-[0.7rem] grid-cols-1 mb-10 px-0;
        }
        .container .page-title .base {
            @apply text-[17px];
        }
        .page-title.title-font {
            @apply mt-0 px-6;
        }
        .container-filter-cat .mp-sidebar .row div.block-title {
            overflow-y: unset;
            overflow-x: unset;
        }
        ul.menu-categories a.list-categories {
            @apply px-3 py-2.5 block w-full text-center text-[13px];
        }
        .container-filter-cat .mp-sidebar .row div::-webkit-scrollbar {
            @apply w-0 hidden;
        }
        .mp-sidebar .block-title p {
            @apply text-[13px] mr-0;
        }
        .container-filter-cat .mp-sidebar .row .block-title {
            @apply w-[10%];
            /* display: none; */
        }
        .container-filter-cat .mp-sidebar .row div {
            @apply flex-nowrap w-[90%] overflow-y-hidden overflow-x-scroll basis-auto;
            place-content: flex-start;
        }
        .container-filter-cat {
            place-items: unset;
        }
        .blog-container .post-info-wraper h2.mp-post-title a {
            @apply text-[#16365B] text-[15px] h-11;
            font-family: "Public Sans";
        }
        .blog-container .post-author {
            @apply text-[13px] opacity-50;
            font-family: "Public Sans";
        }
        .post-list-item .post-info-wraper {
            @apply px-3 py-2.5;
        }
        .blog-container .post-list-item .cat-banner {
            @apply w-1/5;
        }
        .blog-container .post-list-item .cat-banner p {
            @apply text-[11px] text-center font-bold px-[11px] py-1;
        }
        .blog-container .post-list-item .cat-banner a.mp-info {
            @apply text-[11px];
        }
        .container-filter-cat {
            @apply flex-col-reverse mb-[30px] p-1;
        }
        .block-content.menu-categories.category-level2 {
            @apply basis-[37%] flex-[0_0_auto] w-[33.33333333%] mr-px mt-1.5 px-[5px] py-2.5;
        }
        .mp-sidebar {
            @apply w-[-webkit-fill-available] mt-3.5;
        }
        .mpblog-search {
            @apply max-w-full;
        }
        .post-list-content .post-list-body .post-list-item {
            @apply basis-full ml-0 mb-[11px];
        }
        .pages strong.page span {
            @apply px-[5px] py-0;
        }
        #mpblog-list-container .pager {
            @apply flex flex-wrap place-content-around mb-0;
        }
        .post-info-wraper h2.mp-post-title {
            @apply text-[17px] leading-[21px];
        }
        #limiter {
            @apply text-sm;
        }
        #mpblog-list-container .pager li a span {
            @apply text-sm;
        }
        #maincontent {
            @apply pb-0 ml-[2%];
        }
        .mpblog-search input {
            @apply text-[13px];
        }
    }
}
@media (max-width: 1024px) and (min-width: 821px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .back-page-cat {
            @apply ml-[2%] mt-[35px] mb-[13px];
        }
        .block-content.menu-categories.category-level2 {
            @apply basis-[27%];
        }
    }
}
@media (max-width: 820px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .page-title.title-font {
            @apply mt-0;
        }
        .back-page-cat {
            @apply ml-[1.5em] mb-0;
        }
    }
}
@media (max-width: 768px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .page-title.title-font {
            @apply mt-[30px];
        }
        .back-page-cat {
            display: none;
        }
        .page-title.title-font {
            padding-left: 0px;
        }
    }
}
@media (max-width: 576px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .page-title.title-font {
            @apply mt-[30px];
        }
        ul.menu-categories a.list-categories {
            display: block;
            width: 100%;
            padding-left: 12px;
            padding-right: 12px;
            padding-top: 7px;
            padding-bottom: 4px;
            text-align: center;
            font-size: 13px;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 19px;
        }
    }
}
@media (max-width: 430px) {
    .mpblog-post-index,
    .mpblog-category-view {
        .container-filter-cat .mp-sidebar .row .block-title {
            @apply w-[17.5%];
        }
        .container-filter-cat .mp-sidebar .row div {
            @apply w-4/5;
        }
        #maincontent {
            @apply mt-0;
        }
        .page-title.title-font {
            @apply mr-[-0.6rem] ml-[-0.6rem];
        }
        .blog-container .post-list-item .cat-banner {
            @apply w-[30%];
        }
        .blog-container .post-item-wraper .post-image a img {
            @apply h-auto;
        }
        .columns {
            @apply pl-[0.7em] pr-[0.7rem];
        }
        .blog-container .post-image {
            min-height: auto;
        }
    }
}
