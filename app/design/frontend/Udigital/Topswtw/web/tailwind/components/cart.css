.checkout-cart-index .top-cart {
    @apply -mt-px;
}

.color-title-cart {
    @apply text-[#16365B];
}

.title-top-crossel {
    @apply mt-[0px];
}

.gap-cart {
    @apply mb-4; /* Closest value to 17px */
}

.shipping-cart .label-product-image-list {
    @apply absolute top-[59px] left-0 z-[2];
}




/* mobile */
@media screen and (max-width: 1280px) {
    .line-cart{
       @apply w-full border-t border-[#16365B]
    }
    .pt-custom-shoppingcart {
        @apply pt-[30px];
    }
    
    .checkout-cart-index {
        .title-countainer {
            @apply mx-auto mb-[9px];
        }
    
        .p-custom-content {
            @apply w-max p-0 m-0;
        }
    
        .content-slide-cart {
            @apply pl-0 -ml-1;
        }
    
        div::-webkit-scrollbar {
            width: 8px;
            height: unset;
        }
    
        .w-custom-shoppingcart {
            @apply w-full;
        }
    
        .p-custom-shoppingcart {
            @apply pr-[0px];
        }
    
        .overflow-x-hidden {
            @apply overflow-x-auto;
        }
    }

    .content-slide-cart .cart-left:first-child {
        @apply ml-0 w-[263px] mr-[50px];
    }
    
    .cart-left {
        @apply w-[263px] mr-[50px];
    }
    
    .content-image-shopping-cart {
        @apply mx-auto;
    }

    .checkout-cart-index{

        .align-middle {
            @apply pl-0;
        }
        
        .card-product {
            @apply h-auto pb-[17px];
        }
        
        .mb-mobile-text {
            @apply mb-[10px];
        }
        
        .item-product-bottom {
            @apply hidden;
        }
        
        .content-p-slide {
            @apply pl-[0];
        }
        
        .min-text-sku {
            @apply overflow-hidden text-ellipsis w-full;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }
        
        .content-persen-h {
            @apply h-[25px];
        }
        
        .product-description-container {
            @apply h-[40px] w-full;
            
        }
        
        ::placeholder {
            @apply text-[15px];
        }
        
        .slider-none {
            @apply hidden;
        }
        
        .item-quantity input {
            @apply w-[73px];
        }
        
        .title-cart-checkout {
            @apply mt-[45px];
        }
        
        .top-cart-shopping {
            @apply mt-[30px];
        }
        
        .top-cart-price {
            @apply -mt-[5px];
        }
        
        .product-item-photo {
            @apply mr-[0];
           
        }
        
        .title-crossel {
            @apply mb-[10px] -mt-[20px];
        }
        
        .top-content-footer {
            @apply -mt-[80px];
        }
        
        .price-content-cart {
            @apply -mt-[5px];
        }
        
        .l-title-text {
            @apply ml-0 font-bold text-[#16365B] text-[17px];
        }
    }

   
    

    .text-mobile-checkout {
        @apply text-[15px];
        
    }
    
    .h-subtotal {
        @apply h-[30px];
       
    }
    
    .font-size-qty {
        @apply text-[19px];
        
    }
    
    .text-title-bottom {
        @apply text-[11px] text-[#B8C2CD];
        
    }
    
    .title-cart {
        @apply text-[17px] -mt-[70px] mb-[20px] text-[#16365B] font-bold ml-[15px];
    }
    
    .padding-top-bottom {
        @apply pb-[7px] pt-[7px];
        
    }
    
    .f-size-cart-m-15 {
        @apply text-[15px] text-[#002C4B];
        
    }
    
    .f-size-cart-m-13 {
        @apply text-[13px] text-[#002C4B];
        
    }
    
    .content-link-cart {
        @apply hidden;
    }
    
    .border-b-color {
        @apply border-b border-[#16365B];
    }
    
    .mobile-position-cart {
        @apply mt-auto flex flex-col;
    }

    .order-position-cart {
        @apply order-1 bg-[#DEF3FC] rounded-tl-[10px] rounded-tr-[10px] px-[25px] pb-[20px] mb-[60px];
        
    }
    
    .checkout-cart-index {
        @apply bg-white;
    }
    
    .title-mobile-cart {
        @apply text-[15px] text-[#16365B] font-bold;
        
    }
    
    .trash-mobile {
        @apply text-[#2EB6ED] ml-auto;
        
    }
    
    .qty-product {
        @apply ml-0;
    }
    
    .icon-plus {
        @apply absolute mt-[-50%] cursor-pointer text-black translate-x-[2px] right-[30px];
        
    }

    .icon-min {
        @apply absolute -mt-[30%] cursor-pointer text-black translate-x-[2px] right-[30px];
        
    }
    
    .product-image-container img {
        @apply w-[110px];
    }
    
    
    .qty-shipping {
        @apply outline-none mt-[10px] rounded-[8px];
        
    }
    
    .checkout-cart-index .price-shoppingcart-list {
        @apply text-[#2EB6ED] text-[15px] text-right mt-[26px];
    }
    
    .price-perset {
        @apply text-[11px] text-[#16365B] text-right mt-[3px];
    }
    
    .content-button-persen {
        @apply border-0 w-full h-auto text-[11px] text-[#A2AFBD] flex border border-[#D5EAF0] rounded-[8px] mt-[5px];
        
    }
    
    .content-button {
        @apply w-[40%] text-[#16365B] text-center cursor-pointer;
    }
    
    .active-persen, .content-button:hover {
        @apply bg-[#2EB6ED] w-[40%] text-white text-center cursor-pointer;
    }
    
    .title-persen {
        @apply text-[11px] text-[#16365B] font-normal;
    }
    
    .cart-mobile {
        @apply grid w-[90%] bg-white border-t border-b border-[#D5E9F0] pt-[20px] pb-[20px] h-auto mx-auto;
        /*height: auto !important;*/
    }
    
    .shipping-cart {
        @apply hidden;
    }
    
    .buttonIn {
        @apply w-[93px] relative grid;
    }
    
    .price-wrapper > .price-shoppingcart-list {
        @apply text-[24px];
    }
    
    .content-trash {
        @apply text-[#2EB6ED] flex mt-[90px];
    }
    
    #shopping-cart-table > caption {
        @apply hidden;
    }
    
    #form-validate > div.cart.main.actions > a > span {
        @apply hidden;
    }
    
    #form-validate > div.cart.main.actions > button > span {
        @apply hidden;
    }
    
    #checkout-link-button {
        @apply bg-[#00B900] text-white rounded-[8px];
    }
    
    #block-shipping {
        @apply hidden;
    }
    
    #loading {
        @apply border-[16px] border-solid border-[#f3f3f3] border-t-[16px] border-t-[#3498db] rounded-full w-[30px] h-[30px] mx-auto hidden -mt-[150px];
        animation: spin 2s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    body.loading {
        @apply overflow-hidden;
    }
    
    .content-buttonccc {
        @apply bg-[#F9B734] text-[#002C4B] rounded-[8px] pt-[10px] pb-[10px] px-[40px];
    }

}

@media screen and (max-width: 280px) {
    .icon-plus {
        @apply absolute -mt-[59%] cursor-pointer text-black translate-x-[2px] right-[30px];
    }
 
    .icon-min {
        @apply absolute -mt-[39%] cursor-pointer text-black translate-x-[2px] right-[30px];
    }
}

.mb-mobile-text {
    @apply screen-192.25:mb-5 lg:mb-8;
}

@media only screen and (min-width: 820px) and (max-width: 820px) {
    .content-link-cart {
        @apply flex text-[#2EB6ED] text-[15px];
    }
    .content-trash {
        @apply flex flex-col justify-end ml-auto mt-auto;
    }
}


/* mobile xd */

@media only screen and (min-width: 280px) and (max-width: 768px) {
    .checkout-cart-index .item-product-bottom-mobile {
        @apply block h-[110px];
    }
    .checkout-cart-index .custom-top-perset {
        @apply -mt-[7px];
    }
    .checkout-cart-index .custom-top {
        @apply -mt-[10px];
    }
}

@media only screen and (min-width: 1024px) and (max-width: 1280px) {
    .checkout-cart-index .p-custom-content {
        @apply mt-[20px];
    }
}

@media only screen and (min-width: 768px) and (max-width: 768px) {
    .checkout-cart-index .custom-top {
        @apply mt-[9.5px];
        
    }
    .checkout-cart-index .title-countainer {
        @apply mx-auto pt-[32px] pb-[9px];
    }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
    .checkout-cart-index .item-product-bottom-mobile {
        @apply block h-[163px];
       
    }
    .checkout-cart-index .custom-top {
        @apply -mt-[1px];
    }
}

@media only screen and (min-width: 280px) and (max-width: 768px) {
    .title-top-crossel {
        @apply pl-[20px];
    }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
    .checkout-cart-index .top-cart {
        @apply mb-0;
    }
    .checkout-cart-index .title-cart-checkout {
        @apply mt-0;
    }
    .checkout-cart-index .title-account {
        @apply mt-0;
    }
    .checkout-cart-index .back-page-cat {
        @apply flex;
        flex-direction: unset;
    }
}

@media only screen and (min-width: 768px) and (max-width: 1024px) {
    div::-webkit-scrollbar {
        width: 8px;
        height: 0 !important;
    }
}

@media only screen and (min-width: 390px) and (max-width: 390px) {
    .pt-custom-shoppingcart {
        @apply mt-[0px];
    }
    .content-slide-cart .cart-left:first-child {
        @apply ml-0 w-[263px] mr-[50px];
    }
    .content-title-product {
        @apply w-[200px];
    }
    .checkout-cart-index .item-product-bottom-mobile {
        @apply block h-[117px];
       
    }
    .checkout-cart-index .card-product-list {
        @apply w-[263px] rounded-[10px] p-[1rem] max-w-[263px] min-w-[263px];
    }
    .checkout-cart-index .item-product-reviews {
        @apply flex;
        
    }
    .checkout-cart-index .price {
        @apply text-[21px];
    }
    
    .checkout-cart-index .text-blue-picton {
        @apply text-[12px];
    }
    
}

@media only screen and (min-width: 1920px) and (max-width: 1920px) {
    .checkout-cart-index{

        .title-countainer {
            @apply pb-[30px];
        }
        
        .align-middle {
            @apply pl-0;
        }
        
        .text-perset-desktop {
            @apply text-[13px];
            
        }
        
        .card-product {
            @apply pb-[15px];
            
        }
        
        .content-slide-cart {
            @apply pl-0 -ml-[4px];
            
        }
        
        .cart-left {
            @apply w-[24.9%] ml-[3px] mr-[0px];
        }
        
        .cart-left:first-child {
            @apply ml-0;
            
        }
    }
    .content-title-product {
        @apply overflow-hidden text-ellipsis w-full;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    
    .content-title-sku {
        @apply h-[94.9px];
    }
    
    .checkout-cart-index .top-cart-shopping {
        @apply mt-[70px];
    }
    
    .checkout-cart-index .top-cart-price {
        @apply mt-0;
    }
    
    .checkout-cart-index .title-cart-checkout {
        @apply -mt-[25px] mb-[10px];
    }
    
    .checkout-cart-index .title-font {
        @apply text-[#002C4B];
        
    }
    
    .b-list {
        @apply mb-[30px];
    }
    
    .padding-top-bottom {
        @apply pt-[7px] -mt-[10px] pb-['7px'];
        
    }
    
    .text-title-shipping-bottom {
        @apply text-[15px];
    }
    
    .font-size-subtotal {
        @apply text-[16px];
    }
    
    .content-link-cart {
        @apply flex text-[#2EB6ED] text-[15px];
    }
    
    .w-cart-list {
        @apply w-[90%];
    }
    
    .left-qty {
        @apply ml-[145px];
        
    }
    
    .top-title-persen {
        @apply mt-[34.9px];
    }
    
    .icon-plus {
        @apply absolute -mt-[55px] cursor-pointer text-black translate-x-[2px] -right-[20px];
    }
    
    .icon-min {
        @apply absolute -mt-[30px] cursor-pointer text-black translate-x-[2px] -right-[20px];
    }
    
    .content-button-persen {
        @apply border-0 h-auto text-[16px] text-[#A2AFBD] flex border border-[#D5EAF0] rounded-[8px] mt-[2px] w-max;
        
    }
    .content-button {
        @apply w-[114px] text-[#16365B] text-center cursor-pointer;
    }
    
    .active-persen, .content-button:hover {
        @apply bg-[#2EB6ED] w-[114px] text-white text-center cursor-pointer;
    }
    
    .buttonIn {
        @apply w-[93px] relative grid;
    }
    
    .price-wrapper > .price-shoppingcart-list {
        @apply text-[24px];
    }
    
    .shipping-cart {
        @apply border-[#D5EAF0] rounded-[12px] mb-[20px] min-h-[227px];
        
    }
    
    .content-trash {
        @apply text-[#2EB6ED] flex mt-[139px] text-[14px];
       
    }
    
    .qty-product {
        @apply w-full outline-none h-[54px] rounded-[8px] mt-[10px];
    }
    
    #shopping-cart-table > caption {
        @apply hidden;
    }
    
    #form-validate > div.cart.main.actions > a > span {
        @apply hidden;
    }
    
    #form-validate > div.cart.main.actions > button > span {
        @apply hidden;
    }
    
    #checkout-link-button {
        @apply bg-[#00B900] text-white rounded-[8px];
    }
    
    #block-shipping {
        @apply hidden;
    }
    
    
    
    #cart-totals > div:nth-child(3) > div {
        @apply border border-[#fafafa];
    }
    
    .checkout-cart-index .price-shoppingcart {
        @apply text-[#2EB6ED] text-[30px];
    }
    
    #loading {
        @apply border-[16px] border-solid border-[#f3f3f3] border-t-[16px] border-t-[#3498db] rounded-full w-[30px] h-[30px] mx-auto hidden -mt-[150px];
        animation: spin 2s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    body.loading {
        @apply overflow-hidden;
    }
    
    .content-buttonccc {
        @apply bg-[#F9B734] text-[#002C4B] rounded-[8px] pt-[10px] pb-[10px] px-[40px];
    }
}

/* desktop */
@media screen and (min-width: 1280px) {

.slider-none {
    @apply hidden;
}

.cart-left {
    @apply w-[29%];
}

.cart-mobile {
    @apply hidden;
}

.checkout-cart-index{
    .title-countainer {
        @apply mx-auto pt-[10px];
    }
    
    .p-custom-content {
        
        @apply p-0 m-0 w-full;
        
    }
    
    .title-font {
        @apply text-[#002C4B];
        
    }
    
    div::-webkit-scrollbar {
        width: 8px;
        height: unset;
    }
    
    .content-slide-cart {
        @apply pl-0 -ml-[4px];
        
    }
    
    .align-middle {
        @apply pl-0;
        
    }
    
    .overflow-x-hidden {
        @apply overflow-x-auto;
       
    }
    
}


.title-crossel {
    @apply mb-[20px] mt-[20px];
}

.title-cart {
    @apply hidden;
}

.padding-top-bottom {
    @apply pb-[7px] pt-[7px] -mt-[10px] mb-[30px];
}

.text-title-shipping-bottom {
    @apply text-[15px];
    
}

.font-size-subtotal {
    @apply text-[16px] text-[#002C4B];
}

.content-link-cart {
    @apply flex text-[#2EB6ED] text-[15px];
}

.w-cart-list {
    @apply w-[90%];
}

.left-qty {
    @apply ml-auto;
}

.top-title-persen {
    @apply mt-0;
}

.icon-plus {
    @apply absolute -mt-[55px] cursor-pointer text-black translate-x-[2px] -right-[20px];
}

.icon-min {
    @apply absolute -mt-[30px] cursor-pointer text-black translate-x-[2px] -right-[20px];
}

.content-button-persen {
    @apply border-0 h-auto text-[16px] text-[#A2AFBD] flex border border-[#D5EAF0] rounded-[8px] mt-[10px] w-max;
}

.content-button {
    @apply w-[114px] text-[#16365B] text-center cursor-pointer;
}

.active-persen, .content-button:hover {
    @apply bg-[#2EB6ED] w-[114px] text-white text-center cursor-pointer;
}

.buttonIn {
    @apply w-[93px] relative grid;
}

.price-wrapper > .price-shoppingcart-list {
    @apply text-[24px];
}

.shipping-cart {
    @apply grid border border-[#D5EAF0] rounded-[12px] mb-[20px];
}

.content-trash {
    @apply text-[#2EB6ED] flex text-[14px] mt-[110px];
    
}

.qty-product {
    @apply w-full outline-none h-[54px] rounded-[8px] mt-[10px];
}

#shopping-cart-table > caption {
    @apply hidden;
}

#form-validate > div.cart.main.actions > a > span {
    @apply hidden;
}

#form-validate > div.cart.main.actions > button > span {
    @apply hidden;
}

#checkout-link-button {
    @apply bg-[#00B900] text-white rounded-[8px];
}

#block-shipping {
    @apply hidden;
}


#cart-totals > div:nth-child(3) > div {
    @apply border border-[#fafafa];
}

.checkout-cart-index .price-shoppingcart {
    @apply text-[#2EB6ED] text-[30px];
}

#loading {
    @apply border-[16px] border-solid border-[#f3f3f3] border-t-[16px] border-t-[#3498db] rounded-full w-[30px] h-[30px] mx-auto hidden -mt-[150px];
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

body.loading {
    @apply overflow-hidden;
}

.content-buttonccc {
    @apply bg-[#F9B734] text-[#002C4B] rounded-[8px] pt-[10px] pb-[10px] px-[40px];
}

}



@media only screen and (min-width: 820px) and (max-width: 1199px) {

    .content-link-cart {
        @apply flex text-[#2EB6ED] text-[15px];
    }
    
    .icon-plus {
        @apply absolute -mt-[55px] cursor-pointer text-black translate-x-[2px] -right-[20px];
    }
    
    .icon-min {
        @apply absolute -mt-[30px] cursor-pointer text-black translate-x-[2px] -right-[20px];
    }
    
    .content-button-persen {
        @apply border-0 h-auto w-[340px] text-[16px] text-[#A2AFBD] flex border border-[#D5EAF0] rounded-[8px] mt-[10px] w-max;
        
    }
    
    .content-button {
        @apply w-[114px] text-[#16365B] text-center cursor-pointer;
    }
    
    .active-persen, .content-button:hover {
        @apply bg-[#2EB6ED] w-[114px] text-white text-center cursor-pointer;
    }
    
    .buttonIn {
        @apply w-[93px] relative grid;
    }
    
    .price-wrapper > .price-shoppingcart-list {
        @apply text-[24px];
    }
    
    .shipping-cart {
        @apply mt-[20px] border border-[#D5EAF0] rounded-[12px];
    }
    
    .content-trash {
        @apply text-[#2EB6ED] flex flex-col justify-end mt-auto;
        
    }
    
    #shopping-cart-table > caption {
        @apply hidden;
    }
    
    #form-validate > div.cart.main.actions > a > span {
        @apply hidden;
    }
    
    #form-validate > div.cart.main.actions > button > span {
        @apply hidden;
    }
    
    #checkout-link-button {
        @apply bg-[#00B900] text-white rounded-[8px];
    }
    
    #block-shipping {
        @apply hidden;
    }
    
    
    #cart-totals > div:nth-child(3) > div {
        @apply border border-[#fafafa];
    }
    
    .checkout-cart-index .price-shoppingcart-list {
        @apply text-[#2EB6ED] text-[30px];
    }
    
    #loading {
        @apply border-[16px] border-solid border-[#f3f3f3] border-t-[16px] border-t-[#3498db] rounded-full w-[30px] h-[30px] mx-auto hidden -mt-[150px];
        animation: spin 2s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    body.loading {
        @apply overflow-hidden;
    }
    
    .content-buttonccc {
        @apply bg-[#F9B734] text-[#002C4B] rounded-[8px] pt-[10px] pb-[10px] px-[40px];
    }
}

/* terug position */

@media only screen and (min-width: 768px) and (max-width: 1280px) {
    .checkout-cart-index .title-countainer {
        @apply mb-[30px];
    }
}

/* mobile 820 to 1280 */

@media only screen and (min-width: 820px) and (max-width: 1280px) {
    .w-cart-list {
        @apply w-full;
    }
    
    .cart-mobile {
        @apply w-full m-0;
    }
}

/* mobile 1280 */

@media only screen and (min-width: 1280px) and (max-width: 1280px) {
    .cart-mobile {
        @apply grid;
        
    }
    
    .cart-left {
        @apply w-[23%];
        
    }
    
    #cart-totals > div:nth-child(3) > div {
        @apply border-0 border-[#fafafa];
    }
    
    .w-cart-list {
        @apply w-full;
    }
    
    .cart-mobile {
        @apply w-full m-0;
       
    }
    
    .checkout-cart-index .title-countainer {
        @apply mb-[30px];
       
    }
    
    .checkout-cart-index .text-mobile-checkout {
        @apply mt-[20px];
       
    }
}

@media only screen and (max-width: 768px){

    .checkout-cart-index .title-cart-checkout {
        @apply mt-[39px];
    }
    .checkout-cart-index .top-content-footer{
        @apply mt-[10px];
    }

}
  
  

