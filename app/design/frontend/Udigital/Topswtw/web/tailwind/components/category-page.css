.column.main {
    background: transparent;
}

.sections.nav-sections
    .section-item-content
    .navigation
    ul
    li.level-top
    a
    span {
    color: var(--blue-picton);
    font-size: 14px;
}

.page-wrapper .navigation {
    display: none;
}

.navigation-desktop {
    /* border: 1px solid #d5eaf06b; */
    background: var(--white);
}

#search_home {
    border-radius: 5px;
    border-color: var(--white-azureish) !important;
    font-size: 16px;
}

.navigation-desktop ul {
    /* max-width: 1536px; */
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    /* padding-right: 1.5rem;
    padding-left: 1.5rem; */
}

.navigation-desktop ul li {
    margin-right: 10px;
    flex: 0 0 auto;
    width: auto;
}

.level2.nav-menu-top {
    color: var(--blue-picton);
}

.page-title-wrapper .page-title span {
    display: none;
}

.container-homepage {
    font-family: "Public Sans";
}

.container-homepage .notif-tops {
    background: var(--saffron);
    border-radius: 10px;
    padding: 10px;
    text-align: center;
    height: 70px;
    place-items: center;
    justify-content: center !important;
    flex-direction: unset !important;
}

.container-homepage .notif-text {
    flex-basis: 95%;
}

.container-homepage .header-tops {
    /* background-image: url('../images/pages/homepage/banner-tops.webp'); */
    padding: 20px;
    border-radius: 12px;
    /* background-size: 1400px 700px !important; */
    background-position: center !important;
    place-content: center;
    display: flex;
    flex-wrap: wrap;
}


/* Medium */
@media (min-width: 768px) {
    .container-homepage .header-tops {
        margin: 13px 0 0 0;
        height: 41vh;
    }

    .page-products .blog-container .blog-title {
        margin-bottom: 10px;
    }

    .header-tops .header-tops-one h2,
    .header-tops .header-tops-two h2 {
        margin-bottom: 10px;
    }

    .header-tops-two figure img{
        width: 16vw;
    }

    .header-tops .header-tops-two .first-child {
        margin-top: 9px;
    }

    .page-products .blog-container .mp-post-title a {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1; /* number of lines to show */
        line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    .page-with-filter .blog-container {
        margin-top: 75px;
    }

    .header-tops .header-tops-two .first-child {
        padding-bottom: 10px;
    }

    .page-products .blog-container .blog-title p a,
    .suggested-prod .suggested-prod-title p a {
        font-size: 14px;
    }

    .page-products .blog-container .post-list-item .cat-banner p {
        font-size: 14px;
    }

    .header-tops .header-tops-one a {
        color: var(--blue-picton);
        font-size: 14px;
    }

    .header-tops .header-tops-two .first-child p {
        font-size: 14px;
    }

    .header-tops .header-tops-two p {
        font-size: 15px;
        padding: 9.5px 0 0 0;
    }
}

.container-homepage .header-tops .container,
.container-homepage .header-tops .pagebuilder-column-line {
    background: var(--white);
    margin: 0 auto;
    max-width: 898px;
    max-height: 203px;
    display: flex;
    flex-wrap: wrap;
    padding: 25px 40px;
    border-radius: 12px;
    place-content: space-around;
    width: 810px !important;
}

.header-tops .header-tops-one {
    flex-basis: 50%;
    display: flex;
    flex-wrap: wrap;
}

.header-tops .header-tops-one h2,
.header-tops .header-tops-two h2 {
    font-size: 24px;
    font-weight: bold;
    color: var(--blue-prussian);
}

.header-tops .header-tops-one a {
    text-decoration: underline;
}

.header-tops .header-tops-two {
    flex-basis: 35%;
    display: flex;
    flex-wrap: wrap;
}

.header-tops .header-tops-two p {
    color: var(--blue-prussian);
    flex-basis: 100%;
}

.header-tops .header-tops-two p:hover {
    color: var(--blue-picton);
}

.header-tops .header-tops-two .first-child {
    border-bottom: 1px solid var(--blue-picton);
}

b,
strong {
    font-weight: bolder;
    font-family: "Public Sans, Bold";
}

.container-homepage.container-homepage-mobile {
    display: none !important;
}

.equipment-cat {
    margin-top: 50px;
}

.suggested-prod {
    margin-top: 70px;
}

.blog-container .blog-title h2,
.suggested-prod .suggested-prod-title h2,
.suggested-prod-title .suggested-prod-title,
.equipment-cat .equipment-cat-title h2 {
    color: var(--cadet-space);
    font-size: 24px;
    font-family: "Public Sans, Bold";
}

.blog-container .blog-title,
.suggested-prod .suggested-prod-title {
    display: flex;
    flex-wrap: wrap;
    place-content: space-between;
    place-items: baseline;
}

.blog-container .blog-title p{
    display: flex;
}

.blog-container .blog-title p a,
.suggested-prod .suggested-prod-title p a {
    color: var(--blue-picton);
    place-items: center;
    display: flex;
}

/* .blog-container .blog-title p a::after{
.suggested-prod .suggested-prod-title p a::after{
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 14px;
    line-height: 33px;
    color: var(--blue-picton);
    content: '\e608';
    font-family: 'luma-icons';
    margin: 5px;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    text-align: center;
} */
.equipment-cat .pagebuilder-column-line .cat-title {
    background: var(white);
    height: 43px;
    border-radius: 0 0 12px 12px;
}

.equipment-cat .pagebuilder-column-line .cat-title p {
    margin: 0;
    display: flex;
    place-content: center;
    place-items: center;
    height: 100%;
    width: 100%;
}

.equipment-cat .pagebuilder-column-line .cat-title p a {
    text-align: center;
    flex-basis: 100%;
    color: var(--blue-picton);
    font-size: 18px;
    font-family: "Public Sans, Bold";
}

.equipment-cat .pagebuilder-column figure {
    display: flex;
    place-content: space-around;
    padding: 28px;
    height: 180px;
    place-items: center;
}

.equipment-cat .pagebuilder-column figure a {
    position: absolute;
    width: 122px;
}

.equipment-cat .pagebuilder-column figure img {
    /* position: absolute; */
    width: 122px;
}

.equipment-cat
    .pagebuilder-column-group
    .pagebuilder-column-line
    .pagebuilder-column {
    background: #d5eaf06b;
    border-radius: 12px;
    border: 1px solid #d5eaf06b;
    height: 225px;
    margin: 5px;
}

.waarom-container {
    margin-top: 100px;
    background: var(--blue-alice);
    padding: 100px 0px;
}

.waarom-container figure img {
    border-radius: 12px;
}

.waarom-container .pagebuilder-column-group {
    display: flex;
    margin: 0 auto;
    width: 100%;
    max-width: 88.5%;
}

.waarom-container .column-text-container {
    width: 100%;
    font-size: 16px;
    justify-content: center !important;
}

.waarom-container .pagebuilder-column.column-text-container h3 {
    font-size: 24px;
    font-family: "Public Sans, Bold";
}

.waarom-container .pagebuilder-column-line {
    place-content: space-between;
}

.page-products .blog-container .post-item-wraper {
    padding: 0px !important;
    border-radius: 10px;
    border: 1px solid var(--white-azureish) !important;
}

.page-products .blog-container .post-list-item {
    flex-basis: 32% !important;
}

.blog-container .post-list-item .cat-banner {
    z-index: 1;
    position: absolute;
    top: 12px;
    left: 12px;
}

.blog-container .post-list-item .cat-banner p {
    background: var(--saffron);
    padding: 2px 12px;
    border-radius: 15px;
    font-weight: 600;
}

.blog-container .post-list-item .cat-banner a.mp-info {
    color: var(--blue-alice);
}

.page-products .blog-container .post-item-wraper {
    position: relative;
}

.blog-container .mp-post-title a {
    font-size: 20px;
    color: var(--blue-picton);
    font-family: "Public Sans, Bold";
}

.blog-container .post-author {
    font-size: 17px;
    color: var(--blue-prussian);
}

.blog-container .post-info-wraper {
    padding: 15px 20px;
}

.page-products .blog-container .post-item-wraper .post-image a img {
    object-position: top;
    object-fit: cover;
    border-radius: 12px 12px 0px 0px;
    width: 100%;
}

.input-search-container {
    margin-bottom: 10px;
}

.input-search-home-container {
    font-size: 14px;
    background: var(--white) 0% 0%no-repeat padding-box;
    border: 1px solid var(--platinum);
    border-radius: 8px;
    opacity: 1;
    height: 41px;

    .action {
        &.search {
            .icon-search {
                @apply text-blue-picton md:text-blue-picton hover:md:text-blue-prussian;
            }

            &:hover {
                .icon-search {
                    @apply text-blue-prussian;
                }
            }
        }
    }
}

[type='search']:focus{
    --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
    --tw-ring-offset-width: 0px;
    --tw-ring-color: transparent;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    border-color: transparent;
}

.input-search-home {
    @apply text-3.25 md:text-3.5;
    background: var(--white) 0% 0%no-repeat padding-box;
    border-radius: 8px;
    opacity: 1;
    color: var(--blue-prussian);

    &:focus {
        @apply !border-transparent !ring-transparent;
        outline: none;

        + {
            .action {
                &.search {
                    @apply bg-blue-picton border border-solid border-blue-picton;

                    svg {
                        @apply text-blue-prussian;
                    }
                }
            }
        }
    }

    &::placeholder,
    &:-ms-input-placeholder,
    &::-ms-input-placeholder {
        @apply text-3.25 md:text-3.5;
        color: var(--gray-spanish);
        opacity: 1;
    }

    &::-webkit-search-cancel-button,
    &::-webkit-search-decoration,
    &::-webkit-search-results-button,
    &::-webkit-search-results-decoration {
        @apply hidden;
    }
}

.mpblog-search input {
    border-radius: 8px;
    height: 40px !important;
}

.mpblog-search .control::after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 20px;
    line-height: 40px;
    color: var(--blue-picton);
    content: "\e615";
    font-family: "luma-icons";
    margin-left: -33px;
    vertical-align: top;
    font-weight: normal;
    overflow: hidden;
    text-align: center;
}
.post-info-wraper h3.mp-post-title a {
    @apply overflow-hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
  }

@media (min-widht: 1440px) {
    .waarom-container .pagebuilder-column-group {
        width: 1224px;
    }
}

@media (min-width: 768px) {
    .pagebuilder-column-group.header-tops.header-tops-mobile {
        display: none !important;
    }

    .mp-post-title {
        margin-bottom: 5px;
    }
    .header-tops-two figure {
        display: none;
    }
}

@media (max-width: 768px) {
    .page-products .blog-container .blog-title p a,
    .suggested-prod .suggested-prod-title p a {
        font-size: 13px;
    }

    .page-products .blog-container .post-list-item .cat-banner p {
        font-size: 11px;
    }

    .header-tops .header-tops-two p {
        font-size: 13px;
    }

    .header-tops .header-tops-one a {
        color: var(--blue-jeans);
        font-size: 13px;
    }

    .page-products .blog-container .row-full-width-inner {
        padding-right: 0!important;
    }

    .page-products .blog-container .blog-title {
        margin-right: 1.5rem;
    }

    .container-homepage .pagebuilder-column-line{
        margin-top: 5vh;
    }

    .container-homepage .submit-search-header svg{
        width: 16px;
        height: 16px;
    }



    .header-tops .header-tops-one h2,
    .header-tops .header-tops-two h2 {
        margin-bottom: 3px;
    }

    .container-homepage .row-full-width-inner {
        padding: 0px !important;
    }

    .pagebuilder-column-group.header-tops.header-tops-desktop .pagebuilder-column-line {
        position: relative;
    }
    .container-homepage .header-tops {
        border-radius: 0px;
    }

    .navigation-desktop.shadow,
    .container-homepage.container-homepage-mobile {
        display: none !important;
    }

    .page-products .blog-container .mp-post-title a {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* number of lines to show */
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .container-homepage .header-tops {
        margin: 0 0 13px 0;
        height: 46vh;
        padding: 0px;
    }

    .container-homepage .header-tops .pagebuilder-column-line {
        padding: 0px;
        margin: 0 5% 0 5%;
    }

    .container-homepage.container-homepage-mobile {
        background: var(--blue-alice);
        margin-top: 20px;
    }

    .container-homepage.container-homepage-mobile
        .header-tops-mobile
        .header-tops-one
        h2 {
        text-align: center;
        font-size: 1.8em;
    }

    .container-homepage
        .header-tops-mobile
        .pagebuilder-column.header-tops-one-mobile {
        flex-basis: 30%;
        overflow: hidden;
        height: 100%;
    }

    .header-tops .header-tops-two p {
        color: #002C4B;
        font-size: 13px;
        padding-top: 0.7vh;
    }

    .header-tops .header-tops-two {
        flex-basis: 55%;
        margin-left: 10px;
    }

    .header-tops-mobile .pagebuilder-column-line:nth-child(1) {
        background: none !important;
    }

    .header-tops-one,
    .header-tops-mobile .pagebuilder-column-line:nth-child(2) {
        z-index: 0;
        background: var(--cadet-space);
        padding: 20px 20px 0;
        border-radius: 10px;
    }

    .header-tops .header-tops-one h2 {
        font-size: 15px;
        color: var(--white);
        flex-basis: 100%;
    }

    .header-tops-one .input-search-html {
        flex-basis: 100%;
    }

    .header-tops-two {
        /* .header-tops-mobile .pagebuilder-column-line:nth-child(3) { */
        position: absolute;
        right: 0%;
        width: 50% !important;
        border: 1px solid #b4b4b461;
        place-content: flex-start !important;
        z-index: 12;
        bottom: -20vh;
        background: var(--white);
        border-radius: 12px;
        display: grid !important;
        grid-template-columns: auto auto;
        box-shadow: 0px 3px 15px #16365b33;
    }
    .header-tops-two figure{
        grid-row: 1 / span 2;
        display: inline-table;
        border-radius: 12px 0px 0px 12px;
        height: inherit;
    }
    .header-tops-two figure img{
        width: 19vw;
        margin-right: 10px;
        height: 100% !important;
        border-radius: 12px 0px 0px 12px;
    }

    .header-tops-two h2 {
        display: none;
    }

    .header-tops .header-tops-one {
        flex-basis: 100%;
        flex-direction: unset !important;
        padding: 11px 15px;
        z-index: 44;
    }

    .header-tops-two img,
    .pagebuilder-column-group.header-tops.header-tops-mobile
        .pagebuilder-column-line:nth-child(3)
        img {
        max-width: 25%;
    }
    .suggested-prod .suggested-prod-title h2{
        font-size: 16px;
    }
    .suggested-prod .suggested-prod-title p a {
        display: none;
    }
    .page-products .blog-container .post-item-wraper {
        height: inherit;
        min-height: 100%;
    }

    .suggested-prod {
        margin-top: 10px;
    }

    .page-products .blog-container .post-item-wraper .post-image a img {
        height: auto !important;
    }

    .page-products .blog-container .row {
        flex-wrap: initial;
        width: 100%;
        overflow-y: hidden;
        overflow-x: auto;
    }

    .page-products .blog-container .row::-webkit-scrollbar:horizontal {
        height: 0;
        width: 0;
        display: none;
    }

    .page-products .blog-container .post-list-item {
        flex-basis: 45% !important;
        flex: 0 0 auto;
        width: 33.33333333%;
    }
    .page-products .blog-container .mp-post-title{
        font-size: 16px !important;
    }
    .waarom-container .pagebuilder-column.column-text-container h3 {
        margin-top: 15px;
    }

    .waarom-container .pagebuilder-column-line {
        place-content: center !important;
        flex-wrap: wrap;
    }

    .waarom-container .pagebuilder-column-group {
        display: flex;
        margin: 0 auto;
        max-width: 100%;
        width: 100%;
        padding-right: 1.5rem;
        padding-left: 1.5rem;
    }

    .waarom-container figure img {
        width: auto;
    }

    /* .waarom-container .column-img-container {
        display: none !important;
    } */

    .waarom-container .pagebuilder-column {
        flex-basis: 100% !important;
        max-width: 100% !important;
    }

    .equipment-cat .pagebuilder-column-line .cat-title p a {
        font-size: 16px;
        padding: 4px;
    }

    .equipment-cat
        .pagebuilder-column-group
        .pagebuilder-column-line
        .pagebuilder-column {
        height: 136px;
    }

    .equipment-cat .pagebuilder-column-line {
        flex-wrap: wrap;
    }

    .equipment-cat .pagebuilder-column {
        flex-basis: 48% !important;
    }

    .waarom-container {
        margin-top: 25px;
        background: unset;
        padding: 0px;
    }

    .page-products .blog-container .blog-title {
        margin-bottom: 0;
    }
    .page-products .blog-container .blog-title h2,
    .waarom-container .pagebuilder-column.column-text-container h3 {
        font-size: 17px;
    }

    .waarom-container .pagebuilder-column.column-text-container p {
        font-size: 13px;
    }
    /* .pagebuilder-column-group.header-tops {
        background-image: unset !important;
        background: var(--blue-alice);
    } */
    /* .pagebuilder-column-group.header-tops.header-tops-desktop{
        display: none !important;
    } */
}

@media (max-width:639px) {
    .page-products .blog-container .mp-post-title a {
        font-size: 15px;
    }
    .page-products .blog-container .post-author {
        font-size: 13px;
    }
    .page-products .blog-container .post-list-item {
        flex-basis: 59% !important;
    }

    .container-homepage
        .header-tops-mobile
        .pagebuilder-column.header-tops-one-mobile {
        flex-basis: 36%;
    }

    .header-tops-two {
        width: 60% !important;
    }
}

@media (max-width:502px){
    .header-tops .header-tops-two p {
        padding-top: 3%;
    }

    .header-tops-two {
        width: 60% !important;
    }
}

@media (max-width: 479px) {
    .header-tops-mobile .pagebuilder-column-line:nth-child(3) {
        width: 70% !important;
        height: 10%;
    }
    .equipment-cat .pagebuilder-column {
        flex-basis: 47% !important;
    }
    .header-tops-two {
        width: 61% !important;
    }
}

@media (max-width: 445px) {
    .header-tops-two {
        width: 65% !important;
    }
}

@media (max-width: 425px) {
    .header-tops-mobile .pagebuilder-column-line:nth-child(3) {
        width: 71% !important;
        height: 10%;
    }

    .container-homepage
        .header-tops-mobile
        .pagebuilder-column.header-tops-one-mobile {
        flex-basis: 40%;
    }

    .page-products .blog-container .post-list-item {
        flex-basis: 68% !important;
    }

    .equipment-cat .pagebuilder-column {
        flex-basis: 47.34% !important;
    }

    .header-tops-two {
        width: 69% !important
    }
}
@media (max-width: 414px) {
    .equipment-cat .pagebuilder-column {
        flex-basis: 46.34% !important;
    }
}
@media (max-width: 390px){
    .page-products .blog-container .post-info-wraper {
        padding: 7px 14px;
    }
    .page-products .blog-container .post-item-wraper {
        height: 100%;
    }
    .header-tops-two figure img{
        width: 22vw;
    }
    .header-tops-two {
        width: 75% !important
    }
}

@media (max-width: 375px) {
    .page-products .blog-container .post-info-wraper {
        padding: 12px 14px;
    }
    .header-tops-two {
        padding: 0px 12px 0px 0px !important;
        height: 58px;
    }
    .header-tops-mobile .pagebuilder-column-line:nth-child(3) {
        width: 81% !important;
    }

    .equipment-cat .pagebuilder-column {
        flex-basis: 46.34% !important;
    }
}

@media (max-width:371px) {
    .header-tops .header-tops-two p {
        font-size: 12px;
    }
}

@media (max-width:346px) {
    .header-tops .header-tops-two p {
        font-size: 11px;
    }
}

@media (max-width:322px) {
    .header-tops .header-tops-two p {
        padding-top: 0.5vh;
        font-size: 10px;
    }
}

@media (max-width:297px) {
    .header-tops-two {
        width: 100% !important;
    }
}

.sidebar.sidebar-main{
        display: none;
}

.sales-order-view .sidebar.sidebar-main,
.sales-order-history .sidebar.sidebar-main, 
.customer-account-index .sidebar.sidebar-main,
.customer-address-form .sidebar.sidebar-main, 
.customer-account-edit .sidebar.sidebar-main{
    display: block;
}

#category-view-container {
    @apply mx-auto flex md:pt-4 md:pb-4 flex-col items-center;
}

.category-description {
    @apply hidden w-full px-6 pt-4 md:pt-5 md:mt-2 md:pb-6;
}

.toolbar-products {
    .modes-mode {
        @apply w-6 h-6;

        span {
            @apply sr-only;
        }

        &.mode-grid {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg>');
        }

        &.mode-list {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" /></svg>');
        }
    }
}
