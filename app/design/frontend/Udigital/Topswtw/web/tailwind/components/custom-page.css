.page-main {
    margin-top: 5px;
}

.back-page-cat a {
    text-decoration: none;
    font-size: 14px;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice {
    margin-left: 0;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block > [data-element='main'] {
    padding-left: 0;
    padding-right: 0;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block > [data-appearance='contained'] {
    max-width: 100%;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .bottom-sidebar {
    position: relative;
    margin-top: 60px;
    padding: 0 26px 20px 26px;
    width: 98%;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .bottom-sidebar > [data-content-type='text'] {
    line-height: 14px;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .bottom-sidebar figure img {
    height: 200px !important;
    margin-top: 28px;
    margin-left: 42px;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .bottom-sidebar div p {
    margin-top: 8px;
}

.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content > [data-element='main'] p .title {
    color:#16365B;
    font: normal normal bold 24px/28px Public Sans;
    font-size: 24px;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content > [data-element='main'] p .desc {
    color:#16365B;
    font: normal normal normal 16px/24px Public Sans;
    font-size: 16px !important;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .toggle {
    padding-bottom: 14px;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .toggle .content-toggle p {
    color: #002C4B;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .toggle .content-toggle img {
    margin-left: 40px;
}

.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .right-sidebar-handige-links p {
    margin-bottom: 30px;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice li {
    padding: 8px 20px 8px 20px;
    margin-bottom: 20px;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice li span img {
    margin-top: 3px;
}

.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .content-link-klantenservice {
    font-size: 14px;
    color: #002C4B;
    font: normal normal bold 14px/17px Public Sans;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column > [data-element='main'] ul li {
    padding: 10px 20px;
    margin-bottom: 18px;
    margin-right: 0;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column > [data-element='main'] ul li a span img,
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column > [data-element='main'] ul li div a span img {
    width: 15px; 
    height: 15px;
    margin-top: 3px;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.left_nav .sidebar-menu-klantenservice {
    margin-right: 0;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.left_nav .sidebar-menu-klantenservice ul li {
    padding: 0;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.left_nav .sidebar-menu-klantenservice a {
    color:#16365B;
}

.sidebar.sidebar-main {
    width:70%;
}
.sidebar.sidebar-main {
    width:70%;
}
.order_records .col.actions a.action.view {
    display: inline-block;
    margin: 0 10px 15px 0;
    padding: 10px 20px;
}
.order_records .col.actions button.action.order {
    margin-left: -5px !important;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .toggle .a {
    margin: 10px 0 5px 0;
}

.-ml\[11px\]{
    margin-left: -6px;
}
.sidebar-main .nav.items .nav.item .delimiter.block.border-b.border-container {
    border-bottom-width: 0px;
    border-top: 0px;    
    margin-bottom: 20px;
}
.sidebar-main .account-nav .border-b {
    border-bottom-width: 0px;
    padding-bottom: 0px;
}
.grow .title-and-btn {
    margin-bottom: 15px;
}
.pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .bestellen-betalen p strong span {
    font-size: 24px;
}
#html-body [data-pb-style=T9FM5Q0] {
    margin-left: 20px;
}
.sidebar.sidebar-main .block.account-nav.card .title.border-container span.border-container {
    display: none;
}

/* Mobile view */
@media(max-width: 440px){
    /* .columns {
        padding-right: 1.2rem !important;
        padding-left: 1.2rem !important;
    } */
    .page-title {
        font-size: 17px !important;
        color: #16365B;
        line-height: 27px;
    }
    .flex-columns-wrapper {
        margin-top: -10px !important;
    }
    .footer-other.container {
        padding-right: 1.125rem !important;
        padding-left: 1.125rem !important;
    }

    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content,
    .pagebuilder-column-group .pagebuilder-column-line > [data-pb-style=Y024QVH] {
        padding: 0;
        margin-top: -60px;
        order: 1;
        width: 100%;
    }
    .pagebuilder-column-group .pagebuilder-column-line > [data-pb-style=A7LQ1OC] {
        font-size: 13px !important;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .bestellen-betalen {
        margin: 0 0 20px 0;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .bestellen-betalen p {
        line-height: 1.2;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .bestellen-betalen p span {
        margin-top: 20px;
        color:#16365B;
        font-size: 13px;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .bestellen-betalen p strong span {
        font-size: 17px;
        color:#16365B;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .toggle .content-toggle p {
        font-size: 15px;
        color:#002C4B;
    }

    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.left_nav {
        margin-top: 70px;
        order: 3;
        margin-right: 0 !important;
    }
    
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.left_nav .sidebar-menu-klantenservice ul li span strong {
        font-size: 15px;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.left_nav .sidebar-menu-klantenservice ul li span {
        font-size: 13px !important;
        text-align: left;
        font: normal normal normal 13px/15px Public Sans;
        letter-spacing: 0px;
        color: #16365B;
    }
    
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav {
        order: 2;
        margin-top: 40px;
        margin-bottom: 0 !important;
        margin-left: 0 !important;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice {
        margin-top: 0;
        margin-left: 0;
        margin-bottom: 0 !important;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .bottom-sidebar {
        position: relative;
        margin: 0;
        margin-top: 80px;
        width: 100%;
        display: none !important;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .right-sidebar-handige-links ul li {
        border: 0;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid #2EB6ED;
        border-color: #D5EAF0;
        border-radius: 0;
        color: #2EB6ED;
        padding: 10px 0;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .right-sidebar-handige-links ul li a span {
        font-size: 13px;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .right-sidebar-handige-links ul li img {
        display: none;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .right-sidebar-handige-links p {
        margin-bottom:10px;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block .right-sidebar-handige-links p strong span {
        font-size: 17px !important;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right_nav .right-sidebar-klantenservice .block-static-block div {
        margin:0;
        padding:0;
        width: 100%;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .toggle {
        border-bottom: solid 1px #D5E9F0;
    }

    .footer-other .justify-between {
        font-size: 15px;
    }
    .footer-other .justify-between .justify-start {
        width: 3rem;
    }
    .footer-other .justify-between .justify-start .box-footer {
        margin-top: 5px;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content > [data-element='main'] p .title {
        color:#16365B;
        font: normal normal bold Public Sans;
        font-size: 17px !important;
        font-weight: bold;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content > [data-element='main'] p .desc {
        color:#16365B;
        font-size: 13px !important;
        font-weight: normal;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column > [data-element='main'] ul {
        margin-bottom: -10px;
    }

    .order_records .col.actions {
        margin-top: 50px !important;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .content-link-klantenservice li {
        margin-right: 0;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column > [data-element='main'] ul li {
        margin-bottom: 12px;
    }
    .pagebuilder-column [data-pb-style='FCO1YW9'] > [data-element='main'] {
        margin-bottom: 30px !important;
    }
    button.btn.btn-primary.contact-form {
        font-size: 15px !important;
    }

    .block.account-nav.card.filter-option.py-4.px-8.mb-6 {
        margin-top: 10%;
        border: none;
        padding-top: 0px;
        padding-bottom: 0px;
        padding-left: 0px;
        padding-right: 0px;
        background-color: transparent;
        --tw-shadow: transparent;
    }
    div#account-nav {
        padding-top: 0.25rem;
    }
    .account-nav .account-nav-content li.nav.item a {
        padding: 3px 0px;
    }
    .account-nav .account-nav-content li.nav.item strong {
        display: block;
        padding: 3px 0px;
    }
    .title.account-nav-title .rounded.border-container {
        display: none;
    }
    .account-nav .account-nav-content li.nav.item strong:before {
        content: '>';
        margin-right: 5px;
        color: #2EB6ED;
        /* background-repeat: no-repeat;
        background-image: url(../images/arrow-right-blue.svg); */
        /* content: url(../images/arrow-right-blue.svg); */
    }
    .block-content .flex-wrap.justify-between .w-full.p-4.custom-card {
        padding-top: 0.3rem;
        padding-bottom: 0.3rem;
        padding: 0.5rem;
    }
    .block-content .flex-wrap.justify-between .w-full.p-4.custom-card {
        padding-top: 0.3rem;
        padding-bottom: 0.3rem;
        padding: 0.5rem;
    }
    .column.main > .flex-wrap.justify-between > .custom-card.contact-info {
        padding-top: 0.3rem;
        padding-bottom: 0.3rem;
        padding: 0.5rem;
    }
    .block-dashboard-addresses .custom-title-page {
        font-size: 17px;
    }
    .block-dashboard-addresses .title-and-btn .title-font {
        font-size: 15px;
    }
    .block-dashboard-addresses .title-and-btn .btn-edit {
        font-size: 11px;
    }
    .block-dashboard-addresses .card .grow address {
        font-size: 13px;
    }
    .custom-card .flex.card .grow p {
        font-size: 13px;
    }
    .block.account-nav .account-nav-title .title {
        font-size: 15px;
    }
    .account-nav.card .account-nav-content .nav.items {
        font-size: 13px;
    }
    .sidebar-main .nav.items .nav.item .delimiter.block.border-b.border-container {
        margin-bottom: 0px;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.body-content .content-link-klantenservice {
        margin-bottom: 60px;
        margin-top: -100px;
    }
    #html-body [data-pb-style=T9FM5Q0] {
        margin-left: 0;
    }
    #html-body [data-pb-style=FYOQYM9] {
        margin-top: 50px;
    }
    #html-body [data-pb-style=IWIKJEP] {
        margin-top: 25px;
    }
    .sidebar.sidebar-main .block.account-nav.card {
        border: 0;
        background: none;
        border-radius: 0;
        --tw-shadow: 0;
        padding: 2rem 0 !important;
    }
    .sidebar.sidebar-main .block.account-nav.card .border-container {
        border: none;
    }
    .sidebar.sidebar-main .block.account-nav.card .title.border-container {
        padding-bottom: 0;
    }
    .sidebar.sidebar-main .block.account-nav.card .title.border-container span.border-container {
        display: none;
    }
    .block.account-nav.card .item .delimiter {
        border: none;
    }
    .pagebuilder-column-group .pagebuilder-column-line .pagebuilder-column.right-sidebar {
        margin-left: 0 !important;
    }
    .main-bestellen-betalen {margin-top: 110px;margin-bottom: 0 !important;}
}
@media(max-width: 768px){
    .back-page-cat {
        display: none;
    }

    .sidebar.sidebar-main {
        width:100%;
    }

    .page-main {
        margin-bottom: 0 !important;
    }
}

/* faq responsive */

@media only screen and (min-width: 441px) and (max-width: 767px) {
   
    .body-content{
        padding: 0px !important;
    }
    .right_nav{
        margin-left: 0px !important;
    }
}

@media only screen and (min-width: 768px) and (max-width: 768px) {

    /* .left_nav{
        width: max-content;
    } */
    .body-content{
        padding: 0px;
    }
    .right_nav{
        width: 65% !important;
    }
    .top-klantenservice{
        margin-top: 100px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {

    /* .body-content{
        padding: 0px !important;
        width: 100% !important;
    } */

    .right_nav{
        width: 100%;
        margin-left: 0px;
    }

    .right-sidebar-handige-links{
        width: max-content;
    }

    
    .right-sidebar-handige-links ul li{
        width: 100%;
    }

    .display-grid-mobile{
        .pagebuilder-column-line{
            display: grid !important;
        }
    }
}

@media only screen and (min-width: 0px) and (max-width: 440px) {
    .top-klantenservice{
        margin-top: 150px;
    }
}

.post-view-image img.img-responsive {
    object-position: top;
}