.customer-address-form .div-name {
    @apply flex gap-0;
}


.customer-address-form {
    .title-account, .title-cart-checkout {
        @apply hidden;
    }
}



.customer-address-form .field:not(.choice).required > .label:after {
    content: '*';
    @apply text-red-600 text-sm ml-1.5;
}

.form-address-edit .action.save.primary {
    background-color: rgb(46 182 237/var(--tw-bg-opacity)) !important;
    &:hover {
        background-color: rgb(37 99 235/var(--tw-bg-opacity)) !important;
    }
}

@media screen and (max-width: 768px) {


    .customer-address-form {
        .columns {
            @apply pr-6 pl-6;
        }
    }

    .customer-address-form .sidebar.sidebar-main {
        @apply hidden;
    }
}

.customer-account-login {
    .login-container {
        @apply lg:my-25;
    }
}