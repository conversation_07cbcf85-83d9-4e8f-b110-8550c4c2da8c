.hide-input{
    display: none;
}
.s-font-button span{
    font-family: "Public Sans";
    font-size: 18px;
    color: #002C4B;
}
.s-font-button{
    border-radius: 10px;
}


@media screen and (max-width: 768px) {
    .customer-account-index{
        .messages{
            display: none;
        }
    }
    
    .input-icon {
        position: relative;
        display: inline-block !important;
        width: 100%;
    }
    .input-icon .i-password-content {
        position: absolute;
        left: 80%;
        top: 35%;
        transform: translateY(-50%);
        float: left;
    }

    .content-form-p{
        display: grid !important;
    }
    .s-font-form{
        text-align: left;
        font: normal normal bold 17px Public Sans;
        letter-spacing: 0px;
        color: #002C4B;
        opacity: 1;
    }
    .cart-content-adress{
        width: 100%;
        height: auto;
        border: 1px solid #D5EAF0;
        padding:20px;
        border-radius: 12px;
    }
    .cart-content-adress{
        .form-input{
            width: 100% !important;
        }
        .content-address{
            width: 100%;
        }
        .label{
            color: #002C4B;
            font-size: 13px;
        }
        .primary{
            background-color: #2EB6ED;
        }
    }
    .customer-account-edit{
        .title-cart-checkout{
            display: none !important;
        }
        .title-edit-address{
            text-align: left;
            font: normal normal bold 17px Public Sans;
            letter-spacing: 0px;
            color: #002C4B;
            opacity: 1;
            margin-bottom: 20px;
            margin-top: 30px;
        }
        .label{
            color: #002C4B;
            font-size: 13px;
        }
        .password-strength-meter{
            color: #002C4B;
            font-size: 13px;
        }
    }
}
@media screen and (min-width: 820px) {
    .input-icon {
        position: relative;
        display: inline-block !important;
        width: 100%;
    }
    .input-icon .i-password-content {
        position: absolute;
        left: 40%;
        top: 35%;
        transform: translateY(-50%);
       
        float: left;
    }

    .content-form-p{
        display: grid !important;
    }

    .s-font-form{
        text-align: left;
        font: normal normal bold 20px Public Sans;
        letter-spacing: 0px;
        color: #002C4B;
        opacity: 1;
    }
    .cart-content-adress{
        width: max-content;
        height: auto;
        border: 1px solid #D5EAF0;
        padding:30px;
        border-radius: 12px;
    }
    .cart-content-adress{
        .label{
            color: #002C4B;
        }
        .primary{
            background-color: #2EB6ED;
        }
    }
    .customer-account-edit{
        .title-cart-checkout{
            display: none !important;
        }
        .title-edit-address{
            text-align: left;
            font: normal normal bold 24px Public Sans;
            letter-spacing: 0px;
            color: #002C4B;
            opacity: 1;
            margin-bottom: 20px;
        }
    }
    
}