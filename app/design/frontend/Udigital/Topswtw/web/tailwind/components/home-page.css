.cart-product {
  @apply border h-[560px] w-[383px] mr-2.5 pb-[15px] rounded-xl border-solid border-[#D5EAF0];
}
.content-image-product {
  @apply w-[250px] h-[300px] mx-auto my-0;
}
.content-text-product {
  @apply text-[#a2afbd] mr-2.5;
}
.title-color-product {
  @apply text-[#a2afbd];
}
.color-star {
  @apply text-[#cbd5e0];
}
.content-title-persen {
  @apply w-auto max-w-[250px];
}
.title-review {
  @apply text-[#2eb6ed] ml-2.5;
}
.content-title-price-total {
  @apply w-[130px] text-[#2eb6ed] pl-[-100px] mr-0 mt-0;
}
.content-price-product {
  @apply text-[#16365b] text-[13px];
}
.qty-product {
  @apply w-full h-[54px] ml-[30px] rounded-lg;
  outline: none;
}
.button-cart {
  @apply bg-[#00b900] h-[53px] w-[53px] ml-2.5 ml-[50px] rounded-lg border-0 border-solid;
}
.cms-index-index {
    .icon-plus {
        @apply absolute cursor-pointer text-[black] translate-x-0.5 mr-[-30px] mt-5 right-2.5;
    }
    .icon-min {
        @apply absolute cursor-pointer text-[black] translate-x-0.5 mr-[-30px] mt-10 right-2.5;
    }
}
.filter-option-init:disabled {
  @apply pointer-events-none text-[grey];
}
.filter-footer-text {
  @apply bg-green-islamic cursor-pointer;
}
.text-filter-modal {
  @apply text-sm font-medium text-left flex justify-between items-center px-4 xl:pl-8 xl:pr-5.75 text-[#002c4b] bg-saffron py-2.5 w-full 2xl:w-65 rounded-md;
}
.arrow-filter {
  @apply ml-8;
}
@media (min-width: 769px) {
  .modal-filter-home {
    @apply w-3/12 h-auto;
  }
}
@media (max-width: 768px) {
  .modal-filter-home {
    @apply w-[70%] h-min mt-[33%];
  }
}
