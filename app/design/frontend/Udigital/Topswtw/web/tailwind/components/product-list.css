.product-item {
    @apply card-interactive w-full;

    .price-container {
        @apply block;

        .price {
            @apply font-semibold text-4.25 leading-6 md:text-2xl text-blue-picton;
        }

        .price-label {
            @apply text-xs block text-blue-picton;
        }
    }

    .special-price .price-container .price-label {
        @apply sr-only;
    }

    .old-price .price-container {
        @apply text-gray-500;

        .price {
            @apply font-normal text-base;
        }
    }
}



.item-quantity {
    @apply flex relative justify-end xl:mr-3.75 max-xl:mr-2.5;

    input {
        @apply xl:w-23.25 xl:h-13.5 max-xl:w-18.25 max-xl:h-12.25 md:text-5.5;
    }

    input[type="number"] {
        @apply no-spin;
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        @apply no-spin m-0;
    }

    input {
        @apply text-center leading-relaxed float-left block text-blue-prussian border border-blue-prussian opacity-100 m-0 pr-5 p-0 rounded-lg border-solid;
    }

    input:focus {
        @apply outline-0;
    }

    .item-quantity-nav {
        @apply xl:h-13.5 max-xl:h-12.25 float-left relative;
    }

    .item-quantity-button {
        @apply relative cursor-pointer w-6.25 text-center text-blue-prussian text-3.25 -translate-x-full select-disable font-trebuchet-ms;
    }

    .item-quantity-button.item-quantity-up {
        @apply absolute h-3/6 flex flex-col justify-start top-0;
    }

    .item-quantity-button.item-quantity-down {
        @apply absolute h-3/6 flex flex-col justify-end -bottom-px;
    }
}

.input-qty {
    @apply h-9.5 border border-blue-prussian rounded opacity-100 border-solid;
}


.minicart-item-quantity {
    @apply flex relative justify-end mr-3.75 h-9.5;

    input[type="number"] {
        @apply no-spin;
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        @apply no-spin m-0;
    }

    input {
        @apply w-15 text-center leading-relaxed float-left block border border-blue-prussian opacity-100 m-0 pr-5 p-0 rounded-md border-solid;
    }

    input:focus {
        @apply outline-0;
    }

    .item-quantity-nav {
        @apply float-left relative;
    }

    .item-quantity-button {
        @apply relative cursor-pointer w-6.25 text-center text-blue-prussian text-3.25 -translate-x-full select-disable font-trebuchet-ms;
    }

    .item-quantity-button.item-quantity-up {
        @apply absolute h-3/6 flex flex-col justify-start top-0;
    }

    .item-quantity-button.item-quantity-down {
        @apply absolute h-3/6 flex flex-col justify-end -bottom-px;
    }
}

.card-product, 
.card-product-list {
    @apply border border-white-azureish min-w-75 opacity-100 border-solid bg-white bg-clip-padding bg-no-repeat;
}

.content-button-persen-item-product {
    @apply justify-between items-center w-full h-auto text-base text-blue-cadet flex border border-white-azureish rounded-lg border-solid;

    .content-button {
        @apply w-full text-cadet-space text-center cursor-pointer flex items-center justify-center;
    }

    .active-persen,
    .content-button:hover {
        @apply w-full text-white text-center cursor-pointer bg-blue-picton bg-no-repeat bg-clip-padding;
    }
}

.item-product-add-to-cart {
    @apply justify-center py-3 w-auto pl-3 pr-3 xl:h-13.5 max-xl:h-12.25 max-md:w-10.5 max-md:h-10.5 max-md:mr-auto;
}

.content-image-shopping-cart-sugest img{
    @apply h-60 md:h-60;
}

.is-product-discount {
    @apply xl:mt-1.5 max-xl:mt-0.36;
}

.card-product, 
.card-product-list {
    @apply md:justify-between md:pt-0 md:pb-3.75 md:px-4 md:rounded-xl;
}

.item-product-content{
    @apply md:grid md:grid-rows-product-content;
}

.item-product-content-desc{
    @apply md:grid md:grid-rows-product-content-3;
}

.content-image-shopping-cart-sugest,
.content-image-shopping-cart{
    @apply md:flex md:justify-center;
}

.label-product-image {
    @apply max-md:hidden md:opacity-100 md:justify-center md:items-center md:flex-row md:flex-wrap md:content-center left-auto absolute md:flex md:w-30.5 border-white-azureish md:px-0 md:py-0.5 md:rounded-e-md md:rounded-s-none md:border-y-2 md:border-r-2 md:border-l-0 md:bg-blue-prussian md:bg-clip-padding md:bg-no-repeat -ml-4 mt-7;

    span {
        @apply md:text-left md:tracking-0 md:text-white md:opacity-100 md:text-base;
    }

    span.title {
        @apply md:font-bold md:text-left md:tracking-0 md:text-blue-jeans md:opacity-100 md:text-base;
    }
}

.item-product-reviews-mobile{
    @apply md:hidden max-md:flex max-md:items-center max-md:justify-center max-md:pt-2 max-md:flex-col;
}

.item-product-list {
    @apply md:flex-col md:flex md:justify-between max-md:mb-4 max-md:h-max;
}

.item-product-name {
    @apply md:mt-2;
}

.product-item-photo {
    @apply md:block;
}

.item-product-bottom {
    @apply hidden md:flex w-full flex-col justify-between h-43;
}

.item-product-bottom-mobile{
    @apply h-36.5;
}

.item-product-status-stock {
    @apply mr-auto;
}

.label-product-image-list {
    @apply left-auto opacity-100 justify-center items-center flex-row flex-wrap content-center absolute flex w-23.75 md:w-30.5 border-white-azureish px-0  py-[1.25px] md:py-0.5 -ml-4 rounded-s-none rounded-e-md border-y-2 border-r-2 border-l-0 bg-blue-prussian bg-clip-padding bg-no-repeat mt-7;

    span {
        @apply text-left tracking-0 text-white opacity-100 max-md:text-3.25 md:text-base;

        &.title {
            @apply font-bold text-left tracking-0 text-blue-jeans opacity-100;
        }
    }
}

.product-description-container {
    @apply line-clamp-2;
}

.product-item-link {
    @apply line-clamp-2;
}

.product-item-code {
    @apply line-clamp-2;
}

.card-product-list {
    @apply max-md:p-4 max-md:rounded-2.5;
}

.card-product {
    @apply max-md:rounded-2.5 max-md:pt-0 max-md:pb-3 max-md:px-3;
}

.item-product-wrapper {
    @apply max-md:grid max-md:grid-cols-3 max-md:h-max max-md:gap-x-2.5;
}

.item-product-content-sugest{
    @apply grid grid-rows-product-content;
}

.item-product-content-sugest-desc{
    @apply grid max-md:grid-rows-product-content-2 md:grid-rows-product-content-3;
}

.item-product-wrapper-sugest {
    @apply max-md:flex max-md:flex-col max-md:justify-between max-md:h-full;
}

.item-quantity {
    input {
        @apply max-md:text-4.25 max-md:w-16 max-md:h-10.5;
    }

    .item-quantity-nav {
        @apply max-md:h-10.5;
    }
}

.label-product-image-mobile {
    @apply md:hidden max-md:opacity-100 max-md:justify-center max-md:items-center max-md:flex-row max-md:flex-wrap max-md:content-center max-md:left-auto max-md:absolute max-md:flex max-md:w-23.75 max-md:border-white-azureish max-md:text-3.25 max-md:px-0 max-md:py-[1.25px] max-md:rounded-r-md max-md:rounded-l-none max-md:border-y-2 max-md:border-r-2 max-md:border-l-0 max-md:bg-blue-prussian max-md:bg-no-repeat max-md:bg-clip-padding max-md:-ml-4;

    span {
        @apply max-md:text-left max-md:tracking-0 max-md:text-white max-md:opacity-100 max-md:text-3.25;
    }

    span.title {
        @apply max-md:font-bold max-md:text-left max-md:tracking-0 max-md:text-blue-jeans max-md:opacity-100 max-md:text-3.25;
    }
}

.product-info {
    @apply max-md:col-span-2;
}

.item-product-reviews {
    @apply max-md:hidden md:flex md:items-center md:justify-start;
}

.card-product-list {
    @apply lg:max-2xl:min-w-62.5;
}

.item-product-status {
    @apply mt-2 text-3.25 md:text-3.75;
}

.is-not-product-discount {
    @apply mt-4;
}