.product-info-main {
    @apply screen-94:mt-7.5 screen-110:mt-6.25 md:mt-2.5 lg:mt-0;
}

.catalog-product-view #html-body {
    @apply bg-white;
}

.catalog-product-view .title-h1 {
    @apply text-4xl;
    color: #16365B;
}

.catalog-product-view .breadcrumbs {
    @apply flex flex-wrap items-center;
}

.catalog-product-view .arrow-down {
    @apply flex items-center;
}

.catalog-product-view .arrow-down svg {
    @apply w-4 h-4 text-[#2EB6ED];
}

.catalog-product-view .mx-filterklasse {
    @apply !mx-auto;
}

.catalog-product-view .merk-title {
    @apply mr-2.5;
}

@media (max-width: 768px) {
    .catalog-product-view .modal-view-product {
        @apply w-4/5 h-5/6;
    }

    .catalog-product-view .mx-filterklasse {
        @apply justify-start w-full;
    }

    .catalog-product-view .mx-filterklasse .modal-view-product {
        @apply w-11/12;
    }

    .catalog-product-view .mx-filterklasse h3 {
        @apply text-lg;
    }

    .catalog-product-view .mx-filterklasse .modal-view-product {
        @apply pt-5 px-2.5 pb-0;
    }
}

.catalog-product-view .rounded-r-0 {
    @apply rounded-r-none;
}

.catalog-product-view .content-modal-attr [data-content-type=row][data-appearance=contained] {
    @apply !px-0;
}

.catalog-product-view .content-modal-attr h3 {
    @apply text-2xl mb-6 font-bold;
    font-family: 'Public Sans, Bold';
}

.catalog-product-view .content-modal-attr h3 {
    @apply font-bold mt-6;
}
.catalog-product-view .page-title-wrapper.product {
    @apply mx-auto w-[86%] pt-[49px] pr-5 pb-0 pl-5;;
}

.catalog-product-view .page-title-wrapper.product h1 span {
    @apply text-4xl;
    font-family: 'Public Sans, Bold';
}
.catalog-product-view #product-attributes table tr:nth-child(odd) th,
.catalog-product-view #product-attributes table tr:nth-child(odd) td {
    @apply bg-blue-mist
}

.catalog-product-view #product-attributes table tr:nth-child(even) th,
.catalog-product-view #product-attributes table tr:nth-child(even) td {
    @apply bg-white;
}

.catalog-product-view #description a {
    @apply text-[#2EB6ED];
}

.catalog-product-view #description h3 {
    @apply mt-6;
}

.catalog-product-view #description p {
    @apply text-base max-w-[630px] text-[#002C4B];
}

.catalog-product-view #description [data-content-type=row][data-appearance=contained] {
    @apply ml-0 !pl-0;
}

.catalog-product-view ul.magepsycho-product-attachments li a {
    @apply !text-[#2EB6ED] flex items-center text-sm;
}

.catalog-product-view .make-modal {
    @apply flex;
}

.catalog-product-view .veelgestelde-vragen ul li:nth-of-type(2n+1) {
    @apply border-b-2 border-[lightgray] flex flex-wrap items-center justify-between py-[10px];
}

.catalog-product-view .veelgestelde-vragen ul li:nth-of-type(2n+2) {
    @apply py-2.5 hidden;
}

.catalog-product-view .tooltip:hover .tooltiptext {
    @apply visible;
}

.catalog-product-view .make-modal div[role="dialog"] {
    @apply absolute right-[-50%] left-[50%] top-[15%] h-[78%];
    max-height: -webkit-fill-available;
}
.catalog-product-view .make-modal div.modal-view-product-bypass[role="dialog"] {
    @apply right-[-20%];
}

.catalog-product-view .mx-filterklasse div.modal-view-product[role="dialog"] {
    @apply left-0 right-0;
}

.catalog-product-view .make-modal .content-modal-attr p {
    @apply text-base !text-left;
    /* font-size: 16px; */
    font-family: 'Open Sans';

}

.catalog-product-view .body-content-scroll.content-modal-attr::-webkit-scrollbar {
    @apply w-0 hidden;
}

.catalog-product-view .total-reviews-share {
    /* @apply text-[#2EB6ED] text-sm mr-[10px]; */
    font-family: 'Public Sans';
}

.catalog-product-view #the-labelmx-filterklasse .content-modal-attr h3 {
    @apply text-xl;
}

.catalog-product-view .additional-attributes tr th {
    /* @apply text-[#3D3D3D] text-[16px]; */
    font-family: 'Public Sans, Bold';
}
.catalog-product-view #product-attributes table tr td {
    @apply text-[#3D3D3D];
}
.catalog-product-view .percent-status li.percent-status-li:nth-child(2) button {
    @apply !rounded-l-md !rounded-r-none;
}

.catalog-product-view .percent-status li.percent-status-li:nth-child(3) button {
    @apply !rounded-none;
}

.catalog-product-view .percent-status li.percent-status-li:nth-child(4) button {
    @apply !rounded-r-md !rounded-l-none;
}

.catalog-product-view .percent-status li.percent-status-li:nth-child(2) button:focus,
.catalog-product-view .percent-status li.percent-status-li:nth-child(3) button:focus,
.catalog-product-view .percent-status li.percent-status-li:nth-child(4) button:focus{
    --tw-ring-opacity: 0;
}

.catalog-product-view .make-modal .modal-view-product-bypas {
    right: 0;
}
@media (min-width: 1920px) {
    .catalog-product-view .overlay-filterklasse-d picture img {
        @apply !h-[620px];
    }

    .catalog-product-view .mx-filterklasse div.modal-view-product[role="dialog"] {
        @apply left-auto right-auto;
    }

    .catalog-product-view .content-modal-attr [data-content-type=row][data-appearance=contained] {
        @apply max-w-full !important;
    }
}

/* ============================== */
@media (min-width: 1295px) and (max-width: 1424px) {
    .catalog-product-view .review-container {
        @apply grid-cols-[24%_39%_29%];
    }
}

@media (min-width: 1125px) and (max-width: 1294px) {
    .catalog-product-view .review-container {
        @apply grid-cols-[27%_39%_29%];
    }
}

@media (min-width: 1162px) and (max-width: 1190px) {
    .catalog-product-view .review-list {
        @apply !p-5;
    }
    .catalog-product-view .review-container {
        @apply grid-cols-[28%_34%_32%];
    }
}

@media (min-width: 1195px) and (max-width: 1318px) {
    .catalog-product-view li.percent-status-li button {
        @apply p-4;
    }
}

@media (min-width: 1025px) and (max-width: 1194px) {
    .catalog-product-view li.percent-status-li button {
        @apply p-1 mr-[2px] ml-[1px];
    }
}

@media (min-width: 1025px) and (max-width: 1161px) {
    .catalog-product-view .review-list {
        @apply p-3 !important;
    }
    .catalog-product-view .review-container {
        @apply grid-cols-[28%_34%_32%];
    }
    .catalog-product-view .total-reviews {
        @apply w-full !important;
    }
}
@media (max-width: 1024px) and (min-width: 769px) {
    .catalog-product-view #maincontent {
        @apply mt-16;
    }
}

@media (min-width: 1381px) {
    .catalog-product-view .cart-left {
        @apply w-1/4 mr-1 pl-0 !important;
    }
    .catalog-product-view .content-p-slide {
        @apply overflow-x-scroll;
    }
}
@media (min-width: 1280px) and (max-width: 1380px) {
    .catalog-product-view .cart-left {
        @apply w-[29%] mr-1 pl-0 !important;
    }
    .catalog-product-view .content-p-slide {
        @apply overflow-x-scroll;
    }
}

@media (max-width: 1280px) {
    .catalog-product-view .is-product-discount {
        @apply mt-0 !important;
    }
}
@media (min-width: 1025px) and (max-width: 1280px) {
    .catalog-product-view .cart-left {
        @apply !w-[31%] !mr-[5px] !pl-0;
    }
    .catalog-product-view .content-p-slide {
        @apply overflow-x-scroll;
    }
}

@media(min-width: 1025px) {
    .catalog-product-view [data-ui-id='page-title-wrapper'] {
        @apply text-4xl;
    }

    .catalog-product-view .reviewer-name-mobile {
        @apply hidden;
    }

    .catalog-product-view .overlay-filterklasse-m {
        @apply hidden;
    }

    .catalog-product-view .product-option-values .swatch-attribute-options {
        @apply ml-[3px];
    }

    .catalog-product-view .mx-filterklasse .modal-view-product .content-modal-attr {
        @apply mt-[-50px] h-[calc(100%-36vh)];
    }

    .catalog-product-view .mx-filterklasse .content-modal-attr p span strong {
        @apply text-2xl;
    }

    .catalog-product-view .labeled-hide-mobile {
        @apply mr-[5px];
    }

    .catalog-product-view .modal-header-filterklasse p {
        @apply mb-7;
    }

    .catalog-product-view #product-addtocart-button {
        @apply h-[54px] w-[304px];
    }

    .catalog-product-view .op-voorraad-desktop span {
        @apply text-base;
    }

    .catalog-product-view .per-set span {
        @apply text-sm text-[#002C4B];
    }

    .catalog-product-view .container-merk-title {
        @apply text-lg;
        font-family: 'Public Sans';
    }

    .catalog-product-view #product-attributes-u-ontvangt {
        @apply mb-10;
    }

    .catalog-product-view .label-product-image-gallery {
        @apply flex top-[150px] z-10 rounded border-2 border-[#2EB6ED] left-0 absolute;
    }

    .catalog-product-view #review-form .total-reviews {
        @apply min-h-[290px] bg-[#d5eaf075];
    }

    .catalog-product-view .review-list {
        @apply min-h-[320px] w-[49%] border border-[#D5EAF0];
    }

    .catalog-product-view .additional-attributes tr th,
    .catalog-product-view .additional-attributes tr td {
        @apply text-base text-[#002C4B];
    }

    .catalog-product-view .toon-allen-spec a {
        @apply text-base;
    }

    .catalog-product-view #product.info.faq.tops {
        @apply mt-[-50px];
    }

    .catalog-product-view #all-bekijk-alle-reviews {
        @apply text-base;
    }

    .catalog-product-view #description [data-content-type='html'] {
        @apply text-[#002C4B];
    }

    .catalog-product-view .form-reviews {
        @apply text-[#002C4B];
    }

    .catalog-product-view .op-voorraad-mobile {
        @apply hidden;
    }

    .catalog-product-view .tier-price-container {
        @apply hidden;
    }
    .catalog-product-view .swatch-attribute.border-container::-webkit-scrollbar {
        @apply hidden;
    }
    .catalog-product-view .swatch-attribute.border-container {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }
    .catalog-product-view .tooltiptext-div {
        @apply pl-[10px];
    }
}

.catalog-product-view .price-quantity-cart-content {
    @apply max-lg:shadow-[0px_2px_27px_1px_rgba(136,136,136,0.38)];
}

@media (max-width:1024px) {
    .catalog-product-view #maincontent {
        @apply mt-0 mb-0;
    }

    .catalog-product-view .con-prod-detail {
        @apply p-0 !important;
    }

    .catalog-product-view .sku-title {
        @apply text-[11px] text-[#B8C2CD];
    }

    .catalog-product-view .cat-first-title {
        @apply text-sm;
    }
    /* .catalog-product-view .total-reviews-share {
        @apply text-xs;
    } */
    .catalog-product-view .link-share-product .text-share {
        @apply text-[13px];
    }
    .catalog-product-view .label-product-image-gallery {
        @apply top-[75px] z-10 rounded border-2 border-[#2EB6ED] w-[85px] h-[27px] absolute left-0;
    }
    .catalog-product-view .label-product-image-list span {
        @apply text-[13px] !important;
    }

    .catalog-product-view .cart-mobile-product {
        @apply text-white text-sm;
    }

    .catalog-product-view .border-current {
        @apply w-[20px] h-[20px];
    }

    .catalog-product-view .basepricecustom {
        @apply text-[11px] text-[#16365B] !important;
    }

    .catalog-product-view .basetiertext {
        @apply text-xs;
    }

    .catalog-product-view .content-modal-attr h3 {
        @apply text-[17px] mb-[5px] text-left;
    }

    .catalog-product-view #the-labelmx-filterklasse .content-modal-attr h3 strong {
        @apply text-lg;
    }

    .catalog-product-view .make-modal .content-modal-attr p {
        @apply text-[11px] mr-[5px];
    }

    .catalog-product-view .make-modal .content-modal-attr p span strong {
        @apply text-[17px] mr-[5px];
    }

    .catalog-product-view .item-quantity input {
        @apply w-[62px] h-[45px];
    }

    .catalog-product-view .swatch-attribute-options {
        @apply ml-0 w-full relative;
    }

    .catalog-product-view .overlay-filterklasse-d {
        @apply hidden;
    }

    .catalog-product-view  #product-attributes-u-ontvangt th,
    .catalog-product-view #product-attributes-u-ontvangt td {
        @apply p-0 text-[#16365B] !important;
    }

    .catalog-product-view .tooltip {
        @apply ml-1 border-none border-[#002C4B];
    }

    .catalog-product-view li.percent-status-li button {
        @apply m-0 text-[#002C4B] p-2.5 h-[26px];
    }

    .catalog-product-view .price-quantity-cart-content {
        background: #fff;
        flex-direction: initial !important;
    }

    .catalog-product-view .price-quantity-cart-content .container-price {
        @apply basis-[36%] w-1/2;
    }

    .catalog-product-view .price-quantity-cart-content .container-qty-cart {
        @apply basis-[61%] w-1/2 my-auto;
    }

    .catalog-product-view .price-quantity-cart-content .container-qty-cart .form-input-qty {
        @apply w-12;
    }

    .catalog-product-view .price-quantity-cart-content .container-qty-cart .btn-add-to-cart {
        @apply !bg-[#01B902] text-white pl-[15px] pr-2 h-[47px] w-full;
    }

    .catalog-product-view .price-quantity-cart-content .container-qty-cart .btn-add-to-cart span {
        @apply block text-[#002C4B] w-[65%];
    }

    .catalog-product-view .price-quantity-cart-content .container-price .price-wrapper > .price {
        @apply text-[21px] !important;
    }

    .catalog-product-view .price-quantity-cart-content .container-price .op-voorraad-desktop {
        @apply hidden;
    }

    .catalog-product-view .percent-status {
        @apply border border-[#D5EAF0];
    }

    .catalog-product-view .container-percents {
        @apply mt-0 !important;
    }

    .catalog-product-view .container-basetiertext {
        @apply mt-2;
    }

    .catalog-product-view .percent-status li.percent-status-li button.btn:focus {
        @apply border-none;
        --tw-ring-shadow: none !important;
    }

    .catalog-product-view .percent-status li.percent-status-li:nth-child(2) button {
        @apply !rounded-l-md !rounded-r-none;
    }

    .catalog-product-view .percent-status li.percent-status-li:nth-child(3) button {
        @apply !rounded-none;
    }

    .catalog-product-view .percent-status li.percent-status-li:nth-child(4) button {
        @apply !rounded-r-md !rounded-l-none;
    }

    .catalog-product-view .tier-price-container {
        @apply hidden;
    }

    .catalog-product-view .product-option-values {
        @apply w-full;
    }
    .catalog-product-view #review-form {
        @apply p-0;
    }

    .catalog-product-view .button-show-review {
        @apply w-full justify-center;
    }

    .catalog-product-view #product-attributes table tr:nth-child(odd) th,
    .catalog-product-view #product-attributes table tr:nth-child(odd) td {
        @apply bg-[#DFF3FC] text-[#16365B] !important;
    }
    .catalog-product-view #product-attributes table tr:nth-child(even) th,
    .catalog-product-view #product-attributes table tr:nth-child(even) td {
        @apply text-[#16365B] !important;
    }
    .catalog-product-view .veelgestelde-vragen ul li:nth-of-type(2n+1) {
        border-bottom: 1px solid lightgray;
        @apply  grid grid-cols-[1fr_60px] place-items-center pb-[5px] mb-[15px] font-semibold;

    }

    .catalog-product-view .veelgestelde-vragen ul li:nth-of-type(2n+2) {
        @apply mb-[25px];
    }

    .catalog-product-view .arrow-down-faq {
        @apply text-[#5EC2EF];
    }

    .title-bekijk-alle-reviews,
    .catalog-product-view .bekijk-alle-reviews {
        @apply basis-1/2 place-items-center;
    }

    .catalog-product-view .bekijk-alle-reviews {
        @apply hidden;
    }

    .catalog-product-view #customer-review-list h3.title-bekijk-alle-reviews {
        @apply ml-[-1.5rem];
    }

    .catalog-product-view #customer-review-list .customer-review-list-container {
        @apply justify-start flex-nowrap w-full overflow-y-hidden overflow-x-scroll flex;
    }

    .catalog-product-view .make-modal div[role="dialog"] {
        @apply right-[-43%] left-[-50%] top-[9%] h-[91%] rounded-r-md rounded-l-md p-[15px];
    }
    .catalog-product-view .make-modal div.modal-view-product-bypass[role="dialog"] {
        @apply left-[13%];
    }

    .catalog-product-view .close-button {
        @apply mt-[9px] mr-[14px] absolute;
    }

    .catalog-product-view .body-content-modal {
        @apply pr-0 pt-0;
    }

    .catalog-product-view .labeled-hide-mobile {
        @apply hidden;
    }

    .catalog-product-view .swatch-attribute.border-container {
        @apply pb-0 mt-[-25px];
    }

    .catalog-product-view .swatch-attribute.border-container {
        @apply min-h-[180px] overflow-y-hidden;
    }

    .catalog-product-view #description p,
    .catalog-product-view #description [data-content-type='html'],
    .catalog-product-view #description [data-element='main'] {
        @apply !text-[#002C4B];
    }

    .catalog-product-view #description .row-full-width-inner {
        @apply !max-w-full !px-0;
    }
    /* .catalog-product-view #description h3 {
        @apply mt-[-25px];
    } */

    .catalog-product-view #product.info.faq.tops h3,
    .catalog-product-view #description h3 {
        @apply !text-[17px] !text-[#16365B];
    }

    .catalog-product-view #review-form .total-reviews {
        @apply min-h-[223px] bg-[#5fc2f042] opacity-100;
    }

    .catalog-product-view .review-list {
        @apply min-h-[321px] !border-black !border !mt-1.5 w-[75.333333%] mr-[18px];
        flex: 0 0 auto;
    }

    .catalog-product-view #customer-review-list .right-latest-review {
        @apply hidden;
    }

    .catalog-product-view .review-list [itemprop='description'] {
        @apply min-h-[180px];
    }

    .catalog-product-view #review-form svg {
        @apply h-5;
    }

    .catalog-product-view #review-form .left-latest-review {
        @apply text-[13px];
    }

    .catalog-product-view .make-modal svg {
        @apply h-3 w-3;
    }

    .catalog-product-view #description p {
        @apply text-[#16365B] !important;
    }

    .catalog-product-view ul.magepsycho-product-attachments li a {
        @apply underline;
    }
    .catalog-product-view .ct-faq-new {
        @apply !pt-[35px];
    }

    .catalog-product-view .product-slider .title-top-crossel {
        @apply !mb-0;
    }

    .catalog-product-view .product-slider .title-top-crossel .slider-indicator {
        @apply hidden;
    }

    .catalog-product-view .tooltiptext-div {
        @apply min-w-[81px];
    }
    .catalog-product-view .tooltip:hover .tooltiptext {
        @apply pl-0;
    }

    .catalog-product-view .tooltiptext-div {
        @apply min-w-[81px] text-[11px] !p-px;
    }

    .catalog-product-view .tooltiptext-div button {
        @apply text-[11px] ml-px;
    }

    .catalog-product-view .tooltiptext-svg {
        @apply w-[11px] h-[11px] !important;
    }

    .catalog-product-view .op-voorraad-mobile {
        @apply block;
    }
}
@media (min-width: 768px) and (max-width: 1024px) {
    .catalog-product-view .review-list {
      @apply w-[47.333333%];
    }
    .catalog-product-view .price-quantity-cart-content .container-price {
      @apply flex-[0_0_62%];
    }
    .catalog-product-view .price-quantity-cart-content .container-qty-cart {
      @apply flex-[0_0_32%];
    }
}
@media (max-width: 768px) {
    .catalog-product-view .product-description-container {
      @apply mt-1 mb-3 !important;
    }

    .catalog-product-view .item-product-bottom-mobile .top-cart-price {
      @apply h-[69px];
    }
    .catalog-product-view .title-top-crossel {
      @apply pl-0 !important;
    }
    .catalog-product-view .price-quantity-cart-content .container-qty-cart .btn-add-to-cart span {
      @apply w-auto !important;
    }
}
@media (max-width: 576px) {
    .catalog-product-view .colomns {
      @apply !px-[18px];
    }
}

@media (max-width: 430px) {
    .catalog-product-view li.percent-status-li button {
      @apply py-2 px-[18px];
    }
}
@media (max-width:375px) {
    .catalog-product-view #maincontent {
        @apply mt-6.25;
    }
}
.catalog-product-view {
    .page-product-bundle {
        .price-final_price {
            .price-from,
            .price-to {

                .price-container {
                    @apply text-gray-700 block mb-4;

                    .price-label {
                        @apply block text-lg font-medium;
                    }

                    .price {
                        @apply font-semibold text-2xl text-gray-800 leading-tight;
                    }
                }

                .price-including-tax + .price-excluding-tax {
                    @apply mt-1;

                    .price {
                        @apply text-base;
                    }
                }

                .old-price {
                    .price-container {
                        .price,
                        .price-label {
                            @apply text-gray-500 font-normal inline text-base;
                        }
                    }
                }
            }
        }
    }

    .tier-price-container{
        .prices-tier {
            @apply text-cadet-space;

            .price-container{
                .price-wrapper {
                    .price{
                        @apply text-base;
                    }
                }
            }
        }
    }
    .page.messages{
            @apply mt-0;
    }
}
