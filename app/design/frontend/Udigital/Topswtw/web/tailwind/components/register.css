/* Register CSS refactored with Tai<PERSON>wind */

.customer-account-create {
    @apply bg-transparent;

}

.customer-account-create .field:not(.choice).required > .label:after {
    content: '*';
    color: #e02b27;
    font-size: 14px;
}


/* start radio selection function */
.customer-account-create .radio-type input.field.radio {
    @apply hidden;
}

.customer-account-create .radio-type .checked {
    @apply hidden;
}

.customer-account-create .checked {
    @apply w-4 h-4 bg-white bg-no-repeat bg-[url('../images/radio-checked.svg')];
}

.customer-account-create .notchecked {
    @apply inline-block w-4 h-4 bg-white bg-no-repeat bg-[url('../images/radio-empty.svg')];
}

.customer-account-create .radio-type input.personal:checked ~ .checked {
    @apply inline-block;
}

.customer-account-create .radio-type input.personal:checked ~ .notchecked {
    @apply hidden;
}

.customer-account-create .radio-type input.company:checked ~ .checked {
    @apply inline-block;
}

.customer-account-create .radio-type input.company:checked ~ .notchecked {
    @apply hidden;
}

.customer-account-create .radio-label-register {
    @apply flex items-center;
}
/* end radio selection function */

.customer-account-create .termncondition > ul {
    @apply pt-4 mb-0 pl-0;
}

#termncondition, #manualAddressEnabled {
    @apply text-blue-picton w-5 h-5 rounded-md;
}

@media (max-width: 768px) {


    .customer-account-create fieldset label span:not(.nobold) {
        @apply tracking-wide font-semibold;
    }

    .customer-account-create .div-name {
        @apply flex;
    }

    .customer-account-create {
        .columns {
            @apply max-w-full pt-0 pr-6 pl-6;
        }
    }

}
