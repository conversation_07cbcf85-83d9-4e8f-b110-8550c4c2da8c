
/* mobile */

@media only screen and (min-width: 768px) and (max-width: 1280px) {
    #email-thankyou {
        @apply w-full;
    }
    
    #showpassword-mobile,
    #hidepassword-mobile,
    #showpasswordConfirm-mobile,
    #hidepasswordConfirm-mobile {
        @apply ml-[90%];
    }
    
    /* .password-thankyou-mobile {
        @apply w-full;
    } */
    
    .button-email {
        @apply w-full;
    }
    
    .custom-display-f {
        @apply flex;
    }
    
    .content-form-mobile {
        @apply pr-[20px] pl-[20px] w-1/2;
    }
    
    .content-contact-tablet {
        @apply w-1/2 pr-[20px];
    }
    
    .content-image-contact {
        @apply w-max float-right;
    }
}




@media screen and (max-width: 1280px) {
    .checkout-onepage-success .back-page-cat {
        @apply mb-0 ml-0;
    }
    
    .content-tablet {
        @apply grid;
        
    }
    
    .h-content-contact {
        @apply h-max pb-[100px];
        
    }
    
    .cms-bedankpagina .content-video {
        @apply w-full h-[200px] -mt-[20px] mb-[30px];
        
    }
    
    .cms-bedankpagina #maincontent > div.columns > div > div:nth-child(2) > div {
        @apply flex flex-col min-h-[100px];
    }
    
    .cms-bedankpagina .columns {
        @apply w-full mx-auto pr-0 pl-0;
        
    }
    
    .cms-bedankpagina #maincontent > div.columns > div > div {
        @apply pr-0 pl-0;
        
    }
    
    .cms-bedankpagina #maincontent > div.columns > div > div > div {
        @apply p-0;
        
    }
    
    .cms-bedankpagina .top-content {
        @apply -mt-[10px];
    }
    
    .cms-bedankpagina .pisition-video {
        @apply order-1;
    }
    
    .cms-bedankpagina .position-content {
        @apply flex flex-col;
    }
    
    .cms-bedankpagina .title-text-thankyoupage-mobile {
        @apply text-[15px];
        
    }

    .title-text-thankyoupage-mobile {
        @apply mt-[20px] text-[17px];
    }
    
    .content-padding-text {
        @apply pr-[20px] pl-[20px];
    }
    
    .content-image-contact-top {
        @apply w-[150px] -mt-[50px] mx-auto h-auto;
    }
    
    /* .content-text-list {
        @apply text-[#002C4B] text-[15px];
    } */
    
    /* .title-content-hader-thankyoupage {
        @apply text-[17px] text-[#16365B] font-bold;
    } */
    
    /* .title-text-thankyoupage {
        @apply text-[15px] text-[#16365B];
    } */
    
    .content-text-list-mobile {
        @apply text-[15px] text-[#16365B];
    }
    
    .content-desktop-thankyoupage {
        @apply hidden;
    }
    
    .content-text-list-desktop {
        @apply hidden;
    }
    
    .checkout-onepage-success #maincontent > div.columns {
        @apply pr-0 pl-0 max-w-full;
    }
    
    #email-thankyou {
        @apply w-[80%] rounded-[8px] border border-[#A2AFBD] text-[12px] h-[32px];
    }
    
    #email-thankyou::placeholder {
        @apply text-[#B6B6B6];
    }
    
    .password-thankyou-mobile::placeholder {
        @apply text-[#B6B6B6];
    }
    
    /* .password-thankyou-mobile {
        @apply w-[80%] rounded-[8px] border border-[#A2AFBD] text-[12px] h-[32px];
    } */
    
    #showpassword-mobile {
        @apply -mt-[28px] ml-[67%];
    }
    
    #hidepassword-mobile {
        @apply -mt-[28px] ml-[67%] hidden;
    }
    
    #showpasswordConfirm-mobile {
        @apply -mt-[28px] ml-[67%];
    }
    
    #hidepasswordConfirm-mobile {
        @apply -mt-[28px] ml-[67%] hidden;
    }
    
    .content-video {
        @apply w-full h-[200px] mt-[35px] mb-[30px];
    }
    
    .button-email {
        @apply w-[80%] text-center bg-[#2EB6ED] py-[10px] rounded-[8px] text-[15px];
    }
    
    .content-title-text {
        @apply text-[17px] text-[#16365B];
    }
    
    .content-form-desktop {
        @apply hidden;
    }
    
    .video-player {
        @apply h-[219px];
    }
    
    .content-contact {
        @apply hidden;
    }
    
    .contact-image {
        @apply w-[354px] h-[301px] mt-[30px] mb-[55px] mx-auto;
    }
    
    .content-image-contact {
        @apply w-full;
    }
}
/* destop */

/* only 1280 */

@media only screen and (min-width: 1280px) and (max-width: 1280px) {
    .content-desktop-thankyoupage {
        @apply block;
    }
    
    .content-text-list-desktop {
        @apply block;
    }
    
    .content-tablet {
        @apply flex;
    }
    
    .video-player {
        @apply h-full;
        height: -webkit-fill-available;
    }
    
    .content-contact-tablet {
        @apply w-auto pr-0;
    }
    
    .content-contact {
        @apply block;
        
    }
    
    .content-image-contact {
        @apply mt-[12px];
        
    }
    
    .content-form-desktop {
        @apply block;
    }
    
    #password-thankyou {
        @apply h-[35px] mt-[15px];
    }
    
    #showpassword {
        @apply -mt-[29px];
        
    }
    
    #hidepassword {
        @apply -mt-[29px];
        
    }
}

@media screen and (min-width: 1280px) {

    .title-mogelijk {
        @apply w-[550px];
    }
    
    .title-text-thankyoupage-contact {
        @apply text-[#002C4B] w-[540px];
    }
    
    .bottom-sidebar-bedank {
        @apply ml-[80px] rounded-[12px] bg-[#002C4B] w-[260px] absolute pb-[15px];
    }
    
    .content-video-bedankpagina {
        @apply w-[536px];
    }
    
    .content-padding-text {
        @apply pr-[20px] pl-0;
    }
    
    /* .title-text-thankyoupage {
        @apply text-[#002C4B];
    } */
    
    .content-image-contact-top {
        @apply w-[150px] -mt-[50px] mx-auto h-auto;
    }
    
    /* .title-content-hader-thankyoupage {
        @apply text-[20px] font-bold text-[#002C4B] mt-[55px];
    } */
    
    /* .content-text-list {
        @apply text-[#002C4B] text-[16px];
    } */
    
    .content-text-list-mobile {
        @apply hidden;
    }
    
    .content-mobile-thankyoupage {
        @apply hidden;
    }
    
    .checkout-onepage-success #maincontent > div.columns {
        @apply max-w-[1640px] mt-14;
    }
    
    .content-form-mobile {
        @apply hidden;
    }
    
    .content-form-top {
        @apply mt-[73px];
    }
    
    .content-title-text {
        @apply text-[32px] text-[#16365B] font-bold;
    }
    
    .contact-image {
        @apply w-[300px] h-[221px];
    }
    
    #hidepassword {
        @apply -mt-[34px] ml-[350px] hidden;
    }
    
    #showpassword {
        @apply -mt-[34px] ml-[350px];
    }
    
    #hidepasswordConfirm {
        @apply -mt-[34px] ml-[350px] hidden;
    }
    
    #showpasswordConfirm {
        @apply -mt-[34px] ml-[350px];
    }
    
    #email-thankyou {
        @apply w-[398px] rounded-[8px] border border-[#A2AFBD] text-[14px];
    }
    
    #email-thankyou::placeholder {
        @apply text-[#B6B6B6];
    }
    
    .password-thankyou::placeholder {
        @apply text-[#B6B6B6];
    }
    
    .password-thankyou {
        @apply w-[398px] rounded-[8px] border border-[#A2AFBD] text-[14px];
    }
    
    .button-email {
        @apply text-center w-[398px] bg-[#2EB6ED] py-[10px] rounded-[8px] text-[20px] text-[#002C4B];
    }
    
    .content-video {
        @apply w-[536px] h-[640px] mx-auto rounded-[12px];
    }
    
    .content-contact {
        @apply w-[300px] h-auto border bg-[#002C4B] rounded-[12px] ml-auto pl-[10px] pr-[10px];
    }
    
    .content-contact-info {
        @apply w-[235px] border-t border-[#A2AFBD] mx-auto pt-[10px] pb-[10px];
    }
    
    .font-contact-title {
        @apply text-[#FFFFFF] text-[16px];
    }
    
    .font-contact-content {
        @apply text-[#2EB6ED] text-[14px];
    }
    
    .content-image-contact {
        @apply w-[300px] h-[221px] border-0 rounded-[12px] mt-[40px] ml-auto;
    }
    
    .input-icons svg {
        @apply absolute;
    }
    
    .video-player {
        @apply w-[95%] rounded-[12px];
    }
    
}

@media only screen and (min-width: 1920px) and (max-width: 1920px) {
    .video-player {
        @apply w-full rounded-[12px];
    }
    
    .content-image-contact {
        @apply mt-[69px];
    }

}

.checkout-onepage-success{
    .title-account{
        @apply hidden;
    }
    #maincontent > a{
        @apply -mt-[0.1px];
    }
}