.wishlist-widget .price-box {
    & .price-label {
        @apply sr-only;
    }

    & .old-price {
        @apply sr-only;
    }
}

.max-h-screen-25{
    max-height: 25vh;
}

[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select{
    background-color: var(--white)!important;
    background: var(--white) 0% 0% no-repeat padding-box;
    border-color: var(--blue-cadet)!important;
    border: 1px solid var(--blue-cadet);
    border-width: 1px;
    border-radius: 8px!important;
    opacity: 1!important;
}

.input-quantity {
    display: flex;
    position: relative;

    input[type="number"] {
        -moz-appearance: textfield;
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    input {
        text-align: center;
        line-height: 1.65;
        float: left;
        display: block;
        padding: 0;
        margin: 0;
        padding-right: 20px;
        opacity: 1;
    }

    input:focus {
        outline: 0;
    }

    .item-quantity-nav {
        float: left;
        position: relative;
        height: 48px;
    }

    .item-quantity-button {
        position: relative;
        cursor: pointer;
        width: 25px;
        text-align: center;
        color: var(--blue-prussian);
        font-size: 13px;
        font-family: "Trebuchet MS", Helvetica, sans-serif !important;
        line-height: 1.7;
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        -o-user-select: none;
        user-select: none;
    }

    .item-quantity-button.item-quantity-up {
        position: absolute;
        height: 50%;
        top: 0;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
    }

    .item-quantity-button.item-quantity-down {
        position: absolute;
        bottom: -1px;
        height: 50%;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
    }
}