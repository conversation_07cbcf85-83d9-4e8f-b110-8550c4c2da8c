const {
    spacing
} = require('tailwindcss/defaultTheme');

const colors = require('tailwindcss/colors');
const plugin = require('tailwindcss/plugin')

const hyvaModules = require('@hyva-themes/hyva-modules');

module.exports = hyvaModules.mergeTailwindConfig({
    theme: {
        mode: process.env.TAILWIND_COMPILE_MODE || "jit", // either 'jit' or 'aot'
        extend: {
            screens: {
                'sm': '640px',
                // => @media (min-width: 640px) { ... }
                'md': '768px',
                // => @media (min-width: 768px) { ... }
                'lg': '1024px',
                // => @media (min-width: 1024px) { ... }
                'xl': '1280px',
                // => @media (min-width: 1280px) { ... }
                '2xl': '1536px',
                // => @media (min-width: 1536px) { ... }

                // Custom screen
                'screen-78.25': '313px',
                'screen-94': '376px',
                'screen-107.5': '430px',
                'screen-110': "440px",
                "screen-192": "768px",
                "screen-192.25": "769px",
                'screen-107.75': '431px',
                "screen-205": "820px",
                "screen-205.25": "821px",
                'screen-256.25': '1025px',
                "screen-255.75": "1023px",
                "screen-255.5": "1022px",
                'screen-256': '1024px',
                "screen-262.5": "1050px",
                "screen-267.5": "1070px",
                "screen-279.75": "1119px",
                'screen-287.25': '1149px',
                "screen-282.5": "1130px",
                'screen-287.5': '1150px',
                "screen-295": "1180px",
                "screen-308.75": "1235px",
                "screen-325": "1300px",
                "screen-333.75": "1335px",
                "screen-346": "1384px",
                "screen-346.25": "1385px",
                "screen-371": "1484px",
                "screen-370.75": "1483px",
                "screen-384": "1536px",
                "4xl": "1640px",
                'screen-451': '1804px',
                'screen-480': '1920px',
            },
            fontFamily: {
                sans: ["Segoe UI", "Helvetica Neue", "Arial", "sans-serif"],
                'public-sans': ['"Public Sans"', 'sans-serif'],
                'trebuchet-ms': ['"Trebuchet MS"',  'Helvetica', 'sans-serif'],
            },
            colors: {
                'blue-prussian': {
                    lighter: '#33566f',
                    "DEFAULT": '#002c4b',
                    darker: '#001e34'
                },
                'saffron': {
                    lighter: '#f7bd47',
                    "DEFAULT": '#f9b734',
                    darker: '#ff9029'
                },
                'green-islamic': {
                    lighter: '#03c603',
                    "DEFAULT": '#00b900',
                    darker: '#008100'
                },
                'silver-philippine': {
                    lighter: '#c4c4c4',
                    "DEFAULT": '#b6b6b6',
                    darker: '#7f7f7f'
                },
                'blue-cadet': {
                    lighter: '#b4bfca',
                    "DEFAULT": '#a2afbd',
                    darker: '#717a84',
                    equivocal: 'rgba(162, 175, 189, 0.6)'
                },
                'blue-picton': {
                    lighter: '#57c4f0',
                    "DEFAULT": '#2eb6ed',
                    darker: '#207fa5'
                },
                'filter-blue': {
                    "DEFAULT": '#018de4',
                },
                'gray-bright': {
                    lighter: '#edf0f3',
                    "DEFAULT": '#e9edf0',
                    darker: '#a3a5a8'
                },
                'cadet-space': {
                    lighter: '#445e7b',
                    "DEFAULT": '#16365b',
                    darker: '#0f253f'
                },
                'white-azureish': {
                    lighter: '#ddeef3',
                    "DEFAULT": '#d5eaf0',
                    darker: '#95a3a8'
                },
                'blue-alice': {
                    lighter: '#f4fafb',
                    "DEFAULT": '#f2f9fb',
                    darker: '#a9aeaf'
                },
                'blue-jeans': {
                    lighter: '#7dcef2',
                    "DEFAULT": '#5dc2ef',
                    darker: '#4187a7'
                },
                'blue-mist': {
                    "DEFAULT": '#edf6f9'
                },

                'platinum': {
                    lighter: '#eee9e9',
                    "DEFAULT": '#eae4e4',
                    darker: '#a39f9f'
                },
                'gray-spanish': {
                    lighter: '#b1b1b1',
                    "DEFAULT": '#9e9e9e',
                    darker: '#6e6e6e'
                },
                'pink-cherry-blossom': {
                    lighter: '#ffc2d2',
                    "DEFAULT": '#ffb3c7',
                    darker: '#b27d8b'
                },
                'lavender': {
                    lighter: '#f3f9fba6',
                    "DEFAULT": '#d5eaf06b',
                    darker: '#3d4344a6'
                },
                primary: {
                    lighter: colors.blue['300'],
                    "DEFAULT": colors.blue['800'],
                    darker: colors.blue['900']
                },
                secondary: {
                    lighter: colors.blue['100'],
                    "DEFAULT": colors.blue['200'],
                    darker: colors.blue['300']
                },
                background: {
                    lighter: colors.blue['100'],
                    "DEFAULT": colors.blue['200'],
                    darker: colors.blue['300']
                },
                green: colors.emerald,
                yellow: colors.amber,
                purple: colors.violet
            },
            fontSize: {
                0: "0",
                0.25: "1px",
                0.5: "2px",
                0.75: "3px",
                1: "4px",
                1.25: "5px",
                1.5: "6px",
                2: "8px",
                2.5: "10px",
                2.75: "11px",
                3: "12px",
                3.25: "13px",
                3.5: "14px",
                3.75: "15px",
                4: "16px",
                4.25: "17px",
                5: "20px",
                5.5: "22px",
                6: "24px",
                6.25: "25px",
                6.5: "26px",
                7: "28px",
                8: "32px",
                9: "36px",
                10: "40px",
                11: "44px",
                12: "48px",
                14: "56px",
                16: "64px",
                20: "80px",
                24: "96px",
                28: "112px",
                32: "128px",
                36: "144px",
                40: "160px",
                44: "176px",
                48: "192px",
                52: "208px",
                56: "224px",
                60: "240px",
                64: "256px",
                72: "288px",
                80: "320px",
                96: "384px",
                100: "400px",

                xs: ["12px", { lineHeight: "16px" }],
                sm: ["14px", { lineHeight: "20px" }],
                base: ["16px", { lineHeight: "24px" }],
                lg: ["18px", { lineHeight: "28px" }],
                xl: ["20px", { lineHeight: "28px" }],
                "2xl": ["24px", { lineHeight: "32px" }],
                "3xl": ["30px", { lineHeight: "36px" }],
                "4xl": ["36px", { lineHeight: "36px" }],
                "5xl": ["48px", { lineHeight: "1" }],
                "6xl": ["60px", { lineHeight: "1" }],
                "7xl": ["72px", { lineHeight: "1" }],
                "8xl": ["96px", { lineHeight: "1" }],
                "9xl": ["144px", { lineHeight: "1" }],
            },
            spacing: {
                px: "1px",
                0: "0",
                0.36: "1.44px",
                0.5: "2px",
                0.75: "3px",
                1: "4px",
                1.25: "5px",
                1.5: "6px",
                1.6: "6.4px",
                1.75: "7px",
                2: "8px",
                2.5: "10px",
                9.75: "39px",
                3: "12px",
                3.5: "14px",
                3.75: "15px",
                4: "16px",
                4.25: "17px",
                4.75: "19px",
                5: "20px",
                5.25: "21px",
                5.75: "23px",
                6: "24px",
                6.25: "25px",
                6.5: "26px",
                7: "28px",
                7.5: "30px",
                8: "32px",
                8.25: "33px",
                8.5: "34px",
                8.75: "35px",
                9: "36px",
                9.5: "38px",
                10: "40px",
                10.75: "43px",
                11: "44px",
                12: "48px",
                12.5: "50px",
                14: "56px",
                15: "60px",
                15.25: "61px",
                16: "64px",
                18: "72px",
                18.75: "76px",
                20: "80px",
                22.25: "89px",
                22.5: "90px",
                22.75: "91px",
                24: "96px",
                25: "100px",
                27: "105px",
                27.5: "110px",
                28: "112px",
                28.75: "115px",
                30.5: '122px',
                31.5: "126px",
                32: "128px",
                33.5: "134px",
                35: "140px",
                36: "144px",
                36.25: "145px",
                39.5: "158px",
                40: "160px",
                41: "164px",
                42: "168px",
                43.25: "173px",
                44: "176px",
                45: '180px',
                47.5: '190px',
                48: "192px",
                49: "196px",
                52: "208px",
                56: "224px",
                56.25: '225px',
                60: "240px",
                64: "256px",
                72: "288px",
                80: "320px",
                96: "384px",
                100: "400px",
                110: "440px",
                2.75: "11px",
                3.25: "13px",
                5.5: "22px",
                "3/50": "6%",
                "3/100": "3%",
                "99/100":"99%",
                "23/25": "92%",
                "91/100": "91%",
                "9/10":"90%",
                "7/10":"70%",
                "13/20":"65",
                "11/20": "55%",
                "1/2": "50%",
                "9/20": "45%",
                "7/20": "35%",
                "3/10": "30%",
                "1/4": "25%",
                "1/5": "20%",
                "3/20": "15%",
                "1/10": "10%",
                "2/25": "8%",
                "3/20": "15%",
                "9/200": "4.5%",
                "21/100": "21%",
                "35/500": "7%",
                "87/1000": "8.7%",
                'screen-h-5': "5vw",
                'screen-h-7.9': "7.9vh",
                'screen-h-13.5': "13.5vh",
                'screen-h-15': "15vh",
                'screen-h-15.5': "15.5vh",
                'screen-w-8': "8vw",
            },
            zIndex: {
                1: '1',
                11: '11',
                40: '40',
                45: '45',
            },
            textColor: {
                orange: colors.orange,
                red: {
                    ...colors.red,
                    "DEFAULT": colors.red['500']
                },
                primary: {
                    lighter: colors.gray['700'],
                    "DEFAULT": colors.gray['800'],
                    darker: colors.gray['900']
                },
                secondary: {
                    lighter: colors.gray['400'],
                    "DEFAULT": colors.gray['600'],
                    darker: colors.gray['800']
                }
            },
            backgroundColor: {
                primary: {
                    lighter: colors.blue['600'],
                    "DEFAULT": colors.blue['700'],
                    darker: colors.blue['800']
                },
                secondary: {
                    lighter: colors.blue['100'],
                    "DEFAULT": colors.blue['200'],
                    darker: colors.blue['300']
                },
                container: {
                    lighter: '#ffffff',
                    "DEFAULT": '#fafafa',
                    darker: '#f5f5f5'
                }
            },
            borderColor: {
                primary: {
                    lighter: colors.blue['600'],
                    "DEFAULT": colors.blue['700'],
                    darker: colors.blue['800']
                },
                secondary: {
                    lighter: colors.blue['100'],
                    "DEFAULT": colors.blue['200'],
                    darker: colors.blue['300']
                },
                container: {
                    lighter: '#f5f5f5',
                    "DEFAULT": '#e7e7e7',
                    darker: '#b6b6b6'
                },
                'green-islamic': {
                    lighter: '#03c603',
                    "DEFAULT": '#00b900',
                    darker: '#008100'

                }
            },
            borderRadius: {
                inherit: "inherit",
                2.5: "10px",
            },
            width: {
                6.25: "25px",
                10.25: "41px",
                10.5: "42px",
                15: "60px",
                17.5: "70px",
                18.25: "73px",
                20.5: "82px",
                22: "88px",
                23.25: "93px",
                23.75: "95px",
                25: "100px",
                30.5: "122px",
                35: "140px",
                36.25: "145px",
                47.5: "190px",
                53.75: "215px",
                56.25: "225px",
                65: "260px",
                75: "300px",
                82.5: "330px",
                95: "380px",
                108.75: "435px",
                120: "480px",
                127.5: "510px",
                143.75: "575px",
                136.25: "545px",
                142.5: "570px",
                151.5: "606px",
                156.25: "625px",
                170: "680px",
                181.25: "725px",
                188.75: "755px",
                203.75: "815px",
                212.75: "851px",
                55: "220px",
                "6/25": "24%",
                "2/5": "40%",
                "9/20": "45%",
                "11/20": "55%",
                "117/200": "58.5%",
                "17/20": "85%",
                "22/25": "88%",
                "19/20": "95%",
                "106+": "106%",
                "112+": "112%",
                'screen-27': "27vw",
                'screen-full': "100vw",
            },
            minWidth: {
                8: spacing["8"],
                14.25: "57px",
                20: spacing["20"],
                35: "140px",
                40: spacing["40"],
                48: spacing["48"],
                62.5: "250px",
                75: "300px",
                "1/5": "20%",
                "6/25": "24%",
            },
            maxWidth: {
                8.75: "35px",
                "177/200": "88.5%",
                "22/25": "88%",
                'screen-52': "52vw",
                55: "220px",
            },
            height: {
                0: '0px',
                5.5: "22px",
                6.5: "26px",
                6.75: "27px",
                7.75: "31px",
                8.75: "35px",
                9.5: "38px",
                10: "40px",
                10.5: "42px",
                11.5: "46px",
                12.25: "49px",
                13.5: "54px",
                14: "56px",
                10.25: "41px",
                10.75: "43px",
                11.25: "45px",
                15: "60px",
                16.5: "66px",
                36.25: "145px",
                36.5: "146px",
                17.5: "70px",
                18: "72px",
                18.75: "76px",
                20: "80px",
                21: "84px",
                25: "100px",
                30: "120px",
                43: "172px",
                45: "180px",
                46.25: '185px',
                53: "212px",
                56.25: "225px",
                66.25: '265px',
                72: "288px",
                74.75: '299px',
                98.5: '394px',
            },
            minHeight: {
                14: "56px",
                18.75: "75px",
                21: "84px",
                56.25: "225px",
                'screen-25': '25vh',
                'screen-50': '50vh',
                'screen-75': '75vh'
            },
            maxHeight: {
                0: '0',
                8.75: "35px",
                45: '180px',
                'screen-25': '25vh',
                'screen-50': '50vh',
                'screen-52': "52vh",
                'screen-75': '75vh'
            },
            container: {
                center: true,
                padding: '1.5rem'
            },
            letterSpacing: {
                0: '0',
                0.1: '0.4px',
                normal: '0',
            },
            boxShadow: {
                'header': '0px 3px 6px #0000000d',
            },
            outline: {
                0: '0',
            },
            lineHeight: {
                0: '0',
                1.2: '1.2',
                1.3: '1.3',
                4.5: '18px',
                6.25: '25px',
            },
            gridTemplateRows: {
                'product-content': '68px 1fr 1fr',
                'product-content-2': '60px 1fr 1fr 55px',
                'product-content-3': '68px 1fr 1fr 62px',
            },
            strokeWidth: {
                px: "1px",
                0.5: "2px",
            }
        }
    },
    variants: {
        extend: {
            borderWidth: ["last", "hover", "focus"],
            margin: ["last"],
            opacity: ["disabled"],
            backgroundColor: ["even", "odd"],
            ringWidth: ["active"],
        },
    },
    plugins: [
        require('@tailwindcss/forms'),
        require('@tailwindcss/typography'),
        require('tailwindcss-base-buttons')({
            baseClass: '.btn',
            borderRadius: '.5rem',
            padding: '.5rem 1rem',
            borderWidth: 2,
            colors: {
                theme: {
                    'blue-prussian': {
                        activeBackground: '#001e34',
                        background: '#002c4b',
                        hoverBackground: '#33566f',
                        hoverBorderColor: '#33566f',
                        text: '#ffffff',
                        activeText: '#ffffff'
                    },
                    'saffron': {
                        activeBackground: '#ff9029',
                        background: '#f9b734',
                        hoverBackground: '#f7bd47',
                        hoverBorderColor: '#f7bd47',
                        text: '#002c4b',
                        activeText: '#002c4b'
                    },
                    'green-islamic': {
                        activeBackground: '#008100',
                        background: '#00b900',
                        hoverBackground: '#03c603',
                        hoverBorderColor: '#03c603',
                        text: '#ffffff',
                        activeText: '#ffffff'
                    },
                    'silver-philippine': {
                        activeBackground: '#7f7f7f',
                        background: '#b6b6b6',
                        hoverBackground: '#c4c4c4',
                        hoverBorderColor: '#c4c4c4',
                        text: '#002c4b',
                        activeText: '#ffffff'
                    },
                    'blue-cadet': {
                        activeBackground: '#717a84',
                        background: '#a2afbd',
                        hoverBackground: '#b4bfca',
                        hoverBorderColor: '#b4bfca',
                        text: '#002c4b',
                        activeText: '#ffffff'
                    },
                    'blue-picton': {
                        activeBackground: '#207fa5',
                        background: '#2eb6ed',
                        hoverBackground: '#57c4f0',
                        hoverBorderColor: '#57c4f0',
                        text: '#ffffff',
                        activeText: '#ffffff'
                    },
                    'gray-bright': {
                        activeBackground: '#a3a5a8',
                        background: '#e9edf0',
                        hoverBackground: '#edf0f3',
                        hoverBorderColor: '#edf0f3',
                        text: '#002c4b',
                        activeText: '#002c4b'
                    },
                    'cadet-space': {
                        activeBackground: '#0f253f',
                        background: '#16365b',
                        hoverBackground: '#445e7b',
                        hoverBorderColor: '#445e7b',
                        text: '#ffffff',
                        activeText: '#ffffff'
                    },
                    'white-azureish': {
                        activeBackground: '#95a3a8',
                        background: '#d5eaf0',
                        hoverBackground: '#ddeef3',
                        hoverBorderColor: '#ddeef3',
                        text: '#0f253f',
                        activeText: '#0f253f'
                    },
                    'blue-alice': {
                        activeBackground: '#a9aeaf',
                        background: '#f2f9fb',
                        hoverBackground: '#f4fafb',
                        hoverBorderColor: '#f4fafb',
                        text: '#0f253f',
                        activeText: '#0f253f'
                    },
                    'blue-jeans': {
                        activeBackground: '#4187a7',
                        background: '#5dc2ef',
                        hoverBackground: '#7dcef2',
                        hoverBorderColor: '#5dc2ef',
                        text: '#ffffff',
                        activeText: '#ffffff'
                    },
                    'platinum': {
                        activeBackground: '#a39f9f',
                        background: '#eae4e4',
                        hoverBackground: '#eee9e9',
                        hoverBorderColor: '#eee9e9',
                        text: '#0f253f',
                        activeText: '#0f253f'
                    },
                    'gray-spanish': {
                        activeBackground: '#6e6e6e',
                        background: '#9e9e9e',
                        hoverBackground: '#b1b1b1',
                        hoverBorderColor: '#b1b1b1',
                        text: '#ffffff',
                        activeText: '#ffffff'
                    },
                    'pink-cherry-blossom': {
                        activeBackground: '#b27d8b',
                        background: '#ffb3c7',
                        hoverBackground: '#ffc2d2',
                        hoverBorderColor: '#ffc2d2',
                        text: '#0f253f',
                        activeText: '#ffffff'
                    },
                    'lavender': {
                        activeBackground: '#3d4344a6',
                        background: '#d5eaf06b',
                        hoverBackground: '#f3f9fba6',
                        hoverBorderColor: '#f3f9fba6',
                        text: '#ffffff',
                        activeText: '#ffffff'
                    }
                }
            },
            sizes: {
                '2xl': {
                    fontSize: '2rem',
                    padding: '1.2rem 2.3rem'
                }
            }
        }),
        plugin(function({ addUtilities }) {
            addUtilities({
                '.no-scrollbar': {
                    '-ms-overflow-style': 'none',
                    'scrollbar-width': 'none',
                },
                '.clip-path-torn' : {
                    '-webkit-clip-path': 'polygon(0 0, 100% 0, 100% 99.8%, 17.1% 100%, 17.1% 86.5%, 1.6% 86.5%, 1.6% 86.5%, 1.456% 86.464%, 1.322% 86.416%, 1.199% 86.356%, 1.083% 86.284%, .975% 86.2%, .873% 86.104%, .775% 85.996%, .682% 85.876%, .59% 85.744%, .5% 85.6%, .5% 85.6%, .416% 85.477%, .342% 85.346%, .279% 85.205%, .223% 85.053%, .175% 84.888%, .133% 84.707%, .095% 84.51%, .062% 84.294%, .03% 84.058%, 0 83.8%)',
                    'clip-path': 'polygon(0 0, 100% 0, 100% 99.8%, 17.1% 100%, 17.1% 86.5%, 1.6% 86.5%, 1.6% 86.5%, 1.456% 86.464%, 1.322% 86.416%, 1.199% 86.356%, 1.083% 86.284%, .975% 86.2%, .873% 86.104%, .775% 85.996%, .682% 85.876%, .59% 85.744%, .5% 85.6%, .5% 85.6%, .416% 85.477%, .342% 85.346%, .279% 85.205%, .223% 85.053%, .175% 84.888%, .133% 84.707%, .095% 84.51%, .062% 84.294%, .03% 84.058%, 0 83.8%)',
                },
                '.no-spin' : {
                    '-moz-appearance': 'textfield',
                    '-webkit-appearance': 'none'
                },
                '.select-disable' : {
                    '-webkit-user-select': 'none',
                    '-moz-user-select': 'none',
                    '-ms-user-select': 'none',
                    '-o-user-select': 'none',
                    'user-select': 'none'
                }
            })
        })
    ],
    // Examples for excluding patterns from purge
    content: [
        // this theme's phtml files
        "../../**/*.phtml",
        // hyva theme-module templates (if this is the default theme in vendor/hyva-themes/magento2-default-theme)
        "../../../magento2-theme-module/src/view/frontend/templates/**/*.phtml",
        // hyva theme-module templates (if this is a child theme)
        //'../../../../../../../vendor/hyva-themes/magento2-theme-module/src/view/frontend/templates/**/*.phtml',
        // parent theme in Vendor (if this is a child-theme)
        "../../../../../../../vendor/hyva-themes/magento2-default-theme/**/*.phtml",
        // '../../../../../../../vendor/hyva-themes/magento2-theme-module/src/view/frontend/templates/**/*.phtml',
        // app/code phtml files (if need tailwind classes from app/code modules)
        '../../../../../../../app/code/Udigital/**/*.phtml',
        // react app src files (if Hyvä Checkout is installed in app/code)
        //'../../../../../../../app/code/**/src/**/*.jsx',
        // react app src files in vendor (If Hyvä Checkout is installed in vendor)
        //'../../../../../../../vendor/hyva-themes/magento2-hyva-checkout/src/reactapp/src/**/*.jsx',
        //'../../../../../../../vendor/hyva-themes/magento2-hyva-checkout/src/view/frontend/templates/react-container.phtml',
        // widget block classes from app/code
        //'../../../../../../../app/code/**/Block/Widget/**/*.php'
    ],
    safelist: [
        //Dynamic status colors. Must be available every time
        'text-red',
        'text-green-islamic',
        'text-saffron-darker',
        'text-gray-spanish-darker'
    ]
});
