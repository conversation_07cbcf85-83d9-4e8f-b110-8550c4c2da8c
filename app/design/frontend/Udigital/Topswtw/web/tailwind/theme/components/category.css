.category-view {
    .sidebar .block.filter {
    }
}

.z-200 {
    z-index: 200;
}

.w-8\/10 {
    width: 80%;
}

.h-8\/10 {
    height: 80%;
}

.text-justify {
    text-align: justify;
}

@media (min-width: 768px) {
    .md\:px-16 {
        padding-left: 4rem;
        padding-right: 4rem;
    }

    .md\:w-8\/12 {
        width: 66.666667%;
    }

    .md\:h-8\/10 {
        height: 80%;
    }

    .md\:pb-4{
        padding-bottom: 1rem;
    }
}

.px-24 {
    padding-left: 6rem;
    padding-right: 6rem;
}

.px-25 {
    padding-left: 6.25rem;
    padding-right: 6.25rem;
}

@media (min-width: 1024px) {
    .lg\:px-24 {
        padding-left: 6rem;
        padding-right: 6rem;
    }

    .lg\:px-25 {
        padding-left: 6.25rem;
        padding-right: 6.25rem;
    }

    .lg\:ml-24 {
        margin-left: 6rem;
    }
}

.pl-16 {
    padding-left: 4rem;
}

.pl-24 {
    padding-left: 6rem;
}

.pt-8 {
    padding-top: 2rem;
}

body::-webkit-scrollbar {
    width: 7px;
}
div::-webkit-scrollbar {
    width: 8px;
    height: 0;
}

body {
    scrollbar-width: thin;
    scrollbar-color: var(--silver-philippine) var(--white);
}

div {
    scrollbar-width: thin;
    scrollbar-color: var(--blue-picton) var(--white);
}

body::-webkit-scrollbar-track,
div::-webkit-scrollbar-track {
    background: var(--white) 0% 0% no-repeat padding-box;
    box-shadow: inset 0px 3px 6px #00000029;
    border-radius: 2px;
}

body::-webkit-scrollbar-thumb {
    background: var(--silver-philippine) 0% 0% no-repeat padding-box;
    box-shadow: inset 2px 3px 6px #d5eaf08a, 0px 3px 6px #002c4b33;
    border-radius: 2px;
}

div::-webkit-scrollbar-thumb {
    background: var(--blue-picton) 0% 0% no-repeat padding-box;
    box-shadow: inset 2px 3px 6px #d5eaf08a, 0px 3px 6px #002c4b33;
    border-radius: 2px;
}

@media (min-width: 768px) {
    .modal-view-category {
        width: 50%;
        height: 80%;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        padding-right: 1.75rem;
        padding-left: 1.75rem;
        margin-top: 6%;
    }
}

@media (max-width: 768px) {
    .modal-view-category {
        width: 95%;
        height: 90.5%;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        margin-top: 6%;
        padding-right: 1.25rem;
        padding-left: 1.25rem;
    }
}

.modal-view-category {
    h1, h2, h3, h4, h5, h6 {
        font-size: revert;
        font-weight: revert;
    }

    blockquote, dd, dl, figure, hr, p, pre {
        margin: revert;
    }

    h1, h2, h3 {
        margin-top: 0.5rem;
        margin-bottom: 1.5rem;
    }

    h4, h5, h6 {
        margin-bottom: 0.5rem;
        margin-top: 0.5rem;
    }

    p {
        margin-top: 0.5rem;
        margin-bottom: 1rem;
    }

    .pagebuilder-column-group {
        padding: 0 !important;
    }
}

@media (min-width: 768px) {
    .body-content-scroll {
        overflow-x: hidden;
        height: calc(100vh - 36vh);
    }
}

@media (max-width: 768px) {
    .body-content-scroll {
        overflow-x: hidden;
        height: calc(100vh - 20vh);
    }
}