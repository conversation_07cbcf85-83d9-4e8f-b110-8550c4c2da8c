.field-success {
    .input-group {
        @apply relative;

        &:after {
            @apply absolute inline-flex right-2 top-1/2 -translate-y-1/2 w-5 h-5 text-green-islamic p-1;

            content: '';
            background-size: 20px 20px;
            background-image: url('data:image/svg+xml;base64,"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGNsYXNzPSJoLTUgdy01IiB2aWV3Qm94PSIwIDAgMjAgMjAiIGZpbGw9IiMwMGI5MDAiPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTE2LjcwNyA1LjI5M2ExIDEgMCAwMTAgMS40MTRsLTggOGExIDEgMCAwMS0xLjQxNCAwbC00LTRhMSAxIDAgMDExLjQxNC0xLjQxNEw4IDEyLjU4Nmw3LjI5My03LjI5M2ExIDEgMCAwMTEuNDE0IDB6IiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIC8+Cjwvc3ZnPg=="');
        }
    }
}

:where(#hyva-checkout-main) .input-group > :where(.form-input-addon) + :where( input:not([type="radio"], [type="checkbox"], [type="range"]), select, textarea ) {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}

:where(#hyva-checkout-main) .input-group :where(.form-input-addon) {
    border-color: var(--blue-cadet) !important;
}

.checkout-default:where(:has(#hyva-checkout-main)) .header-main .container {
    max-width: 1024px;
}

.checkout-default .input-group:focus-within {
    box-shadow: none;
}
