.page-footer {
    margin-top: 5px;
    background: #16365b;
    color: var(--white-azureish);
}
.footer.content {
    border-top: none;
    padding-top: 20px;
    padding-bottom: 0;
}
.copyright {
    background-color: var(--gray-bright);
    color: var(--white);
    box-sizing: border-box;
    display: block;
    padding: 10px;
    text-align: center;
    height: 93px;
}



.col-span-5 {
    grid-column: span 5 / span 5;
}

.my-auto {
    margin-top: auto;
    margin-bottom: auto;
}

.fz-20 {
    font-size: 20px;
}

.fz-16 {
    font-size: 16px;
}

/* Small */
@media (min-width: 640px) {
    .sm\:hidden {
        display: none;
    }

    .sm\:col-span-4 {
        grid-column: span 4 / span 4;
    }

    .sm\:-mt-20 {
        margin-top: -5rem;
    }
}

/* Medium */
@media (min-width: 768px) {
    .md\:fz-20 {
        font-size: 20px;
    }
}

.footer-bottom {
    height: 93px;
    background: var(--gray-bright) 0% 0% no-repeat padding-box;
    opacity: 1;
}

.-mt-20 {
    margin-top: -5rem;
}

.box-footer {
    width: 28px;
    height: 28px;
    /* UI Properties */
    background: #b8c2cd 0% 0% no-repeat padding-box;
}

@media (max-width: 768px) {
    .footer-other {
        display: block;
    }
}

@media (min-width: 768px) {
    .footer-other {
        display: none;
    }
}

.space-x-32 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(8rem * var(--tw-space-x-reverse));
    margin-left: calc(8rem * calc(1 - var(--tw-space-x-reverse)));
}

@media (min-width: 1406px) and (max-width: 1525px) {
    .topswtw-img-sven-home-desktop {
        margin-top: -50px !important;
    }
}

@media (min-width: 1648px) {
    .gap-footer-content{
        gap: 8.5rem;
    }
}

@media (max-width: 1647px) {
    .gap-footer-content{
        gap: 1rem;
    }
}

/* Small */
@media (min-width: 640px) {

}

/* Medium */
@media (min-width: 768px) {

}

/* Large */
@media (min-width: 1024px) {

}

/* X-Large */
@media (min-width: 1280px) {
    .footer-bottom {
        height: 128px!important;
    }
}

/* 2X-Large */
@media (min-width: 1536px) {
    .footer-bottom {
        height: 128px!important;
    }
}

@media (min-width: 820px) and (max-width: 1200px) {
    .footer-content-data {
        padding-bottom: 10px;
    }
}
