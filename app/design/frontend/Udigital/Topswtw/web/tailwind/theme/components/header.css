@tailwind base;

@layer base {
    :root {
        --white: #ffffff;
        --blue-prussian: #002c4b;
        --blue-prussian-lighter: #0c3655;
        --saffron-darker: #ff9029;
        --saffron: #f9b734;
        --saffron-lighter: #f7bd47;
        --green-islamic: #00b900;
        --green-islamic-lighter: #03c603;
        --silver-philippine: #b6b6b6;
        --blue-cadet: #a2afbd;
        --blue-picton: #2eb6ed;
        --gray-bright: #e9edf0;
        --cadet-space: #16365b;
        --white-azureish: #d5eaf0;
        --blue-mist: #edf6f9;
        --blue-alice: #f2f9fb;
        --blue-jeans: #5dc2ef;
        --platinum: #eae4e4;
        --gray-spanish: #9e9e9e;
        --pink-cherry-blossom: #ffb3c7;
    }

    @font-face {
        font-family: 'Public Sans';
        src: url('../fonts/PublicSans-VariableFont_wght.ttf') format('truetype');
        font-weight: normal;
        font-style: normal;
        font-display: swap;
    }

    @font-face {
        font-family: 'Public Sans, Regular';
        src: url('../fonts/static/PublicSans-Regular.ttf') format('truetype');
        font-weight: normal;
        font-style: normal;
        font-display: swap;
    }

    @font-face {
        font-family: 'Public Sans, Bold';
        src: url('../fonts/static/PublicSans-Bold.ttf') format('truetype');
        font-weight: bold;
        font-style: normal;
        font-display: swap;
    }

    @font-face {
        font-family: 'Public Sans, Italic';
        src: url('../fonts/PublicSans-Italic-VariableFont_wght.ttf') format('truetype');
        font-weight: normal;
        font-style: normal;
        font-display: swap;
    }

    @font-face {
        font-family: 'Open Sans';
        src: url('../fonts/OpenSans.ttf') format('truetype');
        font-weight: normal;
        font-style: normal;
        font-display: swap;
    }
}

body{
    @apply bg-white;
}

@layer components {
    .title-categories {
        @apply px-3 py-1 my-1 mb-2 font-bold;
    }

    .item-categories {
        @apply block w-full px-3 py-1 my-1 whitespace-nowrap first:mt-0 hover:underline;
    }

    .other-categories {
        @apply px-3 py-1 my-1 text-blue-picton absolute bottom-0 block mb-3;
    }
}


.badge-minicart {
    @apply text-3.75 left-2.5 pt-0.5 px-1.6;
}

.page-header {
    @apply w-full fixed border-0 top-0 z-45 mb-0 mt-0 bg-blue-prussian;

    .wrapper-searching {
        @apply max-md:block hidden;
    }

    .header.panel {
        @apply box-border w-auto mx-auto px-5 py-1.75 md:flex md:flex-row md:flex-nowrap md:justify-between md:items-center max-md:flex max-md:items-center max-md:h-7.75 max-md:justify-center;
    }

    .panel.wrapper {
        @apply top-[102px] max-md:absolute max-md:w-full max-md:z-1 md:max-h-8.75 md:bg-white md:static;

        .switcher {

            .label {
                @apply h-px overflow-hidden absolute w-px -m-px p-0 border-0;
            }

            .options {
                @apply inline-block relative;
            }
        }
    }

    .header-welcome {
        @apply text-3.25 text-blue-prussian;
    }
}

.page-footer {
    .switcher {
        .options {
            @apply inline-block relative;
        }
    }
}

.w-menu-top,
.w-menu-top-nav {
    @apply w-47.5;
}

.w-menu-top{
    @apply lg:max-xl:w-25;
}

.clear-scroll::-webkit-scrollbar{
    @apply w-0 h-0;
}

.category-description{
    p {
        @apply text-base text-cadet-space;
    }
}

.menu-header {
    @apply flex w-menu-top border py-2 pl-4 pr-3 relative lg:flex h-10.25 text-white rounded-lg border-blue-cadet hover:text-blue-picton items-center justify-between;

    &.active {
        @apply text-blue-prussian bg-white border-white rounded-b-none;
    }
}

.wrapper-home-categories {
    @apply bg-left-top bg-cover bg-no-repeat bg-scroll;

    .wrapper-child-home {
        @apply flex justify-start flex-col bg-left-top bg-cover bg-no-repeat w-full self-stretch bg-lavender rounded-xl border border-solid border-lavender h-56.25 box-border max-w-full bg-scroll;

        .wrapper-img-category {
            @apply flex place-content-around p-7 h-45 place-items-center box-border m-0;

            a {
                img {
                    @apply max-w-full h-full rounded-inherit;
                }
            }
        }

        .category-title {
            @apply h-10.75 break-words rounded-br-xl rounded-t-none rounded-bl-xl bg-white;

            p {
                @apply flex place-content-center place-items-center h-full w-full m-0;

                a {
                    @apply text-center basis-full text-blue-picton decoration-inherit font-bold;
                }
            }
        }
    }
}

.mobile-clear-scroll::-webkit-scrollbar {
    @apply max-md:w-0 max-md:h-0;
}

.overflow-x-auto-mobile {
    @apply max-screen-107.5:ml-0 max-screen-107.5:overflow-x-auto max-screen-256:w-full max-screen-256:overflow-y-hidden max-screen-256:overflow-x-auto max-screen-256:flex;
}

.title-category-view {
    @apply max-md:text-4.25 md:text-2xl font-bold text-cadet-space tracking-0 opacity-100;
}


.wrapper-category-second-level {
    @apply w-full flex opacity-100 flex-nowrap flex-row justify-between items-center m-auto bg-blue-mist bg-no-repeat bg-clip-padding max-md:rounded-2xl max-md:h-14 md:rounded-xl md:h-20;
}

.panel.header {
    @apply container max-md:text-center md:text-left md:bg-white bg-white p-2;

    .links, .switcher {
        @apply max-screen-287.25:hidden screen-287.5:text-3.25 screen-287.5:flex screen-287.5:justify-end;
    }
}

.page-wrapper {
    @apply relative top-[120px] md:top-27.5;

    > .page-bottom {
        @apply max-xl:flex max-xl:justify-center max-xl:mb-2.5 xl:ml-21/100 xl:mr-2/25;

        .welcome {
            @apply md:inline-flex md:justify-center md:items-center;

            .description {
                @apply md:ml-2.5;
            }
        }
    }
}

.is-nav {
    @apply top-[120px] md:top-43.25;
}

.container {
    @apply max-md:!max-w-full;
}

#header > div#sub-header > .search-header-top {
    @apply max-md:hidden;
}

.suggestion-search-header {
    @apply absolute;
}

.search-header-form,
.suggestion-search-header {
    @apply screen-370.75:w-120 screen-346.25:max-screen-370.75:w-95 screen-262.5:max-screen-346:w-75 screen-255.75:max-screen-279.75:w-56.25 md:w-53.75;
}

.suggestion-search-header {
    @apply ml-0;
}

#search_mini_form_homepage2 {
    .suggestion-search-header {
        @apply w-[calc(90vw-35px)] screen-110:w-[calc(96vw-46px)] md:w-[325px] screen-205:w-[355px] screen-255.5:w-[475px] lg:w-[340px] screen-262.5:w-[355px] screen-267.5:w-[370px] screen-279.75:w-[400px] screen-282.5:w-[405px] screen-287.25:w-[415px] screen-295:w-[435px] screen-308.75:w-[465px] screen-325:w-[485px] screen-333.75:w-[505px] screen-346:w-[535px] screen-370.75:w-[595px] 2xl:w-[430px] 4xl:w-[468px];
        max-height:60vh;
        overflow:hidden;
        z-index:1;
    }
}

.catalog-category-view {
    #search_mini_form_homepage2 {
        .suggestion-search-header {
            @apply w-[calc(90vw-35px)] screen-110:w-[calc(96vw-46px)] md:w-[345px] screen-205:w-[375px] screen-255.5:w-[495px] lg:w-[405px] screen-262.5:w-[415px] screen-267.5:w-[425px] screen-279.75:w-[450px] screen-282.5:w-[460px] screen-287.25:w-[470px] screen-295:w-[485px] screen-308.75:w-[515px] screen-325:w-[525px] screen-333.75:w-[540px] screen-346:w-[565px] screen-370.75:w-[615px] 2xl:w-[440px] 4xl:w-[480px];
        }
    }
}

.switcher-language {
    @apply max-md:hidden;
}


.title-category-view-top {
    @apply max-md:text-4.25 md:text-5 xl:text-8 font-bold text-cadet-space tracking-0 opacity-100 ml-4.25;
}

.suggestion-search-mobile {
    @apply max-md:text-sm max-md:text-white max-md:rounded-lg max-md:border-2;
}

.header-logo {
    @apply max-md:absolute max-md:-translate-x-2/4 max-md:-translate-y-2/4 max-md:left-2/4 max-md:top-2/4;
}

.checkout-default {
    .header-logo {
        @apply static translate-x-0 translate-y-0;
    }
}

.wrapper-banner-category {
    @apply max-md:-mt-6.5;
}



.input-search-header {
    @apply max-md:text-3.25 md:h-10.25 md:text-sm opacity-100 rounded-lg border-transparent bg-white bg-no-repeat bg-clip-padding;

    ::placeholder {
        @apply text-silver-philippine opacity-100;
    }

    + button {
        @apply -ml-7.5;
    }

    &::-webkit-search-decoration,
    &::-webkit-search-cancel-button,
    &::-webkit-search-results-button,
    &::-webkit-search-results-decoration {
        -webkit-appearance:none;
    }
}

.logo-header {
    @apply max-md:w-full max-md:h-6 object-contain;
}

.category-description p {
    @apply max-md:text-3.25;
}

.category-description .pagebuilder-column-group {
    @apply max-md:!mt-0;
}

.help-category {
    @apply order-2 no-underline border border-saffron text-sm opacity-100 flex flex-row justify-end items-center rounded-lg border-solid md:w-65 md:justify-between md:mr-12.5 md:px-3.75 md:py-1.5 max-md:mr-3.75 max-md:px-3.75 max-md:py-0.75;

    .title {
        @apply md:block;
    }
}

.wrapper-home-categories .wrapper-child-home .category-title p a {
    @apply max-md:text-3.25;
}

.text-xs-lh-none {
    @apply text-xs;
}

.rounded-top-xl {
    @apply rounded-t-xl;
}

.container-page-content{
    @apply container !px-6;

    figure[data-content-type='image']{
        @apply my-auto md:pt-24 md:pb-20;
    }

    img {
        @apply rounded-xl clip-path-torn;
    }

    h3{
        @apply text-4.25 leading-6 md:text-2xl font-bold text-cadet-space mb-2 md:mb-4;
    }

    p{
        @apply text-3.25 md:text-base text-cadet-space;
    }

    .column-text{
        @apply m-auto md:px-87/1000 md:py-4 max-md:pt-4 md:py-6;
    }
}

.footer-description > div {
    @apply mr-screen-h-15;
}


.header-welcome {
    .welcome {
        @apply md:inline-flex md:justify-center md:items-center;

        .description {
            @apply md:ml-2.5;
        }
    }
    .reviews {
        @apply md:mr-35/500;
    }

    .stars {
        @apply mr-2.5;
    }
}

.footer-top {
    .reviews {
        @apply md:mr-35/500;
    }

    .stars {
        @apply mr-2.5;
    }
}

.header-tops-filters {
    @apply screen-333.75:mr-5 max-screen-325:hidden;

    span {
        @apply px-3.75 py-0;
    }
}

.category-description {
    .pagebuilder-column-line {
        .pagebuilder-column:first-child{
            @apply md:pr-6.25;
        }
        .pagebuilder-column:not(:first-child){
            @apply md:pl-6.25;
        }
    }
}

.wrapper-home-categories{
    .wrapper-child-home{
        @apply md:basis-auto;

        .category-title{
            p {
                a {
                    @apply md:text-lg;
                }
            }
        }
    }
}

.card-footer-payment-method {
    @apply flex justify-center items-center w-10.25 h-6.5 rounded opacity-100;
}

.show-menu-levels {
    @apply xl:w-127.5 lg:w-95 w-151.5 lg:shadow-md opacity-100 ml-47.5 rounded-tl-none rounded-tr-lg rounded-br-lg rounded-bl-none top-0 screen-267.5:w-108.75 screen-282.5:w-95 screen-295:w-108.75 screen-308.75:w-120 screen-333.75:w-142.5;
}

.container {
    @apply 4xl:max-w-screen-4xl;
}

.cms-home {
    [data-content-type=row][data-appearance=full-width]>.row-full-width-inner {
        @apply 4xl:max-w-screen-4xl;
    }
}

.wrapper-header {
    @apply h-18.75;
}


.rating-summary-rate {
    @apply whitespace-nowrap text-3.25 text-blue-prussian inline-block;

    .rating-result {
        @apply w-22 inline-flex relative align-middle;
    }
}

.header.content {
    @apply box-border max-w-177/200 w-auto relative bg-blue-prussian mx-auto pt-6.25 pb-0 px-5;
}

.topmenu-desktop a:link,
.topmenu-desktop a:visited,
.topmenu-desktop a:hover,
.topmenu-desktop a:active {
    @apply no-underline;
}

.content-categories {
    @apply h-53 mb-10;
}

.wrapper-page-content-category{

    h2 {
        @apply font-bold text-2xl mt-3.75;
    }

    [data-content-type=row]>div {
        @apply max-md:px-screen-h-5 !my-0 py-12.5;
    }

    .pagebuilder-column-group {
        @apply !mt-0;
    }

    [data-content-type="row"][data-appearance="contained"] {
        @apply bg-white pl-screen-h-5 !px-0 w-full max-w-full;

        [data-element=inner] {
            @apply md:w-22/25 md:mx-auto;
        }
    }

    [data-content-type="row"][data-appearance="contained"]:nth-child(even) {
        @apply bg-blue-mist;
    }

    [data-content-type=column] {
        @apply mb-auto;

        p, h2 {
            @apply md:w-full;
        }

        [data-content-type=text] {
            @apply md:m-auto;
        }
    }

    img {
        @apply my-5 rounded-xl clip-path-torn;
    }
}

.navigation-header {
    @apply shrink-0 w-full fixed z-30 basis-auto top-27.5 bg-white md:max-md:top-31.5 md:max-screen-255.75:top-22.75;
}

.title-categories-menu-header {
    @apply block lg:max-xl:hidden;
}


.navigation-desktop {
    @apply block box-border text-sm font-normal h-11.5 mx-auto px-0 py-1.75 left-auto;

    ul {
        @apply flex relative px-2 py-0;

        li {
            @apply inline-flex justify-start items-center no-underline box-border relative m-0 px-3.75 py-0;
        }
    }

    ul::-webkit-scrollbar {
        @apply h-0;
    }

    a, a:hover {
        @apply text-blue-picton no-underline;
    }
}

nav ul,
nav ol {
    @apply list-none;
}

.wrapper-categories-page {
    @apply mb-2.5;
}

.wrapper-category-view {
    @apply min-h-[75px] grid border border-[color:var(--gray-bright)] opacity-100 rounded-lg border-solid bg-white bg-no-repeat bg-clip-padding;
}

.title-category-image {
    @apply xl:max-screen-451:text-blue-picton xl:max-screen-451:text-4.25 xl:max-screen-451:font-bold xl:max-screen-451:no-underline xl:max-screen-451:block xl:max-screen-451:hyphens-auto xl:max-screen-451:break-words max-screen-480:text-blue-picton max-screen-480:text-4.25 max-screen-480:font-bold max-screen-480:no-underline max-screen-480:block max-screen-480:hyphens-auto max-screen-480:break-words;
}

.category-image-view-top {
    @apply w-auto h-16.5 opacity-100 ml-1.75 rounded-xl;
}


#maincontent {
    @apply min-h-56.25 mb-20;
}

.wrapper-categories-child {
    @apply border border-gray-bright h-full opacity-100 p-5 rounded-xl border-solid bg-white bg-no-repeat bg-clip-padding;

    ul > li,
    ol > li {
        @apply mt-0 mb-1.25;
    }
}

.list-item-category-child {
    @apply list-none mt-5 pl-0;

    li.title a {
        @apply text-lg text-blue-prussian font-bold opacity-100 tracking-0 no-underline flex justify-between mb-2.5;
    }

    li a {
        @apply text-base text-blue-picton font-normal opacity-100 tracking-0 no-underline flex justify-between;
    }
}

.other-all-category-child {
    @apply mt-2.5;

    a {
        @apply text-base text-blue-picton font-normal opacity-100 tracking-0 no-underline flex justify-between;
    }
}

.img-child-category {
    @apply h-45 max-h-45;
}

.wrapper-child-categories-last {
    @apply text-center h-full border border-gray-bright opacity-100 flex flex-col items-center justify-between border-solid bg-white bg-no-repeat bg-clip-padding;

    a {
        @apply no-underline text-blue-picton font-bold;
    }
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
    @apply bg-[none];
}

.title-countainer {
    @apply container max-md:mb-3;
}

.__web-inspector-hide-shortcut__,
.__web-inspector-hide-shortcut__ *,
.__web-inspector-hidebefore-shortcut__::before,
.__web-inspector-hideafter-shortcut__::after {
    visibility: visible !important;
}
