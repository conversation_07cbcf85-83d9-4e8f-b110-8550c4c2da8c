form,
fieldset {
    .field {
        @apply mt-1
    }

    :disabled {
        @apply !bg-gray-200 !text-gray-500 cursor-not-allowed shadow-none;
        opacity: 1;
    }

    /* Reserve space for single line form validation messages */
    .field.field-reserved {
        @apply mb-0
    }

    .field.field-reserved ul:last-of-type {
        @apply -mb-0 pb-1 /* The sum has to match the value set above for field.field-reserved */
    }

    .field.field-reserved ul {
        @apply text-sm
    }

    label {
        @apply mb-2 block text-secondary-darker
    }

    .field.choice {
        @apply flex items-center
    }

    .field.choice input {
        @apply mr-4
    }

    .field.choice label {
        @apply mb-0
    }

    .field.field-error .messages {
        @apply text-red-500 mt-1;
        max-width: fit-content;
    }

    legend {
        @apply text-primary text-xl mb-3
    }

    legend + br {
        @apply hidden
    }
}

fieldset ~ fieldset {
    @apply mt-8
}

input[type=checkbox],input[type=radio] {
    @apply border-cadet-space;

    &:checked {
        @apply bg-blue-picton border-blue-picton;

        &:hover {
            @apply bg-blue-picton;
        }
    }

    &:focus {
        @apply ring-blue-picton;

        &:checked {
            @apply bg-blue-picton;
        }
    }
}
