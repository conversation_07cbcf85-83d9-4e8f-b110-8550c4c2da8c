<?php
return [
    'modules' => [
        'Magento_Store' => 1,
        'Magento_Config' => 1,
        'Magento_AdminGraphQlServer' => 1,
        'Magento_AdminNotification' => 1,
        'Magento_AdobeIms' => 1,
        'Magento_AdobeImsApi' => 1,
        'Magento_AdvancedPricingImportExport' => 1,
        'Magento_Directory' => 1,
        'Magento_Amqp' => 1,
        'Magento_Theme' => 1,
        'Magento_ApplicationPerformanceMonitor' => 1,
        'Magento_AsyncConfig' => 1,
        'Magento_Backend' => 1,
        'Magento_Authorization' => 1,
        'Magento_Variable' => 1,
        'Magento_Eav' => 1,
        'Magento_User' => 1,
        'Magento_Backup' => 1,
        'Magento_Customer' => 1,
        'Magento_Indexer' => 1,
        'Magento_BundleImportExport' => 1,
        'Magento_CacheInvalidate' => 1,
        'Magento_Cms' => 1,
        'Magento_Rule' => 1,
        'Magento_Security' => 1,
        'Magento_GraphQl' => 1,
        'Magento_EavGraphQl' => 1,
        'Magento_CatalogImportExport' => 1,
        'Magento_Catalog' => 1,
        'Magento_CatalogPageBuilderAnalytics' => 1,
        'Magento_CatalogRule' => 1,
        'Magento_Payment' => 1,
        'Magento_CatalogRuleGraphQl' => 1,
        'Magento_Search' => 1,
        'Magento_CatalogUrlRewrite' => 1,
        'Magento_GraphQlResolverCache' => 1,
        'Magento_MediaStorage' => 1,
        'Magento_Quote' => 1,
        'Magento_SalesSequence' => 1,
        'Magento_Robots' => 1,
        'Magento_CmsGraphQl' => 1,
        'Magento_CmsPageBuilderAnalytics' => 1,
        'Magento_CmsUrlRewrite' => 1,
        'Magento_Integration' => 1,
        'Magento_Msrp' => 1,
        'Magento_Sales' => 1,
        'Magento_StoreGraphQl' => 1,
        'Magento_CatalogInventory' => 1,
        'Magento_Contact' => 1,
        'Magento_ContactGraphQl' => 1,
        'Magento_Cookie' => 1,
        'Magento_Cron' => 1,
        'Magento_Csp' => 1,
        'Magento_Widget' => 1,
        'Magento_Bundle' => 1,
        'Magento_Analytics' => 1,
        'Magento_Newsletter' => 1,
        'Magento_CustomerImportExport' => 1,
        'Magento_DataExporter' => 1,
        'Magento_Deploy' => 1,
        'Magento_Developer' => 1,
        'Magento_AdvancedSearch' => 1,
        'Magento_DirectoryGraphQl' => 1,
        'Magento_Downloadable' => 1,
        'Magento_CatalogGraphQl' => 1,
        'Magento_ImportExport' => 1,
        'Magento_CatalogCustomerGraphQl' => 1,
        'Magento_QuoteGraphQl' => 1,
        'Magento_CatalogSearch' => 1,
        'Magento_Elasticsearch' => 1,
        'Magento_Email' => 1,
        'Magento_EncryptionKey' => 1,
        'Magento_GiftMessage' => 1,
        'Magento_Checkout' => 1,
        'Magento_UrlRewriteGraphQl' => 1,
        'Magento_PageCache' => 1,
        'Magento_ConfigurableProduct' => 1,
        'Magento_GroupedProduct' => 1,
        'Magento_GroupedImportExport' => 1,
        'Magento_GroupedCatalogInventory' => 1,
        'Magento_GroupedProductGraphQl' => 1,
        'Magento_DownloadableImportExport' => 1,
        'Magento_ConfigurableImportExport' => 1,
        'Magento_InstantPurchase' => 1,
        'Magento_CatalogAnalytics' => 1,
        'Magento_IntegrationGraphQl' => 1,
        'Magento_Inventory' => 1,
        'Magento_InventoryAdminUi' => 1,
        'Magento_InventoryAdvancedCheckout' => 1,
        'Magento_InventoryApi' => 1,
        'Magento_InventoryBundleImportExport' => 1,
        'Magento_InventoryBundleProduct' => 1,
        'Magento_InventoryBundleProductAdminUi' => 1,
        'Magento_InventoryBundleProductIndexer' => 1,
        'Magento_InventoryCatalog' => 1,
        'Magento_InventorySales' => 1,
        'Magento_InventoryCatalogAdminUi' => 1,
        'Magento_InventoryCatalogApi' => 1,
        'Magento_InventoryCatalogFrontendUi' => 1,
        'Magento_InventoryCatalogSearch' => 1,
        'Magento_InventoryCatalogSearchBundleProduct' => 1,
        'Magento_InventoryCatalogSearchConfigurableProduct' => 1,
        'Magento_ConfigurableProductGraphQl' => 1,
        'Magento_InventoryConfigurableProduct' => 1,
        'Magento_InventoryConfigurableProductFrontendUi' => 1,
        'Magento_InventoryConfigurableProductIndexer' => 1,
        'Magento_InventoryConfiguration' => 1,
        'Magento_InventoryConfigurationApi' => 1,
        'Magento_InventoryDistanceBasedSourceSelection' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionAdminUi' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionApi' => 1,
        'Magento_InventoryElasticsearch' => 1,
        'Magento_InventoryExportStockApi' => 1,
        'Magento_InventoryIndexer' => 1,
        'Magento_InventoryGroupedProduct' => 1,
        'Magento_InventoryGroupedProductAdminUi' => 1,
        'Magento_InventoryGroupedProductIndexer' => 1,
        'Magento_InventoryImportExport' => 1,
        'Magento_InventoryCache' => 1,
        'Magento_InventoryLowQuantityNotification' => 1,
        'Magento_Reports' => 1,
        'Magento_InventoryLowQuantityNotificationApi' => 1,
        'Magento_InventoryMultiDimensionalIndexerApi' => 1,
        'Magento_InventoryProductAlert' => 1,
        'Magento_InventoryRequisitionList' => 1,
        'Magento_InventoryReservations' => 1,
        'Magento_InventoryReservationCli' => 1,
        'Magento_InventoryReservationsApi' => 1,
        'Magento_InventorySalesApi' => 1,
        'Magento_InventorySalesAdminUi' => 1,
        'Magento_InventoryExportStock' => 1,
        'Magento_InventorySalesAsyncOrder' => 1,
        'Magento_InventorySalesFrontendUi' => 1,
        'Magento_InventorySetupFixtureGenerator' => 1,
        'Magento_InventoryShipping' => 1,
        'Magento_Ui' => 1,
        'Magento_InventorySourceDeductionApi' => 1,
        'Magento_InventorySourceSelection' => 1,
        'Magento_InventorySourceSelectionApi' => 1,
        'Magento_InventorySwatchesFrontendUi' => 1,
        'Magento_InventoryVisualMerchandiser' => 1,
        'Magento_InventoryWishlist' => 1,
        'Magento_JwtFrameworkAdapter' => 1,
        'Magento_JwtUserToken' => 1,
        'Magento_LayeredNavigation' => 1,
        'Magento_LoginAsCustomer' => 1,
        'Magento_LoginAsCustomerAdminUi' => 1,
        'Magento_LoginAsCustomerApi' => 1,
        'Magento_LoginAsCustomerAssistance' => 1,
        'Magento_LoginAsCustomerFrontendUi' => 1,
        'Magento_LoginAsCustomerLog' => 1,
        'Magento_LoginAsCustomerPageCache' => 1,
        'Magento_LoginAsCustomerQuote' => 1,
        'Magento_LoginAsCustomerSales' => 1,
        'Magento_MediaContent' => 1,
        'Magento_MediaContentApi' => 1,
        'Magento_MediaContentCatalog' => 1,
        'Magento_MediaContentCms' => 1,
        'Magento_MediaContentSynchronization' => 1,
        'Magento_MediaContentSynchronizationApi' => 1,
        'Magento_MediaContentSynchronizationCatalog' => 1,
        'Magento_MediaContentSynchronizationCms' => 1,
        'Magento_MediaGallery' => 1,
        'Magento_MediaGalleryApi' => 1,
        'Magento_MediaGalleryCatalog' => 1,
        'Magento_MediaGalleryCatalogIntegration' => 1,
        'Magento_MediaGalleryCatalogUi' => 1,
        'Magento_MediaGalleryCmsUi' => 1,
        'Magento_MediaGalleryIntegration' => 1,
        'Magento_MediaGalleryMetadata' => 1,
        'Magento_MediaGalleryMetadataApi' => 1,
        'Magento_MediaGalleryRenditions' => 1,
        'Magento_MediaGalleryRenditionsApi' => 1,
        'Magento_MediaGallerySynchronization' => 1,
        'Magento_MediaGallerySynchronizationApi' => 1,
        'Magento_MediaGallerySynchronizationMetadata' => 1,
        'Magento_MediaGalleryUi' => 1,
        'Magento_MediaGalleryUiApi' => 1,
        'Magento_CatalogWidget' => 1,
        'Magento_MessageQueue' => 1,
        'Magento_CatalogRuleConfigurable' => 1,
        'Magento_MsrpConfigurableProduct' => 1,
        'Magento_MsrpGroupedProduct' => 1,
        'Magento_Multishipping' => 1,
        'Magento_MysqlMq' => 1,
        'Magento_CustomerGraphQl' => 1,
        'Magento_OfflinePayments' => 1,
        'Magento_SalesRule' => 1,
        'Magento_OpenSearch' => 1,
        'Magento_OrderCancellation' => 1,
        'Magento_OrderCancellationUi' => 1,
        'Magento_Sitemap' => 1,
        'Magento_PageBuilder' => 1,
        'Magento_PageBuilderAnalytics' => 1,
        'Magento_GraphQlCache' => 1,
        'Magento_Captcha' => 1,
        'Magento_Vault' => 1,
        'Magento_Paypal' => 1,
        'Magento_Persistent' => 1,
        'Magento_ProductAlert' => 1,
        'Magento_ProductVideo' => 1,
        'Magento_QueryXml' => 1,
        'Magento_CheckoutAgreements' => 1,
        'Magento_QuoteAnalytics' => 1,
        'Magento_QuoteBundleOptions' => 1,
        'Magento_QuoteConfigurableOptions' => 1,
        'Magento_QuoteDownloadableLinks' => 1,
        'Magento_DownloadableGraphQl' => 1,
        'Magento_ReCaptchaAdminUi' => 1,
        'Magento_ReCaptchaCheckout' => 1,
        'Magento_ReCaptchaCheckoutSalesRule' => 1,
        'Magento_ReCaptchaContact' => 1,
        'Magento_ReCaptchaCustomer' => 1,
        'Magento_ReCaptchaFrontendUi' => 1,
        'Magento_ReCaptchaMigration' => 1,
        'Magento_ReCaptchaNewsletter' => 1,
        'Magento_ReCaptchaPaypal' => 1,
        'Magento_ReCaptchaReview' => 1,
        'Magento_ReCaptchaSendFriend' => 1,
        'Magento_ReCaptchaStorePickup' => 1,
        'Magento_ReCaptchaUi' => 1,
        'Magento_ReCaptchaUser' => 1,
        'Magento_ReCaptchaValidation' => 1,
        'Magento_ReCaptchaValidationApi' => 1,
        'Magento_ReCaptchaVersion2Checkbox' => 1,
        'Magento_ReCaptchaVersion2Invisible' => 1,
        'Magento_ReCaptchaVersion3Invisible' => 1,
        'Magento_ReCaptchaWebapiApi' => 1,
        'Magento_ReCaptchaWebapiRest' => 1,
        'Magento_ReCaptchaWebapiUi' => 1,
        'Magento_ReCaptchaWishlist' => 1,
        'Magento_RelatedProductGraphQl' => 1,
        'Magento_ReleaseNotification' => 1,
        'Magento_RemoteStorage' => 1,
        'Magento_InventoryLowQuantityNotificationAdminUi' => 1,
        'Magento_RequireJs' => 1,
        'Magento_Review' => 1,
        'Magento_ReviewAnalytics' => 1,
        'Magento_ReviewGraphQl' => 1,
        'Magento_AwsS3' => 1,
        'Magento_Rss' => 1,
        'Magento_PageBuilderAdminAnalytics' => 1,
        'Magento_ConfigurableProductSales' => 1,
        'Magento_SalesAnalytics' => 1,
        'Magento_SalesDataExporter' => 1,
        'Magento_SalesGraphQl' => 1,
        'Magento_SalesInventory' => 1,
        'Magento_OfflineShipping' => 1,
        'Magento_InventoryConfigurableProductAdminUi' => 1,
        'Magento_Elasticsearch7' => 1,
        'Magento_CustomerAnalytics' => 1,
        'Magento_Securitytxt' => 1,
        'Magento_Shipping' => 1,
        'Magento_AwsS3PageBuilder' => 1,
        'Magento_TwoFactorAuth' => 1,
        'Magento_StoreDataExporter' => 1,
        'Magento_BundleGraphQl' => 1,
        'Magento_Swatches' => 1,
        'Magento_SwatchesGraphQl' => 1,
        'Magento_SwatchesLayeredNavigation' => 1,
        'Magento_Tax' => 1,
        'Magento_TaxImportExport' => 1,
        'Magento_AsynchronousOperations' => 1,
        'Magento_Translation' => 1,
        'Magento_AdminAdobeIms' => 1,
        'Magento_InventoryShippingAdminUi' => 1,
        'Magento_UrlRewrite' => 1,
        'Magento_CatalogUrlRewriteGraphQl' => 1,
        'Magento_AdminAdobeImsTwoFactorAuth' => 1,
        'Magento_GoogleGtag' => 1,
        'Magento_PaypalCaptcha' => 1,
        'Magento_Version' => 1,
        'Magento_Webapi' => 1,
        'Magento_WebapiAsync' => 1,
        'Magento_WebapiSecurity' => 1,
        'Magento_Weee' => 1,
        'Magento_CurrencySymbol' => 1,
        'Magento_Wishlist' => 1,
        'Magento_WishlistAnalytics' => 1,
        'Magento_WishlistGraphQl' => 1,
        'Amasty_Base' => 1,
        'Amasty_CronSchedule' => 1,
        'Amasty_ImportExportCore' => 1,
        'Amasty_ExportCore' => 1,
        'Amasty_ImageOptimizer' => 1,
        'Amasty_ImageOptimizerUi' => 1,
        'Amasty_ImageOptimizerSpeedSize' => 1,
        'Amasty_ImportCore' => 1,
        'Amasty_ExportPro' => 1,
        'Amasty_ImportPro' => 1,
        'Amasty_InventoryExportEntity' => 1,
        'Amasty_LazyLoad' => 1,
        'Amasty_LazyLoadUi' => 1,
        'Amasty_Oaction' => 1,
        'Amasty_OrderExportEntity' => 1,
        'Amasty_OrderExport' => 1,
        'Amasty_OrderImportEntity' => 1,
        'Amasty_OrderImport' => 1,
        'Amasty_PageSpeedOptimizer' => 1,
        'Amasty_PageSpeedOptimizerPro' => 1,
        'Amasty_PageSpeedOptimizerSubscriptionPackagePro' => 1,
        'Amasty_PageSpeedTools' => 1,
        'AvS_ScopeHint' => 1,
        'Baldwin_UrlDataIntegrityChecker' => 1,
        'Catgento_AdminActivity' => 1,
        'DpdConnect_Shipping' => 1,
        'EcomDev_DisablePriceCache' => 1,
        'EcomDev_LayeredNavigation' => 1,
        'EcomDev_LayoutCache' => 1,
        'EcomDev_PageCacheFixes' => 1,
        'EcomDev_ProductDataPreLoader' => 1,
        'EcomDev_ProductPreloader' => 1,
        'EcomDev_TopMenuFixes' => 1,
        'Element119_IndexerDeployConfig' => 1,
        'Elgentos_ConsentModeV2' => 1,
        'Elgentos_CreateCustomerOrderLink' => 1,
        'Hyva_Admin' => 1,
        'Vendic_OhDear' => 1,
        'Elgentos_PopularSearchSuggestions' => 1,
        'Hyva_Theme' => 1,
        'Elgentos_RegenerateCatalogUrls' => 1,
        'Elgentos_Rumvision' => 1,
        'Elgentos_Skwirrel' => 1,
        'EthanYehuda_CronjobManager' => 1,
        'Experius_WysiwygDownloads' => 1,
        'Flekto_Postcode' => 1,
        'Fooman_EmailAttachments' => 1,
        'Fooman_PdfCore' => 1,
        'Fooman_PdfDesign' => 1,
        'Fooman_PrintOrderPdf' => 1,
        'Fooman_PdfCustomiser' => 1,
        'Geissweb_Euvat' => 1,
        'Gene_EncryptionKeyManager' => 1,
        'GhoSter_ChangeCustomerPassword' => 1,
        'Elgentos_InventoryLog' => 1,
        'Magewirephp_Magewire' => 1,
        'Hyva_Checkout' => 1,
        'Hyva_CheckoutAutoComplete' => 1,
        'Hyva_CompatModuleFallback' => 1,
        'Hyva_DpdConnectShipping' => 1,
        'Hyva_Email' => 1,
        'Hyva_GraphqlTokens' => 1,
        'Hyva_GraphqlViewModel' => 1,
        'Hyva_ThemeFallback' => 1,
        'Hyva_MollieThemeBundle' => 1,
        'Hyva_OrderCancellationWebapi' => 1,
        'Smile_ElasticsuiteCore' => 1,
        'Elgentos_PostcodeValidator' => 1,
        'Hyva_LumaCheckout' => 1,
        'IntegerNet_SansecWatch' => 1,
        'Interactivated_Customerreview' => 1,
        'JustBetter_Sentry' => 1,
        'JustBetter_SentryFilterEvents' => 1,
        'Magefan_AdminUserGuide' => 1,
        'Magefan_Community' => 1,
        'Magefan_WebP' => 1,
        'Magenizr_ResetUiBookmarks' => 1,
        'Mageplaza_Core' => 1,
        'Mageplaza_Blog' => 1,
        'Mageplaza_Smtp' => 1,
        'MagestyApps_WebImages' => 1,
        'Hyva_CheckoutPostcodeNL' => 1,
        'Magmodules_Channable' => 1,
        'MarkShust_DisableTwoFactorAuth' => 1,
        'Mollie_Payment' => 1,
        'Mollie_HyvaCompatibility' => 1,
        'Mollie_HyvaCheckout' => 1,
        'PayPal_Braintree' => 0,
        'PayPal_BraintreeGraphQl' => 0,
        'RedChamps_CleanMenu' => 1,
        'Returnless_Connector' => 1,
        'Sansec_Shield' => 1,
        'Semaio_ConfigImportExport' => 1,
        'Siteation_HyvaIconsFlags' => 1,
        'Smile_ElasticsuiteAdminNotification' => 1,
        'Smile_ElasticsuiteCatalog' => 1,
        'Smile_ElasticsuiteSwatches' => 1,
        'Smile_ElasticsuiteCatalogGraphQl' => 1,
        'Smile_ElasticsuiteCatalogRule' => 1,
        'Smile_ElasticsuiteCatalogOptimizer' => 1,
        'Smile_ElasticsuiteTracker' => 1,
        'Smile_ElasticsuiteThesaurus' => 1,
        'Hyva_SmileElasticsuite' => 1,
        'Smile_ElasticsuiteIndices' => 1,
        'Smile_ElasticsuiteAnalytics' => 1,
        'Smile_ElasticsuiteVirtualCategory' => 1,
        'Squeezely_Plugin' => 1,
        'Ssx_RemoveElasticSuiteNotification' => 1,
        'Swissup_AddressAutocomplete' => 1,
        'Swissup_Checkout' => 1,
        'Swissup_Codemirror' => 1,
        'Swissup_Core' => 1,
        'Swissup_Marketplace' => 1,
        'Tops_AutocompletePriceFix' => 1,
        'Tops_Breadcrumb' => 1,
        'Tops_Channable' => 1,
        'Vendic_HyvaCheckoutHideBusinessFields' => 1,
        'Tops_Csp' => 1,
        'Tops_CustomSkwirrelActions' => 1,
        'Tops_DpdExtend' => 1,
        'Tops_ElasticSuite' => 1,
        'Tops_MinicartForceReload' => 1,
        'Tops_OrderStatus' => 1,
        'Tops_OrderSupplier' => 1,
        'Udigital_CategoryAttribute' => 1,
        'Tops_StockStatus' => 1,
        'Tops_SupplierCodeLookup' => 1,
        'Udigital_Blog' => 1,
        'Udigital_CatalogProduct' => 1,
        'Tops_SetShippingCountryObserver' => 1,
        'Udigital_ContactThankyou' => 1,
        'Udigital_CustomerAttribute' => 1,
        'Udigital_DynamicFaq' => 1,
        'Udigital_DynamicProduct' => 1,
        'Udigital_FilterModal' => 1,
        'Udigital_DynamicTitleCatHomePage' => 1,
        'Udigital_Homepage' => 1,
        'Udigital_ImportAdmin' => 1,
        'Udigital_Minor' => 1,
        'Udigital_OrderTrackandtrace' => 1,
        'Udigital_PageBuilder' => 1,
        'Udigital_ProductAttributePosition' => 1,
        'Udigital_Returnless' => 1,
        'Udigital_StoreInformation' => 1,
        'Vendic_GoogleAutocomplete' => 1,
        'Vendic_HyvaCheckoutGeisswebEuvat' => 1,
        'Vendic_HyvaCheckoutGoogleAddressAutocomplete' => 1,
        'Tops_CheckoutBusinessFields' => 1,
        'Elgentos_OhDearChecks' => 1,
        'Vendic_OptimizeCacheSize' => 1,
        'Yireo_CspUtilities' => 1,
        'Yireo_GoogleTagManager2' => 1
    ],
    'system' => [
        'default' => [
            'amoptimizer' => [
                'general' => [
                    'enabled' => 1
                ],
                'javascript' => [
                    'bundling_type' => 1,
                    'is_cloud' => 1,
                    'bundling_files' => '[]'
                ]
            ],
            'dev' => [
                'front_end_development_workflow' => [
                    'type' => 'server_side_compilation'
                ],
                'debug' => [
                    'gene_encryption_manager_only_log_old_decrypts' => '1',
                    'gene_encryption_manager_enable_decrypt_logging' => '1'
                ],
                'template' => [
                    'allow_symlink' => '0',
                    'minify_html' => '1'
                ],
                'translate_inline' => [
                    'active' => '0',
                    'active_admin' => '0',
                    'invalid_caches' => [
                        'block_html' => null
                    ]
                ],
                'js' => [
                    'merge_files' => '0',
                    'enable_js_bundling' => null,
                    'minify_files' => '0',
                    'move_script_to_bottom' => '0',
                    'translate_strategy' => 'dictionary',
                    'session_storage_logging' => '0',
                    'minify_exclude' => [
                        'tiny_mce' => '/tiny_mce/',
                        'cardinal_commerce' => '/v1/songbird'
                    ]
                ],
                'css' => [
                    'merge_css_files' => '1',
                    'minify_files' => '1',
                    'use_css_critical_path' => '0',
                    'minify_exclude' => [
                        'tiny_mce' => '/tiny_mce/'
                    ]
                ],
                'image' => [
                    'default_adapter' => 'GD2',
                    'adapters' => [
                        'GD2' => [
                            'title' => 'PHP GD2',
                            'class' => 'MagestyApps\\WebImages\\Model\\Image\\Adapter\\Gd2'
                        ],
                        'IMAGEMAGICK' => [
                            'title' => 'ImageMagick',
                            'class' => 'Magento\\Framework\\Image\\Adapter\\ImageMagick'
                        ]
                    ]
                ],
                'caching' => [
                    'cache_user_defined_attributes' => '0'
                ],
                'static' => [
                    'sign' => '1'
                ],
                'grid' => [
                    'async_indexing' => '0'
                ]
            ]
        ],
        'stores' => [
            'default_en' => [
                'general' => [
                    'locale' => [
                        'code' => 'en_US'
                    ]
                ]
            ],
            'de_en' => [
                'general' => [
                    'locale' => [
                        'code' => 'en_US'
                    ]
                ]
            ],
            'be_en' => [
                'general' => [
                    'locale' => [
                        'code' => 'en_US'
                    ]
                ]
            ],
            'at_en' => [
                'general' => [
                    'locale' => [
                        'code' => 'en_US'
                    ]
                ]
            ]
        ],
        'hyva_themes_checkout' => [
            'general' => [
                'checkout' => 'default'
            ]
        ]
    ],
    'scopes' => [
        'websites' => [
            'admin' => [
                'website_id' => '0',
                'code' => 'admin',
                'name' => 'Admin',
                'sort_order' => '0',
                'default_group_id' => '0',
                'is_default' => '0'
            ],
            'base' => [
                'website_id' => '1',
                'code' => 'base',
                'name' => 'Main Website',
                'sort_order' => '0',
                'default_group_id' => '1',
                'is_default' => '1'
            ],
            'web_de' => [
                'website_id' => '2',
                'code' => 'web_de',
                'name' => 'German Website',
                'sort_order' => '0',
                'default_group_id' => '2',
                'is_default' => '0'
            ],
            'web_be' => [
                'website_id' => '3',
                'code' => 'web_be',
                'name' => 'Belgian Website',
                'sort_order' => '0',
                'default_group_id' => '3',
                'is_default' => '0'
            ],
            'web_at' => [
                'website_id' => '4',
                'code' => 'web_at',
                'name' => 'Austria Website',
                'sort_order' => '0',
                'default_group_id' => '4',
                'is_default' => '0'
            ]
        ],
        'groups' => [
            [
                'group_id' => '0',
                'website_id' => '0',
                'name' => 'Default',
                'root_category_id' => '0',
                'default_store_id' => '0',
                'code' => 'default'
            ],
            [
                'group_id' => '1',
                'website_id' => '1',
                'name' => 'Main Website Store',
                'root_category_id' => '2',
                'default_store_id' => '1',
                'code' => 'main_website_store'
            ],
            [
                'group_id' => '2',
                'website_id' => '2',
                'name' => 'Topswtwfilters German',
                'root_category_id' => '492',
                'default_store_id' => '3',
                'code' => 'de_website_store'
            ],
            [
                'group_id' => '3',
                'website_id' => '3',
                'name' => 'Topswtwfilters Belgium',
                'root_category_id' => '494',
                'default_store_id' => '5',
                'code' => 'be_website_store'
            ],
            [
                'group_id' => '4',
                'website_id' => '4',
                'name' => 'Topswtwfilters Austria',
                'root_category_id' => '493',
                'default_store_id' => '7',
                'code' => 'at_website_store'
            ]
        ],
        'stores' => [
            'admin' => [
                'store_id' => '0',
                'code' => 'admin',
                'website_id' => '0',
                'group_id' => '0',
                'name' => 'Admin',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'default' => [
                'store_id' => '1',
                'code' => 'default',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'Default Store View',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'default_en' => [
                'store_id' => '2',
                'code' => 'default_en',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'Default English',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'de_de' => [
                'store_id' => '3',
                'code' => 'de_de',
                'website_id' => '2',
                'group_id' => '2',
                'name' => 'German Deutsch',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'de_en' => [
                'store_id' => '4',
                'code' => 'de_en',
                'website_id' => '2',
                'group_id' => '2',
                'name' => 'German English',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'be_nl' => [
                'store_id' => '5',
                'code' => 'be_nl',
                'website_id' => '3',
                'group_id' => '3',
                'name' => 'Belgium Dutch',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'be_en' => [
                'store_id' => '6',
                'code' => 'be_en',
                'website_id' => '3',
                'group_id' => '3',
                'name' => 'Belgium English',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'at_de' => [
                'store_id' => '7',
                'code' => 'at_de',
                'website_id' => '4',
                'group_id' => '4',
                'name' => 'Austria German',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'at_en' => [
                'store_id' => '8',
                'code' => 'at_en',
                'website_id' => '4',
                'group_id' => '4',
                'name' => 'Austria English',
                'sort_order' => '0',
                'is_active' => '1'
            ]
        ]
    ],
    'whitelisted_disabled_modules' => [
        'PayPal_Braintree',
        'PayPal_BraintreeGraphQl',
        'Magento_Multishipping',
        'Magento_ServicesConnector',
        'Magento_TwoFactorAuth',
        'RedChamps_CleanMenu',
        'Magento_Weee'
    ]
];
