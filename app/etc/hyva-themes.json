{"extensions": [{"src": "vendor/elgentos/magento2-consentmode-v2/src"}, {"src": "vendor/hyva-themes/magento2-theme-module/src"}, {"src": "vendor/magewirephp/magewire/src"}, {"src": "vendor/hyva-themes/magento2-hyva-checkout/src"}, {"src": "vendor/mollie/magento2-hyva-compatibility/src/<PERSON><PERSON>_HyvaCompatibility"}, {"src": "vendor/mollie/magento2-hyva-checkout/src/<PERSON><PERSON>_HyvaCheckout"}, {"src": "vendor/vendic/hyva-checkout-geissweb-euvat"}, {"src": "vendor/hyva-themes/magento2-hyva-checkout-postcodenl/src"}, {"src": "vendor/hyva-themes/magento2-smile-elasticsuite/src"}]}