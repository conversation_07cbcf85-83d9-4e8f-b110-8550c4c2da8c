{"name": "magento/project-community-edition", "description": "eCommerce Platform for Growth (Community Edition)", "type": "project", "license": ["OSL-3.0", "AFL-3.0"], "config": {"sort-packages": true, "platform": {"php": "8.3"}, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "digitalrevolution/php-codesniffer-baseline": true, "elgentos/coding-standard-phpstorm": true, "laminas/laminas-dependency-plugin": true, "magento/*": true, "php-http/discovery": true, "phpro/grumphp-shim": true, "phpstan/extension-installer": true, "vaimo/composer-patches": true, "youwe/testing-suite": true}, "preferred-install": "dist"}, "require": {"amasty/module-mass-order-actions": "^1.6", "amasty/module-order-export": "^2.0.1", "amasty/module-order-import": "^2.0.0", "amasty/module-page-speed-optimizer-subscription-package-pro": "^3.1", "avstudnitz/scopehint2": "^1.3", "baldwin/magento2-module-url-data-integrity-checker": "^1.5", "catgento/module-admin-activity": "^1.1", "community-engineering/language-nl_nl": "^0.0.67", "dpdconnect/magento2-shipping": "1.3.6", "dpdconnect/magento2-shipping-hyva": "1.0.2", "ecomdev/magento2-product-data-preloader": "^1.1", "element119/module-indexer-deploy-config": "^1.2", "elgentos/magento2-cicd": "^3.1", "elgentos/magento2-consentmode-v2": "^1.0", "elgentos/magento2-inventory-log": "^1.1", "elgentos/magento2-oh-dear-checks": "^0.0.10", "elgentos/magento2-rumvision": "^1.0", "elgentos/magento2-skwirrel": "dev-develop", "elgentos/regenerate-catalog-urls": "^0.4.5", "elgentos/testing-suite": "^0.2.1", "ethanyehuda/magento2-cronjobmanager": "^2.2", "experius/module-wysiwygdownloads": "^1.2", "fooman/pdfcustomiser-m2": "^8.0", "geissweb/module-euvat": "^1.21", "gene/module-encryption-key-manager": "v0.0.16-alpha", "ghoster/changecustomerpassword": "^1.0", "hyva-themes/magento2-default-theme": "^1.3", "hyva-themes/magento2-default-theme-csp": "^1.3", "hyva-themes/magento2-hyva-checkout": "1.2.0", "hyva-themes/magento2-hyva-checkout-postcodenl": "^1.0", "hyva-themes/magento2-luma-checkout": "^1.1", "hyva-themes/magento2-smile-elasticsuite": "^1.2", "integer-net/magento2-sansec-watch": "^1.0", "interactivated/customerreview": "^2.1", "justbetter/magento2-sentry": "^4.0", "justbetter/magento2-sentry-filter-events": "^1.2", "league/csv": "^9.16", "magefan/module-webp": "^2.3.1", "magenizr/magento2-resetuibookmarks": "^1.3", "magento/composer-dependency-version-audit-plugin": "~0.1", "magento/composer-root-update-plugin": "^2.0.4", "magento/product-community-edition": "2.4.7-p7", "magento/quality-patches": "^1.1", "mageplaza/magento-2-blog-extension": "^4.1", "mageplaza/magento-2-dutch-language-pack": "dev-master", "mageplaza/magento-2-english-united-kingdom-language-pack": "dev-master", "mageplaza/magento-2-german-language-pack": "dev-master", "mageplaza/module-smtp": "^4.7", "magestyapps/module-web-images": "^1.1", "magmodules/magento2-channable": "^1.21", "markshust/magento2-module-disabletwofactorauth": "^2.0", "mollie/magento2": "^2.29", "mollie/magento2-hyva-checkout": "^1.7", "opensearch-project/opensearch-php": "^2.0,<2.4.0", "redchamps/module-clean-admin-menu": "^1.1", "returnless-com/magento2": "^1.3", "sansec/magento2-module-shield": "^1.0", "semaio/magento2-configimportexport": "^4.4", "siteation/magento2-hyva-icons-flags": "^1.0", "smile/elasticsuite": "2.11.13", "squeezely/magento2-plugin": "^2.10", "ssx/module-remove-elastic-suite-notification": "^1.1", "swissup/address-autocomplete": "^1.7", "swissup/module-marketplace": "^1.10", "vaimo/composer-patches": "^5.2", "vendic/hyva-checkout-geissweb-euvat": "^1.4", "vendic/hyva-checkout-google-address-autocomplete": "^1.4", "vendic/hyva-checkout-hide-business-fields": "^1.0", "vendic/module-optimize-cache-size": "^1.0", "yireo/magento2-googletagmanager2": "^3.9", "youwe/coding-standard-magento2": "^2.2"}, "replace": {"magento/services-connector": "*", "magento/module-adobe-stock-asset": "*", "magento/module-adobe-stock-asset-api": "*", "magento/module-adobe-stock-client": "*", "magento/module-adobe-stock-client-api": "*", "magento/module-adobe-stock-image": "*", "magento/module-adobe-stock-image-api": "*", "magento/module-adobe-stock-image-admin-ui": "*", "magento/module-adobe-stock-admin-ui": "*", "magento/module-admin-analytics": "*", "magento/module-application-performance-monitor-new-relic": "*", "magento/module-catalog-cms-graph-ql": "*", "magento/module-cardinal-commerce": "*", "magento/module-catalog-inventory-graph-ql": "*", "magento/module-checkout-agreements-graph-ql": "*", "magento/module-cms-url-rewrite-graph-ql": "*", "magento/module-compare-list-graph-ql": "*", "magento/module-customer-downloadable-graph-ql": "*", "magento/module-dhl": "*", "magento/module-fedex": "*", "magento/module-gift-message-graph-ql": "*", "magento/module-google-adwords": "*", "magento/module-google-analytics": "*", "magento/module-google-optimizer": "*", "magento/module-graph-ql-new-relic": "*", "magento/module-graph-ql-server": "*", "magento/module-inventory-in-store-pickup": "*", "magento/module-inventory-in-store-pickup-admin-ui": "*", "magento/module-inventory-in-store-pickup-api": "*", "magento/module-inventory-in-store-pickup-frontend": "*", "magento/module-inventory-in-store-pickup-quote": "*", "magento/module-inventory-in-store-pickup-sales": "*", "magento/module-inventory-in-store-pickup-sales-admin-ui": "*", "magento/module-inventory-in-store-pickup-sales-api": "*", "magento/module-inventory-in-store-pickup-shipping": "*", "magento/module-inventory-in-store-pickup-shipping-admin-ui": "*", "magento/module-inventory-in-store-pickup-shipping-api": "*", "magento/module-inventory-in-store-pickup-webapi-extension": "*", "magento/module-inventory-graph-ql": "*", "magento/module-inventory-in-store-pickup-graph-ql": "*", "magento/module-inventory-in-store-pickup-multishipping": "*", "magento/module-inventory-in-store-pickup-quote-graph-ql": "*", "magento/module-inventory-quote-graph-ql": "*", "magento/module-login-as-customer-graph-ql": "*", "magento/module-marketplace": "*", "magento/module-multiple-wishlist-graph-ql": "*", "magento/module-new-relic-reporting": "*", "magento/module-newsletter-graph-ql": "*", "magento/module-order-cancellation-graph-ql": "*", "magento/module-payment-services-paypal-graph-ql": "*", "magento/module-payment-graph-ql": "*", "magento/module-paypal-graph-ql": "*", "magento/module-re-captcha-webapi-graph-ql": "*", "magento/module-sales-rule-graph-ql": "*", "magento/module-services-id-graph-ql-server": "*", "magento/module-service-proxy": "*", "magento/module-services-id": "*", "magento/module-services-id-layout": "*", "magento/module-saas-common": "*", "magento/module-sample-data": "*", "magento/module-send-friend": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-swagger": "*", "magento/module-swagger-webapi": "*", "magento/module-swagger-webapi-async": "*", "magento/module-tax-graph-ql": "*", "magento/module-theme-graph-ql": "*", "magento/module-vault-graph-ql": "*", "magento/module-weee-graph-ql": "*", "magento/module-ups": "*", "magento/module-usps": "*", "magento/module-payment-services-base": "*", "magento/module-payment-services-dashboard": "*", "magento/module-payment-services-paypal-graphql": "*", "magento/module-payment-services-paypal": "*", "magento/module-payment-services-saas-export": "*", "paypal/module-braintree-customer-balance": "*", "paypal/module-braintree-gift-card-account": "*", "paypal/module-braintree-gift-wrapping": "*", "paypal/module-braintree-graphql": "*"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\Setup\\": "setup/src/Magento/Setup/"}}, "require-dev": {"allure-framework/allure-phpunit": "^2.1", "bitexpert/phpstan-magento": "^0.32.0", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "dg/bypass-finals": "^1.9", "friendsofphp/php-cs-fixer": "^3.70", "hypernode/deploy-configuration": "^3.3", "hyva-themes/upgrade-helper-tools": "dev-main", "lusitanian/oauth": "^0.8.11", "magento/magento-coding-standard": "^36.0", "magento/magento2-functional-testing-framework": "^4.7", "pdepend/pdepend": "^2.16", "phpcompatibility/php-compatibility": "^9.3", "phpmd/phpmd": "^2.15", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^9.6", "sebastian/phpcpd": "^6.0", "squizlabs/php_codesniffer": "^3.11", "symfony/finder": "^6.4"}, "conflict": {"gene/bluefoot": "*"}, "autoload-dev": {"psr-4": {"Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/", "Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/"}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"packagist": {"type": "composer", "url": "https://repo.packagist.com/elgentos/tops/"}, "packagist.org": false}, "extra": {"magento-force": "override", "patches-file": ["composer.patches.json"], "patches-search": "patches", "composer-exit-on-patch-failure": true}, "archive": {"exclude": ["/.giti<PERSON>re", "/grumphp.yml", "/pdepend.xml", "/phpstan.neon", "/phpunit.xml", "/phpcs.xml", "/phpmd.xml"]}, "scripts": {"post-install-cmd": ["./vendor/bin/magento-patches status -n | grep 'Not applied' | cut -d ' ' -f2 | xargs --no-run-if-empty ./vendor/bin/magento-patches apply"]}}