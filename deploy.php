<?php

namespace Deployer;

require_once 'vendor/elgentos/magento2-cicd/deploy.php';

task('deploy:symlink:languages', function () {
    run('ln -s /data/web/public /data/web/public/en');
});

//Temp. Remove after `Go Live`
task('basic-auth:remove-existing', function () {
    run('rm /data/web/nginx/server.elgentos.basicauth 2> /dev/null');
})->select('stage=review');

before('magento:cache:flush', 'deploy:symlink:languages');
after('nginx:config:upload', 'basic-auth:remove-existing');
