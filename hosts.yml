hosts:
  .base: &base
    remote_user: app
    port: 22
    deploy_path: /data/web
    hypernode: true
    setSshOptions:
      UserKnownHostsFile: /dev/null
      StrictHostKeyChecking: no
      ConnectionAttempts: 100

  topswtwfilters.hypernode.io:
    <<: *base
    hostname: topswtwfilters.hypernode.io
    labels:
      stage: production

  REVIEW_HOSTNAME:
    <<: *base
    hostname: REVIEW_HOSTNAME
    labels:
      stage: review
    dbname: magento
