From 9257f45ac8c5eed8a20e78f7669a8d3c3de5c845 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 23 Jun 2025 14:22:17 +0200
Subject: [PATCH] Add view model back to xml

@package dpdconnect/magento2-shipping
@level 0

diff --git view/adminhtml/layout/adminhtml_order_shipment_view.xml view/adminhtml/layout/adminhtml_order_shipment_view.xml
index 9602bc0..0ca0544 100644
--- view/adminhtml/layout/adminhtml_order_shipment_view.xml	2025-04-14 13:23:05.000000000 +0200
+++ view/adminhtml/layout/adminhtml_order_shipment_view.xml	2025-06-23 14:25:17.768908328 +0200
@@ -22,7 +22,14 @@
                         <block class="Magento\Sales\Block\Adminhtml\Items\Column\Name" name="column_name" template="Magento_Sales::items/column/name.phtml" group="column"/>
                         <block class="Magento\Framework\View\Element\Text\ListText" name="order_item_extra_info"/>
                     </block>
-                    <block class="Magento\Sales\Block\Adminhtml\Order\Comments\View" name="order_comments" template="Magento_Sales::order/comments/view.phtml"/>
+                    <block class="Magento\Sales\Block\Adminhtml\Order\Comments\View" name="order_comments" template="Magento_Sales::order/comments/view.phtml">
+                        <action method="setParentType">
+                            <argument name="type" xsi:type="string">shipment</argument>
+                        </action>
+                        <arguments>
+                            <argument name="editCommentCheck" xsi:type="object">Magento\Sales\ViewModel\Order\Entity\EditCommentCheck</argument>
+                        </arguments>
+                    </block>
                     <block class="Magento\Shipping\Block\Adminhtml\Order\Tracking\View" name="shipment_tracking" template="Magento_Shipping::order/tracking/view.phtml"/>
                     <block class="DpdConnect\Shipping\Block\Adminhtml\Order\Packaging" name="shipment_packaging" template="DpdConnect_Shipping::order/packaging/popup.phtml"/>
                     <block class="Magento\Shipping\Block\Adminhtml\Order\Packaging" name="shipment_packed" template="Magento_Shipping::order/packaging/packed.phtml"/>
