@package elgentos/magento2-skwirrel
@level 0

See https://github.com/magento/magento2/issues/35444

From c30224cff0023964bf6d9f44a6900fabd9476f22 Mon Sep 17 00:00:00 2001
From: peterjaap <<EMAIL>>
Date: Thu, 3 Jul 2025 15:23:41 +0200
Subject: [PATCH] Use model to save model instead of repository to circumvent bug with tier prices


diff --git src/Processor/Action/Product/StoreProduct.php src/Processor/Action/Product/StoreProduct.php
index 630951c..2a20882 100644
--- src/Processor/Action/Product/StoreProduct.php
+++ src/Processor/Action/Product/StoreProduct.php
@@ -50,7 +50,9 @@ class StoreProduct implements ActionInterface
             return $model;
         }
 
-        $model = $this->productRepository->save($model);
+        // Commented out, see https://github.com/magento/magento2/issues/35444
+        // $model = $this->productRepository->save($model);
+        $model->save();
 
         if (!$model->getId() || !$model instanceof Product) {
             throw new UnprocessableEntityException(
