From 214dbaeea85cf1a79e1863f076309a5aa2876ecf Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 20 Mar 2025 11:52:51 +0100
Subject: [PATCH] Added check for instance in plugin list

@package magento/framework
@level 0

diff --git Interception/PluginList/PluginList.php Interception/PluginList/PluginList.php
index 67c53e4..578b29f 100644
--- Interception/PluginList/PluginList.php
+++ Interception/PluginList/PluginList.php
@@ -170,9 +170,11 @@ class PluginList extends Scoped implements InterceptionPluginList
     public function getPlugin($type, $code)
     {
         if (!isset($this->_pluginInstances[$type][$code])) {
-            $this->_pluginInstances[$type][$code] = $this->_objectManager->get(
-                $this->_inherited[$type][$code]['instance']
-            );
+            if (isset($this->_inherited[$type][$code]['instance'])) {
+                $this->_pluginInstances[$type][$code] = $this->_objectManager->get(
+                    $this->_inherited[$type][$code]['instance']
+                );
+            }
         }
         return $this->_pluginInstances[$type][$code];
     }
