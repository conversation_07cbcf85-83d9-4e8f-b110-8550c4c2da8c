@package magento/framework
@level 0

[PATCH] Unsubscribe mviews only when action classes are present


diff --git Mview/TriggerCleaner.php Mview/TriggerCleaner.php
index 81ccf9a..aba8d5f 100644
--- Mview/TriggerCleaner.php
+++ Mview/TriggerCleaner.php
@@ -87,8 +87,10 @@ class TriggerCleaner
         $remainingTriggers = array_diff_key($this->DbTriggers, $this->processedTriggers);
         foreach ($remainingTriggers as $trigger) {
             $view = $this->createViewByTableName($trigger['EVENT_OBJECT_TABLE']);
-            $view->unsubscribe();
-            $view->getState()->delete();
+            if ($view->getActionClass()) {
+                $view->unsubscribe();
+                $view->getState()->delete();
+            }
         }
 
         return true;
