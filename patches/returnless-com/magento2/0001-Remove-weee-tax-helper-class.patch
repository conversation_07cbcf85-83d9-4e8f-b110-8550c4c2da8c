@package returnless-com/magento2

diff --color -urN /Model/Api/OrderInfo.php /Model/Api/OrderInfo.php
--- Model/Api/OrderInfo.php	2024-07-31 13:49:14.000000000 +0200
+++ Model/Api/OrderInfo.php	2025-05-30 09:46:46.595241635 +0200
@@ -12,7 +12,6 @@
 use Returnless\Connector\Model\Config;
 use Magento\Framework\Exception\NoSuchEntityException;
 use Magento\Framework\App\ObjectManager;
-use Magento\Weee\Helper\Data;
 use Returnless\Connector\Helper\Data as RetHelper;
 
 /**
@@ -74,11 +73,6 @@
     protected $searchCriteriaBuilder;
 
     /**
-     * @var Data
-     */
-    protected $weeeHelper;
-
-    /**
      * @var RetHelper
      */
     protected $retHelper;
@@ -93,7 +87,6 @@
      * @param ResourceInterface $moduleResource
      * @param OrderRepository $orderRepository
      * @param SearchCriteriaBuilder $searchCriteriaBuilder
-     * @param Data $weeeHelper
      * @param RetHelper $retHelper
      */
     public function __construct(
@@ -104,7 +97,6 @@
         ResourceInterface $moduleResource,
         OrderRepository $orderRepository,
         SearchCriteriaBuilder $searchCriteriaBuilder,
-        Data $weeeHelper,
         RetHelper $retHelper
     ){
         $this->productRepository = $productRepository;
@@ -114,7 +106,6 @@
         $this->moduleResource = $moduleResource;
         $this->orderRepository = $orderRepository;
         $this->searchCriteriaBuilder = $searchCriteriaBuilder;
-        $this->weeeHelper = $weeeHelper;
         $this->retHelper = $retHelper;
     }
 
@@ -213,8 +204,7 @@
                 $totalPrice = $orderItem->getRowTotal()
                     - $orderItem->getDiscountAmount()
                     + $orderItem->getTaxAmount()
-                    + $orderItem->getDiscountTaxCompensationAmount()
-                    + $this->weeeHelper->getRowWeeeTaxInclTax($orderItem);
+                    + $orderItem->getDiscountTaxCompensationAmount();
                 $orderInfo['order_products'][$orderItemKey]['total_price'] = $totalPrice;
                 $orderInfo['order_products'][$orderItemKey]['model'] = $orderItem->getSku();
                 $orderInfo['order_products'][$orderItemKey]['name'] = $orderItem->getName();
