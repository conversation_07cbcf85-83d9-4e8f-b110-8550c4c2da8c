<?xml version="1.0"?>
<ruleset name="PHPCS"
         xmlns="http://pmd.sf.net/ruleset/1.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd"
         xsi:noNamespaceSchemaLocation="http://pmd.sf.net/ruleset_xml_schema.xsd">
    <description>PHPCS</description>
    <exclude-pattern>phpserver/*</exclude-pattern>
    <exclude-pattern>app/etc/*</exclude-pattern>
    <exclude-pattern>dev/*</exclude-pattern>
    <exclude-pattern>setup/*</exclude-pattern>
    <exclude-pattern>extensions/*</exclude-pattern>
    <exclude-pattern>pub/*</exclude-pattern>
    <exclude-pattern>generated/*</exclude-pattern>
    <exclude-pattern>app/bootstrap.php</exclude-pattern>
    <exclude-pattern>app/autoload.php</exclude-pattern>
    <exclude-pattern>app/code/Tops/CustomSkwirrelActions/*</exclude-pattern>
    <exclude-pattern>app/code/Udigital/*</exclude-pattern>
    <exclude-pattern>app/design/frontend/Udigital/Topswtw/*</exclude-pattern>
    <exclude-pattern>vendor/*</exclude-pattern>
    <exclude-pattern>deploy.php</exclude-pattern>
    <exclude-pattern>index.php</exclude-pattern>
    <rule ref="YouweMagento2">
        <exclude name="PSR2"/>
        <exclude name="PHPCompatibility.Classes.NewReadonlyClasses.Found" />
    </rule>
</ruleset>
