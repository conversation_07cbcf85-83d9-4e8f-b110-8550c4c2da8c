includes:
    - phpstan-baseline.neon

parameters:
  level: 6
  paths:
      - app
  ignoreErrors:
      - '#(class|type) <PERSON>gento\\TestFramework#i'
      - '#(class|type) Magento\\\S*Factory#i'
      - '#(class|type) Magento\\\S*ExtensionInterface#i'
      - '#(method) Magento\\Framework\\Api\\ExtensionAttributesInterface#i'
      - '#(class|type) Elgentos\\TestFramework#i'
      - '#(class|type) Elgentos\\\S*Factory#i'
      - '#(class|type) Elgentos\\\S*ExtensionInterface#i'
      - '#(method) Magento\\Framework\\GraphQl\\Query\\Resolver\\ContextInterface#i'
  excludePaths:
      - %rootDir%/../../../patches/*
      - %rootDir%/../../../phpserver/*
      - %rootDir%/../../../deploy.php
      - %rootDir%/../../../app/etc/*
      - %rootDir%/../../../.php-cs-fixer.dist.php
      - %rootDir%/../../../app/code/Udigital/*
      - %rootDir%/../../../app/code/Tops/CustomSkwirrelActions/*
      - %rootDir%/../../../app/extensions/*
      - %rootDir%/../../../index.php
  treatPhpDocTypesAsCertain: false
